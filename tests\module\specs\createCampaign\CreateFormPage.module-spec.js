/// <reference types="Cypress" />

import CreateFormPage from '@pages/createCampaign/CreateFormPage';
import CreateCampaignMock from '@mock/CreateCampaignMock';
import AdvertiserMock from '@mock/AdvertiserMock';
const createFormPage = new CreateFormPage();
const createCampaignService = new CreateCampaignMock();
const advertiserService = new AdvertiserMock();

const sessionInfo = {
  sessionId: 'test-session-id',
  memberId: 1,
  memberName: 'Hong Gil-dong',
  roleType: 'USER',
};


const setupPage = () => {
  advertiserService.successWhenGetAdvertisers();
  createCampaignService.successWhenGetMediaAccount();
  createCampaignService.successWhenGetShoppingMall();
  createCampaignService.successWhenGetProducts()
};

const visitAndWait = () => {
  createFormPage.fakeSession(sessionInfo);
  createFormPage.visitCreateCampaignPage();
  cy.wait('@successWhenGetAdvertisers');
  cy.wait('@successWhenGetMediaAccount');
  cy.wait('@successWhenGetShoppingMall');
  cy.wait('@successWhenGetProducts');
};

describe('Create Campaign Form Page', () => {
  beforeEach(() => {
    cy.window().then((win) => {
      win.localStorage.setItem('lang', 'en');
    });
  });

  describe('Campaign Basic Information Form', () => {
    it.guide('should handle campaign name input and validation', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();

        createFormPage.typeCampaignName('Test Campaign 2024');
      },
      assertFunc: () => {
        cy.get(createFormPage.CAMPAIGN_NAME_INPUT).should('have.value', 'Test Campaign 2024');
        createFormPage.assertCampaignNameRequired();
      }
    });

    it.guide('should handle daily budget options', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();
        createFormPage.selectDailyBudgetUnlimited();
        createFormPage.selectDailyBudgetLimited();
        createFormPage.setDailyBudget(50000);
      },
      assertFunc: () => {
        cy.get(createFormPage.DAILY_BUDGET_LIMITED_RADIO).should('be.checked');
        cy.get(createFormPage.DAILY_BUDGET_INPUT).should('not.be.disabled');
        cy.get(createFormPage.DAILY_BUDGET_INPUT).should('have.value', '50,000');
        createFormPage.assertDailyBudgetValidation();
      }
    });

    it.guide('should handle device type selection', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();
        createFormPage.selectDeviceType('ALL');
        createFormPage.selectDeviceType('PC');
        createFormPage.selectDeviceType('MOBILE');
      },
      assertFunc: () => {
        cy.get(createFormPage.DEVICE_TYPE_MOBILE_RADIO).should('be.checked');
      }
    });
  });

  describe('Media Account and Channel Selection', () => {
    it.guide('should handle media account and channel selection', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();
        createFormPage.selectMediaAccount('시원스쿨랩 (ID: 2232364)');
        createFormPage.selectChannel('http://storefarm.naver.com/artmu');
      },
      assertFunc: () => {
        cy.get(createFormPage.MEDIA_SELECT).should('contain', '시원스쿨랩 (ID: 2232364)');
        cy.get(createFormPage.CHANNEL_SELECT).should('contain', 'http://storefarm.naver.com/artmu');
      }
    });
  });

  describe('Product Selection and Filtering', () => {
    it.guide('should handle product selection', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();
        createFormPage.selectProduct(0);
        createFormPage.selectAllProducts();
      },
      assertFunc: () => {
        cy.get(createFormPage.PRODUCT_CHECKBOX_PARENT).should('have.class', 'Mui-checked');
      }
    });

    it.guide('should handle product filtering and search', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();
        createFormPage.filterProductsByCondition('SALE');
        createFormPage.filterProductsByAdStatus('ON');
        createFormPage.searchProducts('Bluetooth');
      },
      assertFunc: () => {
        cy.get(createFormPage.PRODUCT_CONDITION_FILTER).should('contain', 'SALE');
        cy.get(createFormPage.AD_STATUS_FILTER).should('contain', 'ON');
      }
    });
  });

  describe('Campaign Creation Workflow', () => {
    it.guide('should enable create button when form is filled', {
      mockFunc: setupPage,
      actionFunc: () => {
        visitAndWait();
        createFormPage.fillBasicCampaignInfo('Test Campaign', 50000, 'ALL');
        createFormPage.selectProductsForCampaign(1);
      },
      assertFunc: () => {
        createFormPage.assertCreateButtonEnabled();
      }
    });

    it.guide('should handle completion modal workflow', {
      mockFunc: () => {
        setupPage();
      },
      actionFunc: () => {
        visitAndWait();
        createFormPage.completeCampaignCreation('Test Campaign', 50000, 'ALL', 1);
      },
      assertFunc: () => {
        // Verify modal content
        cy.get(createFormPage.COMPLETION_MODAL).should('be.visible');
        cy.get(createFormPage.COMPLETION_MODAL).should('contain', 'Test Campaign');
        cy.get(createFormPage.COMPLETION_MODAL).should('contain', '50,000');
        cy.get(createFormPage.COMPLETION_MODAL).should('contain', 'PC, Mobile');
      }
    });

    it.guide('should display completion modal when campaign creation is triggered', {
      mockFunc: () => {
        setupPage();
      },
      actionFunc: () => {
        visitAndWait();
        createFormPage.completeCampaignCreation('Test Campaign', 50000, 'ALL');
      },
      assertFunc: () => {
        cy.get(createFormPage.COMPLETION_MODAL).should('be.visible');
      }
    });

    it.guide('should hide completion modal when cancel button is clicked', {
      mockFunc: () => {
        setupPage();
      },
      actionFunc: () => {
        visitAndWait();
        createFormPage.completeCampaignCreation('Test Campaign', 50000, 'ALL');
        createFormPage.clickModalCancel();
      },
      assertFunc: () => {
        createFormPage.assertCompletionModalHidden();
      }
    });


    it.guide('should handle modal confirm action', {
      mockFunc: () => {
        setupPage();
        createCampaignService.successWhenCreateCampaign();
      },
      actionFunc: () => {
        visitAndWait();
        createFormPage.completeCampaignCreation('Test Campaign', 200, 'ALL');
        createFormPage.clickModalConfirm();
      },
      waitFunc: () => {
        cy.wait('@successWhenCreateCampaign');
      },
      assertFunc: () => {
        createFormPage.assertCompletionModalHidden();
      }
    });
  });
}); 