.detection-url-page {
  .url-detection-filter-group .list-item-filter {
    &--optimization, &--name {
      flex: 2;
    }
    &--anomalyType, &--mediaType {
      flex: 1;
    }
  }
}

.url-anomaly-filter-popover {
  .MuiPopover-paper {
    margin-left: -1px;
    margin-top: 2px;
    box-sizing: border-box;
  }

  .MuiPopover-paper .MuiMenu-list .MuiMenuItem-root {
    width: 100%;
    height: 34px;
    font-size: 12px !important;
    font-weight: 200 !important;
    color: var(--point_color);
    text-align: center;
    border-radius: 0px;

    &.Mui-selected {
      font-weight: 500 !important;
      background-color: transparent !important;
    }
  }

  .MuiListItem-button:hover {
    font-weight: 500;
  }

  .MuiMenu-list {
    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
  }
  .filter-item {
    &.none {
      display: none;
    }
    &.name {
      display: flex;
      flex-direction: column;
      .title {
        font-weight: 700;
      }
    }
    &.optName {
      .optName-id {
        font-weight: 700;
        display: inline-block;
        margin-right: 4px;
      }
    }
  }
}