.budget-chart-legend-label-list {
  margin-left: 20px;
  width: 188px;
  max-height: 200px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    padding: 3px;
    width: 8px;
    background-color: #fff;
  }
  &::-webkit-scrollbar-thumb {
    width: 5px;
    border-radius: 10px;
    background-color: #d9d9d9;
  }
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  li {
    margin-bottom: 8px;
    display: grid;
    gap: 9px;
    grid-template-columns: 58px 39px 54px;
    .media-type{
      padding-left: 9px;
      font-size: 12px;
      font-weight: 400;
      text-align: left;
      position: relative;
      &::before {
        position: absolute;
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 100%;
        background-color: var(--label-color);
        top: 6px;
        left: 0px;
      }
    }
    span {
      display: inline-block;
      text-align: center;
    }
    .adType {
      min-width: 39px;
      vertical-align: middle;
      font-size: 10px;
      font-weight: 700;
      color: #5E81F4;
      border-radius: 20px;
      border: 1px solid #5E81F4;
    }
    .increase, .decrease, .none {
      padding: 0 5px;
      min-width: 50px;
      font-size: 10px;
      line-height: 18px;
      font-weight: 700;
      border-radius: 3px;
    }
    .increase {
      background-color: #ffe1e3;
      color: #EB424C;
    }
    .decrease {
      background-color: #E1E6FF;
      color: #5E81F4;
    }
    .none {
      background-color: #E6E6E6;
      color: #535972;
    }
  }
}