import React, { useState, useEffect } from 'react';
import {
  getAdEfficiencyAnalysisResult,
  updateAdEfficiency
} from '@api/adEfficiency/AdEfficiency';
import {
  AdEfficiencyStatus,
  AdEfficiencyInfo,
  AdEfficiencyAnalysisResult,
  AdEfficiencyTargetAdgroups,
  AdEfficiencyAnalysisParams,
  KpiType, AdEfficiencyTab,
  AnalysisPeriod
} from '@models/adEfficiency/AdEfficiency';
import { ReactComponent as ProcessingIcon } from '@components/assets/images/processing.svg';
import { ReactComponent as BlinkIcon } from '@components/assets/images/icon_star_blink.svg';
import './AdEfficiencyResult.scss';
import AdEfficiencyResultFilter from './AdEfficiencyResultFilter'
import AdEfficiencyResultChart from './AdEfficiencyResultChart'
import AdEfficiencyResultTable from './AdEfficiencyResultTable'
import { ActionType } from '@models/common/CommonConstants';
import MultiGoalSettings from './MultiGoalSettings'
import { Tabs, Tab } from '@material-ui/core';
import { DatePeriodPicker } from '@components/common/DatePeriodPicker';
import { sub, format } from 'date-fns';
import { useRecoilState, useRecoilValue, useResetRecoilState, useSetRecoilState, useRecoilTransaction_UNSTABLE } from 'recoil';
import { DateFnsFormat } from '@models/common/CommonConstants';
import { convertStrToDate } from '@utils/DateUtil';
import { xAxisState, yAxisState, sizeState } from './AdEfficiencyChart/store'
import { adEfficiencyOriginAllData, adEfficiencyOffHistory, adEfficiencyMutableAllData } from '@store/AdEfficiency'
import { useToast } from "@hooks/common";
import { getGapInDays } from '@utils/DateUtil'
import { ChartIndicator } from '@models/adEfficiency/AdEfficiency'
import { YNFlag } from '@models/common'

interface Props {
  analysis: AdEfficiencyInfo;
  refetchAnalysis: () => any;
  hasAuthority: boolean;
  needUpdateTarget: boolean;
  setInitTarget: (_target: AdEfficiencyTargetAdgroups[]) => void;
  setNeedUpdateTarget: (_status: boolean) => void;
}
const formatDate = (date: Date) => format(date, DateFnsFormat.DISP_DATE)
const yesterday = sub(new Date(), { days: 1 })
const yesterdayStr = formatDate(yesterday)

const convertKpisMap = (kpis: KpiType[]): [KpiType, boolean][] => {
  return kpis.length > 0
    ? kpis.map((kpi) => [kpi, true])
    : [[KpiType.CLICKS, true]]
}

const AdEfficiencyResult: React.FC<Props> = ({ analysis, refetchAnalysis, hasAuthority, needUpdateTarget, setInitTarget, setNeedUpdateTarget }: Props) => {
  const { analysisStartDate, analysisEndDate, status } = analysis
  const resetX = useResetRecoilState(xAxisState)
  const resetY = useResetRecoilState(yAxisState)
  const resetSize = useResetRecoilState(sizeState)
  const [resultData, setResultData] = useState<AdEfficiencyAnalysisResult | null>(null)
  const [refetchingKey, setRefetchingKey] = useState<number>(-1)
  const refetchingPeriod = 5000
  const [kpis, setKpis] = useState<Map<KpiType, boolean>>(new Map(convertKpisMap(analysis.kpiList)));
  const [resultTab, setResultTab] = useState(AdEfficiencyTab.ADGROUP)
  const [startDate, setStartDate] = useState(analysisStartDate === '1900.01.01' ? formatDate(sub(yesterday, { days: 6 })) : analysis.analysisStartDate)
  const [endDate, setEndDate] = useState(analysisEndDate === '1900.01.01' ? yesterdayStr : (analysis.analysisEndDate < yesterdayStr ? analysis.analysisEndDate : yesterdayStr))
  const [selectedPeriod, setSelectedPeriod] = useState<AnalysisPeriod>(AnalysisPeriod.Week)
  const storeOriginAnalysisDetails = useSetRecoilState(adEfficiencyOriginAllData)
  const [allAnalysisDetails, setAllAnalysisDetails] = useRecoilState(adEfficiencyMutableAllData)
  const [settingValues, setSettingValues] = useState<{indicator: ChartIndicator, analysisValue: number, isGreater: YNFlag}>({
    indicator: ChartIndicator.IMPRESSIONS,
    analysisValue: 0,
    isGreater: YNFlag.Y
  })

  const { openToast } = useToast()

  const minDate = convertStrToDate(analysis.analysisStartDate)
  const [isRequested, setIsRequested] = useState(false)
  const [isSettingRequested, setSettingRequested] = useState(true)

  const getResult = async () => {
    const result = await getAdEfficiencyAnalysisResult(analysis.analysisId)
    setResultData(result)
    groupAnalysisDetailsByRange(result, resultTab)
    setIsRequested(true)
  }

  const getSettingResult = async (settings: AdEfficiencyAnalysisParams) => {
    setSettingRequested(false)
    const result = await getAdEfficiencyAnalysisResult(analysis.analysisId, settings)
    if (!result || !result.ranges) {
      openToast('조회 결과가 없습니다.')
    } else {
      groupAnalysisDetailsByRange(result, resultTab)
    }
    setSettingRequested(true)
  }

  const groupAnalysisDetailsByRange = (analysis: AdEfficiencyAnalysisResult, tab: AdEfficiencyTab) => {
    const selectedRange = analysis.ranges.find((range) => range.analysisRange === tab)
    if (selectedRange?.details) {
      storeOriginAnalysisDetails(selectedRange.details)
      setAllAnalysisDetails(selectedRange.details)
    }
  }

  const handleSelectPeriod = ({ currentTarget: { value }}: React.MouseEvent<HTMLButtonElement>) => {
    const targetValue: AnalysisPeriod = parseInt(value)
    setSelectedPeriod(targetValue)
    if (targetValue === AnalysisPeriod.Custom) return
    setStartDate(formatDate(sub(yesterday, { days: targetValue - 1 })))
    setEndDate(formatDate(yesterday))
  }

  const handleSelectDate = (date: Date, type: string) => {
    if (type === 'start') {
      setStartDate(formatDate(date))
    } else {
      setEndDate(formatDate(date))
    }
    setSelectedPeriod(AnalysisPeriod.Custom)
  }

  const requestAnalysis = async() => {
    if (kpis.size === 0) {
      return openToast('분석대상 지표를 하나 이상 설정해주세요.')
    }
    setNeedUpdateTarget(false)
    const replaceStart = startDate.replace(/\./g, '')
    const replaceEnd = endDate.replace(/\./g, '')
    const updateParams = {
      analysisName: analysis.analysisName,
      analysisStartDate: replaceStart,
      analysisEndDate: replaceEnd,
      kpis: [...kpis.keys()]
    }
    resetSize()
    resetX()
    resetY()
    await updateAdEfficiency(analysis.analysisId, updateParams)
    setIsRequested(true)
    setResultData(null)
    setTimeout(async() => {
      const result: AdEfficiencyInfo = await refetchAnalysis()
      setInitTarget(result.targetList)
    }, 2000)
  }

  const handleKpis = (key: KpiType) => {
    if (kpis.has(key)) kpis.delete(key)
    else kpis.set(key, true)
    setKpis(kpis)
  }

  const offHistory = useRecoilValue(adEfficiencyOffHistory)
  // NOTE: 테이블에서 수정된 소재 off를 전체 데이터에 적재
  const updateMutableAllData = useRecoilTransaction_UNSTABLE(
    ({ set, reset }) => () => {
      set(adEfficiencyMutableAllData, allAnalysisDetails.map((all) => {
        const target = offHistory.find(status => status.creativeId === all.creativeId)
        if (target) return { ...all, onoff: 'OFF' }
        return all
      }))
      reset(adEfficiencyOffHistory)
    }
  )

  const setInitPeriod = (startDate: string, endDate: string) => {
    const diff = getGapInDays(startDate, endDate) + 1
    if ((endDate === yesterdayStr && diff === AnalysisPeriod.Week) || diff === 1) return setSelectedPeriod(AnalysisPeriod.Week)
    if (endDate === yesterdayStr && diff === AnalysisPeriod.Forthnight) return setSelectedPeriod(AnalysisPeriod.Forthnight)
    if (endDate === yesterdayStr && diff === AnalysisPeriod.Month) return setSelectedPeriod(AnalysisPeriod.Month)
    return setSelectedPeriod(AnalysisPeriod.Custom)
  }

  useEffect(() => {
    window.clearInterval(refetchingKey)
    if (status === AdEfficiencyStatus.SETTING || status === AdEfficiencyStatus.RUNNING) {
      const key = window.setInterval(() => {
        refetchAnalysis()
      }, refetchingPeriod)
      setRefetchingKey(key)
    } else if (status === AdEfficiencyStatus.FINISHED && resultData === null) {
      if (!needUpdateTarget) {
        getResult()
      }
    } else if (status === AdEfficiencyStatus.ERROR) {
      window.clearInterval(refetchingKey)
    }
    setInitPeriod(analysis.analysisStartDate, analysis.analysisEndDate)
    return () => window.clearInterval(refetchingKey)
  }, [analysis])

  return (
    <div id="AdEfficiencyResult">
      <section className='analysis-setting'>
        <div className='analysis-setting__label'>
          <span className='title'>Analysis Period</span>
          <span className='sub-title'>분석대상 기간 선택</span>
        </div>
        <div className='analysis-setting__option'>
          <DatePeriodPicker
            id="adEfficiency-result-date"
            disableToolbar={true}
            startDate={startDate}
            endDate={endDate}
            autoOk
            onClickStartDate={(date) => handleSelectDate(date!, 'start')}
            onClickEndDate={(date) => handleSelectDate(date!, 'end')}
            minStartDate={minDate}
            maxStartDate={yesterday}
            minEndDate={minDate}
            maxEndDate={yesterday}
            disabledStartDate={false}
            disabledEndDate={false}
            allowUnsetEndDate={false}
          />
          <div className="date-chip-box">
            <button className={`date-chip ${selectedPeriod === AnalysisPeriod.Custom && 'isSelected'}`} onClick={handleSelectPeriod} value={AnalysisPeriod.Custom}>직접입력</button>
            <button className={`date-chip ${selectedPeriod === AnalysisPeriod.Week && 'isSelected'}`} onClick={handleSelectPeriod} value={AnalysisPeriod.Week}>지난 7일</button>
            <button className={`date-chip ${selectedPeriod === AnalysisPeriod.Forthnight && 'isSelected'}`} onClick={handleSelectPeriod} value={AnalysisPeriod.Forthnight}>지난 15일</button>
            <button className={`date-chip ${selectedPeriod === AnalysisPeriod.Month && 'isSelected'}`} onClick={handleSelectPeriod} value={AnalysisPeriod.Month}>지난 30일</button>
          </div>
        </div>
        <div className='analysis-setting__label'>
          <span className='title'>Analysis Goal</span>
          <span className='sub-title'>분석대상 지표 선택</span>
        </div>
        <div className='analysis-setting__option'>
          <MultiGoalSettings
            kpis={kpis}
            setKpis={handleKpis}
            actionType={hasAuthority ? ActionType.CREATE : ActionType.READ}
          />
        </div>
        <div className='analysis-setting__submit'>
          <button className="submit-button" onClick={requestAnalysis}>Run Analysis</button>
        </div>
      </section>
      <section className='analysis-result'>
        <div className='analysis-result__label'>
          <span className='title'>Analysis Result</span>
          <span className='sub-title'>소재효율성 분석 결과</span>
          {resultData?.analysisDate && <span className="date">Date : <b>{resultData.analysisDate}</b></span>}
        </div>
        { !isRequested && (
          <div className="budget-result-running">
            <BlinkIcon />
            <p>소재 효율성 분석 후 결과를 확인할 수 있습니다.</p>
          </div>
        )}
        { isRequested && (<>
          {(
            status === AdEfficiencyStatus.SETTING ||
            status === AdEfficiencyStatus.RUNNING ||
            status === AdEfficiencyStatus.FINISHED && resultData === null) && (
              <div className="budget-result-running">
                <ProcessingIcon />
                <div>소재 효율성 분석 수행 중입니다.</div>
              </div>
          )}
          {
            (status === AdEfficiencyStatus.FINISHED && resultData !== null) &&  (
              <>
                {
                  !isSettingRequested && (
                    <div className="budget-result-running">
                      <ProcessingIcon />
                      <div>분석 중입니다</div>
                    </div>
                  )
                }
                {
                  isSettingRequested && (<>
                    <div className='analysis-result__tabs'>
                      <Tabs
                        value={resultTab}
                        onChange={(_e, newTab) => {
                          updateMutableAllData()
                          setResultTab(newTab)
                        }}
                      >
                        <Tab label="Ad groups" value={AdEfficiencyTab.ADGROUP} />
                        <Tab label="Campaigns" value={AdEfficiencyTab.CAMPAIGN} />
                        <Tab label="Media" value={AdEfficiencyTab.MEDIA} />
                      </Tabs>
                    </div>

                    <section className='analysis-result__container'>
                      <AdEfficiencyResultFilter
                        range={resultTab}
                        analysis={analysis}
                        getResult={getSettingResult}
                        updateMutableAllData={updateMutableAllData}
                        settingValues={settingValues}
                        setSettingValues={setSettingValues}
                      />
                      <AdEfficiencyResultChart />
                      <AdEfficiencyResultTable analysis={analysis} />
                    </section>
                  </>)
                }

              </>
            )
          }
          {
            status === AdEfficiencyStatus.ERROR && (
              <div className="budget-result-running">
                <ProcessingIcon />
                <p>분석 수행중 에러가 발생하였습니다.<br/>운영담당자에게 문의해주세요.</p>
              </div>
            )
          }
        </>)}
      </section>
    </div>
  )
}

export default AdEfficiencyResult