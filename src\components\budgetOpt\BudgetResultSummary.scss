.budget-result-report-summary {
  margin: 0 -44px;
  display: flex;
  height: 300px;
  background-color: var(--bg-gray-light);
  justify-content: center;
  position: relative;
  .label {
    margin-top: 8px;
    color: var(--point_color);
    font-size: 14px;
    display: inline-block;
    width: 100%;
    text-align: center;
    svg {
      transform: translateY(2px);
    }
  }

  .chip {
    border-radius: 9999px;
    padding: 4px 16px;
    margin: 0 auto;
    width: fit-content;
  }

  .budget-opt-analyze {
    margin-right: 1000px;
    width: 805px;
    justify-content: space-between;
    position: relative;
    padding: 17px 25px 17px 45px;
    background-color: var(--bg-gray-light);
    background-position: right 16px center;
    background-repeat: no-repeat;
    height: 100%;
    display: flex;
    gap: 20px;
    overflow: visible;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 100%;
      width: 80px;
      height: 100%;
      background-color: var(--bg-gray-light);
    }
    // justify-content: space-between;

    .budget-analyze-table {
      padding-top: 25px;
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 15px;
      color: var(--point_color);

      &__row {
        display: flex;
        gap: 8px;
        justify-content: space-between;
      }

      .label {
        &__box {
          min-width: 145px;
          display: flex;
          flex-direction: column;
        }
        &__en {
          font-size: 14px;
          font-weight: 700;
        }
        &__ko {
          font-size: 10px;
        }
      }

      .budget-date {
        margin: 0;
        background-color: var(--point_color);
        color: white;
        font-weight: 700;
        height: fit-content;
        font-size: 12px;
      }

      .kpi-chip-box {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;
        .chip {
          width: fit-content;
          margin: 0;
          font-size: 12px;
          color: white;
          padding: 2px 10px;
          &.IMPRESSIONS {
            background-color: #00359c;
          }
          &.VIEWS {
            background-color: #ff5c00;
          }
          &.CLICKS {
            background-color: #774cf2;
          }
          &.REVENUE {
            background-color: #ffd600;
            color: var(--point_color);
          }
          &.CONVERSIONS {
            background-color: #adda2f;
            color: var(--point_color);
          }

          &.disabled {
            background-color: #cccccc;
            color: white;
          }
        }
      }
    }

    .budget-analyze-chart {
      flex: 1;
      padding-left: 20px;
      height: 100%;
      display: flex;
      flex-direction: column;
      border-left: 1px solid #ccc;
      .chart-container {
        margin: 23px 0;
        display: flex;
        height: 300px;
      }
    }
  }
  .budget-analyze-chart {
    .chart-center {
      left: calc(50% - 17px);
    }
  }
  .budget-opt-recommendation {
    .chart-center {
      left: calc(50% - 11px);
    }
  }
  .budget-analyze-chart,
  .budget-opt-recommendation {
    .chart-container {
      position: relative;
      .chart-center {
        position: absolute;
        top: calc(50% - 6px);
        text-align: center;
        font-weight: 700;
        font-size: 12px;
      }
      div[class^='media-label-container'] {
        display: flex;
        flex-direction: column;
        transform: translate(-50%, -50%);
        .media-label-type {
          --label-color: var(--point_color);
          display: inline-block;
          font-size: 12px;
          padding-right: 4px;
          white-space: nowrap;
          position: relative;
          text-align: center;
          .adType {
            display: inline-block;
            width: 40px;
            text-align: center;
            color: #fff;
            font-size: 10px;
            font-weight: 700;
            background-color: var(--point_color);
            padding: 1px 9px;
            border-radius: 9999px;
          }
          &::before {
            position: absolute;
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 100%;
            background-color: var(--label-color);
            top: 4px;
            left: -10px;
          }
        }
        .media-label-budget {
          font-size: 1rem;
          font-weight: 700;
        }
        .media-budget-ratio {
          font-weight: 700;
          font-size: 10px;
          border-radius: 2px;
          width: fit-content;
          padding: 2px 4px;
          &.plus {
            background-color: #ffe1e3;
            color: #eb414c;
          }
          &.minus {
            background-color: #d5dfff;
            color: var(--color-active-blue);
          }
        }
      }
    }
  }

  .budget-opt-prediction {
    position: absolute;
    display: flex;
    background-color: white;
    top: -30px;
    left: 45%;
    right: 0;
    height: calc(100% + 60px);
    border-radius: 200px 0px 0px 200px;
    border: 1px solid var(--color-active-blue);
    border-right: none;
    box-shadow: 0px 9px 12px 0px rgba(0, 0, 0, 0.19);
    display: flex;
    padding: 40px 33px 40px 0px;
    gap: 20px;

    .budget-opt-recommendation {
      width: 407px;
      padding: 0 15px 0 33px;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-right: 1px solid #cccccc;
      .label {
        margin-top: 0px;
      }
      .chart-container {
        margin-top: 33px;
      }
    }
    .budget-opt-estimated {
      width: 500px;
      display: flex;
      flex-direction: column;
      .label {
        margin-top: 0px;
      }
      .estimated-box {
        margin-top: 49px;
        display: flex;
        justify-content: space-between;
        gap: 15px;
        flex: 1;
        .chart-container {
          flex: 1;
          width: 320px;
          height: 213px;
          // padding: 0 24px;
        }
        .avg-impact {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          .avg-impact-label {
            font-size: 22px;
            color: var(--point_color);
            font-weight: 300;
          }
          .avg-impact-rate {
            svg {
              width: auto;
              height: 39px;
            }
          }
          .avg-impact-num {
            font-size: 50px;
            line-height: normal;
            color: var(--point_color);
            font-weight: 700;
            .avg-impact-pct {
              font-size: 30px;
            }
          }
          .arror-rotate svg {
            transform: rotate(180deg);
          }
        }
      }
      .perform-impact {
        &__box {
          padding: 15px;
          display: flex;
          align-items: center;
          justify-content: space-around;
          width: 100%;
          border-top: 1px solid var(--color-active-blue);
        }
        &__icon {
          width: 36px;
          height: 36px;
          border-radius: 2px;
          background-color: #3b5ec91a;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        &__label {
          display: flex;
          flex-direction: column;
          color: var(--color-active-blue);
          &-en {
            font-size: 14px;
            font-weight: 700;
          }
          &-ko {
            font-size: 12px;
            font-weight: 300;
          }
        }
      }
      .impact-ticker {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 12px;
        border: 1px solid var(--color-active-blue);
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.19);
        color: var(--color-active-blue);
      }
    }
  }
  .budget-opt-switch {
    margin-top: 4px;
    &.readOnly {
      opacity: 0.5;
    }
    &.MuiSwitch-root {
      width: auto;
      height: auto;
      padding: 0;
      align-items: center;
      display: flex;

      .MuiSwitch-switchBase {
        left: 2px;
        padding: 0;
        cursor: default;

        .MuiIconButton-label {
          input[readonly] {
            cursor: default;
          }
        }

        .MuiSwitch-thumb {
          position: relative;
          top: 2px;
          width: 14px;
          height: 14px;
          padding: 0;
          background-color: #ffffff;
          opacity: 1;
          box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        }

        &.Mui-checked {
          transform: translateX(18px);
        }
      }

      .MuiSwitch-track {
        width: 36px;
        height: 18px;
        padding: 0;
        border-radius: 10px;
        background-color: #707395;
        opacity: 0.6;
        border: 1px solid #707395;
      }

      .Mui-checked {
        & + .MuiSwitch-track {
          background-color: var(--status-active);
          opacity: 1;
          border: 1px solid var(--status-active);
        }
      }

      .Mui-disabled {
        .MuiSwitch-thumb {
          background-color: #ccc;
          opacity: 1;
        }

        & + .MuiSwitch-track {
          background-color: #fff;
          opacity: 1;
          border: 1px solid #959595;
        }
      }
    }
  }
}
