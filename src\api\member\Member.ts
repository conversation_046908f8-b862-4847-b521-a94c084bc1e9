/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import { MemberProfile } from '@models/member/Member'
import { UpdateMemberParams, SignInParams } from '@models/member/Session'

export const getMember = async (isLoading = true) => {
  return callApi<MemberProfile>({
    service: Service.MOP_BE,
    url: `/v1/members`,
    method: Method.GET,
    config: { isLoading }
  })
}

export const updateMember = async (bodyParams: UpdateMemberParams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members',
    method: Method.PUT,
    params: {
      bodyParams
    },
    config: { isLoading }
  })
}

export const skipUpdatePassword = async (isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/postpone-pw',
    method: Method.PUT,
    config: { isLoading }
  })
}

export const checkCurrentPassword = async (bodyParams: SignInParams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/check',
    method: Method.PUT,
    params: {
      bodyParams
    },
    config: { isLoading }
  })
}
