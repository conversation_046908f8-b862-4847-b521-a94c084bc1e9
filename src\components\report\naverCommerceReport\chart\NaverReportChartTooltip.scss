#report-chart-tooltip {
  position: absolute;
  z-index: 100;
  width: 240px;
  background-color: white;
  border-top: 2px solid var(--color-active-blue);
  padding: 8px;
  box-shadow: 0px 4px 4px 0px #00000040;

  .report-chart-tooltip {
    &__content {
      display: contents;

      hr.divider {
        margin: 0;
        border: 1px solid var(--gray-light);
        &:last-child {
          display: none;
        }
      }
      svg {
        vertical-align: middle;
        margin-left: 4px;
        width: 27px;
      }
    }

    &__title,
    &__current,
    &__compare {
      display: grid;
      align-items: center;
      grid-template-columns: 30% 40% 30%;
      width: 100%;
      padding: 4px 0;

      span {
        color: var(--point_color);
        text-align: center;
        font-size: 8px;

        &.first-pointer {
          color: #0094ff;
        }

        &.second-pointer {
          color: #eb414c;
        }
      }
    }

    &__title {
      background-color: var(--bg-gray-light);

      span {
        font-weight: 700;
      }
    }

    &__current span {
      font-weight: 700;
    }
  }
}
