.CampaignBasicInformation {
  display: flex;
  flex-direction: column;
  height: 100%;
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .wrapper-input {
    .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline {
      border: 1px solid #efefef;
    }
    .MuiInputBase-root.Mui-focused .MuiOutlinedInput-notchedOutline {
      border: 1px solid #17171780 !important;
    }
  }

  .currency-budget {
    p {
      font-size: 14px;
      font-weight: 500;
      font-family: 'Pretendard', 'sans-serif';
      color: rgba(0, 0, 0, 0.54);
    }
  }

  .wraper-input-daily-buget {
    width: 100%;
    display: flex;
    align-items: center;
    .MuiInputBase-root {
      height: 35px;
    }
  }

  .vat-excluded {
    position: absolute;
    right: 0;
    bottom: -20px;
    z-index: 1;
    color: #909090;
    font-size: 11px;
  }

  .text-alert{
    color: red;
    font-size: 12px;
  }

  // .mop-input:hover .MuiOutlinedInput-notchedOutline {
  //   border: 1px solid #efefef;
  // }

  // .mop-input.Mui-focused .MuiOutlinedInput-notchedOutline {
  //   border-width: 1px;
  //   border-color: #17171780;
  // }

  // .mop-input input::-webkit-outer-spin-button,
  // .mop-input input::-webkit-inner-spin-button {
  //   -webkit-appearance: none;
  //   margin: 0;
  // }

  .text-field-bg-white {
    & .MuiInputBase-root {
      background-color: #fff;
    }
  }

  .MuiTypography-root,
  .MuiFormLabel-root {
    font-size: 14px !important;
    color: #333333;
    white-space: nowrap;
  }
  .N-label,
  .Y-label,
  .ALL-label,
  .PC-label,
  .MOBILE-label {
    white-space: nowrap;
    gap: 4px;
  }
}
