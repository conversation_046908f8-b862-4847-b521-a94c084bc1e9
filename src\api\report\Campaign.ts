/* istanbul ignore file */
import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import {
  MediaAdgroupIdsByPlatform,
  CampaignReportByMedia,
  CampaignReportDateTable,
  CampaignReportMediaTable
} from '@models/report/Campaign'
import { IndicatorSummary, IndicatorSummaryQuery, ReportTableQuery, AvailableDate, ReportCategoryType } from '@models/report/Common'
import { Service } from '@models/common/Service';

export const getCampaignReportAdgroups = async (advertiserId: number, isLoading = true): Promise<CampaignReportByMedia[]|undefined> => {
  const response: CommonResponse<CampaignReportByMedia[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/campaign/${advertiserId}/adgroups`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : []);
};

export const getCampaignReportSummary = async (
  advertiserId: number,
  queryParams: IndicatorSummaryQuery,
  bodyParams: MediaAdgroupIdsByPlatform,
  isLoading = true
): Promise<IndicatorSummary|undefined> => {
  const response: CommonResponse<IndicatorSummary> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/campaign/${advertiserId}/summary`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : undefined);
};

export const getCampaignReportTable = async (
  advertiserId: number,
  queryParams: ReportTableQuery,
  bodyParams: MediaAdgroupIdsByPlatform,
  isLoading = true
): Promise<CampaignReportDateTable|CampaignReportMediaTable|undefined> => {
  const response: CommonResponse<CampaignReportDateTable|CampaignReportMediaTable> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/campaign/${advertiserId}/table`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : undefined)
};

export const getReportMinMaxDate = async (
  advertiserId: number,
  reportCategoryType: ReportCategoryType,
  isLoading = true
): Promise<AvailableDate|undefined> => {
  const response: CommonResponse<AvailableDate> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/available-period/${advertiserId}`,
    method: Method.GET,
    params: {
      queryParams: {
        reportType: reportCategoryType
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : undefined);
};
