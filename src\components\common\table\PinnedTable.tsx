import React, { CSSProperties, PropsWithChildren, useEffect, useRef, useState } from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import './PinnedTable.scss';

export type PinnedConfig = {
  /** 좌측에서 고정할 컬럼 수(0~2). */
  left?: number;
};

export type RowPanelRenderers<TData> = {
  leftPanel?: (row: TData) => React.ReactNode;
  rightPanel?: (row: TData) => React.ReactNode;
};

export type PinnedTableProps<TData> = {
  columns: ColumnDef<TData, unknown>[];
  data: TData[];
  /**
   * 테이블 컨테이너 높이. 세로 스크롤을 위해 필요합니다.
   * 예: 360, '50vh'
   */
  height?: number | string;
  /**
   * 테이블 컨테이너 최대 가로 너비. 가로 스크롤을 위해 필요합니다.
   * 예: '100%', 800
   */
  maxWidth?: number | string;
  /**
   * 표 스타일 오버라이드가 필요할 때 전달합니다.
   */
  style?: CSSProperties;
  className?: string;
  /**
   * 무한 스크롤: 추가 데이터를 로드하도록 요청합니다.
   */
  onLoadMore?: () => void;
  /**
   * 더 불러올 데이터가 남아있는지 여부. 기본값 true
   */
  hasMore?: boolean;
  /**
   * 현재 로딩 중인지 여부. 로딩 중엔 추가 호출을 방지합니다.
   */
  isLoading?: boolean;
  /**
   * 하단 감지 여백(px). 기본 200
   */
  loadMoreOffset?: number;
  /** 핀 설정(좌측 컬럼 수) */
  pinned?: PinnedConfig;
  /** 행 패널 렌더러(left/right) */
  rowPanelRenderers?: RowPanelRenderers<TData>;
  /** 외부 패널 토글 핸들러(선택). 제공 시 내부 토글 대신 이 콜백 호출 */
  onToggleRowPanel?: (rowId: string, row: TData) => void;
  /** 현재 열려있는 패널 rowId 목록(제공 시 완전 제어 모드) */
  openRowPanelIds?: string[];
  /** 컬럼 가시성 제어(제어 모드). 키는 column id(accessorKey) */
  columnVisibility?: VisibilityState;
  /** 컬럼 가시성 변경 콜백 */
  onColumnVisibilityChange?: (updater: VisibilityState | ((old: VisibilityState) => VisibilityState)) => void;
};

function classNames(...names: Array<string | false | null | undefined>): string {
  return names.filter(Boolean).join(' ');
}

export function PinnedTable<TData>(props: PropsWithChildren<PinnedTableProps<TData>>): JSX.Element {
  const {
    columns,
    data,
    height = 360,
    maxWidth = '100%',
    style,
    className,
    onLoadMore,
    hasMore = true,
    isLoading = false,
    loadMoreOffset = 200,
    pinned,
    rowPanelRenderers,
    onToggleRowPanel,
    openRowPanelIds,
    columnVisibility,
    onColumnVisibilityChange,
  } = props;

  const pinnedLeft = Math.max(0, Math.min(2, (pinned?.left ?? 0) || 0));

  const containerRef = useRef<HTMLDivElement | null>(null);
  const sentinelRef = useRef<HTMLDivElement | null>(null);
  const theadRef = useRef<HTMLTableSectionElement | null>(null);

  const table = useReactTable<TData>({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: { columnVisibility: columnVisibility as any },
    onColumnVisibilityChange: onColumnVisibilityChange as any,
    debugTable: false,
  });

  const containerStyle: CSSProperties = {
    maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
    maxHeight: typeof height === 'number' ? `${height}px` : height,
    ...style,
  };

  const headerGroups = table.getHeaderGroups();
  const leafHeaderGroup = headerGroups[headerGroups.length - 1];

  // meta에서 설정 읽기
  const leafHeaders = leafHeaderGroup?.headers ?? [];
  const metaSubHeaders = leafHeaders.map((h) => (h.column.columnDef as any)?.meta?.subHeader as React.ReactNode | undefined);
  const metaRowSpan = new Set<number>(
    leafHeaders
      .map((h, idx) => ({ idx, span: (h.column.columnDef as any)?.meta?.headerRowSpan as number | undefined }))
      .filter((x) => (x.span ?? 1) > 1)
      .map((x) => x.idx)
  );

  // headerColSpan 동적 계산(숨김 컬럼 고려)
  const colSpanMap = new Map<number, { span: number; content?: React.ReactNode }>();
  const colSpanSkip = new Set<number>();
  {
    const allLeaf = (table as any).getAllLeafColumns?.() ?? [];
    const visibleLeaf = (table as any).getVisibleLeafColumns?.() ?? [];
    const visibleIndexById = new Map<string, number>();
    visibleLeaf.forEach((c: any, i: number) => visibleIndexById.set(c.id, i));

    // 모든 컬럼 순서에서 그룹 시작 인덱스 수집
    const groupStarts: Array<{ startAll: number; maxSpan: number; content?: React.ReactNode }> = [];
    for (let i = 0; i < allLeaf.length; i += 1) {
      const meta = (allLeaf[i].columnDef as any)?.meta?.headerColSpan as { span: number; content?: React.ReactNode } | undefined;
      if (meta && meta.span >= 2) {
        groupStarts.push({ startAll: i, maxSpan: meta.span, content: meta.content });
      }
    }
    // 각 그룹에 대해 가시 컬럼 범위 계산
    for (let g = 0; g < groupStarts.length; g += 1) {
      const { startAll, maxSpan, content } = groupStarts[g];
      const nextStart = g + 1 < groupStarts.length ? groupStarts[g + 1].startAll : Number.POSITIVE_INFINITY;
      const endAll = Math.min(startAll + maxSpan - 1, nextStart - 1);

      // 이 그룹 내의 보이는 컬럼 목록 찾기
      const visibleMembers: number[] = [];
      for (let i = startAll; i <= endAll; i += 1) {
        const col = allLeaf[i];
        const vIdx = visibleIndexById.get(col.id);
        if (typeof vIdx === 'number') visibleMembers.push(vIdx);
      }
      if (visibleMembers.length === 0) continue; // 전부 숨김이면 그룹 헤더 없음

      // 그룹의 첫 보이는 열에서 시작, 보이는 멤버 수만큼 span
      const vStart = Math.min(...visibleMembers);
      const span = visibleMembers.length;
      colSpanMap.set(vStart, { span, content });
      for (let k = 1; k < span; k += 1) colSpanSkip.add(vStart + k);
    }
  }

  const hasSubHeaderRow = metaSubHeaders.some((v) => v !== undefined && v !== null);

  // 두 번째 헤더 높이 보정
  const [firstHeaderRowHeight, setFirstHeaderRowHeight] = useState<number>(0);
  useEffect(() => {
    if (!hasSubHeaderRow) return;
    const el = theadRef.current;
    if (!el) return;
    const measure = () => {
      const firstRow = el.querySelector('tr');
      if (firstRow instanceof HTMLElement) {
        const rect = firstRow.getBoundingClientRect();
        setFirstHeaderRowHeight(rect.height);
      }
    };
    measure();
    const RO = (window as any).ResizeObserver as undefined | (new (callback: ResizeObserverCallback) => ResizeObserver);
    let ro: ResizeObserver | undefined;
    if (RO) {
      ro = new RO(measure as unknown as ResizeObserverCallback);
      ro.observe(el);
    } else {
      window.addEventListener('resize', measure);
    }
    return () => {
      if (ro) ro.disconnect();
      else window.removeEventListener('resize', measure);
    };
  }, [headerGroups.length, hasSubHeaderRow]);

  useEffect(() => {
    if (!onLoadMore || !hasMore) return;
    const containerEl = containerRef.current;
    const sentinelEl = sentinelRef.current;
    if (!containerEl || !sentinelEl) return;

    let lastCallAt = 0;
    const observer = new IntersectionObserver(
      (entries) => {
        const now = Date.now();
        const entry = entries[0];
        if (!entry) return;
        if (entry.isIntersecting && !isLoading) {
          if (now - lastCallAt > 300) {
            lastCallAt = now;
            onLoadMore();
          }
        }
      },
      { root: containerEl, rootMargin: `0px 0px ${loadMoreOffset}px 0px`, threshold: 0.01 }
    );

    observer.observe(sentinelEl);
    return () => observer.disconnect();
  }, [onLoadMore, hasMore, isLoading, loadMoreOffset]);

  const clampedPinned = pinnedLeft;

  // 왼쪽 고정 컬럼들의 left 누적 오프셋 계산 (leaf 헤더 기준)
  const colWidths: number[] = (leafHeaderGroup?.headers || []).map((h) => (
    h.column.getSize?.() ?? (h.column.columnDef as any).size ?? 0
  ));
  const leftOffsets: number[] = [];
  leftOffsets[0] = 0;
  // 핀 경계에서 축소 현상을 방지하기 위해 1px 겹치기(seam) 적용
  const stickySeamPx = 1;
  for (let i = 1; i < clampedPinned; i += 1) {
    leftOffsets[i] = (leftOffsets[i - 1] ?? 0) + (colWidths[i - 1] ?? 0) + stickySeamPx;
  }
  // 패널 폭 강제 설정은 제거 (테이블 레이아웃에 맡김)

  const getLeftStyle = (idx: number): CSSProperties | undefined => {
    if (idx >= clampedPinned) return undefined;
    const left = idx === 0 ? 0 : (leftOffsets[idx] ?? 0);
    return { left: `${left}px` } as CSSProperties;
  };

  const isLeftEdge = (startIdx: number, span = 1) => {
    if (clampedPinned <= 0) return false;
    const endIdx = startIdx + span - 1;
    const lastPinnedIdx = clampedPinned - 1;
    return startIdx <= lastPinnedIdx && endIdx >= lastPinnedIdx;
  };

  const headerZIndex = (idx: number, isFirstOnLeaf: boolean) => {
    if (idx < clampedPinned) {
      return 4 + (clampedPinned - idx);
    }
    return isFirstOnLeaf ? 4 : 3;
  };

  const subHeaderZIndex = (idx: number, isFirst: boolean) => {
    if (idx < clampedPinned) {
      return 4 + (clampedPinned - idx);
    }
    return isFirst ? 4 : 3;
  };

  const bodyZIndex = (idx: number, isFirst: boolean) => {
    return idx < clampedPinned ? (isFirst ? 3 : 2) : (isFirst ? 2 : 1);
  };

  // 패널 토글 상태(비제어 시)
  const [openRowIdsUncontrolled, setOpenRowIdsUncontrolled] = useState<Set<string>>(new Set());
  const openSet = openRowPanelIds ? new Set(openRowPanelIds) : openRowIdsUncontrolled;
  const toggleRowPanel = (rowId: string, row: any) => {
    if (onToggleRowPanel) return onToggleRowPanel(rowId, row);
    setOpenRowIdsUncontrolled((prev) => {
      const next = new Set(prev);
      if (next.has(rowId)) next.delete(rowId);
      else next.add(rowId);
      return next;
    });
  };

  const renderPanelRow = (row: any) => {
    if (!rowPanelRenderers) return null;
    const isOpen = openSet.has(row.id);
    if (!isOpen) return null;
    const leftCells = clampedPinned;
    const rightCells = Math.max(0, leafHeaders.length - leftCells);
    return (
      <tr key={`${row.id}-panel`}>
        {leftCells > 0 && (
          <td
            className="rowPanelCell stickyLeft"
            colSpan={leftCells}
            style={{ position: 'sticky', left: 0, zIndex: 1 }}
          >
            <div className="rowPanelInner">{rowPanelRenderers.leftPanel?.(row.original)}</div>
          </td>
        )}
        {rightCells > 0 && (
          <td className="rowPanelCell" colSpan={rightCells}>
            <div className="rowPanelInner">{rowPanelRenderers.rightPanel?.(row.original)}</div>
          </td>
        )}
      </tr>
    );
  };

  return (
    <div ref={containerRef} className={classNames('pinnedTableContainer', className)} style={containerStyle}>
      <table className="pinnedTable">
        <thead ref={theadRef}>
          {headerGroups.map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header, headerIdx) => {
                const isLeafRow = headerGroup.id === leafHeaderGroup.id;
                const isFirstColOnLeafRow = isLeafRow && headerIdx === 0;
                if (isLeafRow && colSpanSkip.has(headerIdx)) {
                  return null;
                }
                const colSpanInfo = isLeafRow ? colSpanMap.get(headerIdx) : undefined;
                const span = colSpanInfo?.span ?? 1;
                const colSize = header.column.getSize?.() ?? (header.column.columnDef as any).size;
                const widthStyle = colSize ? { width: `${colSize}px`, minWidth: `${colSize}px` } : undefined;
                const rowSpan = isLeafRow && metaRowSpan.has(headerIdx) && hasSubHeaderRow ? 2 : undefined;
                const stickyClasses = classNames(
                  'th',
                  'stickyHeader',
                  headerIdx < clampedPinned && 'stickyLeft',
                  isLeftEdge(headerIdx, span) && 'stickyLeftEdge',
                  isFirstColOnLeafRow && 'stickyFirstCol'
                );
                const leftStyle = getLeftStyle(headerIdx);
                const zIndex = headerZIndex(headerIdx, isFirstColOnLeafRow);
                const contentNode = colSpanInfo?.content ?? (header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext()));
                return (
                  <th
                    key={header.id}
                    className={stickyClasses}
                    style={{ zIndex, ...leftStyle, ...widthStyle }}
                    rowSpan={rowSpan as any}
                    colSpan={span > 1 ? span : undefined}
                  >
                    {contentNode}
                  </th>
                );
              })}
            </tr>
          ))}
          {leafHeaderGroup && hasSubHeaderRow && (
            <tr key={`${leafHeaderGroup.id}-extra`}>
              {leafHeaderGroup.headers.map((header, headerIdx) => {
                if (metaRowSpan.has(headerIdx)) {
                  return null; // 첫 번째 행에서 rowspan으로 병합된 경우 스킵
                }
                const isFirstCol = headerIdx === 0 && !metaRowSpan.has(0);
                const colSize = header.column.getSize?.() ?? (header.column.columnDef as any).size;
                const widthStyle = colSize ? { width: `${colSize}px`, minWidth: `${colSize}px` } : undefined;
                const content = metaSubHeaders[headerIdx] ?? '';
                const stickyClasses = classNames(
                  'th',
                  'stickyHeader',
                  headerIdx < clampedPinned && !metaRowSpan.has(headerIdx) && 'stickyLeft',
                  isLeftEdge(headerIdx) && 'stickyLeftEdge',
                  isFirstCol && 'stickyFirstCol'
                );
                const leftStyle = getLeftStyle(headerIdx);
                const zIndex = subHeaderZIndex(headerIdx, isFirstCol);
                return (
                  <th
                    key={`${header.id}-extra`}
                    className={stickyClasses}
                    style={{ zIndex, top: `${firstHeaderRowHeight}px`, ...leftStyle, ...widthStyle }}
                  >
                    {content}
                  </th>
                );
              })}
            </tr>
          )}
        </thead>
        <tbody>
          {table.getRowModel().rows.map((row) => (
            <React.Fragment key={row.id}>
              <tr>
                {row.getVisibleCells().map((cell, cellIdx) => {
                  const isFirstCol = cellIdx === 0;
                  const colSize = cell.column.getSize?.() ?? (cell.column.columnDef as any).size;
                  const widthStyle = colSize ? { width: `${colSize}px`, minWidth: `${colSize}px` } : undefined;
                  const stickyClasses = classNames(
                    'td',
                    cellIdx < clampedPinned && 'stickyLeft',
                    isLeftEdge(cellIdx) && 'stickyLeftEdge',
                    isFirstCol && 'stickyFirstCol'
                  );
                  const leftStyle = getLeftStyle(cellIdx);
                  return (
                    <td
                      key={cell.id}
                      className={stickyClasses}
                      style={{ zIndex: isFirstCol ? 2 : 1, ...leftStyle, ...widthStyle }}
                    >
                      {flexRender(cell.column.columnDef.cell, {
                        ...cell.getContext(),
                        tableApi: {
                          toggleRowPanel: () => toggleRowPanel(row.id, row),
                          isRowPanelOpen: openSet.has(row.id),
                        },
                      } as any)}
                    </td>
                  );
                })}
              </tr>
              {renderPanelRow(row)}
            </React.Fragment>
          ))}
        </tbody>
      </table>
      <div ref={sentinelRef} className="pinnedTableSentinel" />
      {(isLoading || !hasMore) && (
        <div className="loadMoreStatus">
          {isLoading ? '로딩 중…' : '모든 데이터를 불러왔습니다.'}
        </div>
      )}
    </div>
  );
}

export default PinnedTable; 