import CommonResponse from '@models/common/CommonResponse';
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import {
  AbnormalDetection,
  AbnormalPerformance,
  Attribution,
  BudgetUpdateBatchHistory,
  CollectionItems,
  CollectionPerformance,
  CollectionStatus,
  Planning,
  Prediction,
  PreviousBudgetUsage,
  TodayBudgetUsage,
} from '@models/dashboard/DashboardStatus';
import { GetReportSummaryResponse } from '@models/report/SearchReport';

export const getDvaReport = async (advertiserId: number, chkDate: string[], isLoading = false) => {
  const response: CommonResponse<GetReportSummaryResponse> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/report',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        startDate: chkDate[0],
        endDate: chkDate[1],
        compareStartDate: chkDate[2],
        compareEndDate: chkDate[3],
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDvaAbnormalPerformance = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalPerformance[]> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/abnormal/performances',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : [];
};

export const getDVaCollectionStatus = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<CollectionStatus> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/collection/status',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaAbnormalUrls = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalDetection> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/abnormal/urls',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaAbnormalUtms = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalDetection> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/abnormal/utms',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaCollectionItem = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<CollectionItems> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/collection/items',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaCollectionPerformance = async (advertiserId: number, lastDays: number, isLoading = false) => {
  const response: CommonResponse<CollectionPerformance> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/collection/performances',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        lastDays: lastDays,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaProjectionPredictions = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Prediction> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/projection/predictions',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaProjectionPlanning = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Planning> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/projection/planning',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaProjectionAttribution = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Attribution> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/projection/attribution',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaTodayBudgetUsage = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<TodayBudgetUsage> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/flight/cost/today',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaPreviousBudgetUsage = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<PreviousBudgetUsage> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/flight/cost/previous',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getDVaBudgetUpdateBatchHistory = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<BudgetUpdateBatchHistory[]> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/da/flight/budget-status',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};
