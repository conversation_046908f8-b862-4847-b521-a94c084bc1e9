import React, { ReactElement, useEffect, useState } from 'react'
import {
  Chart as ChartJS,
  LinearScale,
  registerables,
  CategoryScale,
  PointElement,
  LineController,
  LineElement,
  Legend,
  Tooltip
} from 'chart.js'
import ChartAnnotation from 'chartjs-plugin-annotation'
import { Chart } from 'react-chartjs-2'
import { ActiveDate } from '@models/report/ActiveDate'
import { DateFnsFormat } from '@models/common/CommonConstants'
import { chartOptions, generateLabels, generateDataSet, ChartTerm } from './chartUtils'
import ReportChartTooltip from './NaverReportChartTooltip'
import './NaverCommerceChart.scss'
import { parseDateByOptValue } from '@utils/reports'
import { getVerticalLineAnnotation } from '@utils/common/Chart'
import { NaverCommerceIndicator } from '@utils/naverCommerceReport'

interface ChartData {
  value: {
    [key in NaverCommerceIndicator]: string[]
  }
  compareValue?: {
    [key in NaverCommerceIndicator]: string[]
  }
}

interface Props {
  chartData: ChartData
  chartType: string
  period?: ActiveDate
  firstAxis: NaverCommerceIndicator
  secondAxis?: NaverCommerceIndicator
  chartTerm: ChartTerm
  optimization?: string
}
type ChartDataset = {
  first: string[]
  second: string[]
  firstCompare: string[]
  secondCompare: string[]
}

const defaultChartData = {
  labels: [],
  datasets: []
}

const firstChartUI = {
  id: 'first',
  backgroundColor: '#0081FF',
  borderColor: '#0081FF',
  yAxisID: 'first',
  pointRadius: 1,
  pointHoverRadius: 2,
  pointBackgroundColor: '#0081FF'
}

const firstCompareChartUI = {
  id: 'firstCompare',
  backgroundColor: '#0081FF40',
  borderColor: '#0081FF',
  borderDash: [10, 10],
  yAxisID: 'first',
  pointRadius: 1,
  pointHoverRadius: 2,
  pointBackgroundColor: '#0081FF'
}

const secondChartUI = {
  id: 'second',
  backgroundColor: '#EB414C',
  borderColor: '#EB414C',
  yAxisID: 'second',
  pointRadius: 1,
  pointHoverRadius: 2,
  pointBackgroundColor: '#EB414C'
}

const secondCompareChartUI = {
  id: 'secondCompare',
  backgroundColor: '#EB414C40',
  borderColor: '#EB414C',
  borderDash: [10, 10],
  yAxisID: 'second',
  pointRadius: 1,
  pointHoverRadius: 2,
  pointBackgroundColor: '#EB414C'
}

function filterTermDate(array: string[], target: number) {
  return array.filter((_element, index) => index % target === 0)
}

const sumDayValue = (data: string[], firstIdx: number, lastIdx: number) => {
  const termData = data.slice(firstIdx, lastIdx)
  return termData.reduce((all: number, curr: string) => Number(all) + Number(curr), 0)
}

const getAllTermValues = (maxTerm: number, termDays: number, data: string[]) => {
  if (data.length === 0) return []
  const allTermValue: string[] = []
  for (let i = 0; i < maxTerm; i++) {
    const totalValue = sumDayValue(data, i * termDays, termDays * (i + 1))
    allTermValue.push(String(totalValue))
  }
  return allTermValue
}

const NaverCommerceChart: React.FC<Props> = ({
  chartData,
  chartTerm,
  period,
  chartType,
  firstAxis,
  secondAxis,
  optimization
}: Props): ReactElement => {
  const [options, setOptions] = useState<any>()
  const [data, setData] = useState<any>(defaultChartData)
  const [tooltipInfo, setTooltipInfo] = useState({
    visible: false,
    left: '0px',
    top: '0px',
    data: {}
  })
  const [currentDays, setCurrentDays] = useState<string[]>([])
  const [compareDays, setCompareDays] = useState<string[]>([])
  const [chartDataset, setChartDataset] = useState<ChartDataset>({
    first: [],
    second: [],
    firstCompare: [],
    secondCompare: []
  })

  ChartJS.register(...registerables)
  ChartJS.register(
    LineController,
    LinearScale,
    CategoryScale,
    PointElement,
    LineElement,
    Legend,
    Tooltip,
    ChartAnnotation
  )

  const handleChartTooltip = () => {
    setTooltipInfo((before) => ({ ...before, visible: false }))
  }

  useEffect(() => {
    let isMounted = true

    if (!isMounted) return

    if (chartTerm === 1) {
      try {
        if (period && !chartData) {
          setChartDataset({ first: [], second: [], firstCompare: [], secondCompare: [] })
        }
        if (!period || !chartData) return


        let currentDaysLabels
        if (period.startDate && period.endDate) {
          currentDaysLabels = generateLabels(period.startDate, period.endDate, DateFnsFormat.DISP_DATE)
        } else if (chartData && (chartData as any).dates) {
          currentDaysLabels = (chartData as any).dates.map((date: string) => date.replace(/-/g, '.'))
        } else {
          currentDaysLabels = []
        }

        setCurrentDays(currentDaysLabels)

        if (period.compareStartDate && period.compareEndDate) {
          const compareDaysLabels = generateLabels(
            period.compareStartDate,
            period.compareEndDate,
            DateFnsFormat.DISP_DATE
          )
          setCompareDays(compareDaysLabels)
        }

        const first = chartData?.value?.[firstAxis] || []
        const second = secondAxis ? (chartData?.value?.[secondAxis] || []) : []
        const firstCompare = !!chartData?.compareValue ? (chartData?.compareValue?.[firstAxis] || []) : []
        const secondCompare = (!!chartData?.compareValue && secondAxis) ? (chartData?.compareValue?.[secondAxis] || []) : []


        setChartDataset({ first, second, firstCompare: firstCompare || [], secondCompare: secondCompare || [] })
      } catch (error) {
        console.error('Error in updateDailyChart:', error)
        setCurrentDays([])
        setCompareDays([])
        setChartDataset({ first: [], second: [], firstCompare: [], secondCompare: [] })
      }
    } else {
      try {
        if (!period || !chartData) return

        const allCurrentDays = generateLabels(period.startDate, period.endDate, DateFnsFormat.DISP_DATE)
        const currentTermDate = filterTermDate(allCurrentDays, chartTerm)
        setCurrentDays(currentTermDate)

        if (period.compareStartDate && period.compareEndDate) {
          const allCompareDays = generateLabels(period.compareStartDate, period.compareEndDate, DateFnsFormat.DISP_DATE)
          setCompareDays(filterTermDate(allCompareDays, chartTerm))
        }

        const allData = {
          first: chartData?.value?.[firstAxis] || [],
          second: secondAxis ? (chartData?.value?.[secondAxis] || []) : [],
          firstCompare: !!chartData?.compareValue ? (chartData?.compareValue?.[firstAxis] || []) : [],
          secondCompare: (!!chartData?.compareValue && secondAxis) ? (chartData?.compareValue?.[secondAxis] || []) : []
        }
        const maxTerm = Math.ceil(allData.first.length / chartTerm)
        const first = getAllTermValues(maxTerm, chartTerm, allData.first)
        const second = getAllTermValues(maxTerm, chartTerm, allData.second)
        const firstCompare = getAllTermValues(maxTerm, chartTerm, allData.firstCompare || [])
        const secondCompare = getAllTermValues(maxTerm, chartTerm, allData.secondCompare || [])
        setChartDataset({ first, second, firstCompare, secondCompare })
      } catch (error) {
        console.error('Error in updateTermChart:', error)
        setCurrentDays([])
        setCompareDays([])
        setChartDataset({ first: [], second: [], firstCompare: [], secondCompare: [] })
      }
    }

    return () => {
      isMounted = false
    }
  }, [period, chartData, chartType, firstAxis, secondAxis, chartTerm])

  useEffect(() => {
    // Add cleanup to prevent memory leaks
    let isMounted = true

    if (!isMounted) return

    const { first, second, firstCompare, secondCompare } = chartDataset
    const hasSecond = second && second.length > 0
    const hasCompare = firstCompare && firstCompare.length > 0


    const allValues = [...(first || []), ...(firstCompare || []), ...(second || []), ...(secondCompare || [])]
    const max = allValues.length > 0 ? Math.max.apply(this, allValues.map(str => Number(str))) * 1.1 : 100
    const matchedYAxis =
      (firstAxis === NaverCommerceIndicator.SALES_AMOUNT && secondAxis === NaverCommerceIndicator.COST) ||
      (firstAxis === NaverCommerceIndicator.COST && secondAxis === NaverCommerceIndicator.SALES_AMOUNT)

    // Inline tooltipHandler logic to avoid dependency
    const inlineTooltipHandler = (context: any) => {
      const { tooltip, chart } = context
      const position = chart.canvas.getBoundingClientRect()
      const tooltipWidth = 240

      let moveLeft = 0
      const tooltipRight = tooltipWidth + Number(tooltip.caretX)
      const positionRight = Number(position.width)
      if (tooltipRight > positionRight) {
        moveLeft = tooltipRight - positionRight
      }

      if (tooltip.opacity === 0) {
        return
      }
      const left = tooltip.caretX - moveLeft + 'px' // eslint-disable-line
      const top = tooltip.caretY + 'px'
      const currentIndex = currentDays.findIndex((day) => day === tooltip.title[0])

      // Inline getTooltipData logic
      const firstValue = chartDataset.first[currentIndex]
      const firstCompareValue = chartData?.compareValue ? chartDataset.firstCompare[currentIndex] : null
      const secondValue = secondAxis ? chartDataset.second[currentIndex] : null
      const secondCompareValue = secondAxis && chartData?.compareValue ? chartDataset.secondCompare[currentIndex] : null

      const tooltipData = {
        first: firstValue ? { date: currentDays[currentIndex], pointer: firstAxis, value: firstValue } : null,
        second: secondValue ? { date: currentDays[currentIndex], pointer: secondAxis, value: secondValue } : null,
        firstCompare: firstCompareValue
          ? {
              date: compareDays[currentIndex],
              pointer: firstAxis,
              value: firstCompareValue
            }
          : null,
        secondCompare:
          secondAxis && secondCompareValue
            ? {
                date: compareDays[currentIndex],
                pointer: secondAxis,
                value: secondCompareValue
              }
            : null
      }

      setTooltipInfo({ visible: true, left, top, data: tooltipData })
    }

    const chartOpt = chartOptions(currentDays, !!secondAxis, inlineTooltipHandler, matchedYAxis ? max : undefined)
    if (optimization) {
      const { startDate, endDate, isUnsetEndDate } = parseDateByOptValue(optimization)
      const startLabel = startDate.replace(/-/g, '.')
      const endLabel = endDate.replace(/-/g, '.')
      const hasStartDate = currentDays.includes(startLabel)
      const hasEndDate = isUnsetEndDate ? false : currentDays.includes(endLabel)
      const startLine = getVerticalLineAnnotation({ color: '#ECB91A', content: '시작일', value: startLabel })
      const endLine = getVerticalLineAnnotation({ color: '#EB424C', content: '종료일', value: endLabel })
      chartOpt.plugins.annotation.annotations = {
        ...(hasStartDate && { startLine }),
        ...(hasEndDate && { endLine })
      }
    }
    setOptions(chartOpt)
    setData({
      labels: currentDays || [],
      datasets: [
        generateDataSet({ ...firstChartUI, type: chartType, data: first || [] }),
        ...(hasCompare ? [generateDataSet({ ...firstCompareChartUI, type: chartType, data: firstCompare || [] })] : []),
        ...(hasSecond ? [generateDataSet({ ...secondChartUI, type: chartType, data: second || [] })] : []),
        ...(hasSecond && hasCompare
          ? [generateDataSet({ ...secondCompareChartUI, type: chartType, data: secondCompare || [] })]
          : [])
      ]
    })

    return () => {
      isMounted = false
    }
  }, [chartDataset, optimization, chartType, currentDays, firstAxis, secondAxis, chartData, compareDays])
  return (
    <>
      <div className="campaign-report-chart" onMouseLeave={handleChartTooltip}>
        {chartType === 'line' && <Chart datasetIdKey="id" options={options} data={data as any} type="line" />}
        {chartType === 'bar' && <Chart datasetIdKey="id" options={options} data={data as any} type="bar" />}
        {tooltipInfo.visible && <ReportChartTooltip {...tooltipInfo} />}
      </div>
    </>
  )
}

export default NaverCommerceChart
