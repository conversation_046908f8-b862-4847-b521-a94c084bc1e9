import React, { Dispatch, SetStateAction, useState, useEffect } from 'react';
import TreeView from '@material-ui/lab/TreeView';
import TreeItem from '@material-ui/lab/TreeItem';
import { Icon } from '@material-ui/core';
import { cloneDeep } from 'lodash';
import './DvCampaignTreeComponent.scss';

import AddIcon from '@material-ui/icons/Add';
import RemoveRoundedIcon from '@material-ui/icons/RemoveRounded';
import { useTranslation } from 'react-i18next';
import {
  SearchOptimizationAdgroupsOption,
  ConvertCampaignData,
  ConvertedToTreeDataInfo,
} from '@models/budgetOptimization/DvOptimizationAdgroups';
import { ActionType } from '@models/common/CommonConstants';
import { Campaign } from '@models/common/Campaign';
import CustomTooltip from '@components/common/CustomTooltip';
import { useActionType } from '@hooks/common';

interface Props {
  data: ConvertCampaignData;
  setData: Dispatch<SetStateAction<ConvertCampaignData>>;
  selectedGroups: Campaign[];
  setSelectedGroups: (adgroups: Campaign[]) => void;
  options: SearchOptimizationAdgroupsOption;
  type: ActionType;
  notYetStarted: boolean;
}

const addOrPopFromArray = (inputArray: Campaign[], campaign: Campaign) => {
  const idx = inputArray.findIndex((x) => x.campaignId === campaign.campaignId);

  if (idx !== -1) {
    inputArray.splice(idx, 1);
  } else {
    inputArray.push(campaign);
  }

  return inputArray;
};

type paramsNodes = {[key:string]: ConvertedToTreeDataInfo}
const convertMap = (treeNodes: paramsNodes) => {
  const temp = new Map();
  Object.values(treeNodes).forEach(node => {
    temp.set(node.id, node)
  })
  return temp;
}

export default function DvCampaignTreeComponent({
  options,
  data,
  setData,
  selectedGroups,
  setSelectedGroups,
  type,
  notYetStarted,
}: Props) {
  const { t } = useTranslation();
  const { isEditType, isReadType } = useActionType(type);
  const [parentNodes, setParentNodes] = useState<Map<string, ConvertedToTreeDataInfo>>(new Map());
  const [childNodes, setChildNodes] = useState<Map<string, ConvertedToTreeDataInfo>>(new Map());
  const [clickedNode, setClickedNode] = useState('');

  useEffect(() => {
    if(data) {
      let tempAds = new Map();
      Object.values(data).forEach(media => {
        if(media.campaigns) {
          tempAds = new Map([...tempAds, ...convertMap(media.campaigns)])
        }
      });
      setChildNodes(tempAds);
      setParentNodes(convertMap(data));
    }
  }, [data])

  const labelComponent = (nodes: ConvertedToTreeDataInfo) => {
    return (
      <div className="labelComponent">
        <div className="labelName">
          <span>{nodes.name}</span>
          { nodes.newlyCreated && <Icon className="new" /> }
        </div>
        <span className="labelStatus">
          {nodes.active
            ? t('layout.label.optimizationAdgroup.statusOn')
            : t('layout.label.optimizationAdgroup.statusOff')}
        </span>
        <span className="labelBudget">
          {nodes.dailyBudget
            ? nodes.dailyBudget.toLocaleString('ko-KR')
            : t('layout.label.optimizationAdgroup.budgetNoLimit')}
        </span>
      </div>
    );
  };

  const changeAdgroupStatus = (nodes: ConvertedToTreeDataInfo, changeToSelect = false) => {
    const campaign = {
      accountId: nodes.accountId,
      campaignId: nodes.id,
      mediaType: nodes.mediaType,
    } as Campaign;

    if (nodes.isSelected) {
      if (!changeToSelect) {
        nodes.isSelected = false;
        addOrPopFromArray(selectedGroups, campaign);
        data[nodes.mediaType!].activeAdgroupSelectCount -= 1;
      }
    } else {
      nodes.isSelected = true;
      addOrPopFromArray(selectedGroups, campaign);
      data[nodes.mediaType!].activeAdgroupSelectCount += 1;
    }
    if (data[nodes.mediaType!].activeAdgroupSelectCount === data[nodes.mediaType!].maxAdgroupSelectedCount) {
      data[nodes.mediaType!].isSelected = true;
    } else {
      data[nodes.mediaType!].isSelected = false;
    }
    setSelectedGroups(cloneDeep(selectedGroups));
  };

  const addHiddenClass = (nodes: ConvertedToTreeDataInfo, isCampaignNode: boolean) => {
    if (isCampaignNode) {
      const adgroupCntOfCampaign = Object.values(nodes.campaigns!).length;
      const hiddenAdgroupCnt = Object.values(nodes.campaigns!).filter((campaign) => isHiddenNode(campaign)).length;
      return adgroupCntOfCampaign === hiddenAdgroupCnt ? true : false;
    }
    return isHiddenNode(nodes);
  };

  const isHiddenNode = (nodes: ConvertedToTreeDataInfo) => {
    if (options.keywordName && !nodes.name.toLowerCase().includes(options.keywordName.toLowerCase())) {
      return true;
    }

    return false;
  };

  const labelOnclick = (nodes: ConvertedToTreeDataInfo, isMediaNode: boolean, notYetStarted = false) => {
    if (isMediaNode && nodes.canSelected) {
      const campaignAllSelect = nodes.isSelected;
      Object.values(nodes.campaigns!).forEach((campaign) => {
        if (notYetStarted && campaign.isSelected && !campaign.canSelected) {
          campaign.canSelected = true
        }
        if (campaign.canSelected) {
          if (campaignAllSelect) {
            changeAdgroupStatus(campaign, false);
          } else {
            changeAdgroupStatus(campaign, true);
          }
        }
      });

      setData(cloneDeep(data));
    } else if (
      nodes.canSelected && data[nodes.mediaType!].canSelected ||
      (notYetStarted && nodes.isSelected)
    ) {
      if (notYetStarted && nodes.isSelected && !nodes.canSelected) {
        nodes.canSelected = true
      }
      changeAdgroupStatus(nodes);
      setData(cloneDeep(data));
    }
  };

  const selectNodeData = (nodes: ConvertedToTreeDataInfo, isParentNode: boolean) => {
    if (!isEditType) labelOnclick(nodes, isParentNode)
    else if (isEditType && notYetStarted) labelOnclick(nodes, isParentNode, true)
  }

  const renderTree = (nodes: ConvertedToTreeDataInfo, isMediaNode: boolean) => {
    const getClassName = () => {
      if (isMediaNode) {
        if (
          !nodes.campaigns ||
          Object.values(nodes.campaigns).findIndex((campaign) => !campaign.inAllocating || campaign.isSelected) === -1
        ) {
          return 'labelNoSelectGrey';
        }
        return 'labelBasic';
      } else {
        // campaign node
        const isSelectable = nodes.canSelected || nodes.isSelected;
        if (isSelectable) {
          if (nodes.predicted) {
            return 'labelBasic';
          } else {
            return 'labelNotPredicted';
          }
        } else {
          return 'labelNoSelectGrey';
        }
      }
    };

    return (
      <TreeItem
        key={nodes.id}
        nodeId={nodes.id}
        label={
          <CustomTooltip title={isMediaNode ? '' : nodes.name} placement="bottom-start">
            {labelComponent(nodes)}
          </CustomTooltip>
        }
        className={`
          ${getClassName()}
          ${nodes.isSelected ? 'label-selected' : ''}
          ${addHiddenClass(nodes, isMediaNode) ? 'hidden' : ''}
          ${isMediaNode ? 'media-item' : 'campaign-item'}`
        }
        onLabelClick={(event) => {
          event.preventDefault();
          if(event.shiftKey) {
            setClickedNode('');
            return;
          } else {
            setClickedNode(nodes.id);
            selectNodeData(nodes, isMediaNode)
          }
        }}
      >
        {nodes.campaigns ? Object.values(nodes.campaigns).map((campaign) => renderTree(campaign, false)) : ''}
      </TreeItem>
    );
  };

  const readOnly = isReadType || (isEditType && !notYetStarted);

  const handleNodeSelect = (event: React.ChangeEvent<{}>, selected: string[]) => {
    event.preventDefault();
    if(selected.length > 1) {
      const restNodes = selected.filter(node => node !== clickedNode)
      restNodes.forEach(nodeId => {
        const isParentNodes = parentNodes.get(nodeId);
        const ischildNodes = childNodes.get(nodeId);
        if(isParentNodes) {
          selectNodeData(isParentNodes, true);
        }
        if(ischildNodes) {
          selectNodeData(ischildNodes, false);
        }
      })
      setClickedNode('');
    }
  }
  return (
    <TreeView
      className={`treeViewRoot ${readOnly ? 'readOnly' : ''}`}
      defaultCollapseIcon={<RemoveRoundedIcon />}
      defaultExpandIcon={<AddIcon color="disabled" />}
      multiSelect
      onNodeSelect={handleNodeSelect}
    >
      {Object.values(data).map((campaign) => renderTree(campaign, true))}
    </TreeView>
  );
}
