#BudgetOptConfig {
  color: var(--point_color);

  .MuiInputBase-root {
    width: 410px;
    height: 31px;
    padding-right: 12px;
    font-size: 14px;
    font-weight: 300;
    color: var(--point_color);
    border: 1px solid #bbbdcd;
    border-radius: 22.5px;
    background-color: #fff;

    input {
      height: 23px;
      padding: 1px 0px 1px 15px;
      width: 100%;
    }

    fieldset {
      border: none;
    }

    &.MuiInput-underline {
      &::before,
      &::after {
        display: none;
      }
    }

    .MuiInputBase-input {
      font-size: 14px;
      font-weight: 300;
      color: var(--point_color);
      box-sizing: border-box;

      &:focus {
        background: none;
      }
    }
  }

  .MuiToggleButtonGroup-root {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 6px;
    border-radius: 0;
  }

  .MuiCheckbox-root {
    .MuiIconButton-label {
      &::before {
        border: 0.5px solid var(--point_color) !important;
        background-color: transparent !important;
        top: -2px !important;
        left: -2px !important;
        width: 14px !important;
        height: 14px !important;
      }
      &::after {
        top: 1px !important;
        left: 1px !important;
        width: 9px !important;
        height: 9px !important;
      }
    }
  }
  .MuiFormControlLabel-root.kpi-label .MuiFormControlLabel-label {
    padding-left: 10px !important;
  }

  .MuiRadio-root {
    .MuiIconButton-label {
      > div {
        &::before {
          border: 0.5px solid var(--point_color) !important;
          width: 15px !important;
          height: 15px !important;
        }
        &::after {
          top: 3px !important;
          left: 3px !important;
          width: 9px !important;
          height: 9px !important;
        }
      }
    }
  }

  .budget-config-section {
    background-color: var(--bg-gray-light);
    padding: 24px;
    margin: 16px 0;
    display: grid;
    grid-template-columns: 45% 55%;
    align-items: flex-start;
    border-radius: 3px;
    box-shadow: 0px 4px 8px 0px #0000001c;

    .MuiInputBase-input {
      font-size: 16px;
      font-weight: 700;
    }

    .config-label-container {
      display: flex;
      align-items: center;

      svg path {
        fill: var(--point_color);
      }

      .head-label {
        font-size: 20px;
        font-weight: 900;
        padding: 0 6px;
        line-height: 28px;
      }

      .sub-label {
        font-size: 14px;
        font-weight: 400;
        padding: 0 6px;
        line-height: 28px;
      }
    }

    .sub-config-section {
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      margin-top: 16px;
      border-radius: 3px;

      .config-label-container {
        .head-label {
          font-size: 14px;
        }

        .sub-label {
          font-size: 12px;
          padding: 0 4px;
        }
      }

      .budget-config-radio {
        width: 60%;

        .radio-button-label {
          display: flex;
          flex-direction: column;

          &__en {
            font-size: 14px;
            font-weight: 500;
          }
          &__ko {
            font-size: 10px;
          }
        }
      }
    }
  }

  #budget-variance {
    .budget-variance-input {
      margin-left: -20px;
      .MuiInputBase-root {
        width: 100%;
        height: 26px;
        padding-right: 12px;
        font-size: 14px;
        font-weight: 300;
        color: var(--point_color);
        border: 1px solid #bbbdcd;
        border-radius: 22.5px;
        background-color: #fff;

        input {
          height: 23px;
          padding: 1px 0px 1px 15px;
          width: 100%;
        }

        fieldset {
          border: none;
        }

        &.MuiInput-underline {
          &::before,
          &::after {
            display: none;
          }
        }

        .MuiInputBase-input {
          font-size: 14px;
          font-weight: 500;
          color: var(--point_color);
          box-sizing: border-box;
          text-align: right;

          &:focus {
            background: none;
          }
        }

        .MuiTypography-root {
          font-size: 11px;
          font-weight: 300;
          color: var(--point_color);
        }
      }
    }
  }

  .budget-opt-switch {
    &.MuiSwitch-root {
      width: auto;
      height: auto;
      padding: 0;
      margin: 8px 0;
      align-items: center;
      display: flex;

      .MuiSwitch-switchBase {
        left: 2px;
        padding: 0;

        .MuiSwitch-thumb {
          position: relative;
          top: 2px;
          width: 14px;
          height: 14px;
          padding: 0;
          background-color: #ffffff;
          opacity: 1;
          box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        }

        &.Mui-checked {
          transform: translateX(18px);
        }
      }

      .MuiSwitch-track {
        width: 36px;
        height: 18px;
        padding: 0;
        border-radius: 10px;
        background-color: #707395;
        opacity: 0.6;
        border: 1px solid #707395;
      }

      .Mui-checked {
        & + .MuiSwitch-track {
          background-color: var(--status-active);
          opacity: 1;
          border: 1px solid var(--status-active);
        }
      }

      .Mui-disabled {
        .MuiSwitch-thumb {
          background-color: #ccc;
          opacity: 1;
        }

        & + .MuiSwitch-track {
          background-color: #fff;
          opacity: 1;
          border: 1px solid #959595;
        }
      }
    }
  }

  .budget-config-radio {
    justify-content: space-between;

    .MuiRadio-root {
      padding: 7px;
      margin-left: -7px;
    }
    .MuiButtonBase-root {
      .MuiIconButton-label > div {
        svg {
          display: none;
        }
        &::before {
          content: '';
          width: 12px;
          height: 12px;
          border: 1px solid #dedfe4;
          border-radius: 50%;
          background-color: var(--bg-gray-light);
        }
      }
      &.Mui-checked {
        .MuiIconButton-label > div {
          &::after {
            position: absolute;
            top: 2px;
            left: 2px;
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--status-active);
          }
        }
      }
      &.Mui-disabled {
        .MuiIconButton-label > div::after {
          background-color: rgba(0, 0, 0, 0.38);
        }
      }
    }

    [class*='PrivateRadioButtonIcon-checked'] {
      &::after {
        position: absolute;
        top: 2px;
        left: 2px;
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: var(--status-active);
      }
    }
    .MuiIconButton-colorSecondary:hover {
      background-color: rgba(51, 134, 200, 0.04);
    }

    .MuiFormControlLabel-root {
      margin: 0px;
    }
    .MuiFormControlLabel-label {
      padding-left: 6px;
      font-size: 16px;
      font-weight: 400;
      color: var(--point_color);
    }

    &.small {
      .MuiFormControlLabel-label {
        padding-left: 4px;
        font-size: 14px;
      }
    }
  }

  #optimization-goals {
    .MuiToggleButtonGroup-root {
      width: 100%;
      display: flex;
      justify-content: space-between;
      gap: 6px;
      border-radius: 0;
    }

    .optimization-goal {
      flex: 1;
      height: 160px;
      background-color: var(--bg-gray-light);
      border: none;
      padding: 25px 0;
      border: 1px solid transparent;
      border-top: 2px solid var(--point_color);

      &.Mui-selected {
        border-color: #c1415f;
        background-color: #fff;
        border-radius: 0;
        &.Mui-disabled {
          border-color: rgba(0, 0, 0, 0.38);
        }
        &[value='KPIS'] .upper {
          display: none !important;
        }
      }
      &:not(.Mui-selected).Mui-disabled {
        filter: grayscale(1);
        opacity: 0.4;
      }
      .MuiToggleButton-label {
        display: flex;
        flex-direction: column;
        .upper {
          display: flex;
          align-items: center;
          .optimization-icon {
            line-height: 0;
            > .icon-image {
              width: 50px;
              padding-right: 10px;
            }
          }
        }
      }

      .optimization-name-kor {
        font-size: 18px;
        font-weight: 400;
        color: var(--point_color);
        text-align: left;
        line-height: 1.4;
      }

      .optimization-name-eng {
        font-size: 13px;
        font-weight: 700;
        color: var(--color-active-blue);
        text-align: left;
        line-height: 1;
      }

      .optimization-description-container {
        margin-top: 20px;

        &.optimization-kpis-settings {
          margin-top: 0;
        }

        .optimization-description {
          font-size: 12px;
          font-weight: 400;
          color: var(--point_color);
          line-height: 1.3;

          em {
            font-style: normal;
            font-weight: 700;
            color: var(--color-active-blue);
          }
        }

        .MuiTableCell-root {
          border: none;
        }
      }
    }
  }
}

#BudgetOptConfig-advice-tooltip-optimization-goal,
#BudgetOptConfig-advice-tooltip-budget-variance,
#BudgetOptConfig-advice-tooltip-contribution-type {
  border: 1px solid var(--point_color);
  background-color: #fff;
  .MuiTooltip-tooltip {
    padding: 0 0 12px 0;
    margin: 0;
    background-color: transparent;
    .border-bottom {
      border-bottom: 1px solid #9196a4;
    }
    .indent1 {
      padding: 0 12px 0 14px;
      text-indent: -14px;
    }
    .indent2 {
      padding: 0 12px 0 30px;
      text-indent: -14px;
    }
    .indent3 {
      padding: 0 12px 0 38px;
      text-indent: -38px;
    }
    > div {
      color: var(--point_color);
      font-size: 12px;
      font-weight: normal;
      > h1,
      > h2,
      > h3,
      > h4,
      > h5,
      > h6 {
        font-size: 12px;
        font-weight: bold;
        margin: 0;
        padding: 12px 0px;
        border-bottom: 1px solid #9196a4;
        font-weight: 700;
        text-align: center;
      }
      > div {
        padding: 8px 12px 6px;
        line-height: 1.5;
        ul {
          margin: 0;
          padding-inline-start: 2em;
        }
      }
    }
    > div > ul {
      color: var(--point_color);
      font-size: 12px;
      font-weight: 500;
    }
    .MuiTooltip-arrow::before {
      border-color: var(--point_color);
    }
  }
}

#dv-optimization-table-popper {
  z-index: 1400;
  background-color: #fff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  &.disabled {
    filter: grayscale(1);
    th {
      &:first-child {
        border-top-color: gray;
      }
      border-top-color: lightgray;
    }
    td:first-child {
      background-color: gray;
    }
    .recommended {
      background-color: gray;
    }
  }
  .bottom {
    font-size: 8px;
    text-align: right;
    padding: 2px 4px;
  }
}

#dv-optimization-table-caption {
  font-size: 0.5rem;
  text-align: right;
  padding: 4px;
  .reload {
    font-size: 0.875rem;
    padding: 0;
    margin: 0 4px;
    background: #f2f3f6;
    border: 1px solid gray;
    border-radius: 20px;
    .MuiIconButton-label {
      padding: 2px 6px 2px 2px;
      svg {
        width: 20px;
        height: 20px;
      }
      &[disabled] {
        filter: grayscale(1);
        opacity: 0.4;
      }
    }
  }
}

#dv-optimization-table {
  position: relative;
  width: 380px;
  table-layout: fixed;
  th {
    font-size: 1rem;
    font-weight: normal;
    border: 0 none;
    border-top: 3px solid var(--color-blue-darker);
    border-right: 1px solid #000;
    background-color: #f2f3f6;
    text-align: center;
    padding: 6px 16px;

    &:first-child {
      border-top: 3px solid var(--color-active-blue);
      color: var(--color-active-blue);
    }
    &:last-child {
      border-right: 0 none;
    }
  }
  td {
    font-size: 0.875rem;
    border: 0 none;
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    background-color: #fff;
    text-align: center;
    padding: 6px 16px;

    &:first-child {
      background-color: var(--color-active-blue);
      color: #fff;
    }
    &:last-child {
      border-right: 0 none;
    }
  }
  .recommended {
    font-size: 0.5rem;
    background-color: var(--color-active-blue);
    color: #fff;
    position: absolute;
    padding: 2px 4px;
    border-radius: 9999px;
    top: -8px;
    left: 50px;
    line-height: 1;
  }
}
