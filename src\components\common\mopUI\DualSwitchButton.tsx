// src/components/common/mopUI/DualSwitchButton.tsx
import React from 'react'

// 스타일 클래스 정의 인터페이스
export interface StyleClasses {
  /** 첫 번째 버튼이 선택되었을 때의 클래스 */
  firstSelected?: string
  /** 두 번째 버튼이 선택되었을 때의 클래스 */
  secondSelected?: string
  /** 선택되지 않은 상태의 클래스 */
  unselected?: string
}

export interface DualSwitchButtonProps {
  /** 첫 번째 선택지의 텍스트 */
  firstOption: string
  /** 두 번째 선택지의 텍스트 */
  secondOption: string
  /** 현재 선택된 옵션 (true: 첫 번째, false: 두 번째) */
  isFirstSelected: boolean
  /** 선택이 변경될 때 호출되는 콜백 함수 */
  onChange: (isFirstSelected: boolean) => void
  /** 커스텀 스타일 클래스들 */
  selectionStyles?: StyleClasses
  /** 첫 번째 버튼의 추가 클래스 */
  firstButtonClassName?: string
  /** 두 번째 버튼의 추가 클래스 */
  secondButtonClassName?: string
  /** 비활성화 상태 */
  disabled?: boolean
  /** 추가 CSS 클래스 */
  className?: string
}


const DualSwitchButton: React.FC<DualSwitchButtonProps> = ({
  firstOption,
  secondOption,
  isFirstSelected,
  onChange,
  selectionStyles,
  firstButtonClassName = '',
  secondButtonClassName = '',
  disabled = false,
  className = '',
}) => {
  const handleFirstClick = () => {
    if (!disabled && !isFirstSelected) {
      onChange(true)
    }
  }

  const handleSecondClick = () => {
    if (!disabled && isFirstSelected) {
      onChange(false)
    }
  }

  // props와 기본값을 병합한 최종 선택 스타일
  const resolvedSelectionStyles: StyleClasses = {
    firstSelected: selectionStyles?.firstSelected || 'text-[#333333] font-bold bg-white border border-[#efefef]',
    secondSelected: selectionStyles?.secondSelected || 'text-[#333333] font-bold bg-white border border-[#efefef]',
    unselected: selectionStyles?.unselected || 'text-[#969696] font-semibold',
  }

  // 공통 기본 클래스
  const getBaseButtonClasses = (additionalClassName = '') => `
    px-2.5 py-[3px] text-xs
    rounded-full
    font-semibold
    transition-all
    duration-300
    ease-in-out
    cursor-pointer
    select-none
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-sm'}
    ${additionalClassName}
  `.trim()

  // 버튼별 클래스 결정 로직
  const getButtonClasses = (isThisButtonSelected: boolean, buttonType: 'first' | 'second') => {
    const additionalClassName = buttonType === 'first' ? firstButtonClassName : secondButtonClassName
    const baseClasses = getBaseButtonClasses(additionalClassName)
    
    if (isThisButtonSelected) {
      const selectedStyle = buttonType === 'first' ? resolvedSelectionStyles.firstSelected : resolvedSelectionStyles.secondSelected
      return `${baseClasses} ${selectedStyle} shadow-sm transform scale-105`
    } else {
      return `${baseClasses} ${resolvedSelectionStyles.unselected}`
    }
  }

  return (
    <div className={`inline-flex py-[4px] px-[5px] gap-2.5 bg-[#f9f9fb] border border-[#efefef] rounded-full ${className}`}>
      <div
        className={getButtonClasses(isFirstSelected, 'first')}
        onClick={handleFirstClick}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
            e.preventDefault()
            handleFirstClick()
          }
        }}
        aria-pressed={isFirstSelected}
        aria-disabled={disabled}
      >
        {firstOption}
      </div>
      <div
        className={getButtonClasses(!isFirstSelected, 'second')}
        onClick={handleSecondClick}
        role="button"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if ((e.key === 'Enter' || e.key === ' ') && !disabled) {
            e.preventDefault()
            handleSecondClick()
          }
        }}
        aria-pressed={!isFirstSelected}
        aria-disabled={disabled}
      >
        {secondOption}
      </div>
    </div>
  )
}

export default DualSwitchButton
