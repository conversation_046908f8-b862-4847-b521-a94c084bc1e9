#DvOptimizationResultModal {
  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.4);
  }

  .MuiDialog-scrollBody {
    overflow-x: auto;
  }

  .MuiDialog-scrollBody {
    overflow-x: auto;
  }

  .MuiDialogTitle-root {
    height: 0px;
    padding: 0px;

    .modal-close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      border-radius: 50%;
      background: #fff;
    }
  }

  .MuiDialog-paper {
    width: 1600px;
    max-width: 1600px;
    height: 870px;

    padding: 20px 0;
    // padding-left: 0;
    background-color: var(--bg-gray-light);
    box-sizing: border-box;

    #title {
      line-height: 1;
      // margin-left: 18px;
      font-size: 25px;
      font-weight: 900;
      color: var(--point_color);
    }

    span.predict-date {
      display: inline-block;
      margin: 0 22px;
      padding: 0 22px;
      height: 36px;
      line-height: 34px;
      font-size: 18px;
      font-weight: 200;
      color: var(--color-white);
      border-radius: 18px;
      background-color: var(--point_color);
    }

    #result-by-media {
      position: relative;
      width: 100%;
      height: 310px;
      margin-top: 23px;
      &::before {
        content: '';
        position: absolute;
        right: 0;
        width: 100%;
        height: 310px;
        background-color: #e2e5eb;
        border-radius: 155px 0 0 155px;
      }
      #result-by-media-container {
        width: 100%;
        height: 314px;
        padding: 25px;
        padding-right: 40px;
        position: absolute;
        background-color: transparent;

        #result-table-container {
          flex: 1;
          height: 100%;

          #result-table {
            width: 100%;
            height: 211px;
            margin-top: 18px;
          }
        }
        #result-chart {
          height: 100%;
        }
      }
    }

    #result-by-campaign-container {
      margin-top: 25px;
      padding-right: 40px;
      padding-left: 20px;

      #result-by-campaign-name {
        line-height: 1;
        display: flex;
        #result-download-button {
          margin-left: auto;

          #download-button {
            width: 23px;
            height: 23px;
            min-width: 23px;
            padding: 0px;
            span {
              width: 23px;
              height: 23px;
            }
            svg {
              width: 23px;
              height: 23px;
            }
          }
        }
      }

      #result-table {
        margin-top: 15px;
        .MuiTableContainer-root {
          max-height: 331px;
        }
      }
    }

    .result-table-name {
      font-size: 24px;
      font-weight: 700;
      line-height: 1;
      color: var(--point_color);
    }

    .MuiDialogContent-root {
      padding: 0;
      padding-left: 20px;
    }

    .DvOptimizationNoData {
      flex: 1;
      margin-top: 100px;
    }
  }
}
