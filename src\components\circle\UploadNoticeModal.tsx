import './UploadNoticeModal.scss'
import { useTranslation } from 'react-i18next'
import { MopDialog } from '@components/common/dialog'
import InnerHtml from '@components/common/InnerHtml'

interface Props {
  open: boolean
  close: () => void
}

const UploadNoticeModal = ({ open, close }: Props) => {
  const { t } = useTranslation()
  const routeUploadInfo = () => {
    window.open('https://tally.so/r/3EPDdN', '_blank', 'noreferrer')
    close()
  }
  return (
    <MopDialog
      open={open}
      actionLabel={t('circle.existingUnit.uploadNotice.button')}
      handleAction={routeUploadInfo}
      handleClose={close}
      customClass="upload-notice-modal"
    >
      <MopDialog.Header showClose handleClose={close}>
        { t('common.message.title.notice') }
      </MopDialog.Header>
      <MopDialog.Body>
        <>
          <InnerHtml tagName="p" innerHTML={t('circle.existingUnit.uploadNotice.1')} />
          <InnerHtml tagName="p" innerHTML={t('circle.existingUnit.uploadNotice.2')} />
          <InnerHtml tagName="p" className="small" innerHTML={t('circle.existingUnit.uploadNotice.3')} />
        </>
      </MopDialog.Body>
    </MopDialog>
  )
}

export default UploadNoticeModal