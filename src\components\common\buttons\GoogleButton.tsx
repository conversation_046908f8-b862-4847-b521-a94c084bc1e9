import { Button } from "@material-ui/core";
import { ReactComponent as Google<PERSON><PERSON> } from '@components/assets/images/logo_google.svg';
import './GoogleButton.scss';

interface Props {
  onClick: React.MouseEventHandler<HTMLButtonElement>;
  text: string;
  icon?: boolean;
}
const GoogleButton = ({
  onClick,
  text,
  icon = true
}: Props) => {
  return (
    <Button
      className="google-btn"
      variant="contained"
      onClick={onClick}
      startIcon={icon && <GoogleLogo width={18} />}
    >
      {text}
    </Button>
  )
};

export default GoogleButton;