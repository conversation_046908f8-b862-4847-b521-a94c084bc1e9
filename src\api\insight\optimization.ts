/* istanbul ignore file */

import { callApi, Method, handleResultWithDefault, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import CommonResponse from '@models/common/CommonResponse';
import { AxiosResponse } from 'axios';
import type { BudgetByMediaPlatform, BudgetByOptimization, BudgetSummary, RecommendOptimization } from '@models/insight/Optimization';
import { PlatformTypeWithETC } from '@models/common/Platform';
import { YNFlag } from '@models/common/YNFlag'
import { ContributionType } from '@models/budgetOpt/BudgetOpt';

export const getBudgetByMediaPlatform = async (
  advertiserId: number,
  startDate: string,
  endDate: string
) => {
  const response: CommonResponse<BudgetByMediaPlatform[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/insight/optimization/budget/${advertiserId}/cost`,
    method: Method.GET,
    params: {
      queryParams: {
        startDate,
        endDate
      },
    }
  });

  return handleResultWithDefault(response, [])
};

export const getBudgetByOptimization = async (
  advertiserId: number,
  excludeKeywordYn: YNFlag
) => {
  const response: CommonResponse<BudgetByOptimization[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/insight/optimization/budget/${advertiserId}/optimization-cost`,
    method: Method.GET,
    params: {
      queryParams: {
        excludeKeywordYn
      }
    }
  });

  return handleResultWithDefault(response, [])
};

const defaultBudgetSummary:BudgetSummary = {
  optimizationCosts: [
    { platformType: PlatformTypeWithETC.SA, cost: 150 },
  ],
  remainingBidAvailableCost: 100,
  bidUnavailableCost: 0,
  recentCollectionTime: "2024.08.12 05:47:58"
}
export const getBudgetSummary = async (
  advertiserId: number,
  startDate: string,
  endDate: string
) => {
  const response: CommonResponse<BudgetSummary> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/insight/optimization/budget/${advertiserId}/optimization-cost/summary`,
    method: Method.GET,
    params: {
      queryParams: {
        startDate,
        endDate
      },
    }
  });

  return handleResultWithDefault(response, defaultBudgetSummary)
  // return defaultBudgetSummary
}

const defaultRecommendBudget: RecommendOptimization = {
  optimizationId: 0,
  optimizationName: '',
  advertiserId: 0,
  weeklyBudget: 0,
  budgetChangeRate: 0,
  contributionType: ContributionType.NONE,
  mediaBudgetFix: YNFlag.N,
  engineRunDate: '',
  performImproveRate: 0,
  useYn: YNFlag.N,
  status: '',
  results: []
}
export const getRecommendBudget = async (
  advertiserId: number
) => {
  const response: CommonResponse<RecommendOptimization> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/insight/optimization/budget/${advertiserId}/result`,
    method: Method.GET
  });

  return handleResultWithDefault(response, defaultRecommendBudget)
};

export const downloadBudgetByMediaPlatform = async (
  advertiserId: number,
  startDate: string,
  endDate: string
) => {
  const response: CommonResponse|AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/insight/optimization/budget/${advertiserId}/cost/download`,
    method: Method.GET,
    params: {
      queryParams: {
        startDate,
        endDate
      },
    }
  });

  if (response.data) {
    openDownloadLink(response);
    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};