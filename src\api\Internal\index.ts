/* istanbul ignore file */
import CommonResponse from '@models/common/CommonResponse'
import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import { UnsubscribeParams } from '@models/setting/Email'

export const allowAuthInvitation = async (advertiserId: number, email: string, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/internal/authorities/invitation',
    method: Method.PUT,
    params: {
      queryParams: {
        advertiserId,
        email
      }
    },
    config: { isLoading }
  })
  return response.successOrNot
}

export const updateSubscription = async (params: UnsubscribeParams, isLoading = true): Promise<boolean> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/internal/authorities/email/unsubscribe',
    method: Method.PUT,
    params: {
      bodyParams: params
    },
    config: {
      isLoading: isLoading
    }
  })
  return response.successOrNot === 'Y'
}
