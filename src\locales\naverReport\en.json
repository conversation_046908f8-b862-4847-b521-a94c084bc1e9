{"indicator": {"label": {"pageView": "Page Views", "numPurchases": "Orders", "productQuantity": "Units Sold", "payAmount": "Revenue", "refundNumPurchases": "Refunded Orders", "refundProductQuantity": "Units Refunded", "refundPayAmount": "Refund Amount", "productCouponDiscountAmount": "Product Discount", "orderCouponDiscountAmount": "Order Discount", "cost": "Cost", "salesAmount": "Cost", "impressions": "Impressions", "clicks": "<PERSON>licks", "conversion": "Conversions", "directConversion": "Direct Conversions", "conversionRevenue": "Conversion Revenue", "directConversionRevenue": "Direct Revenue", "ctr": "CTR (%)", "cvr": "CVR (%)", "cpm": "CPM", "cpa": "CPA", "roas": "ROAS (%)", "cpc": "CPC"}, "lableWithUnit": {"pageView": "Page Views", "numPurchases": "Orders", "productQuantity": "Units Sold", "payAmount": "Revenue", "refundNumPurchases": "Refunded Orders", "refundProductQuantity": "Units Refunded", "refundPayAmount": "Refund Amount", "productCouponDiscountAmount": "Product Discount", "orderCouponDiscountAmount": "Order Discount", "cost": "Cost", "salesAmount": "Cost", "impressions": "Impressions", "clicks": "<PERSON>licks", "conversion": "Conversions", "directConversion": "Direct Conversions", "conversionRevenue": "Conversion Revenue", "directConversionRevenue": "Direct Revenue", "ctr": "CTR (%)", "cvr": "CVR (%)", "cpm": "CPM", "cpa": "CPA", "roas": "ROAS (%)", "cpc": "CPC"}, "unit": {"ctr": "%", "cvr": "%", "roas": "%"}}, "indicatorGroup": {"product": "Product Performance", "ad": "Ad Performance"}, "productPerformance": "Product Performance", "adPerformance": "Ad Performance", "label": {"button": {"metrics": "Metrics"}, "ReportFilter": {"button": {"search": "Search"}}, "ReportFilterName": {"reportPeriod": "Report Period", "comparePeriod": "Comparison Period"}}, "statusType": {"SALE": "On Sale", "WAIT": "Waiting", "UNADMISSION": "Not Approved", "REJECTION": "Rejected", "SUSPENSION": "Sales Suspended", "CLOSE": "Discontinued", "PROHIBITION": "Sales Prohibited", "OUTOFSTOCK": "Out of Stock", "DELETE": "Deleted"}, "download": {"column": {"productName": "Product Name", "productId": "Product ID", "statusType": "Status", "division": "Division", "date": "Date"}}, "tableHeader": {"division": "Division", "date": "Date", "product": "Product"}, "period": {"reportPeriod": "Report Period", "comparePeriod": "Compare Period"}, "group": {"productPerformance": "Product Performance", "advertisingPerformance": "Advertising Performance"}, "panel": {"optId": "Opt ID", "campaign": "Campaign", "adgroup": "Adgroup", "creativeId": "Creative ID", "open": "Open panel", "close": "Close panel"}, "total": "Total", "filter": {"toastMessages": {"summaryError": "An error occurred while loading summary data.", "graphError": "An error occurred while loading graph data.", "unexpectedError": "An unexpected error occurred while loading data."}, "dialogTitle": "Warning", "formLabels": {"optimization": "Optimization", "product": "Product"}, "placeholder": {"productSearch": "Search products (name/code)"}, "selectAll": {"selectAll": "Select All", "unselectAll": "Unselect All"}, "platform": {"spa": "SPA"}, "optId": "Opt ID", "noImage": "No Image", "endDateNotSet": "End date not set"}, "last7Days": "Last 7 days"}