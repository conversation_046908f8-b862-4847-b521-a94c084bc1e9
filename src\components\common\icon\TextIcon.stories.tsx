// src/components/common/icon/TextIcon.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import TextIcon from './TextIcon';
import { AdvertiserCurrencyCode, AuthorityType } from '@models/common/Advertiser';

const meta: StorybookMeta<typeof TextIcon> = {
  title: 'Components/Common/Icon/TextIcon',
  component: TextIcon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    code: {
      control: 'select',
      options: [...Object.values(AdvertiserCurrencyCode), ...Object.values(AuthorityType)],
      description: 'Icon code (Currency or Authority type)',
    },
    size: {
      control: { type: 'range', min: 16, max: 128, step: 4 },
      description: 'Icon size (px)',
    },
    type: {
      control: 'select',
      options: ['currency', 'authorize'],
      description: 'Icon type',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    code: AuthorityType.ADMINISTRATE,
    size: 32,
    type: 'authorize',
  },
};

// Currency Icons
export const USD: Story = {
  args: {
    code: AdvertiserCurrencyCode.USD,
    size: 32,
    type: 'currency',
  },
  parameters: {
    docs: {
      description: {
        story: 'USD currency icon - Displays "$" character on a circular background.',
      },
    },
  },
};

export const KRW: Story = {
  args: {
    code: AdvertiserCurrencyCode.KRW,
    size: 32,
    type: 'currency',
  },
  parameters: {
    docs: {
      description: {
        story: 'KRW currency icon - Displays "￦" character on a circular background.',
      },
    },
  },
};

// Authority Icons
export const Administrate: Story = {
  args: {
    code: AuthorityType.ADMINISTRATE,
    size: 32,
    type: 'authorize',
  },
  parameters: {
    docs: {
      description: {
        story: 'Administrator authority icon - Displays "A" character on a circular background with admin color.',
      },
    },
  },
};

export const Operate: Story = {
  args: {
    code: AuthorityType.OPERATE,
    size: 32,
    type: 'authorize',
  },
  parameters: {
    docs: {
      description: {
        story: 'Operator authority icon - Displays "O" character on a circular background with operator color.',
      },
    },
  },
};

export const Read: Story = {
  args: {
    code: AuthorityType.READ,
    size: 32,
    type: 'authorize',
  },
  parameters: {
    docs: {
      description: {
        story: 'Read authority icon - Displays "V" character on a circular background with read permission color.',
      },
    },
  },
};

// Currency Group
export const CurrencyGroup: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '24px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AdvertiserCurrencyCode.USD} size={32} type="currency" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>USD ($)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Currency</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AdvertiserCurrencyCode.KRW} size={32} type="currency" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>KRW (￦)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Currency</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Currency icons grouped together. These represent different currency types.',
      },
    },
  },
};

// Authority Group
export const AuthorityGroup: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '24px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>Admin (A)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Administrator</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.OPERATE} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>Operate (O)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Operator</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.READ} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>Read (V)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Viewer</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Authority icons grouped together. Each icon has a unique color representing different permission levels.',
      },
    },
  },
};

// All TextIcons
export const AllTextIcons: Story = {
  render: () => (
    <div>
      <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>Currency Icons</h3>
      <div style={{ 
        display: 'flex', 
        gap: '24px',
        padding: '16px',
        marginBottom: '32px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px'
      }}>
        {Object.values(AdvertiserCurrencyCode).map((currency) => (
          <div key={currency} style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            textAlign: 'center' 
          }}>
            <TextIcon code={currency} size={32} type="currency" />
            <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>{currency}</div>
            <div style={{ fontSize: '10px', color: '#666' }}>Currency</div>
          </div>
        ))}
      </div>

      <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>Authority Icons</h3>
      <div style={{ 
        display: 'flex', 
        gap: '24px',
        padding: '16px',
        backgroundColor: '#f0f8ff',
        borderRadius: '8px'
      }}>
        {Object.values(AuthorityType).map((authority) => (
          <div key={authority} style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            textAlign: 'center' 
          }}>
            <TextIcon code={authority} size={32} type="authorize" />
            <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>{authority}</div>
            <div style={{ fontSize: '10px', color: '#666' }}>Authority</div>
          </div>
        ))}
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All available TextIcons categorized by type. Currency icons use default color, while authority icons have specific colors for each permission level.',
      },
    },
  },
};

// Size Variations
export const SizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={16} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>16px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={24} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>24px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>32px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={48} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>48px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={64} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>64px</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'TextIcon size variations. The text inside automatically scales with the icon size.',
      },
    },
  },
};

// With Hover Tooltip
export const WithTooltip: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '24px',
      padding: '32px',
      minHeight: '120px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AuthorityType.ADMINISTRATE} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Hover for tooltip</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={AdvertiserCurrencyCode.USD} size={32} type="currency" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Hover for tooltip</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'TextIcons with hover tooltips. Hover over the icons to see their full descriptions.',
      },
    },
  },
}; 