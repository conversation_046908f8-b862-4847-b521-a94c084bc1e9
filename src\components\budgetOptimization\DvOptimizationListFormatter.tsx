import './DvOptimizationListFormatter.scss';
import { useTranslation } from 'react-i18next';
import {
  ContextMenuFunctions,
  DvOptimizationInfo,
  DvOptimizationListColumn,
} from '@models/budgetOptimization/DvOptimization';
import Switch from '@material-ui/core/Switch';
import Tooltip from '@material-ui/core/Tooltip';
import { withStyles } from '@material-ui/core/styles';
import { Status, StatusType } from '@models/optimization/Status';
import { getDvaStatusCodes, getErrorStatusCodes, getDvOptimizationGoalCodes } from '@utils/CodeUtil';
import { OptimizationGoal } from '@models/optimization/OptimizationGoal';
import { ErrorStatus } from '@models/optimization/ErrorStatus';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import { checkAuthority } from '@utils/AuthorityUtil';
import { AuthorityType } from '@models/common/Advertiser';
import { advertiserState } from '@store/Advertiser';
import { useRecoilValue } from 'recoil';
import { ReactComponent as DeleteIcon } from '@components/assets/images/icon_delete.svg';
import { ReactComponent as EditIcon } from '@components/assets/images/icon_edit.svg';
import { ReactComponent as ResultIcon } from '@components/assets/images/icon_result.svg';
import { FixedLayoutColumn } from '@components/common/table'
import InnerHtml from '@components/common/InnerHtml';
import { compareDate, currentDate } from '@utils/DateUtil';
import { toCamelCase } from '@utils/StringUtil';
export default class DvOptimizationListFormatter {
  getColumnFormat = (
    contextMenuFunctions?: ContextMenuFunctions,
    orderBy?: string | undefined,
    sorting?: string | undefined
  ): Array<FixedLayoutColumn<DvOptimizationInfo>> => {
    const { t } = useTranslation();
    const advertiser = useRecoilValue(advertiserState);

    const getStatusName = (statusCode: string) => {
      const statusCodes: Status[] = getDvaStatusCodes();

      for (const item of statusCodes) {
        if (item.status === statusCode) {
          return item.statusName;
        }
      }

      return '';
    };

    const getOptimizationGoalName = (optimizationGoalCode: string) => {
      const optimizationGoalCodes: OptimizationGoal[] = getDvOptimizationGoalCodes();

      for (const item of optimizationGoalCodes) {
        if (item.optimizationGoal === optimizationGoalCode) {
          return item.optimizationGoalName;
        }
      }

      return '';
    };

    const getErrorStatusName = (errorStatusCode: string) => {
      const errorStatusCodes: ErrorStatus[] = getErrorStatusCodes();

      for (const item of errorStatusCodes) {
        if (item.errorStatus === errorStatusCode) {
          return item.errorStatusName;
        }
      }

      return '';
    };

    const handleRequestRead = (event?: any) => {
      event?.stopPropagation();
      contextMenuFunctions?.requestRead?.(Number(event.currentTarget.dataset.id));
    };

    const handleRequestEdit = (event?: any) => {
      event?.stopPropagation();
      contextMenuFunctions?.requestEdit?.(Number(event.currentTarget.dataset.id));
    };

    const handleRequestDelete = (event?: any) => {
      event && event.stopPropagation();
      contextMenuFunctions?.requestDelete?.(Number(event.currentTarget.dataset.id));
    };

    const handleAllocatingSwitchChange = (event?: any) => {
      event && event.stopPropagation();
      contextMenuFunctions?.requestAllocatingOnOff?.(Number(event.target.name), event.target.checked as boolean);
    };

    const handleRequestOptimizationResult = (optimizationId: number) => {
      contextMenuFunctions?.requestOptimizationResult?.(optimizationId);
    };

    const AdviceTooltip = withStyles((theme) => ({
      tooltip: {
        backgroundColor: '#ffffff',
        color: '#2b2b2b',
        minWidth: '360px',
        boxShadow: theme.shadows[2],
        fontSize: 13,
      },
      arrow: {
        '&:before': {
          border: '1px solid #E6E8ED',
        },
        color: '#ffffff',
      },
    }))(Tooltip);

    const getColumnTitle = (columnType: string, sorting: boolean) => {
      columnType = toCamelCase(columnType);

      if (sorting) {
        return (
          <>
            <AdviceTooltip
              id="dv-optimization-advice-tooltip"
              title={<InnerHtml innerHTML={t(`optimization.message.dvOptimization.list.${columnType}`) || ''} />}
              placement="right-start"
              arrow
            >
              <span id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
            {t(`optimization.label.list.${columnType}`)}
          </>
        );
      } else {
        return (
          <span className="no-sorting-advice">
            <AdviceTooltip
              id="dv-optimization-advice-tooltip"
              title={<InnerHtml innerHTML={t(`optimization.message.dvOptimization.list.${columnType}`) || ''} />}
              placement="right-start"
              arrow
            >
              <span id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
            {t(`optimization.label.list.${columnType}`)}
          </span>
        );
      }
    };

    const CustomTooltip = withStyles((theme) => ({
      tooltip: {
        backgroundColor: '#56606b',
        color: 'white',
        boxShadow: theme.shadows[1],
        fontSize: 11,
        margin: '0px 0px 0px 0px',
        whiteSpace: 'pre-wrap',
      },
    }))(Tooltip);

    const getColumnAllocationYn = () => {
      return {
        title: getColumnTitle(DvOptimizationListColumn.ALLOCATION_YN, false),
        field: DvOptimizationListColumn.ALLOCATION_YN,
        cellStyle: {
          width: 90,
        },
        sorting: false,
        render: (rowData) => {
          if (rowData.allocationYn) {
            return (
              <div id={`allocationYn-${rowData.optimizationId}`} className={`allocationYn`}>
                <Switch
                  data-testid={`allocationYnSwitch-${rowData.optimizationId}`}
                  className={`allocationYnSwitch`}
                  edge="end"
                  color="primary"
                  disabled={
                    rowData.allocationYn === 'N'
                      ? rowData.status === StatusType.INSPECTING ||
                        rowData.status === StatusType.INSPECTION_ERROR ||
                        rowData.status === StatusType.READY ||
                        rowData.status === StatusType.ALLOCATING ||
                        rowData.status === StatusType.END ||
                        compareDate(rowData.allocationEndDate, currentDate()) < 0 ||
                        !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
                      : rowData.status === StatusType.INSPECTING ||
                        rowData.status === StatusType.INSPECTION_COMPLETED ||
                        rowData.status === StatusType.INSPECTION_ERROR ||
                        rowData.status === StatusType.STOP ||
                        rowData.status === StatusType.END ||
                        !checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
                  }
                  onChange={handleAllocatingSwitchChange}
                  name={rowData.optimizationId.toString()}
                  checked={rowData.allocationYn === 'Y'}
                />
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnStatus = () => {
      return {
        title: getColumnTitle(DvOptimizationListColumn.STATUS, true),
        field: DvOptimizationListColumn.STATUS,
        sorting: true,
        cellStyle: {
          width: 100,
        },
        defaultSort: orderBy === DvOptimizationListColumn.STATUS ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.status) {
            return (
              <div id={`status-${rowData.optimizationId}`} className={`status`}>
                <span
                  data-testid={`status-${rowData.optimizationId}`}
                  className={`statusLabel ${rowData.allocationYn === 'Y' ? 'color-on' : ''}`}
                >
                  {getStatusName(rowData.status)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnOptimizationName = () => {
      return {
        title: t('optimization.label.list.optimizationName'),
        field: DvOptimizationListColumn.OPTIMIZATION_NAME,
        sorting: true,
        cellStyle: {
          minWidth: 150,
        },
        defaultSort: orderBy === DvOptimizationListColumn.OPTIMIZATION_NAME ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.optimizationName !== null) {
            return (
              <div id={`optimizationName-${rowData.optimizationId}`} className={`optimizationName`}>
                <CustomTooltip
                  data-testid={`optimizationNameTooltip-${rowData.optimizationId}`}
                  title={rowData.optimizationName ? rowData.optimizationName : ''}
                  placement="bottom-start"
                  onClick={handleRequestRead}
                >
                  <p
                    data-testid={`optimizationName-${rowData.optimizationId}`}
                    data-id={rowData.optimizationId.toString()}
                    style={{
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      cursor: 'pointer',
                    }}
                  >
                    {rowData.optimizationName}
                  </p>
                </CustomTooltip>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnAllocationStartDate = () => {
      return {
        title: t('optimization.label.list.bidStartDate'),
        field: DvOptimizationListColumn.ALLOCATION_START_DATE,
        defaultSort: orderBy === DvOptimizationListColumn.ALLOCATION_START_DATE ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 120,
        },
        render: (rowData) => {
          if (rowData.allocationStartDate !== null) {
            return (
              <div id={`allocationStartDate-${rowData.optimizationId}`} className={`allocationStartDate`}>
                <span
                  data-testid={`allocationStartDate-${rowData.optimizationId}`}
                  className={`allocationStartDateLabel`}
                >
                  {rowData.allocationStartDate}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnAllocationEndDate = () => {
      return {
        title: t('optimization.label.list.bidEndDate'),
        field: DvOptimizationListColumn.ALLOCATION_END_DATE,
        defaultSort: orderBy === DvOptimizationListColumn.ALLOCATION_END_DATE ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 120,
        },
        render: (rowData) => {
          if (rowData.allocationEndDate !== null) {
            return (
              <div id={`allocationEndDate-${rowData.optimizationId}`} className={`allocationEndDate`}>
                <span data-testid={`allocationEndDate-${rowData.optimizationId}`} className={`allocationEndDateLabel`}>
                  {rowData.allocationEndDate === '9999.12.31' ? t('common.datePeriodPicker.unsetEndDate') : rowData.allocationEndDate}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnDailyBudget = () => {
      return {
        title: t('optimization.label.list.dailyBudget'),
        field: DvOptimizationListColumn.DAILY_BUDGET,
        defaultSort: orderBy === DvOptimizationListColumn.DAILY_BUDGET ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 150,
        },
        render: (rowData) => {
          if (rowData.dailyBudget !== null) {
            return (
              <div id={`dailyBudget-${rowData.optimizationId}`} className={`dailyBudget`}>
                <span data-testid={`dailyBudget-${rowData.optimizationId}`} className={`dailyBudgetLabel`}>
                  {rowData.dailyBudget.toLocaleString('ko-KR')}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnOptimizationGoal = () => {
      return {
        title: t('optimization.label.list.optimizationGoal'),
        field: DvOptimizationListColumn.OPTIMIZATION_GOAL,
        defaultSort: orderBy === DvOptimizationListColumn.OPTIMIZATION_GOAL ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 150,
        },
        render: (rowData) => {
          if (rowData.optimizationGoal !== null) {
            return (
              <div id={`optimizationGoal-${rowData.optimizationId}`} className={`optimizationGoal`}>
                <span data-testid={`optimizationGoal-${rowData.optimizationId}`} className={`optimizationGoalLabel`}>
                  {getOptimizationGoalName(rowData.optimizationGoal)}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnCreatedDateTime = () => {
      return {
        title: t('optimization.label.list.createdDateTime'),
        field: DvOptimizationListColumn.CREATED_DATETIME,
        defaultSort: orderBy === DvOptimizationListColumn.CREATED_DATETIME ? sorting?.toLowerCase() : undefined,
        cellStyle: {
          width: 160,
        },
        render: (rowData) => {
          if (rowData.createdDateTime !== null) {
            return (
              <div id={`createdDateTime-${rowData.optimizationId}`} className={'createdDateTime'}>
                <span data-testid={`createdDateTime-${rowData.optimizationId}`} className={'createdDateTimeLabel'}>
                  {rowData.createdDateTime}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnOptimizationResult = () => {
      return {
        title: t(`optimization.label.list.optimizationResult`),
        field: DvOptimizationListColumn.OPTIMIZATION_RESULT,
        sorting: false,
        cellStyle: {
          width: 130,
        },
        render: (rowData) => {
          if (rowData.optimizationName !== null) {
            return (
              <div id={`optimizationName-${rowData.optimizationId}`} className="optimization-result">
                {rowData.status === StatusType.INSPECTION_ERROR || rowData.status === StatusType.INSPECTING ? (
                  <></>
                ) : (
                  <ResultIcon
                    className="optimization-result-button"
                    data-testid={`optimizationResultButton-${rowData.optimizationId}`}
                    onClick={() => handleRequestOptimizationResult(rowData.optimizationId)}
                  />
                )}
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnOptimizationId = () => {
      return {
        title: t('optimization.label.list.optimizationId'),
        field: DvOptimizationListColumn.OPTIMIZATION_ID,
        sorting: true,
        cellStyle: {
          width: 120,
        },
        defaultSort: orderBy === DvOptimizationListColumn.OPTIMIZATION_ID ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.status) {
            return (
              <div id={`status-${rowData.optimizationId}`} className={`optimization-id`}>
                <span data-testid={`mediaType-${rowData.optimizationId}`} className={`optimization-id`}>
                  {rowData.optimizationId}
                </span>
              </div>
            );
          }
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const getColumnContextMenu = () => {
      return {
        field: DvOptimizationListColumn.CONTEXT_MENU,
        sorting: false,
        cellStyle: {
          width: 130,
        },
        render: (rowData) => {
          return (
            <div className="delete-modify-icons">
              <span className="icon">
                <EditIcon
                  onClick={handleRequestEdit}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`edit-${rowData.optimizationId}`}
                />
              </span>
              <span className="icon">
                <DeleteIcon
                  onClick={handleRequestDelete}
                  data-id={rowData.optimizationId.toString()}
                  data-testid={`delete-${rowData.optimizationId}`}
                />
              </span>
            </div>
          );
        },
      } as FixedLayoutColumn<DvOptimizationInfo>;
    };

    const columns: FixedLayoutColumn<DvOptimizationInfo>[] = [
      getColumnAllocationYn(),
      getColumnStatus(),
      getColumnOptimizationResult(),
      getColumnOptimizationId(),
      getColumnOptimizationName(),
      getColumnAllocationStartDate(),
      getColumnAllocationEndDate(),
      getColumnDailyBudget(),
      getColumnOptimizationGoal(),
      getColumnCreatedDateTime(),
    ];

    if (checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)) {
      columns.push(getColumnContextMenu());
    }

    return columns;
  };
}
