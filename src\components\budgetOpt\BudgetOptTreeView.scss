#BudgetOptTreeView {
  .flex-container {
    display: flex;
    gap: 24px;
    .tree {
      flex: 1;
    }
    .sidebar {
      width: 340px;
    }
  }

  .tree {
    border-top: 2px solid var(--color-blue-darker);
    border-bottom: 2px solid var(--color-blue-darker);
    background: #fff;

    .treeHeader {
      position: relative;
      display: flex;
      width: 100%;
      height: 49px;
      color: var(--text-base);
      font-size: 14px;
      font-weight: 700;
      padding-right: 48px;
      background-color: var(--bg-table-main);
      .toggleWrapper {
        width: 70px;
        border: 0 none;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        svg {
          width: 30px;
          height: 30px;
          stroke: #f2f3f6;
          color: var(--point_color);
          &.MuiSvgIcon-colorDisabled {
            border: 1px solid #b3b8c5;
            border-radius: 50%;
          }
        }
      }
      .labelName {
        flex: 4;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .labelAdType {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .labelStatus {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .download {
        position: absolute;
        top: 0;
        right: 0;
        width: 49px;
        height: 49px;
        border: 0 none;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--color-active-blue);
        cursor: pointer;
        svg {
          stroke: #fff;
          width: 26px;
          height: 26px;
        }
      }
    }

    .treeViewRoot {
      min-height: 500px;
      max-height: 900px;
      overflow-y: auto;
      &::-webkit-scrollbar-thumb {
        background-color: var(--point_color);
      }
      & > .MuiTreeItem-root:first-child > .MuiTreeItem-content:first-child {
        border-top: 0 none;
      }
      .MuiTreeItem-content {
        padding-left: 0;
      }
      .treeItemType-campaign.hidden {
        display: none;
      }
      .treeItemType-mediaType > .MuiTreeItem-content {
        & > .MuiTreeItem-iconContainer {
          margin: 0 20px;
        }
      }
      .treeItemType-accountId > .MuiTreeItem-content {
        & > .MuiTreeItem-iconContainer {
          margin: 0 9px;
        }
        &::before {
          content: '';
          width: 70px;
          min-width: 70px;
        }
      }
      .treeItemType-campaign > .MuiTreeItem-content::before {
        content: '';
        width: 100px;
        min-width: 100px;
      }
    }

    .treeItemContent {
      width: 100%;
      display: flex;
      font-size: 14px;
      .labelName {
        flex: 4;
        display: flex;
        gap: 24px;
        justify-content: flex-start;
        align-items: center;
        min-width: 0;
        word-break: break-all;
        &.notPredicted {
          color: #b51b32;
        }
        .columns {
          display: flex;
          flex-flow: row;
          align-items: center;
          .media-icon-box {
            margin: 0 9px;
          }
          .mediaName {
            padding-left: 14px;
          }
        }
        .rows {
          display: flex;
          flex-flow: column;
          padding-left: 14px;
        }
        .new {
          display: inline-block;
          position: relative;
          height: 16px;
          width: 36px;
          margin-left: 10px;
          &::after {
            content: 'NEW';
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            height: 16px;
            width: 36px;
            font-size: 8px;
            font-weight: 700;
            color: white;
            background-color: var(--color-active-blue);
            border-radius: 8px;
            padding-bottom: 1px;
          }
        }
      }
      .labelAdType {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        b.notPredicted {
          color: #b51b32;
        }
      }
      .labelStatus {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        b.notPredicted {
          color: #b51b32;
        }
      }
    }
  }

  .sidebar {
    .searchInput {
      background-color: #fff;
      border: 1px solid #bbbdcd;
      border-radius: 22.5px;
      color: var(--point_color);
      font-size: 14px;
      font-weight: 300;
      height: 35px;
      padding-right: 12px;
      width: 100%;

      input {
        box-sizing: border-box;
        color: var(--point_color);
        font-size: 14px;
        font-weight: 300;
      }
      svg {
        width: 22px;
        height: 22px;
      }
      fieldset {
        border: none;
      }
    }

    .counter {
      width: 100%;
      margin-top: 56px;
      .MuiInput-underline.Mui-disabled:before {
        border-bottom-style: solid;
      }
      svg {
        width: 32px;
        height: 32px;
      }
      input {
        color: var(--point_color);
        font-size: 24px;
        font-weight: 700;
        text-align: center;
      }
      .endAdornment {
        color: var(--point_color);
        font-size: 12px;
        font-weight: 700;
      }
    }

    .info {
      margin-top: 40px;
      font-size: 11px;
      font-weight: 300;
      color: var(--point_color);

      .marker {
        display: flex;
        margin-bottom: 5px;

        .select-mark-blue,
        .select-mark-gray,
        .select-mark-red {
          display: inline-block;
          width: 36px;
          height: 4px;
          margin-top: 7px;
          margin-right: 4px;
        }

        .select-mark-blue {
          background-color: var(--color-blue-darker);
        }

        .select-mark-gray {
          background-color: #909090;
        }

        .select-mark-red {
          background-color: #b51b32;
        }

        .select-mark-new {
          display: inline-block;
          position: relative;
          height: 16px;
          width: 36px;
          margin-right: 4px;
          &::after {
            content: 'NEW';
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            height: 16px;
            width: 36px;
            font-size: 8px;
            font-weight: 700;
            color: white;
            background-color: var(--color-active-blue);
            border-radius: 8px;
          }
        }
      }

      .indent {
        margin-bottom: 4px;
        text-indent: -12px;
        padding-left: 18px;
      }
    }
  }

  .tooltipContents {
    display: flex;
    align-items: center;
    svg {
      margin-right: 4px;
    }
  }
}
