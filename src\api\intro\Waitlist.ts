/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import { WaitlistRequest } from '@models/intro'
import CommonResponse from '@models/common/CommonResponse'

export const waitlist = async (bodyParams: WaitlistRequest, isLoading = true) => {
  const response: CommonResponse<WaitlistRequest> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/waitlist',
    method: Method.POST,
    params: {
      bodyParams: {
        ...bodyParams,
      }
    },
    config: {
      isLoading: isLoading
    }
  })
  return response
}