import React from 'react'
import { MopIcon } from '../icon';
import { MOPIcon } from '@models/common';
import './CheckBox.scss';

interface CheckBoxProps {
    isChecked: boolean;
    label: string | JSX.Element;
    onClick: () => void;
    disabled: boolean;
}

const CheckBox = (props: CheckBoxProps) => {
    const { label, isChecked, onClick, disabled=false } = props;

    return (
        <label className='checkbox-label' onClick={() => {
            if (!disabled) onClick()
        }}>
            {
                isChecked
                ?
                    <MopIcon size={16.5} name={MOPIcon.BOX_CHECKED2} />
                :   
                    <div className='un-checked'>
                    </div>
            }
            {label}
        </label>
    );
}

export default CheckBox;