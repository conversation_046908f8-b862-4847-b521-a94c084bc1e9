// src/components/common/mopUI/DualSwitchButton.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import DualSwitchButton, { DualSwitchButtonProps } from './DualSwitchButton'

const meta: Meta<typeof DualSwitchButton> = {
  title: 'Components/Common/MopUI/DualSwitchButton',
  component: DualSwitchButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
A dual switch button component that allows switching between two states.

**Styling Options:**
1. **Custom Classes**: Complete custom styling with selectionStyles prop
2. **Individual Button Classes**: Individual customization with firstButtonClassName, secondButtonClassName props
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    firstOption: {
      control: 'text',
      description: 'Text for the first option',
    },
    secondOption: {
      control: 'text',
      description: 'Text for the second option',
    },
    isFirstSelected: {
      control: 'boolean',
      description: 'Currently selected option (true: first, false: second)',
    },
    onChange: {
      action: 'changed',
      description: 'Callback function called when selection changes. Receives the new isFirstSelected boolean value.',
    },
    selectionStyles: {
      control: 'object',
      description: 'Custom style classes for different selection states. Each property accepts CSS class strings.',
      table: {
        type: { 
          summary: '{ firstSelected?: string, secondSelected?: string, unselected?: string }',
          detail: `Object with optional properties:
• firstSelected: CSS classes applied when first option is selected
• secondSelected: CSS classes applied when second option is selected  
• unselected: CSS classes applied to non-selected options`
        },
        defaultValue: { 
          summary: 'undefined',
          detail: 'Uses built-in default styles when not provided' 
        }
      }
    },
    firstButtonClassName: {
      control: 'text',
      description: 'Additional CSS classes for the first button',
    },
    secondButtonClassName: {
      control: 'text',
      description: 'Additional CSS classes for the second button',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// Interactive wrapper component for stories
const InteractiveWrapper = (args: { [x: string]: any }) => {
  const [isFirstSelected, setIsFirstSelected] = useState(args.isFirstSelected)

  return (
    <DualSwitchButton
      {...(args as DualSwitchButtonProps)}
      isFirstSelected={isFirstSelected}
      onChange={setIsFirstSelected}
    />
  )
}

// Internal interactive wrapper for internal use (when onChange prop exists)
const InternalInteractiveWrapper: React.FC<DualSwitchButtonProps> = (props) => {
  const [isFirstSelected, setIsFirstSelected] = useState(props.isFirstSelected)

  return (
    <DualSwitchButton
      {...props}
      isFirstSelected={isFirstSelected}
      onChange={setIsFirstSelected}
    />
  )
}

// Default story
export const Default: Story = {
  render: InteractiveWrapper,
  args: {
    firstOption: 'Conservative',
    secondOption: 'Aggressive',
    isFirstSelected: true,
  },
  parameters: {
    docs: {
      source: {
        code: `const [value, setValue] = useState(true);

<DualSwitchButton
  firstOption="Conservative"
  secondOption="Aggressive"
  isFirstSelected={value}
  onChange={(newValue) => setValue(newValue)}
/>`
      }
    }
  }
}

// Usage example with actual implementation code
export const UsageExample: Story = {
  render: () => {
    const [value, setValue] = useState(true);

    return (
      <div className="space-y-4">
        <div className="text-sm text-gray-600">
          <strong>Implementation:</strong>
          <pre className="bg-gray-100 p-3 rounded mt-2 text-xs">
{`const [value, setValue] = useState(true);

<DualSwitchButton
  firstOption="Conservative"
  secondOption="Aggressive" 
  isFirstSelected={value}
  onChange={(newValue) => setValue(newValue)}
/>`}
          </pre>
        </div>
        <div className="pt-2">
          <strong>Result:</strong>
          <div className="mt-2">
            <DualSwitchButton
              firstOption="Conservative"
              secondOption="Aggressive"
              isFirstSelected={value}
              onChange={(newValue) => setValue(newValue)}
            />
          </div>
          <div className="text-sm text-gray-500 mt-2">
            Current value: <code>{value.toString()}</code>
          </div>
        </div>
      </div>
    );
  },
}


// Custom selection styles usage
export const CustomSelectionStyles: Story = {
  render: InteractiveWrapper,
  args: {
    firstOption: 'Conservative',
    secondOption: 'Aggressive',
    isFirstSelected: true,
    selectionStyles: {
      firstSelected: 'text-[#5e81f4] bg-[#d5dfff]',
      secondSelected: 'text-[#eb424c] bg-[#ffe1e3]',
      unselected: 'text-[#969696] bg-gray-50 border-gray-300 hover:bg-gray-100',
    },
  },
  parameters: {
    docs: {
      source: {
        code: `const [value, setValue] = useState(true);

<DualSwitchButton
  firstOption="Conservative"
  secondOption="Aggressive"
  isFirstSelected={value}
  onChange={(newValue) => setValue(newValue)}
  selectionStyles={{
    firstSelected: 'text-[#5e81f4] bg-[#d5dfff]',
    secondSelected: 'text-[#eb424c] bg-[#ffe1e3]',
    unselected: 'text-[#969696] bg-gray-50 border-gray-300 hover:bg-gray-100',
  }}
/>`
      }
    }
  }
}


// Disabled state
export const Disabled: Story = {
  render: InteractiveWrapper,
  args: {
    firstOption: 'Conservative',
    secondOption: 'Aggressive',
    isFirstSelected: true,
    disabled: true,
  },
  parameters: {
    docs: {
      source: {
        code: `const [value, setValue] = useState(true);

<DualSwitchButton
  firstOption="Conservative"
  secondOption="Aggressive"
  isFirstSelected={value}
  onChange={(newValue) => setValue(newValue)}
  disabled={true}
/>`
      }
    }
  }
}

// Different text example
export const DifferentText: Story = {
  render: InteractiveWrapper,
  args: {
    firstOption: 'Yes',
    secondOption: 'No',
    isFirstSelected: false,
  },
}


// Advanced customization example
export const AdvancedCustomization: Story = {
  render: InteractiveWrapper,
  args: {
    firstOption: '🛡️ Safe',
    secondOption: '🚀 Risk',
    isFirstSelected: true,
    selectionStyles: {
      firstSelected: 'text-green-700 bg-gradient-to-r from-green-50 to-green-100 border-green-300 shadow-green-200/50',
      secondSelected: 'text-red-700 bg-gradient-to-r from-red-50 to-red-100 border-red-300 shadow-red-200/50',
      unselected: 'text-[#969696] bg-gradient-to-r from-gray-50 to-white border-gray-200 hover:from-gray-100 hover:to-gray-50',
    },
    firstButtonClassName: 'font-semibold tracking-wide',
    secondButtonClassName: 'font-semibold tracking-wide',
  },
}
