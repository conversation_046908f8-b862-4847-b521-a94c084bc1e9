import CommonResponse from '@models/common/CommonResponse';
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import { DashboardSetting } from '@models/setting/DashboardSetting';

export const updateDashboardSetting = async (settings: DashboardSetting[], isLoading = true): Promise<boolean> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/setting/dashboard',
    method: Method.POST,
    params: {
      bodyParams: settings,
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y';
};
