/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import { GetBudgetResponse } from '@models/budgetAnalysis/Budget'
import CommonResponse from '@models/common/CommonResponse'

export const getBudget = async (advertiserId: number, platformType: string) => {
  const response: CommonResponse<GetBudgetResponse> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/insight/budget/${advertiserId}/${platformType}`,
    method: Method.GET
  })

  return response.successOrNot === 'Y' ? response.data : null
}
