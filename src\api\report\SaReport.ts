/* istanbul ignore file */

import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import { AxiosResponse } from 'axios';
import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import {
  GetReportDetailResponse,
  GetReportQueryParam,
  GetReportTableQueryParam,
  GetReportTableResponse,
  GetReportSummaryResponse,
} from '@models/report/SearchReport';
import { Report } from '@models/common/Report';
import { SaMedia, SaConfigure, UpdateSaConfigurationsRequest } from '@models/report/Settings';

/* istanbul ignore next */
export const getSaAdgroups = async (advertiserId: number, isLoading = true): Promise<SaMedia[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/report/sa/adgroups',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data.media : []) as SaMedia[];
};

/* istanbul ignore next */
export const getSaConfiguration = async (reportId: string, isLoading = true): Promise<SaConfigure> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/sa/${reportId}/configuration`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as SaConfigure;
};

/* istanbul ignore next */
export const updateSaConfiguration = async (
  reportId: string,
  param: UpdateSaConfigurationsRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/report/sa/${reportId}/configuration`,
    method: Method.PATCH,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

/* istanbul ignore next */
export const downloadSaRawData = async (reportId: string, param: GetReportTableQueryParam, isLoading = true) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/report/sa/${reportId}/raw-data`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
    params: {
      queryParams: {
        ...param,
      },
    },
  });

  if (response.data) {
    openDownloadLink(response);

    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};

/* istanbul ignore next */
export const getSaReportDetail = async (
  reportId: string,
  param: GetReportQueryParam,
  isLoading = true
): Promise<GetReportDetailResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/sa/${reportId}/detail`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetReportDetailResponse;
};

/* istanbul ignore next */
export const getSaReportSummary = async (
  reportId: string,
  param: GetReportQueryParam,
  isLoading = true
): Promise<GetReportSummaryResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/sa/${reportId}/summary`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetReportSummaryResponse;
};

/* istanbul ignore next */
export const getSaReports = async (advertiserId: number, isLoading = true): Promise<Report[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/report/sa',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as Report[];
};

export const getSaReportTable = async (
  reportId: string,
  param: GetReportTableQueryParam,
  isLoading = true
): Promise<GetReportTableResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/sa/${reportId}/table`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetReportTableResponse;
};
