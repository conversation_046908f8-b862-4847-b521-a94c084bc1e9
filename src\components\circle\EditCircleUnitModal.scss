.edit-circle-unit-modal {
  .MuiPaper-root.MuiDialog-paper {
    border-radius: 0;
    max-width: unset;
    width: 500px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .rounded-dropdown .MuiInputBase-root.MuiInput-root .MuiSelect-select.MuiSelect-select {
    height: 32px;
    display: flex;
    align-items: center;
  }

  &__header {
    background-color: var(--bg-gray-light);
    position: relative;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    color: var(--point_color);

    .mop-icon-box.icon-close {
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }

  &__body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__footer .mop-action-button {
    display: block;
    margin: 0;
    margin-left: auto;
  }
}
