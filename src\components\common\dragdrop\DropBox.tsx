import type { ReactNode } from 'react'
import { memo } from 'react'
import { useDrop } from 'react-dnd'

interface Props {
  accepts: string[]
  onDrop: (arg: any) => void
  className?: string
  children: ReactNode
}

const DropBox = memo(function DropBox({
  children, accepts: accept, onDrop,
  className = 'mop-drop-box'
}: Props) {
  const [{ isOver, canDrop }, drop] = useDrop(
    () => ({
      accept,
      collect: (monitor) => ({
        isOver: monitor.isOver(),
        canDrop: monitor.canDrop(),
      }),
      drop: (item: unknown) => onDrop(item),
    }),
    [accept],
  )

  const isActive = isOver && canDrop

  return (
    <div
      ref={drop}
      className={`
        ${className}
        ${ isActive ? `${className}--dropover`: ''}
        ${ canDrop ? `${className}--can-drop` : ''}
      `.trim()}
    >
      { children }
    </div>
  )
})

export default DropBox