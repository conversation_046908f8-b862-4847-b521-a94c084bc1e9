#ChartLegend {
  display: flex;
  justify-content: center;
  flex-direction: column;

  &.flex-row {
    position: relative;
    flex-direction: row;
    gap: 20px;

    .chart-legend-row {
      width: auto;
      + .chart-legend-row {
        margin-top: 0px;
      }
    }
    .chart-legend-more {
      position: absolute;
      top: 16px;
      .legend-list {
        overflow-y: auto;
        flex-direction: row;
        display: flex;
        padding: 10px 20px;
      }
    }
  }

  #ExpandLegendAll {
    border-top: 1px solid #707070;
    display: inline-flex;
    width: 100%;
    box-sizing: border-box;
    margin-top: 20px;
    justify-content: center;
    align-items: flex-start;
    height: 30px;
    .MuiIconButton-root {
      padding: 0px;
      margin-top: 15px;
      svg {
        height: 5px;
      }
    }
  }

  .chart-legend-more {
    margin-top: 16px;
    z-index: 5;
  }
}
#ChartLegendAllDialog {
  .MuiPaper-root {
    padding: 10px 30px;
    width: 250px;
    display: flex;

    align-items: center;
    .MuiDialogTitle-root {
      display: flex;
      width: 100%;
      box-sizing: border-box;
      padding: 16px 0px;
      border-bottom: 1px solid #dbdbdb;
      .MuiTypography-root {
        font-size: 12px;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .MuiButtonBase-root {
        padding: 0px;
      }
    }
    .MuiDialogContent-root {
      width: 100%;
      box-sizing: border-box;
      padding: 30px 20px;
      .chart-legend-row {
        margin-bottom: 8px;
      }
    }
  }
}
