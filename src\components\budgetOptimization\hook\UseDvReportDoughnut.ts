import { ColorSetBudget } from '@models/common/ChartColors';
import { GeneralChartData, ChartLegendData } from '@models/common/ChartData';
import { generateColor } from '@utils/ColorUtil';

interface Props {
  report?: any;
}

const useDvReportDoughnut = ({
  report,
}: Props): {
  mediaChartData: GeneralChartData;
  mediaLegendData: ChartLegendData[];
} => {
  const targetDataType = 'budgetRate';

  const mediaChartData: GeneralChartData = {
    labels: [],
    datasets: [{ data: [], backgroundColor: [] }],
  };
  const mediaLegendData: ChartLegendData[] = [];

  const media = report?.media;

  const mediaEtcIndex = media?.findIndex((item: any) => item.mediaType.toUpperCase() === 'ETC') ?? -1;
  if (mediaEtcIndex >= 0) {
    media?.push(media?.splice(mediaEtcIndex, 1)[0]);
  }

  media?.forEach((target: any, idx: number) => {
    const tmpLegendData: ChartLegendData = { backgroundColor: '', label: '', value: '' };
    const color = idx < ColorSetBudget.length ? ColorSetBudget[idx] : generateColor();
    const label = `${target.mediaType}`;

    mediaChartData.labels.push(label);
    mediaChartData.datasets[0].backgroundColor!.push(color);
    mediaChartData.datasets[0].data.push(target[targetDataType].toFixed(6));

    tmpLegendData.value = target[targetDataType].toFixed(2);
    tmpLegendData.label = label;
    tmpLegendData.backgroundColor = color;

    mediaLegendData.push(tmpLegendData);
  });

  return { mediaChartData, mediaLegendData };
};

export default useDvReportDoughnut;
