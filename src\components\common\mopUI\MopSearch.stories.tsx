// src/components/common/mopUI/MopSearch.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import MopSearch from './MopSearch';
import { action } from '@storybook/addon-actions';

const meta: StorybookMeta<typeof MopSearch> = {
  title: 'Components/Common/MopUI/MopSearch',
  component: MopSearch,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    id: {
      control: 'text',
      description: 'Unique identifier for the search input',
    },
    value: {
      control: 'text',
      description: 'Current value of the search input',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text shown when input is empty',
    },
    onChange: {
      action: 'onChange',
      description: 'Callback function triggered when input value changes',
    },
    onFocus: {
      action: 'onFocus',
      description: 'Callback function triggered when input receives focus',
    },
    onSearch: {
      action: 'onSearch',
      description: 'Callback function triggered when search is performed (Enter key or icon click)',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state of the search input',
    },
    visibleIcon: {
      control: 'boolean',
      description: 'Whether to show the search icon',
    },
    size: {
      control: 'number',
      description: 'Size of the search icon (when visible)',
    },
    type: {
      control: 'text',
      description: 'CSS class type for styling variations',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    id: 'default-search',
    value: '',
    placeholder: '검색어를 입력하세요...',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    visibleIcon: true,
    size: 20,
  },
  parameters: {
    docs: {
      description: {
        story: 'Basic MopSearch component. Displays a search icon and allows search execution via Enter key or icon click. Width is 100% of the parent layout.',
      },
    },
  },
};

// Without search icon
export const WithoutIcon: Story = {
  args: {
    id: 'no-icon-search',
    value: '',
    placeholder: '검색 아이콘 없이 사용',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    visibleIcon: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSearch component without search icon. Search can only be performed via Enter key.',
      },
    },
  },
};

// With custom type styling
export const WithCustomType: Story = {
  args: {
    id: 'custom-type-search',
    value: '',
    placeholder: '커스텀 타입 스타일링',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    visibleIcon: true,
    size: 24,
    type: 'custom-style',
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSearch component with custom CSS class applied using the type prop.',
      },
    },
  },
};

// Disabled state
export const Disabled: Story = {
  args: {
    id: 'disabled-search',
    value: '검색할 수 없는 상태',
    placeholder: '비활성화된 검색',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    disabled: true,
    visibleIcon: true,
    size: 20,
  },
  parameters: {
    docs: {
      description: {
        story: 'Disabled MopSearch component. User interaction is not possible.',
      },
    },
  },
};

// With pre-filled value
export const WithValue: Story = {
  args: {
    id: 'with-value-search',
    value: '기존 검색어',
    placeholder: '검색어를 입력하세요',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    visibleIcon: true,
    size: 20,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSearch component with pre-filled initial value.',
      },
    },
  },
};

// Small icon size
export const SmallIcon: Story = {
  args: {
    id: 'small-icon-search',
    value: '',
    placeholder: '작은 검색 아이콘 (16px)',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    visibleIcon: true,
    size: 16,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSearch component with small-sized (16px) search icon.',
      },
    },
  },
};

// Large icon size
export const LargeIcon: Story = {
  args: {
    id: 'large-icon-search',
    value: '',
    placeholder: '큰 검색 아이콘 (24px)',
    onChange: action('onChange'),
    onFocus: action('onFocus'),
    onSearch: action('onSearch'),
    visibleIcon: true,
    size: 24,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSearch component with large-sized (24px) search icon.',
      },
    },
  },
};

export const RawDataSearch: Story = {
    args: {
      id: 'raw-data-search',
      value: '',
      placeholder: '전체 콘텐츠에서 검색',
      onChange: action('onChange'),
      onFocus: action('onFocus'),
      onSearch: action('onSearch'),
      visibleIcon: true,
      size: 20,
      type: 'raw-data',
    },
    parameters: {
      docs: {
        description: {
          story: 'Search component used for large-scale reports.',
        },
      },
    },
  };

export const CommerceSearch: Story = {
    args: {
        id: 'commerce-search',
        value: '',
        placeholder: '전체 콘텐츠에서 검색',
        onChange: action('onChange'),
        onFocus: action('onFocus'),
        onSearch: action('onSearch'),
        visibleIcon: true,
        size: 16,
        type: 'commerce',
    },
    parameters: {
        docs: {
        description: {
            story: 'Search component used for creating shopping search advertising campaigns.',
        },
        },
    },
    };
