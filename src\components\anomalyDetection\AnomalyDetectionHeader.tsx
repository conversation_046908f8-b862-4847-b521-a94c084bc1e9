import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@material-ui/core'
import { ReactComponent as DownloadIcon } from '@components/assets/images/icon_download.svg';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';

import { ReactComponent as IconNormal } from '@components/assets/images/icon_utm_normal.svg';
import { ReactComponent as IconNotExist } from '@components/assets/images/icon_utm_not_exist.svg';
import { ReactComponent as IconNotMatch } from '@components/assets/images/icon_utm_not_match.svg';

import AdviceTooltip from '@components/common/AdviceTooltip';
import InnerHtml from '@components/common/InnerHtml';
import { numberWithCommas } from '@utils/FormatUtil'

import './AnomalyDetectionHeader.scss'
import './DetectionHeader.scss'

interface Props {
  isEmpty?: boolean;
  anomalyCase: number
  title: string
  tooltipId?: string;
  tooltipHtml?: string
  downloadFn?: React.MouseEventHandler<HTMLButtonElement>;
}
const AnomalyDetectionHeader: React.FC<Props> = (props: Props): ReactElement => {
  const { t } = useTranslation()
  return (
    <div className="detection-header">
      <div className="detection-header__title-container">
        <h1 className="detection-header__title">{props.title}</h1>
        {(props.tooltipId && props.tooltipHtml) && (
          <AdviceTooltip
            id={`anomaly-detection-advice-tooltip-${props.tooltipId}`}
            title={<InnerHtml innerHTML={props.tooltipHtml} />}
            placement="bottom-start"
            arrow
          ><AdviceMarkIcon /></AdviceTooltip>
        )}
        <div className="detection-header__anomaly-case">
          <span
            className={`detection-header__anomaly-num ${!props.isEmpty ? 'detection-header__anomaly-num--color': ''}`}
          >{ numberWithCommas(props.anomalyCase) }</span>
          <span>{t('anomalyDetection.label.case')}</span>
        </div>
      </div>
      {!props.isEmpty && (
        <>
          {props.tooltipId === 'utm' && (
            <div className="detection-header__status-icons">
              <IconNormal /><span>{t('anomalyDetection.label.status.normal')}</span>
              <IconNotMatch /><span>{t('anomalyDetection.label.status.notMatch')}</span>
              <IconNotExist /><span>{t('anomalyDetection.label.status.notExitst')}</span>
            </div>
          )}
          <Button
            className="detection-header__download-button"
            disableRipple
            startIcon={<DownloadIcon />}
            onClick={props.downloadFn}
          >{t('anomalyDetection.label.button.download')}</Button>
        </>
      )}
    </div>
  )
}

export default AnomalyDetectionHeader;