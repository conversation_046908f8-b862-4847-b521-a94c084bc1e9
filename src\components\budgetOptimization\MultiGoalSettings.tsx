import React, { ReactElement, useEffect, useState } from 'react';
import { Checkbox, Table, TableContainer, TableRow, TableCell, TableBody, FormControlLabel } from '@material-ui/core';

import { t } from 'i18next';
import { DvKpiType } from '@models/optimization/Kpi';
import { cloneDeep } from 'lodash';
import './MultiGoalSettings.scss';
import { ActionType } from '@models/common/CommonConstants';

export interface Props {
  kpis?: DvKpiType[];
  setKpis: (kpis: DvKpiType[]) => void;
  modalType: ActionType;
  notYetStarted: boolean;
}

type SettingChecked = {
  [key in DvKpiType]?: boolean;
};

const MultiGoalSettings: React.FC<Props> = ({ kpis, setKpis, modalType, notYetStarted }: Props): ReactElement => {
  const [settingChecked, setSettingChecked] = useState<SettingChecked>({
    [DvKpiType.IMPRESSIONS]: false,
    [DvKpiType.VIEWS]: false,
    [DvKpiType.CLICKS]: false,
    [DvKpiType.REVENUE]: false,
    [DvKpiType.CONVERSIONS]: false,
  });

  const disabledKpis = !notYetStarted && modalType != ActionType.CREATE;

  const addKpi = (kpiType: DvKpiType, targetKpis?: DvKpiType[]) => {
    const index = targetKpis?.indexOf(kpiType) ?? -1;

    if (index === -1) {
      targetKpis?.push(kpiType);
    }
  };

  const removeKpi = (kpiType: DvKpiType, targetKpis?: DvKpiType[]) => {
    const index = targetKpis?.indexOf(kpiType) ?? -1;

    if (index !== -1) {
      targetKpis?.splice(index, 1);
    }
  };

  const handleCheckKpiChange = (type: DvKpiType, checked: boolean) => {
    const newKpis = cloneDeep(kpis);

    if (checked) {
      addKpi(type, newKpis);
    } else {
      removeKpi(type, newKpis);
    }

    setKpis(newKpis || []);

    setSettingChecked({ ...settingChecked, [type]: checked });
  };

  useEffect(() => {
    const newSettingChecked = cloneDeep(settingChecked);
    Object.keys(newSettingChecked).forEach((key) => {
      newSettingChecked[(key as unknown) as DvKpiType] = false;
    });

    kpis?.forEach((kpi) => {
      if (Object.hasOwnProperty.call(newSettingChecked, kpi)) {
        newSettingChecked[kpi] = true;
      }
    });
    setSettingChecked(newSettingChecked);
  }, []) //eslint-disable-line

  return (
    <TableContainer id="dv-multi-goal-settings">
      <Table aria-label="simple table" size="small">
        <TableBody>
          <TableRow>
            <TableCell scope="row">
              <FormControlLabel
                className="kpi-label"
                control={
                  <Checkbox
                    disabled={disabledKpis}
                    checked={settingChecked[DvKpiType.IMPRESSIONS]}
                    onChange={(event) => handleCheckKpiChange(DvKpiType.IMPRESSIONS, event.target.checked)}
                  />
                }
                label={t('common.code.kpis.IMPRESSIONS')}
              />
            </TableCell>
            <TableCell scope="row">
              <FormControlLabel
                className="kpi-label"
                control={
                  <Checkbox
                    disabled={disabledKpis}
                    checked={settingChecked[DvKpiType.VIEWS]}
                    onChange={(event) => handleCheckKpiChange(DvKpiType.VIEWS, event.target.checked)}
                  />
                }
                label={t('common.code.kpis.VIEWS')}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell scope="row">
              <FormControlLabel
                className="kpi-label"
                control={
                  <Checkbox
                    disabled={disabledKpis}
                    checked={settingChecked[DvKpiType.CLICKS]}
                    onChange={(event) => handleCheckKpiChange(DvKpiType.CLICKS, event.target.checked)}
                  />
                }
                label={t('common.code.kpis.CLICKS')}
              />
            </TableCell>
            <TableCell scope="row">
              <FormControlLabel
                className="kpi-label"
                control={
                  <Checkbox
                    disabled={disabledKpis}
                    checked={settingChecked[DvKpiType.REVENUE]}
                    onChange={(event) => handleCheckKpiChange(DvKpiType.REVENUE, event.target.checked)}
                  />
                }
                label={t('common.code.kpis.REVENUE')}
              />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell scope="row">
              <FormControlLabel
                className="kpi-label"
                control={
                  <Checkbox
                    disabled={disabledKpis}
                    checked={settingChecked[DvKpiType.CONVERSIONS]}
                    onChange={(event) => handleCheckKpiChange(DvKpiType.CONVERSIONS, event.target.checked)}
                  />
                }
                label={t('common.code.kpis.CONVERSIONS')}
              />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default MultiGoalSettings;
