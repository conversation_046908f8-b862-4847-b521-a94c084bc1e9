import React, { ReactElement, useEffect, useState } from 'react';
import './DvOptimizationListFilter.scss';
import { useTranslation } from 'react-i18next';
import { Box, FormLabel, Grid, MenuItem, OutlinedInput } from '@material-ui/core';
import { getBidYnCodes, getDvOptimizationGoalCodes, getDvaStatusCodes } from '@utils/CodeUtil';
import { DvOptimizationListColumn, DvOptimizationsRequest } from '@models/budgetOptimization/DvOptimization';
import { format } from 'date-fns';
import {
  DateFnsFormat,
  FilterSelectType,
  pageSizeOptions,
  SearchingTextLimitLength,
} from '@models/common/CommonConstants';
import { MediaType } from '@models/common/Media';
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg';
import { useRecoilState, useRecoilValue } from 'recoil';
import { advertiserState } from '@store/Advertiser';
import { dvoListActiveFilterState } from '@store/DvOptimization';
import { object } from 'yup';
import { useToast } from "@hooks/common";
import SelectBottom from '@components/common/SelectBottom';
import DatePeriodPickerSearchPage from '@components/common/DatePeriodPickerSearchPage';
import { ExpandLess } from '@material-ui/icons';

const EMPTY_STR = '';

interface FilterType {
  status: string;
  mediaType: string;
  optimizationName: string;
  allocationStartDate: string;
  allocationEndDate: string;
  optimizationGoal: string;
  allocationYn: string;
}

const DvOptimizationListFilter: React.FC = (): ReactElement => {
  const { t } = useTranslation();
  const { openToast } = useToast()
  const [dvoListActiveFilter, setDvoListActiveFilter] = useRecoilState(dvoListActiveFilterState);
  const advertiser = useRecoilValue(advertiserState);
  const [initFlag, setInitFlag] = useState<boolean>(false);

  const yearAgoDate = new Date();
  yearAgoDate.setMonth(yearAgoDate.getMonth() - 12);
  const yearAgo = format(yearAgoDate, DateFnsFormat.DATE);

  const oneMonthAfterDate = new Date();
  oneMonthAfterDate.setMonth(oneMonthAfterDate.getMonth() + 1);
  const oneMonthAfter = format(oneMonthAfterDate, DateFnsFormat.DATE);

  const filterDefault = {
    status: FilterSelectType.ALL,
    mediaType: MediaType.NAVER,
    optimizationName: EMPTY_STR,
    allocationStartDate: yearAgo,
    allocationEndDate: oneMonthAfter,
    optimizationGoal: FilterSelectType.ALL,
    allocationYn: FilterSelectType.ALL,
  };

  const [filter, setFilter] = useState<FilterType>(filterDefault);

  const validationSchema = object()
    .test('allocationPeriodInvalid', t('optimization.message.dvOptimization.validation.periodInvalid'), (value) => {
      return value.allocationStartDate <= value.allocationEndDate;
    })
    .test('allocationPeriodOverMax', t('optimization.message.dvOptimization.validation.overPeriodMax'), (value) => {
      const startDate = Number(value.allocationStartDate);
      const twoYearsLater = startDate + 20000;

      return twoYearsLater > value.allocationEndDate;
    });

  const handleSearch = () => {
    validationSchema
      .validate(filter)
      .then((value) => {
        const newDvoListFilter = Object.assign({}, dvoListActiveFilter);
        newDvoListFilter.pageIndex = 1;

        if (filter.status !== FilterSelectType.ALL) {
          newDvoListFilter.status = filter.status;
        } else {
          delete newDvoListFilter.status;
        }

        newDvoListFilter.allocationStartDate = filter.allocationStartDate;
        newDvoListFilter.allocationEndDate = filter.allocationEndDate;

        if (filter.optimizationGoal !== FilterSelectType.ALL) {
          newDvoListFilter.optimizationGoal = filter.optimizationGoal;
        } else {
          delete newDvoListFilter.optimizationGoal;
        }

        if (filter.allocationYn !== FilterSelectType.ALL) {
          newDvoListFilter.allocationYn = filter.allocationYn;
        } else {
          delete newDvoListFilter.allocationYn;
        }

        const optimizationName = filter.optimizationName.trim();

        if (optimizationName.length > 0) {
          newDvoListFilter.optimizationName = optimizationName;
          setFilter({ ...filter, optimizationName: optimizationName });
        } else {
          delete newDvoListFilter.optimizationName;
        }
        setDvoListActiveFilter(newDvoListFilter);
      })
      .catch((err) => {
        openToast(err.message as string);
      });
  };

  const initializeFilter = () => {
    const initialFilter: DvOptimizationsRequest = {
      advertiserId: advertiser.advertiserId,
      allocationStartDate: yearAgo,
      allocationEndDate: oneMonthAfter,
      pageSize: pageSizeOptions[0],
      pageIndex: 1,
      orderBy: DvOptimizationListColumn.CREATED_DATETIME,
      sorting: 'DESC',
    };

    setDvoListActiveFilter(initialFilter);
  };

  const handleChangeAllocationYn = (event: any) => {
    const newFilter = Object.assign({}, filter);
    if ((event.target.value as string) === FilterSelectType.ALL) {
      newFilter.allocationYn = FilterSelectType.ALL;
    } else {
      newFilter.allocationYn = event.target.value as string;
    }

    setFilter(newFilter);
  };

  const handleChangeStatus = (event: any) => {
    const newFilter = Object.assign({}, filter);
    if ((event.target.value as string) === FilterSelectType.ALL) {
      newFilter.status = FilterSelectType.ALL;
    } else {
      newFilter.status = event.target.value as string;
    }

    setFilter(newFilter);
  };

  const handleChangeOptimizationGoal = (event: any) => {
    const newFilter = Object.assign({}, filter);
    if ((event.target.value as string) === FilterSelectType.ALL) {
      newFilter.optimizationGoal = FilterSelectType.ALL;
    } else {
      newFilter.optimizationGoal = event.target.value as string;
    }

    setFilter(newFilter);
  };

  const handleChangeOptimizationName = (event: any) => {
    setFilter({ ...filter, optimizationName: event.target.value as string });
  };

  const handleChangeStartDate = (date: Date | null) => {
    date && setFilter({ ...filter, allocationStartDate: format(date, DateFnsFormat.DATE) });
  };

  const handleChangeEndDate = (date: Date | null) => {
    date && setFilter({ ...filter, allocationEndDate: format(date, DateFnsFormat.DATE) });
  };

  const renderDropdownIcon = (props: any) => {
    return (
      <div
        className={`search-input-dropdown ${
          props.className.includes('MuiSelect-iconOpen') ? 'search-input-dropdown-open' : ''
        }`}
      >
        <ExpandLess />
      </div>
    );
  };

  useEffect(() => {
    initializeFilter();
    setInitFlag(true);
  }, []); //eslint-disable-line

  useEffect(() => {
    if (initFlag) {
      handleSearch();
    }
  }, [
    filter.allocationYn,
    filter.mediaType,
    filter.status,
    filter.optimizationGoal,
    filter.allocationStartDate,
    filter.allocationEndDate,
  ]); //eslint-disable-line

  return (
    <div id="dvOptimizationListFilter">
      <Grid container justifyContent="center" className="search-label-container">
        <Grid item>
          <Box className="search-label search-allocation-yn">
            <FormLabel>{t('common.label.filter.allocationYn')}</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label search-status">
            <FormLabel>{t('common.label.filter.status')}</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label search-goal">
            <FormLabel>{t('common.label.filter.optimizationGoal')}</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label search-schedule">
            <FormLabel>{t('common.label.filter.allocationPeriod')}</FormLabel>
          </Box>
        </Grid>
      </Grid>
      <Grid container className="search-input-container" justifyContent="center">
        <Grid item>
          <Box className="search-input search-allocation-yn">
            <SelectBottom
              data-testid="allocationYn"
              value={filter.allocationYn}
              onChange={handleChangeAllocationYn}
              displayEmpty
              MenuProps={{
                className: 'dva-optimization-filter-popover',
                anchorOrigin: {
                  vertical: 30,
                  horizontal: 'left',
                },
              }}
              IconComponent={(props) => renderDropdownIcon(props)}
            >
              <MenuItem data-testid={`allocationItem-${FilterSelectType.ALL}`} value={FilterSelectType.ALL}>
                {t('common.label.filter.all')}
              </MenuItem>
              {getBidYnCodes().map((allocation) => {
                return (
                  <MenuItem
                    data-testid={`allocationItem-${allocation.status}`}
                    key={allocation.status}
                    value={allocation.status}
                  >
                    {allocation.statusName}
                  </MenuItem>
                );
              })}
            </SelectBottom>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-input search-status">
            <SelectBottom
              data-testid="statusSelect"
              value={filter.status}
              onChange={handleChangeStatus}
              displayEmpty
              MenuProps={{
                className: 'dva-optimization-filter-popover',
                anchorOrigin: {
                  vertical: 30,
                  horizontal: 'left',
                },
              }}
              IconComponent={(props) => renderDropdownIcon(props)}
            >
              <MenuItem data-testid={`statusItem-${FilterSelectType.ALL}`} value={FilterSelectType.ALL}>
                {t('common.label.filter.all')}
              </MenuItem>
              {getDvaStatusCodes().map((status) => {
                return (
                  <MenuItem data-testid={`statusItem-${status.status}`} key={status.status} value={status.status}>
                    {status.statusName}
                  </MenuItem>
                );
              })}
            </SelectBottom>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-input search-goal">
            <SelectBottom
              data-testid="optimizationGoalSelect"
              value={filter.optimizationGoal}
              onChange={handleChangeOptimizationGoal}
              displayEmpty
              MenuProps={{
                className: 'dva-optimization-filter-popover',
                anchorOrigin: {
                  vertical: 30,
                  horizontal: 'left',
                },
              }}
              IconComponent={(props) => renderDropdownIcon(props)}
            >
              <MenuItem data-testid={`optimizationGoalItem-${FilterSelectType.ALL}`} value={FilterSelectType.ALL}>
                {t('common.label.filter.all')}
              </MenuItem>
              {getDvOptimizationGoalCodes().map((optimizationGoal) => {
                return (
                  <MenuItem
                    data-testid={`optimizationGoalItem-${optimizationGoal.optimizationGoal}`}
                    key={optimizationGoal.optimizationGoal}
                    value={optimizationGoal.optimizationGoal}
                  >
                    {optimizationGoal.optimizationGoalName}
                  </MenuItem>
                );
              })}
            </SelectBottom>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-input search-schedule">
            <DatePeriodPickerSearchPage
              id="dva-optimizatoin-search-period"
              disableToolbar={true}
              startDate={filter.allocationStartDate}
              endDate={filter.allocationEndDate}
              autoOk
              onClickStartDate={handleChangeStartDate}
              onClickEndDate={handleChangeEndDate}
            />
          </Box>
        </Grid>
      </Grid>
      <Box className="optimization-search-area">
        <div className="search-area">
          <OutlinedInput
            id="outlined-adornment-weight"
            data-testid="optimizationNameInput"
            value={filter.optimizationName}
            placeholder={t('optimization.label.placeholder.optimizationName')}
            onChange={handleChangeOptimizationName}
            endAdornment={<SearchIcon className="search-icon" onClick={handleSearch} data-testid="searchButton" />}
            aria-describedby="outlined-weight-helper-text"
            labelWidth={0}
            inputProps={{
              maxLength: SearchingTextLimitLength,
            }}
          />
        </div>
      </Box>
    </div>
  );
};

export default DvOptimizationListFilter;
