import type { InteractionMode, ChartDataset } from 'chart.js';
import { getDate, parse, format, eachDayOfInterval } from 'date-fns'
import { DateFnsFormat } from '@models/common/CommonConstants';
import { convertStrToDate } from '@utils/DateUtil'

const convertedDate = (dateLabel: string) => parse(dateLabel, DateFnsFormat.ISO_DATE, new Date());

export enum ChartTerm {
  DAY = 1,
  WEEK = 7,
  FORTNIGHT = 14,
  MONTH = 30
}

export enum ChartAxisType {
  FIRST = 'first',
  SECOND = 'second',
  COMPARE_FIRST = 'compareFirst',
  COMPARE_SECOND = 'compareSecond',
}

export const chartAreaBorder = {
  id: 'chartAreaBorder',
  beforeDraw(chart: any, args: any, options: any) {
    const {ctx, chartArea: {left, right, top}} = chart;
    ctx.save();
    ctx.strokeStyle = options.borderColor;
    ctx.lineWidth = options.borderWidth;
    ctx.setLineDash(options.borderDash || []);
    ctx.lineDashOffset = options.borderDashOffset;

    ctx.beginPath();
    ctx.moveTo(left, top)
    ctx.lineTo(right, top);
    ctx.stroke();
    ctx.restore();
  }
}

export interface ChartDataParams {
  data: any[]
  borderColor?: string
  borderDash?: number[]
  yAxisID: string
  backgroundColor?: string
  barThickness?: number
  type?: string
}
export const generateLineDataSet = ({ data, borderColor, borderDash, yAxisID }: ChartDataParams): ChartDataset<'line', (number|null)[]> => {
  return {
    type: 'line' as const,
    borderWidth: 2,
    fill: false,
    data,
    borderColor,
    borderDash,
    yAxisID,
  }
}

export const generateBarDataSet = ({ data, backgroundColor, barThickness, yAxisID }: ChartDataParams): ChartDataset<'bar', (number|null)[]> => {
  return {
    type: 'bar' as const,
    backgroundColor,
    borderWidth: 0,
    barThickness,
    data,
    yAxisID,
  }
}

export const generateDataSet = ({ type, backgroundColor, barThickness, borderColor, borderDash, ...restParams}: ChartDataParams) => ({
  ...restParams,
  borderWidth: type === 'bar' ? 0 : 2,
  backgroundColor: type === 'bar' ? backgroundColor : undefined,
  barThickness: type === 'bar' ? barThickness : undefined,
  borderColor: type === 'line' ? borderColor : undefined,
  borderDash: type === 'line' ? borderDash : undefined
})

export function generateLabels(start: string, end: string, dateFormat = DateFnsFormat.ISO_DATE) {
  const labels = [];
  
  // Debug logging
  
  // Try to convert dates - handle both YYYY-MM-DD and YYYYMMDD formats
  let startDate = convertStrToDate(start);
  let endDate = convertStrToDate(end);
  
  // If convertStrToDate fails, try parsing ISO format directly
  if (!startDate && start.includes('-')) {
    startDate = new Date(start);
  }
  if (!endDate && end.includes('-')) {
    endDate = new Date(end);
  }
  
  
  if (!startDate || !endDate || isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    console.warn('Invalid date format provided to generateLabels:', { start, end });
    return [];
  }
  
  // Validate that start date is not after end date
  if (startDate > endDate) {
    console.warn('Start date is after end date:', { start, end });
    return [];
  }
  
  try {
    const dateRange = eachDayOfInterval({
      start: startDate,
      end: endDate
    });

    labels.push(...dateRange.map(date => format(date, dateFormat)));
    return labels;
  } catch (error) {
    console.error('Error generating date labels:', error, { start, end });
    return [];
  }
}

export const chartOptions = (labels: string[], showSecond: boolean, tooltipHandler: any, matchedYMax?: number) => ({
  maintainAspectRatio: false,
  responsive: true,
  interaction: {
    intersect: false,
    mode: 'x' as InteractionMode,
  },
  stacked: false,
  scaleShowValues: true,
  layout: {
    padding: {
      top: 40
    }
  },
  plugins: {
    legend: { display: false },
    tooltip: {
      enabled: false,
      position: 'nearest',
      external: tooltipHandler
    },
    chartAreaBorder: {  // chart style
      borderColor: '#040A45',
      borderWidth: 0.5,
      borderDash: [5, 5],
      borderDashOffset: 1,
    },
    annotation: {
      clip: false,
      annotations: {},
    },
  },
  scales: {
    x: {
      grid: {
        borderColor: '#040A45',
        tickColor: 'transparent',
        display: false,
      },
      ticks: {
        // minRotation: 0,
        display: true,
        // autoSkip: false,
        maxRotation: 0,
        color: '#040a45',
        font: {
          size: 11,
          weight: ({tick}: any) => {
            const dayOfMonth = getDate(convertedDate(tick.label));
            if(dayOfMonth === 1) {
              return 700
            }
            return 400
          }
        },
        // FIXME
        callback: (tickValue: number|string, index: number) => {
          const date = labels[index]
          if (!date) return ''
          return date
        },
      },
    },
    first: {
      // type: 'line' as const,
      display: true,
      position: 'left',
      grid: {
        display: true,
        tickColor: 'transparent',
        borderColor: 'transparent',
      },
      axis: 'y',
      title: {
        display: false,
      },
      // min: 0,
      max: matchedYMax,
      ticks: {
        color: '#348bd5',
        // stepSize: 0.001,
        autoSkip: true,
        includeBounds: false,
        font: {
          size: 10,
        },
      },
    },
    second: {
      // type: 'line' as const,
      display: showSecond, // TODO shows if second data have
      position: 'right',
      grid: {
        display: true,
        tickColor: 'transparent',
        borderColor: 'transparent',
        drawOnChartArea: false,
      },
      axis: 'y',
      title: {
        display: false,
      },
      // min: 0,
      max: matchedYMax,
      ticks: {
        color: '#d45a4c',
        // stepSize: 0.001,
        autoSkip: true,
        includeBounds: false,
        font: {
          size: 10,
        },
      },
    },
  }
})
