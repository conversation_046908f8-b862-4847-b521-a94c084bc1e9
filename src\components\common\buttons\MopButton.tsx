// src/components/common/buttons/MopButton.tsx
import React, { ReactNode, CSSProperties } from 'react'
import { MOPIcon } from '@models/common'
import MopIcon from '@components/common/icon/MopIcon'
import TagManager from 'react-gtm-module'
import { cn } from '@utils/index'
import './MopButton.scss'

interface Props {
  label?: string
  onClick: () => void
  value?: string | number
  size?: 'mini' | 'sm' | 'md' | 'lg'
  rounded?: 'sm' | 'md' | 'full'
  contained?: boolean
  bgColor?: string
  borderColor?: string
  textColor?: string
  width?: string
  height?: string
  customClass?: string
  customStyle?: CSSProperties
  leftIcon?: ReactNode | MOPIcon
  rightIcon?: ReactNode | MOPIcon
  disabled?: boolean
  gtmId?: string
}

const sizeStyles = {
  mini: { '--button-font-size': '8px', '--button-padding': '4px 8px', '--icon-size': '12px' },
  sm: { '--button-font-size': '10px', '--button-padding': '5px 12px', '--icon-size': '14px' },
  md: { '--button-font-size': '12px', '--button-padding': '6px 16px', '--icon-size': '16px' },
  lg: { '--button-font-size': '14px', '--button-padding': '7px 20px', '--icon-size': '18px' }
}

const roundedStyles = {
  sm: { '--button-radius': '4px' },
  md: { '--button-radius': '8px' },
  full: { '--button-radius': '9999px' }
}

const MopButton = ({
  label,
  onClick,
  value,
  size = 'md',
  rounded = 'full',
  contained = false,
  bgColor,
  borderColor,
  textColor,
  width,
  height,
  customClass = '',
  customStyle,
  leftIcon,
  rightIcon,
  disabled = false,
  gtmId
}: Props) => {
  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (event) => {
    if (disabled) return

    const gtmEventId = event.currentTarget.dataset.gtmId

    if (gtmEventId) {
      try {
        TagManager.dataLayer({
          dataLayer: {
            event: 'click',
            gtm_id: gtmEventId
          }
        })
      } catch (error) {
        console.error('GTM event error:', error)
      }
    }
    onClick()
  }

  const buttonStyle = {
    ...sizeStyles[size],
    ...roundedStyles[rounded],
    ...(bgColor && { '--button-bg-color': bgColor }),
    ...(textColor && { '--button-text-color': textColor }),
    ...(borderColor && { '--button-border-color': borderColor }),
    ...(width && { '--button-width': width }),
    ...(height && { '--button-height': height }),
    ...customStyle
  } as CSSProperties

  const renderIcon = (icon: ReactNode | MOPIcon | undefined, position: 'left' | 'right') => {
    if (!icon) return null
    
    if (typeof icon === 'string' && Object.values(MOPIcon).includes(icon as MOPIcon)) {
      const iconSize = parseInt(sizeStyles[size]['--icon-size'].replace('px', ''))
      return (
        <MopIcon 
          name={icon as MOPIcon} 
          size={iconSize} 
          customClass={cn('button-icon', `icon-${position}`)}
        />
      )
    }
    
    return <span className={cn('button-icon', `icon-${position}`)}>{icon as ReactNode}</span>
  }

  return (
    <button
      className={cn(
        'mop-button',
        `size-${size}`,
        `rounded-${rounded}`,
        contained ? 'contained' : 'outlined',
        disabled && 'disabled',
        customClass
      )}
      value={value}
      onClick={handleClick}
      data-gtm-id={gtmId}
      disabled={disabled}
      style={buttonStyle}
    >
      {renderIcon(leftIcon, 'left')}
      {label && <span className="button-label">{label}</span>}
      {renderIcon(rightIcon, 'right')}
    </button>
  )
}

export default MopButton 