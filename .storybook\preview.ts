import type { Preview } from '@storybook/react'
import { RecoilRoot } from 'recoil'
import React from 'react'
import { advertiserState, advertiserListState, advertiserLoadedState } from '../src/store/Advertiser'
import { AuthorityType, AdvertiserCurrencyCode, SubscriptionProductType } from '../src/models/common/Advertiser'
import { YNFlag } from '../src/models/common'
import '../src/App.scss'
import '../src/index.css'

// 스토리북용 기본 광고주 데이터
const mockAdvertiser = {
  advertiserId: 1,
  advertiserName: 'Test Advertiser',
  managerId: 1,
  authorityType: AuthorityType.OPERATE,
  bookmarkYn: YNFlag.N,
  advertiserCurrencyCode: AdvertiserCurrencyCode.KRW,
  newYn: YNFlag.N,
  useYn: YNFlag.Y,
  createdDatetime: new Date().toISOString(),
  updatedDatetime: new Date().toISOString(),
  administratorEmail: '<EMAIL>',
  exclusiveMaxCpcYn: YNFlag.N,
  exclusiveSprintYn: YNFlag.N,
  exclusiveTurboYn: YNFlag.N,
  exclusiveMaxRankTargetYn: YNFlag.N,
  exclusiveClusteringYn: YNFlag.N,
  subscriptionProductType: SubscriptionProductType.PRO,
  subscriptionFunctions: []
}

const preview: Preview = {
  decorators: [
    (Story) => React.createElement(
      RecoilRoot, 
      {
        initializeState: ({ set }) => {
          set(advertiserState, mockAdvertiser)
          set(advertiserListState, [mockAdvertiser])
          set(advertiserLoadedState, true)
        },
        children: React.createElement(Story)
      }
    ),
  ],
  parameters: {
    controls: {
      matchers: {
       color: /(background|color)$/i,
       date: /Date$/i,
      },
    },
  },
};

export default preview;