import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react'
import './ChartLegend.scss'
import Plotly from "plotly.js-basic-dist-min";

interface Props {
  trace: Partial<Plotly.PlotData>
  onClick: any
}

type LegendHeanle = {
  click: () => void;
}

const ChartLegend = ({ trace, onClick }: Props, ref: React.ForwardedRef<LegendHeanle>) => {
  const [isShown, setIsShown] = useState(true)
  const legendRef = useRef<HTMLDivElement>(null)

  useImperativeHandle(ref, () => ({
    click: () => setIsShown(!isShown)
  }))

  const toggleShow = () => {
    setIsShown(!isShown)
    onClick(trace)
  }

  useEffect(() => {
    setIsShown(true)
  }, [trace])

  return (
    <div
      ref={legendRef}
      className={`chart-legend-box ${isShown ? '' : 'hide' }`}
      onClick={toggleShow}
    >
      <div className="legend-color" style={{'--legend-color': trace.marker?.color} as React.CSSProperties}/>
      <div className="legend-label">{trace.name}</div>
    </div>
  )
}

export default forwardRef<LegendHeanle, Props>(ChartLegend)