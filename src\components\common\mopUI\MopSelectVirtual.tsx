// src/components/common/mopUI/MopSelectVirtual.tsx
import React, { Fragment, useMemo, useRef } from 'react'
import { Listbox, Transition } from '@headlessui/react'
import { useVirtualizer } from '@tanstack/react-virtual'
import { cn } from '@utils/index'

interface MopSelectVirtualProps {
  rowCount: number
  renderRow: (index: number) => React.ReactNode
  estimateSize: number | ((index: number) => number)
  getRowKey?: (index: number) => React.Key
  overscan?: number
  className?: string
  maxHeight?: string
  header?: React.ReactNode
  viewportHeight?: number
  resetScrollOnOpen?: boolean
  onScroll?: (scrollTop: number) => void
  onMeasureItemHeight?: (height: number) => void
}

const MopSelectVirtual = React.forwardRef<HTMLDivElement, MopSelectVirtualProps>(
  ({ rowCount, renderRow, estimateSize, getRowKey, overscan = 8, className, maxHeight = 'max-h-60', header, viewportHeight = 360, resetScrollOnOpen = true, onScroll, onMeasureItemHeight }, ref) => {
    const parentRef = useRef<HTMLDivElement | null>(null)

    const estimator = useMemo(() => {
      return (index: number) => (typeof estimateSize === 'function' ? estimateSize(index) : estimateSize)
    }, [estimateSize])

    const virtualizer = useVirtualizer({
      count: rowCount,
      getScrollElement: () => parentRef.current,
      estimateSize: estimator,
      overscan,
      initialRect: { width: 0, height: viewportHeight }
    })

    return (
      <Transition
        as={Fragment}
        leave="transition ease-in duration-100"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <Listbox.Options
          as="div"
          ref={(node) => {
            if (typeof ref === 'function') ref(node as HTMLDivElement)
            else if (ref) (ref as React.MutableRefObject<HTMLDivElement | null>).current = node as HTMLDivElement
            parentRef.current = node as HTMLDivElement
            if (node) {
              if (resetScrollOnOpen) {
                node.scrollTop = 0
              }
              // Recompute on mount/open to ensure virtual range exists
              virtualizer.measure()
            }
          }}
          onScroll={(e) => onScroll?.((e.target as HTMLDivElement).scrollTop)}
          className={cn(
            'absolute top-full left-0 right-0 z-50 mt-0',
            'bg-white border border-[#efefef] rounded-[4px]',
            'shadow-lg overflow-auto',
            header ? 'py-0' : 'py-1',
            maxHeight,
            className
          )}
          static={false}
        >
          {header}

          <div
            style={{
              height: virtualizer.getTotalSize(),
              position: 'relative'
            }}
          >
            {virtualizer.getVirtualItems().map((vi) => {
              const key: React.Key = getRowKey ? getRowKey(vi.index) : String(vi.key)
              return (
                <div
                  key={key}
                  data-index={vi.index}
                  ref={(el) => {
                    virtualizer.measureElement(el)
                    if (el && onMeasureItemHeight) {
                      const h = el.getBoundingClientRect().height
                      if (h > 0) onMeasureItemHeight(h)
                    }
                  }}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    transform: `translateY(${vi.start}px)`,
                    willChange: 'transform'
                  }}
                >
                  {renderRow(vi.index)}
                </div>
              )
            })}
          </div>
        </Listbox.Options>
      </Transition>
    )
  }
)

MopSelectVirtual.displayName = 'MopSelectVirtual'

export default MopSelectVirtual 