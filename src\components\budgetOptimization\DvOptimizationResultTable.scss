.dv-optimization-result-table {
  flex: 1;

  border-bottom: 1px solid var(--point_color);

  .MuiTableContainer-root {
    max-height: 211px;
    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }

  .MuiTable-root {
    table-layout: fixed;
    width: 100%;
  }

  .MuiTableBody-root {
    background-color: #fff;
  }
  .MuiTableCell-root {
    height: 41px;
    padding: 0px;
    font-size: 10px;
    font-weight: 500;
    color: var(--point_color);
    border-top: 1px solid #d1d4dc;
    text-align: center;
    &.MuiTableCell-head {
      font-size: 11px;
      font-weight: 700;
    }
    &.MuiTableCell-body {
      word-wrap: break-word;
    }
  }

  .MuiTableHead-root {
    .MuiTableCell-head {
      height: 25px;
      background-color: var(--bg-gray-light);

      border-top: 1px solid #445071;
      border-bottom: 1px solid #445071;
    }
    .header-row-second {
      .MuiTableCell-stickyHeader {
        top: 26px;
      }
    }
  }

  .MuiTableRow-head:first-child .MuiTableCell-head:last-child {
    border-bottom: 1px solid #adb3c2;
  }

  .MuiTableBody-root {
    overflow-y: auto;
    overflow-x: hidden;
  }
}
