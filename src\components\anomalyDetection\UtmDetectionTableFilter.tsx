import { useState } from 'react';
import { Box, FormLabel, Grid, MenuItem } from '@material-ui/core';
import { useTranslation } from 'react-i18next';

import { useRecoilValue, useRecoilState } from 'recoil';
import { utmsAnomalySummaries, abnormalLandingUrlDetail, selectedUtmAnomalySummaryDetails } from '@store/AnomalyDetection';
import SelectBottom from '@components/common/SelectBottom';
import CustomTooltip from '@components/common/CustomTooltip';
import { withStyles } from '@material-ui/core/styles';
import { ReactComponent as ArrowDown } from '@components/assets/images/icon_arrow_down.svg';
import { MopSearch } from '@components/common';
import { getAnomalyTypeItems } from '@utils/CodeUtil';
import './UtmDetectionTableFilter.scss'

const renderDropdownIcon = (props: any) => {
  return (
    <div className={`search-input-dropdown ${props.className.includes('MuiSelect-iconOpen') ? 'search-input-dropdown-open' : ''}`}>
      <ArrowDown />
    </div>
  );
};

const UrlDetectionTableFilter = () => {
  const { t } = useTranslation();
  const utmSummaries = useRecoilValue(utmsAnomalySummaries);
  const summaryDetails = useRecoilValue(selectedUtmAnomalySummaryDetails);
  const [currentItem, setCurrentItem] = useRecoilState(abnormalLandingUrlDetail);
  const [searchName, setSearchName] = useState<string>('');
  const [type, setType] = useState<string>('');

  const filtered = summaryDetails.details.filter(
    item => type ? item.anomalyType === type: true
  ).filter(
    item => searchName ? item.name.includes(searchName) : true
  );

  const MyTooltip = withStyles({
    tooltip: {
      minWidth: 'unset !important',
    },
  })(CustomTooltip)

  return (
    <>
      <Grid container justifyContent="center" className="search-label-container">
        <Grid item>
          <Box className="search-label">
            <FormLabel>MEDIA</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label">
            <FormLabel>CAMPAIGN NAME</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label dropdown">
            <FormLabel>TYPE</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label dropdown">
            <FormLabel>NAME</FormLabel>
          </Box>
        </Grid>
      </Grid>
      <Grid container justifyContent="center" className="search-input-container">
        <Grid item>
          <Box className="search-label">
            <span className="ellipsis">
              { t(`common.code.media.${utmSummaries.find(item => item.campaignId === summaryDetails.campaignId)?.mediaType}`) }
            </span>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label">
            <MyTooltip
                title={<p>{utmSummaries.find(item => item.campaignId === summaryDetails.campaignId)?.campaignName}</p>}
                placement="bottom"
                arrow
            >
              <span className="ellipsis">
                { utmSummaries.find(item => item.campaignId === summaryDetails.campaignId)?.campaignName }
              </span>
            </MyTooltip>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-input">
            <SelectBottom
              className="search-input-select"
              data-testid="deviceTypeSelect"
              displayEmpty
              onChange={(e) => setType(e.target.value as string)}
              name="anomalyType"
              value={type}
              MenuProps={{
                className: 'url-anomaly-filter-popover',
              }}
              IconComponent={(props) => renderDropdownIcon(props)}
            >
              <MenuItem value={''}>
                전체
              </MenuItem>
              {getAnomalyTypeItems().map((item) => {
                return (
                  <MenuItem
                    key={item.type}
                    value={item.name}
                  >
                    {item.name}
                  </MenuItem>
                );
              })}
            </SelectBottom>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-input">
            <SelectBottom
              className="search-input-select"
              data-testid="deviceTypeSelect"
              displayEmpty
              onChange={(e) => {
                const newItem = summaryDetails.details.find(item => item.id === e.target.value);
                if (newItem) {
                  setCurrentItem(newItem);
                }
              }}
              value={currentItem.id}
              name="name"
              MenuProps={{
                className: 'url-anomaly-filter-popover',
              }}
              IconComponent={(props) => renderDropdownIcon(props)}
              renderValue={(value) => summaryDetails.details.find(item => item.id === value)?.name || ''}
            >
              <MopSearch
                id="anomaly-search-name"
                placeholder={'Search'}
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                onSearch={() => {}}
                onFocus={() => setSearchName('')}
              />
              {filtered?.map(item => {
                return (
                  <MenuItem key={item.id} value={item.id}>
                    <div className='filter-item name'>
                      <span className='title'>{item.name}</span>
                      <span className='id'>{item.id}</span>
                    </div>
                  </MenuItem>
                )
              })}
            </SelectBottom>
          </Box>
        </Grid>
      </Grid>
    </>
  )
}

export default UrlDetectionTableFilter;