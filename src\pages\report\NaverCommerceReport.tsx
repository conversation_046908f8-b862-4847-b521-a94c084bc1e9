import NaverCommerceReportFilter from '@components/report/naverCommerceReport/NaverCommerceReportFilter'
import { useState } from 'react'
import { ActiveDate } from '@models/report/ActiveDate'
import { NaverCommerceGroupIndicatorFilter } from '@components/report'
import NaverCommerceIndicatorGroup from '@components/report/naverCommerceReport/NaverCommerceIndicatorGroup'
import NaverCommerceChartReport from '@components/report/naverCommerceReport/chart/NaverCommerceChartReport'
import { convertDateToStr } from '@utils/DateUtil'
import { getBeforeDate } from '@utils/DateUtil'
import { NaverCommerceIndicatorSummary, NaverCommerceGraphResponse } from '@models/report/NaverCommerce'
import { NaverCommerceReportTable } from '@components/report/naverCommerceReport/NaverCommerceReportTable'
import { NAVER_COMMERCE_INITIAL_INDICATORS } from '@utils/reports'

const NaverCommerceReport = () => {
  const [appliedProducts, setAppliedProducts] = useState<string[]>([]) // selected products after search
  const [appliedOptimizations, setAppliedOptimizations] = useState<string[]>([]) // selected optimizations after search
  const [activeDate, setActiveDate] = useState<ActiveDate>({
    startDate: convertDateToStr(getBeforeDate(7)),
    endDate: convertDateToStr(new Date())
  })
  const [summary, setSummary] = useState<NaverCommerceIndicatorSummary>()
  const [graphData, setGraphData] = useState<NaverCommerceGraphResponse>()
  const [variance, setVariance] = useState<boolean>(false)
  // State for indicator group (summary cards)
  const storedSummaryIndicator = localStorage.getItem('mop-naver-commerce-report-summary-indicator')?.split(',')
  const [summaryIndicator, setSummaryIndicator] = useState<string[]>(
    storedSummaryIndicator ?? NAVER_COMMERCE_INITIAL_INDICATORS
  )
  const updateSummaryIndicator = (value: string[]) => {
    localStorage.setItem('mop-naver-commerce-report-summary-indicator', value.join(','))
    setSummaryIndicator(value)
  }

  return (
    <div id="CampaignReportPage" className="campaign-report">
      <section className="campaign-report__filter">
        <NaverCommerceReportFilter
          activeDate={activeDate}
          variance={variance}
          setActiveDate={setActiveDate}
          onUpdateSummary={setSummary}
          onUpdateGraphData={setGraphData}
          setAppliedProducts={setAppliedProducts}
          setAppliedOptimizations={setAppliedOptimizations}
          setVariance={setVariance}
        />
      </section>
      <section className="campaign-report__content">
        <section className="campaign-report__indicator">
          <div className="flex justify-end">
            <NaverCommerceGroupIndicatorFilter
              initialValue={NAVER_COMMERCE_INITIAL_INDICATORS}
              existingValue={summaryIndicator}
              getMultiChecks={updateSummaryIndicator}
              hasMinimum={true}
            />
          </div>

          <NaverCommerceIndicatorGroup summaryIndicator={summaryIndicator} summaryData={summary} />
        </section>
        <NaverCommerceChartReport period={activeDate} graphData={graphData} />
        <NaverCommerceReportTable
          activeDate={activeDate}
          variance={variance}
          appliedProducts={appliedProducts}
          appliedOptimizations={appliedOptimizations}
          totalRowData={summary}
          setVariance={setVariance}
        />
      </section>
    </div>
  )
}
export default NaverCommerceReport
