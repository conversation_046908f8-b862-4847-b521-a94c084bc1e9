/* istanbul ignore file */

import { callApi, downloadByteArray, Method, openDownloadLink } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  DvOptimizationDetail,
  DvOptimizationInfo,
  DvOptimizationList,
  DvOptimizationsRequest,
  DvOptimizationSaveRequest,
  DvOptimizationAdGroup,
  DvAdgroupSearchRequest,
  DvOptimizationCostsRequest,
  DvOptimizationCost,
} from '@models/budgetOptimization/DvOptimization';
import { pageSizeOptions } from '@models/common/CommonConstants';
import { AxiosResponse } from 'axios';
import { GetDvReportResponse } from '@models/budgetOptimization/DvReport';

export const getDvOptimizations = async (
  param: DvOptimizationsRequest,
  isLoading = true
): Promise<DvOptimizationList> => {
  const ret: DvOptimizationList = {
    totalCount: 0,
    pageSize: pageSizeOptions[0],
    pageIndex: 1,
    optimizations: [],
  };

  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/dva',
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  if (response.successOrNot === 'Y' && (response.data.totalCount as number) > 0) {
    ret.totalCount = response.data.totalCount as number;
    ret.optimizations = response.data.optimizations as DvOptimizationInfo[];
    ret.pageSize = param.pageSize;
    ret.pageIndex = param.pageIndex;
  }

  return ret;
};

export const updateDvOptimizationAllocatingYn = async (
  optimizationId: number,
  allocationYn: string,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/allocations/${optimizationId}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        allocationYn: allocationYn,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const deleteDvOptimization = async (optimizationId: number, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/${optimizationId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });
};

export const createDvOptimization = async (param: DvOptimizationSaveRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/dva',
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateDvOptimization = async (
  optimizationId: number,
  param: DvOptimizationSaveRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/${optimizationId}`,
    method: Method.PATCH,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const getDvOptimizationDetail = async (
  optimizationId: number,
  isLoading = true
): Promise<DvOptimizationDetail> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/${optimizationId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as DvOptimizationDetail;
};

export const getDvOptimizationResult = async (advertiserId: number, isLoading = true): Promise<GetDvReportResponse> => {
  const ret: GetDvReportResponse = {
    predictionDate: '',
    media: [],
    campaign: [],
  };

  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/budget-result/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  if (response.successOrNot === 'Y') {
    ret.predictionDate = response.data.predictionDate;
    ret.media = response.data.media;
    ret.campaign = response.data.campaign;
  }

  return ret;
};

export const getDvCampaignAdgroups = async (param: DvAdgroupSearchRequest, isLoading = true): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/adgroups`,
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as DvOptimizationAdGroup[];
};

export const downloadMediaAndCampaignBudgetData = async (optimizationId: number, isLoading = true): Promise<any> => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/optimizations/dva/budget-result/${optimizationId}/download`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  if (response.data) {
    openDownloadLink(response);

    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};

export const getDvOptimizationCosts = async (param: DvOptimizationCostsRequest, isLoading = true): Promise<DvOptimizationCost[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/dva/costs',
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as DvOptimizationCost[];
};

export const getDvOptimizationNewAdgroup = async (param: {advertiserId: number}, isLoading = true): Promise<string> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/dva/new-campaign',
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as string;
};