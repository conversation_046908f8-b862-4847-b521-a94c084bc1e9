import { useEffect, useState } from 'react'
import { RoundedSelect } from '@components/common'
import './ChartSetting.scss'
import { indicatorOptions, filterOptions } from './utils'
import { useRecoilState } from 'recoil'
import { xAxisState, yAxisState, sizeState } from './store'
import ChartLegend from './ChartLegend'

interface Props {
  isDisabled?: boolean
  total: any[]
  data: any[]
  setChartData: (data: any, type: string) => void
}

const ChartSetting = ({ isDisabled, data, total, setChartData }: Props) => {
  const [xAxis, setXAxis] = useRecoilState(xAxisState)
  const [yAxis, setYAxis] = useRecoilState(yAxisState)
  const [size, setSize] = useRecoilState(sizeState)

  const [xAxisOptions, setXAxisOptions] = useState(indicatorOptions())
  const [yAxisOptions, setYAxisOptions] = useState(indicatorOptions())
  const [sizeOptions, setSizeOptions] = useState(indicatorOptions())

  const toggleData = (trace: any) => {
    const hasTrace = data.includes(trace)
    if (hasTrace) {
      const filtered = data.filter((item) => item.uid !== trace.uid)
      setChartData(filtered, 'legend')
    } else {
      setChartData([...data, trace], 'legend')
    }
  }

  const updateChartData = (value: any, updateFn: any) => {
    updateFn(value)
  }
  useEffect(() => {
    setXAxisOptions(filterOptions([yAxis, size], indicatorOptions()))
    setYAxisOptions(filterOptions([xAxis, size], indicatorOptions()))
    setSizeOptions(filterOptions([xAxis, yAxis], indicatorOptions()))
  }, [xAxis, yAxis, size])

  return (
    <section className="chart-setting-box">
      <div className="chart-setting__header">
        <span className="title">Settings</span>
        <span className="sub-title">설정</span>
      </div>
      <div className="chart-setting__items">
        <span>X 축</span>
        <RoundedSelect
          options={xAxisOptions}
          isDisabled={isDisabled}
          value={xAxis}
          updateValue={(value) => updateChartData(value, setXAxis)}
        />
        <span>Y 축</span>
        <RoundedSelect
          options={yAxisOptions}
          isDisabled={isDisabled}
          value={yAxis}
          updateValue={(value) => updateChartData(value, setYAxis)}
        />
        <span>Bubble 크기</span>
        <RoundedSelect
          options={sizeOptions}
          isDisabled={isDisabled}
          value={size}
          updateValue={(value) => updateChartData(value, setSize)}
        />
      </div>
      {/* <div className="chart-setting__sizes">
        {
          [20,40,60,80,100].map((bubble) => (
            <div className='chart-bubble__box'>
              <div className='chart-bubble__size' style={{'--size': `${bubble/2.5}px`}  as React.CSSProperties} />
              <span className='chart-bubble__label'>{ bubble }</span>
            </div>
          ))
        }
      </div> */}
      {/* <div className="chart-setting__legends">
        {total.map((trace) => (
          <ChartLegend key={trace.uid} trace={trace} onClick={toggleData} />
        ))}
      </div> */}
    </section>
  )
}

export default ChartSetting
