import React, { ReactElement, Fragment, useState, useEffect } from 'react';
import {
  getBudgetOptimizationCampaigns,
} from '@api/budgetOpt/BudgetOpt'
import {
  BudgetOptimizationInfo,
  BudgetOptimizationResult,
  BudgetOptimizationImpactData,
  BudgetOptImpactKeys,
  BudgetOptImpact,
} from '@models/budgetOpt/BudgetOpt';
import { makeStyles } from '@material-ui/core/styles';
import Popper from '@material-ui/core/Popper';
import Checkbox from '@material-ui/core/Checkbox';
import { ReportIndicator } from '@models/common/Indicator';
import useReportIndicatorFormat from '@components/report/hook/useReportIndicatorFormat';
import { downloadCSVFile } from '@utils/jsonToCSV';
import CustomTooltip from '@components/common/CustomTooltip';
import CommonTooltip from '@components/common/CommonTooltip';
import TooltipCard from '@components/common/tooltip/TooltipCard';
import InnerHtml from '@components/common/InnerHtml';

import { useTranslation } from 'react-i18next';

import AddIcon from '@material-ui/icons/Add';
import RemoveIcon  from '@material-ui/icons/Remove';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import { MopIcon } from '@components/common';
import { MOPIcon, MediaType } from '@models/common';
import { defaultImpactValues, impactKeys, allImpactKeys, BudgetColorByMedia } from '@utils/budgetOpt'

import './BudgetResultTable.scss'

interface Props {
  optimization: BudgetOptimizationInfo;
  optimizationResult: BudgetOptimizationResult[];
  colors: { [k: string]: string; };
}

export interface TreeItem {
  type: 'root' | 'mediaType' | 'accountId' | 'campaign';
  id: string;
  name: string;
  children?: this[];
  parent?: this;
  data: BudgetOptimizationResult;
  accountId?: string;
  accountName?: string;
}

const usePopperStyles = makeStyles({
  root: {
    backgroundColor: '#fff',
    width: 135,
    boxShadow: '0px 0px 10px 0px rgba(144, 144, 144, 0.25)',
    '& > div': {
      height: 30,
      display: 'flex',
      alignItems: 'center',
      '& > label': {
        display: 'inline-block',
        lineHeight: 1,
        textTransform: 'uppercase',
      },
    },
  },
})

const BudgetResultTable: React.FC<Props> = ({ optimization, optimizationResult, colors }: Props) => {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState<string[]>(['root'])
  const [root, setRoot] = useState<TreeItem|null>(null)
  const storedImpactColumns = localStorage.getItem('mop-budget-result-impact')?.split(',')
  const [impactColumns, setImpactColumns] = useState<BudgetOptImpactKeys[]>(storedImpactColumns ? storedImpactColumns as (keyof BudgetOptimizationImpactData)[] : impactKeys)
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState<boolean>(false)
  const [allParents, setAllParents] = useState<string[]>([])
  const [variance, setVariance] = useState<boolean>(true)

  const { indicatorFormatForDownload } = useReportIndicatorFormat();
  const percent = (value: string|number, sign = false, toFixed = 2) => isNaN(Number(value)) ? '-' : `${(sign && Number(value) > 0)  ? '+' : ''}${(Number(value)*100).toFixed(toFixed)}%`
  const rate = (prev: string|number, curr: string|number) => (prev === 0) || (prev === '0') ? 0 : (Number(curr) - Number(prev)) / Number(prev)
  const incdec = (value: string|number) => Number(value) > 0 ? 'increase' : Number(value) < 0 ? 'decrease' : ''
  const _format = (new Intl.NumberFormat('ko-KR', { maximumFractionDigits: 0 })).format
  const _format2 = (new Intl.NumberFormat('ko-KR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })).format
  // const _format4 = (new Intl.NumberFormat('ko-KR', { minimumFractionDigits: 4, maximumFractionDigits: 4 })).format
  const format = (value: string|number) => _format(Number(value))
  const format2 = (value: string|number) => _format2(Number(value))
  // const format4 = (value: string|number) => _format4(Number(value))
  // const formatCsv = (val: string|number, digits=0) => Number(val).toFixed(digits)
  const getColor = (item: TreeItem) => {
    if (item.type === 'root' || item.type === 'mediaType') {
      return BudgetColorByMedia[item.data.mediaType].foreground
    } else if (item.type === 'accountId') {
      return colors[item.id]
    }
    return colors[`${item.data.mediaType}-${item.accountId}-${item.data.adType}`]
  }
  const getBackgroundColor = (mediaType: MediaType) => BudgetColorByMedia[mediaType].background
  const getSums = (arr: any[]) => {
    const obj = arr.reduce((acc, curr) => {
      for (const key of ['budget', 'budgetRatio', 'impressions', 'views', 'clicks', 'conversions', 'revenue']) {
        acc[key] = (acc[key] ?? 0) + Number(curr[key])
      }
      return acc
    }, {})
    obj.vtr = obj.impressions === 0 ? 0 : obj.views / obj.impressions
    obj.ctr = obj.impressions === 0 ? 0 : obj.clicks / obj.impressions
    obj.cvr = obj.clicks === 0 ? 0 : obj.conversions / obj.clicks
    obj.cpm = obj.impressions === 0 ? 0 : obj.budget / obj.impressions * 1000
    obj.cpv = obj.views === 0 ? 0 : obj.budget / obj.views
    obj.cpc = obj.clicks === 0 ? 0 : obj.budget / obj.clicks
    obj.cpa = obj.conversions === 0 ? 0 : obj.budget / obj.conversions
    obj.roas = obj.budget === 0 ? 0 : obj.revenue / obj.budget
    return obj
  }
  const getAllParents = (node: TreeItem): string[] => {
    if (node.children && node.children.length > 0) {
      return [node.id, ...node.children.flatMap(node => getAllParents(node))]
    } else {
      return []
    }
  }
  const setImpactColumnsWithLocalStorage = (value: (keyof BudgetOptimizationImpactData)[]) => {
    setImpactColumns(value)
    localStorage.setItem('mop-budget-result-impact', value.join(','))
  }

  const getTreeData = async() => {
    const res = await getBudgetOptimizationCampaigns(optimization.optimizationId)
    const campaignObject = Object.fromEntries(res.campaigns.map(item => [item.campaignId, item]))
    const hash: {[key: string]: TreeItem[]} = {}
    const _treeData: TreeItem[] = []
    const root: TreeItem = {
      type: 'root',
      id: 'root',
      name: 'Total',
      children: _treeData,
      data: {
        advertiserId: optimization.advertiserId,
        optimizationId: optimization.optimizationId,
        campaignId: '',
        mediaType: 'TOTAL' as MediaType,
        predictionDate: '',
        mopPrevTwoWeeks: {} as BudgetOptImpact,
        mopRecommend: {} as BudgetOptImpact,
      },
    }

    const uniqueMediaTypes = [...new Set(optimizationResult.map(campaign => campaign.mediaType))]
    const uniqueAccountIds = [...new Set(optimizationResult.map(campaign => `${campaignObject[campaign.campaignId].accountId}-${campaign.adType}`))]
    const campaigns = optimizationResult.slice().sort((a, b) => {
      const aName = campaignObject[a.campaignId].campaignName
      const bName = campaignObject[b.campaignId].campaignName
      if (aName < bName) {
        return -1
      } else if (aName > bName) {
        return 1
      }
      return 0
    })

    campaigns.forEach(campaign => {
      const key = `${campaign.mediaType}-${campaignObject[campaign.campaignId].accountId}`
      const campaignNode: TreeItem = {
        type: 'campaign',
        id: `${campaign.mediaType}-${campaignObject[campaign.campaignId].accountId}-${campaign.campaignId}`,
        name: campaignObject[campaign.campaignId].campaignName,
        data: campaign,
        accountId: `${campaignObject[campaign.campaignId].accountId}`,
        accountName: campaignObject[campaign.campaignId].accountName,
      }
      if (!hash[key]) {
        hash[key] = []
      }
      hash[key].push(campaignNode)
    })

    uniqueMediaTypes.forEach(mediaType => {
      const mediaTypeNode: TreeItem = {
        type: 'mediaType',
        id: `${mediaType}`,
        name: `${mediaType}`,
        children: [],
        parent: root,
        data: {
          advertiserId: optimization.advertiserId,
          optimizationId: optimization.optimizationId,
          campaignId: '',
          mediaType: mediaType,
          predictionDate: '',
          mopPrevTwoWeeks: {} as BudgetOptImpact,
          mopRecommend: {} as BudgetOptImpact,
        },
      }
      uniqueAccountIds.forEach(accountIdAdType => {
        const [accountId, adType] = accountIdAdType.split('-')
        const children = hash[`${mediaType}-${accountId}`]?.filter(campaign => campaign.data.adType === adType)
        if (children && children.length > 0) {
          const accountName = campaignObject[children[0].data!.campaignId].accountName
          const accountIdNode: TreeItem = {
            type: 'accountId',
            id: `${mediaType}-${accountId}-${adType}`,
            name: `${accountName!} (${accountId})`,
            children: children,
            parent: mediaTypeNode,
            data: {
              advertiserId: optimization.advertiserId,
              optimizationId: optimization.optimizationId,
              campaignId: '',
              mediaType: mediaType,
              adType: adType,
              predictionDate: '',
              mopPrevTwoWeeks: getSums(children.map(item => item.data.mopPrevTwoWeeks ?? defaultImpactValues)),
              mopRecommend: getSums(children.map(item => item.data.mopRecommend ?? defaultImpactValues)),
            } as BudgetOptimizationResult,
          }
          children.forEach(child => child.parent = accountIdNode)
          mediaTypeNode.children!.push(accountIdNode)
        }
      })
      mediaTypeNode.data.mopPrevTwoWeeks = getSums(mediaTypeNode.children!.map(item => item.data.mopPrevTwoWeeks ?? defaultImpactValues))
      mediaTypeNode.data.mopRecommend = getSums(mediaTypeNode.children!.map(item => item.data.mopRecommend ?? defaultImpactValues))
      _treeData.push(mediaTypeNode)
    })
    root.children = _treeData
    root.data.mopPrevTwoWeeks = getSums(root.children.map(item => item.data.mopPrevTwoWeeks ?? defaultImpactValues))
    root.data.mopRecommend = getSums(root.children.map(item => item.data.mopRecommend ?? defaultImpactValues))
    setRoot(root)
    setAllParents(getAllParents(root))
    setExpanded(['root', ...uniqueMediaTypes])
  }

  const renderImpactColumns = (key: string, value: string, prevValue: string) => {
    const tempKey = key.split('-').slice(-1)[0] as BudgetOptImpactKeys
    const isDefaultImpactKey = ['impressions', 'views', 'clicks', 'conversions', 'revenue'].includes(tempKey)
    return (
      <Fragment key={key}>
        <td className={`impactValue ${variance ? '' : incdec(rate(prevValue, value))}`}>
          {
            isDefaultImpactKey
              ? format(value)
              : ['vtr', 'ctr', 'cvr', 'roas'].includes(tempKey)
                ? format2(Number(value) * 100)
                : format2(value)}
        </td>
        { variance &&
          <td className="impactChangeRate">
            <div className={`${incdec(rate(prevValue, value))}`}>
              {percent(rate(prevValue, value), true)}
            </div>
          </td>
        }
      </Fragment>
    )
  }

  const renderRow = (item: TreeItem): ReactElement => {
    const prevImpactValues = item.data.mopPrevTwoWeeks ?? defaultImpactValues
    const recommendValues = item.data.mopRecommend ?? defaultImpactValues
    return (
      <Fragment key={item.id}>
        <tr>
          <td className="toggle">
            {item.children && item.children.length > 0 && (item.type === 'root' || item.type === 'mediaType') && (expanded.includes(item.id) ?
              <RemoveIcon onClick={() => toggleRow(item.id)} /> :
              <AddIcon onClick={() => toggleRow(item.id)} className="rounded" />
            )}
          </td>
          <td className="parent">
            {item.type === 'accountId' &&
              <>
                { expanded.includes(item.id) ?
                  <RemoveIcon onClick={() => toggleRow(item.id)} /> :
                  <AddIcon onClick={() => toggleRow(item.id)} className="rounded" />
                }
                <span className="adType">{ item.data.adType }</span>
              </>
            }
            {(item.type === 'root' || item.type === 'mediaType' || item.type === 'accountId') ? item.name : ''}
          </td>
          <td className="campaignName">{item.type === 'campaign' ? item.name : ''}</td>
          <td className="budget">{format(prevImpactValues.budget)}</td>
          <CustomTooltip title={percent(prevImpactValues.budgetRatio)} placement="bottom-start">
            <td className="budgetRatio" style={{backgroundColor: getBackgroundColor(item.data.mediaType)} as React.CSSProperties}>
              <div className="budgetRatioBar" style={{width: percent(prevImpactValues.budgetRatio), maxWidth: '100%', backgroundColor: getColor(item)} as React.CSSProperties}></div>
            </td>
          </CustomTooltip>
          <td className="budget">{format(recommendValues.budget)}</td>
          <CustomTooltip title={percent(recommendValues.budgetRatio)} placement="bottom-start">
            <td className="budgetRatio" style={{backgroundColor: getBackgroundColor(item.data.mediaType)} as React.CSSProperties}>
              <div className="budgetRatioBar" style={{width: percent(recommendValues.budgetRatio), maxWidth: '100%', backgroundColor: getColor(item)} as React.CSSProperties}></div>
            </td>
          </CustomTooltip>
          <td className={`changeRate ${incdec(rate(prevImpactValues.budget, recommendValues.budget))}`}>
            {percent(rate(prevImpactValues.budget, recommendValues.budget), true)}
          </td>
          { impactColumns.map(key => renderImpactColumns(`${item.id}-${key}`, recommendValues[key], prevImpactValues[key])) }
        </tr>
        { expanded.includes(item.id) && item.children?.map(item => renderRow(item)) }
      </Fragment>
    )
  }

  const downloadCsv = () => {
    if (!root) {
      return
    }
    const _flatten = (node: TreeItem): TreeItem[] => [node, ...(node.children?.flatMap(_flatten) || [])]
    const nodes = _flatten(root)

    downloadCSVFile(nodes.filter(node => node.type === 'campaign').map(node => ({
      mediaType: node.data.mediaType,
      accountId: node.accountId,
      campaignName: node.name,
      prevTwoWeeksBudget: node.data.mopPrevTwoWeeks.budget,
      prevTwoWeeksBudgetRatio: node.data.mopPrevTwoWeeks.budgetRatio,
      recommendBudget: node.data.mopRecommend.budget,
      recommendBudgetRatio: node.data.mopRecommend.budgetRatio,
      ...Object.fromEntries(allImpactKeys.flatMap(key => [
        [key, indicatorFormatForDownload(node.data.mopRecommend[key], key as ReportIndicator), 2],
        [`${key}%`, percent(rate(node.data.mopPrevTwoWeeks[key], node.data.mopRecommend[key]), true)],
      ])),
    })), `${optimization.optimizationId}-budget-opt-result`)
  }

  const toggleRow = (id: string) => {
    if (expanded.includes(id)) {
      setExpanded(expanded.filter(item => item !== id))
    } else {
      setExpanded([...expanded, id])
    }
  }

  const toggleAll = () => {
    setExpanded(allParents.length === expanded.length ? [] : allParents.slice())
  }

  const togglePopper = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    setOpen((prev) => !prev);
  }

  const toggleCheckbox = (key: BudgetOptImpactKeys) => {
    if (impactColumns.includes(key)) {
      setImpactColumnsWithLocalStorage(impactColumns.filter(item => item !== key))
    } else {
      setImpactColumnsWithLocalStorage([...impactColumns, key])
    }
  }

  useEffect(() => {
    getTreeData()
  }, []) //eslint-disable-line

  const popperClasses = usePopperStyles()

  return (
    <div id="BudgetResultTable">
      <div className="download">
        <Checkbox
          id="variance"
          color="primary"
          checked={variance}
          onChange={() => setVariance(!variance)}
        />
        <label htmlFor="variance">
          {t('optimization.label.budgetOpt.result.label.showVariance')}
        </label>
        <MopIcon name={MOPIcon.DOWNLOAD} onClick={downloadCsv} />
      </div>
      <div className="wrapper">
        <table>
          <colgroup>
            <col width="48px"></col>
            <col width="100px"></col>
            <col width="20%" style={{minWidth: '300px'}}></col>
            <col width="66px"></col>
            <col width="80px" style={{minWidth: '80px'}}></col>
            <col width="66px"></col>
            <col width="80px" style={{minWidth: '80px'}}></col>
            <col width="66px"></col>
            {
              impactColumns.map(key => (
                <Fragment key={`${key}-cols`}>
                  <col width="80px" style={{minWidth: '80px'}}></col>
                  { variance && <col width="66px"></col> }
                </Fragment>
              ))
            }
          </colgroup>
          <thead>
            <tr>
              <th rowSpan={2}>
                {
                  (allParents.length === expanded.length) ?
                    <RemoveIcon onClick={toggleAll}/> :
                    <AddIcon onClick={toggleAll} className="rounded" />
                }
              </th>
              <th rowSpan={2}>Media / Account</th>
              <th rowSpan={2}>Campaign</th>
              <CommonTooltip
                title={
                  <>
                    <h1>{t('optimization.label.budgetOpt.tooltip.spending.title')}</h1>
                    <p>{t('optimization.label.budgetOpt.tooltip.spending.contents.0')}</p>
                    <p className="indent2">{t('optimization.label.budgetOpt.tooltip.spending.contents.1')}</p>
                    <p className="indent2">{t('optimization.label.budgetOpt.tooltip.spending.contents.2')}</p>
                  </>
                }
                placement="bottom"
                arrow
              >
                <th colSpan={2}><AdviceMarkIcon />{t('optimization.label.budgetOpt.result.table.last2weekes')}</th>
              </CommonTooltip>
              <CommonTooltip
                title={
                  <TooltipCard
                    tKey={'optimization.label.budgetOpt.tooltip.recommend'}
                    type={'paragraph'} />
                }
                placement="bottom"
                arrow
              >
                <th colSpan={2}><AdviceMarkIcon />{t('optimization.label.budgetOpt.result.table.suggest')}</th>
              </CommonTooltip>
              <th rowSpan={2}>{t('optimization.label.budgetOpt.result.table.variance')}</th>
              <th colSpan={impactColumns.length * (variance ? 2 : 1)}>{t('optimization.label.budgetOpt.result.table.estimatedImpact')}</th>
            </tr>
            <tr>
              <th><InnerHtml innerHTML={t('optimization.label.budgetOpt.result.table.weeklySpending')} /></th>
              <th><InnerHtml innerHTML={t('optimization.label.budgetOpt.result.table.spending')} /></th>
              <th><InnerHtml innerHTML={t('optimization.label.budgetOpt.result.table.weeklyBudget')} /></th>
              <th><InnerHtml innerHTML={t('optimization.label.budgetOpt.result.table.budget')} /></th>
              <th colSpan={variance ? 2 : 1}>Impressions</th>
              <th colSpan={variance ? 2 : 1}>Views</th>
              <th colSpan={variance ? 2 : 1}>Clicks</th>
              <th colSpan={variance ? 2 : 1}>Conversions</th>
              <th colSpan={variance ? 2 : 1}>Revenue</th>
              {
                impactColumns.slice(5).map(key => (
                  <th key={`${key}-th`} colSpan={variance ? 2 : 1}>{key.toUpperCase()}{['vtr', 'ctr', 'cvr', 'roas'].includes(key) ? '%' : ''}</th>
                ))
              }
            </tr>
          </thead>
          <tbody>
            { root && renderRow(root) }
          </tbody>
        </table>
      </div>
      <button className={`togglePopper ${open ? 'open' : 'closed'}`} onClick={togglePopper}>
        <MopIcon name={MOPIcon.ARROW_DOWN} />
      </button>
      <Popper className={popperClasses.root} open={open} anchorEl={anchorEl} placement={'left-start'} transition>
        <div>
          <Checkbox
            id="all"
            color="primary"
            checked={impactColumns.length === allImpactKeys.length}
            onChange={() => impactColumns.length === allImpactKeys.length ? setImpactColumnsWithLocalStorage(['impressions', 'views', 'clicks', 'conversions', 'revenue']) : setImpactColumnsWithLocalStorage(allImpactKeys.slice())}
          />
          <label htmlFor="all"><b>all</b></label>
        </div>
        { ['vtr', 'ctr', 'cvr', 'cpm', 'cpv', 'cpc', 'cpa', 'roas'].map(key => (
          <div key={key}>
            <Checkbox
              id={key}
              color="primary"
              checked={impactColumns.includes(key as BudgetOptImpactKeys)}
              onChange={() => toggleCheckbox(key as BudgetOptImpactKeys)}
            />
            <label htmlFor={key}>{key}{['vtr', 'ctr', 'cvr', 'roas'].includes(key) ? '%' : ''}</label>
          </div>
        )) }
      </Popper>
    </div>
  )
}

export default BudgetResultTable