import { useTranslation } from 'react-i18next';
import { SpaAnomalyDetectionColumn } from '@models/anomalyDetection'

import './DetectionTableFormatter.scss';

export default class SpaDetectionTableFormatter {
  field: string
  order: 'asc' | 'desc'

  constructor() {
    this.field = ''
    this.order = 'asc'
  }
  getColumnFormat = (
    orderBy?: string | undefined,
    sorting?: string | undefined
  ): Array<SpaAnomalyDetectionColumn> => {
    const { t } = useTranslation();

    const columnAnomalyType = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.type'),
        field: 'anomalyType',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'anomalyType' ? this.order : undefined,
        customSort: (a, b) => {
          return a.spaType.localeCompare(b.spaType)
        },
        cellStyle: { width: '10%' },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <p className={`chip ${rowData.spaType}`}>
                { t(`common.code.saShoppingType.${rowData.spaType}`) }
              </p>
            </div>
          )
        }
      } as SpaAnomalyDetectionColumn
    }

    const columnCampaign = () => {
      return {
        title: t('anomalyDetection.columnHeader.spa.campaign'),
        field: 'campaign',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'campaign' ? this.order : undefined,
        customSort: (a, b) => a.campaignName.localeCompare(b.campaignName),
        cellStyle: { width: '18%' },
        render: (rowData) => {
          return (
            <div className='cell-body-box name'>
              <span className='title'>{rowData.campaignName}</span>
              <span className='id'>{rowData.campaignId}</span>
            </div>
          )
        }
      } as SpaAnomalyDetectionColumn
    }

    const columnAdgroup = () => {
      return {
        title: t('anomalyDetection.columnHeader.spa.adgroup'),
        field: 'adgroupName',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'adgroupName' ? this.order : undefined,
        customSort: (a, b) => a.adgroupName.localeCompare(b.adgroupName),
        cellStyle: { width: '18%' },
        render: (rowData) => {
          return (
            <div className='cell-body-box name'>
              <span className='title'>{rowData.adgroupName}</span>
              <span className='id'>{rowData.adgroupId}</span>
            </div>
          )
        }
      } as SpaAnomalyDetectionColumn
    }

    const columnAd = () => {
      return {
        title: t('anomalyDetection.columnHeader.spa.ad'),
        field: 'productName',
        sorting: true,
        defaultSort: this.field === 'productName' ? this.order : undefined,
        customSort: (a, b) => a.productName.localeCompare(b.productName),
        align: 'center',
        cellStyle: { width: '24%' },
        render: (rowData) => {
          return (
            <div className='cell-body-box name'>
              <span className='title'>{rowData.productName}</span>
              <span className='id'>{rowData.adId}</span>
            </div>
          )
        }
      } as SpaAnomalyDetectionColumn
    }

    const columnProductURL = () => {
      return {
        title: t('anomalyDetection.columnHeader.spa.productUrl'),
        field: 'url',
        align: 'center',
        sorting: false,
        cellStyle: { width: '30%' },
        render: (rowData) => {
          const openUrl = () => {
            window.open(rowData.mallProductUrl, "_blank", "noreferrer")
          }
          return (
            <div className='cell-body-box url' onClick={openUrl}>
              { decodeURIComponent(rowData.mallProductUrl) }
            </div>
          )
        }
      } as SpaAnomalyDetectionColumn
    }

    const columns: SpaAnomalyDetectionColumn[] = [
      columnAnomalyType(),
      columnCampaign(),
      columnAdgroup(),
      columnAd(),
      columnProductURL(),
    ];

    return columns;
  };
}
