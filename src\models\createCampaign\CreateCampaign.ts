export interface ContextMenuFunctions {
  requestDelete?: (campaignId: number) => void
}
export enum CAMPAIGN_CREATION_STATUS {
  SUCCESS = 'SUCCESS',  
  PENDING = 'PENDING',
  PARTIAL_FAIL = 'PARTIAL_FAIL',
  FAIL = 'FAIL'
}

export enum DEVICE_TYPE {
  PC = 'PC',
  MOBILE = 'MOBILE',
  ALL = 'ALL'
}
export enum AD_REVIEW_STATUS {
  PENDING='PENDING',
  UNDER_REVIEW='UNDER_REVIEW',
  DENIED='DENIED',
  APPROVED='APPROVED',
  PARTIAL_DENIED='PARTIAL_DENIED',
}
export interface CampaignInfo {
  requestId: number,  
  campaignName: string,
  mediaType: string,
  customerName: string,
  businessChannelName: string,
  useDailyBudgetYn: string,
  dailyBudget: number,
  device: DEVICE_TYPE,
  campaignCreationStatus: CAMPAIGN_CREATION_STATUS,
  adgroupCreationStatus: CAMPAIGN_CREATION_STATUS,
  adsCreationStatus: CAMPAIGN_CREATION_STATUS,
  adsReviewStatus: AD_REVIEW_STATUS,
  adgroupCreationSuccessCount: number,
  adgroupCreationFailCount: number,
  adsCreationSuccessCount: number,
  adsCreationFailCount: number,
  adsApprovedCount: number,
  adsPendingCount: number,
  adsUnderReviewCount: number,
  adsEligibleCount: number,
  adsDeniedCount: number,
  customerId:number
}

export interface CampaignListState {
  campaigns: CampaignInfo[]
  totalCount: number
}

export interface CampaignListFilter {
  pageIndex: number
  pageSize: number
  orderBy?: string
  sorting?: string
  searchKeyword?: string
}

export interface CampaignFormData {
  basicInfo: CampaignBasicInfo
  selectedProducts: ProductData[]
  mediaAccount: NaverAdsNaverCommerceAccount
  businessChannel: BusinessChannel
}

export interface CampaignParams {
  campaignName: string
  customerId: string
  advertiserId: string
  bizChannelId: string
  useDailyBudget: UseDailyBudgetTypeEnum
  dailyBudget: number
  device: string
  products: string[]
}

export interface CampaignBasicInfo {
  campaignName: string
  useDailyBudget: UseDailyBudgetTypeEnum
  dailyBudget?: number
  deviceType: DeviceTypeEnum
}

export type FilterControls = {
  productCondition: string
  adStatus: string
}

export type FilterDebounceControls = {
  category: string
  minPrice: number
  maxPrice: number
  searchKeyword: string
}

export type NaverAdsNaverCommerceAccount = {
  customerId: string
  customerName: string
  sellerAccountId: string
  sellerAccountName: string
}

export enum ProductStatus {
  ALL = 'ALL',
  WAIT = 'WAIT',
  SALE = 'SALE',
  OUTOFSTOCK = 'OUTOFSTOCK',
  UNADMISSION = 'UNADMISSION',
  REJECTION = 'REJECTION',
  SUSPENSION = 'SUSPENSION',
  CLOSE = 'CLOSE',
  PROHIBITION = 'PROHIBITION',
  DELETE = 'DELETE',
  NOHISTORY = 'NOHISTORY'
}

export enum AdvertisingStatus {
  ALL = 'ALL',
  ELIGIBLE = 'ON',
  INELIGIBLE = 'OFF',
  NOHISTORY = 'NOHISTORY'
}

export enum AdInspectStatusEnum {
  APPROVED = 'APPROVED',
  ELIGIBLE = 'ELIGIBLE',
  UNDER_REVIEW = "UNDER_REVIEW",
  PENDING = "PENDING",
  DENIED = "DENIED",
  LIMITED_ELIGIBLE = "LIMITED_ELIGIBLE",
  PAUSED = "PAUSED",
  DELETED = "DELETED"
}

export enum BusinessChannelStatus {
  ELIGIBLE = 'ELIGIBLE'
}

export const productStatusValues = Object.values(ProductStatus)

export const advertisingStatusValues = Object.values(AdvertisingStatus)

export const MAX_PRODUCTS = 1000

export const MIN_BUDGET = 50

export const MAX_BUDGET = **********

export interface ProductData {
  productId: string
  productName: string
  categoryName: string
  price: number
  stock: number
  adRevenue: number
  totalRevenue: number
  status: ProductStatus
  adStatus: AdvertisingStatus | null
  adInspectStatus: AdInspectStatusEnum | null
  productImageUrl?: string
}

export enum UseDailyBudgetTypeEnum {
  LIMITED = 'Y',
  UNLIMITED = 'N'
}

export enum DeviceTypeEnum {
  ALL = 'ALL',
  PC = 'PC',
  MOBILE = 'MOBILE'
}

export type BusinessChannel = {
  businessChannelId: string
  businessChannelName: string
  status: BusinessChannelStatus
}

export interface CampaignListParams {
  campaignName?: string
  pageIndex?: number
  pageSize?: number
  creationStatus?: CAMPAIGN_CREATION_STATUS
  adReviewStatus?: AD_REVIEW_STATUS
}

export interface CampaignListRes {
  totalCount: number
  configurations: CampaignInfo[]
}

export interface FormErrors {
  campaignName: string;
  dailyBudget: string

}
