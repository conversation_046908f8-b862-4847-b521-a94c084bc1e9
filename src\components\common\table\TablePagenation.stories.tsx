import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import TablePagination from './TablePagination';
import { action } from '@storybook/addon-actions';
import { useState } from 'react';
import { pageSizeOptions, chartPageSizeOptions } from '@models/common/CommonConstants';

// Predefined page size options
const predefinedPageOptions = {
  'default (10, 20, 50, 100)': pageSizeOptions,
  'chart (10, 20)': chartPageSizeOptions,
  'small (5, 10, 15)': [5, 10, 15],
  'large (25, 50, 75, 100)': [25, 50, 75, 100],
  'custom (10, 25, 50, 100, 200)': [10, 25, 50, 100, 200],
};

const meta: StorybookMeta<typeof TablePagination> = {
  title: 'Components/Common/Table/TablePagination',
  component: TablePagination,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    page: {
      control: 'number',
      description: 'Current page number (starting from 1)',
    },
    rowsPerPage: {
      control: 'select',
      options: pageSizeOptions,
      description: 'Number of rows to display per page',
    },
    totalCount: {
      control: 'number',
      description: 'Total number of data items',
    },
    onPageChange: {
      action: 'onPageChange',
      description: 'Callback function called when page changes',
    },
    onRowsPerPageChange: {
      action: 'onRowsPerPageChange',
      description: 'Callback function called when rows per page changes',
    },
    showFirstLastPageMove: {
      control: 'boolean',
      description: 'Whether to show first/last page navigation buttons',
    },
    pageOptions: {
      control: 'select',
      options: Object.keys(predefinedPageOptions), 
      mapping: predefinedPageOptions,
      description: 'Page size options array (number[] type) - e.g., [10, 20, 50, 100]',
      table: {
        type: { 
          summary: 'number[]',
          detail: 'Array of numbers representing page size options' 
        },
        defaultValue: { 
          summary: '[10, 20, 50, 100]',
          detail: 'pageSizeOptions from CommonConstants' 
        },
      },
    },
    id: {
      control: 'text',
      description: 'Component ID',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic story
export const Default: Story = {
  args: {
    page: 1,
    rowsPerPage: 20,
    totalCount: 100,
    onPageChange: action('page-changed'),
    onRowsPerPageChange: action('rows-per-page-changed'),
  },
};

// With first/last buttons
export const WithFirstLastButtons: Story = {
  args: {
    page: 5,
    rowsPerPage: 10,
    totalCount: 200,
    showFirstLastPageMove: true,
    onPageChange: action('page-changed'),
    onRowsPerPageChange: action('rows-per-page-changed'),
  },
};

// Custom page size options
export const CustomPageSizes: Story = {
  args: {
    page: 1,
    rowsPerPage: 25,
    totalCount: 500,
    pageOptions: [25, 50, 75, 100],
    onPageChange: action('page-changed'),
    onRowsPerPageChange: action('rows-per-page-changed'),
  },
};

// Various page options example
export const PageOptionsShowcase: Story = {
  render: () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '30px', width: '600px' }}>
        <div>
          <h4>Default pageSizeOptions: {JSON.stringify(pageSizeOptions)}</h4>
          <TablePagination
            page={1}
            rowsPerPage={20}
            totalCount={100}
            onPageChange={action('default-page-changed')}
            onRowsPerPageChange={action('default-rows-changed')}
          />
        </div>
        
        <div>
          <h4>Chart chartPageSizeOptions: {JSON.stringify(chartPageSizeOptions)}</h4>
          <TablePagination
            page={1}
            rowsPerPage={10}
            totalCount={50}
            pageOptions={chartPageSizeOptions}
            onPageChange={action('chart-page-changed')}
            onRowsPerPageChange={action('chart-rows-changed')}
          />
        </div>
        
        <div>
          <h4>Custom pageOptions: {JSON.stringify([5, 15, 30, 60])}</h4>
          <TablePagination
            page={1}
            rowsPerPage={15}
            totalCount={200}
            pageOptions={[5, 15, 30, 60]}
            onPageChange={action('custom-page-changed')}
            onRowsPerPageChange={action('custom-rows-changed')}
          />
        </div>
      </div>
    );
  },
};

// Interactive example using state
export const Interactive: Story = {
  render: () => {
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(20);
    const totalCount = 350;

    return (
      <div style={{ padding: '20px' }}>
        <div style={{ marginBottom: '20px', color: '#666' }}>
          Showing {((page - 1) * rowsPerPage) + 1} - {Math.min(page * rowsPerPage, totalCount)} of {totalCount}
        </div>
        <TablePagination
          page={page}
          rowsPerPage={rowsPerPage}
          totalCount={totalCount}
          onPageChange={(newPage) => {
            setPage(newPage);
            action('page-changed')(newPage);
          }}
          onRowsPerPageChange={(newRowsPerPage) => {
            setRowsPerPage(newRowsPerPage);
            setPage(1); // Reset to first page when rows per page changes
            action('rows-per-page-changed')(newRowsPerPage);
          }}
          showFirstLastPageMove={true}
        />
      </div>
    );
  },
};

// Large dataset example
export const LargeDataSet: Story = {
  args: {
    page: 50,
    rowsPerPage: 100,
    totalCount: 10000,
    showFirstLastPageMove: true,
    onPageChange: action('page-changed'),
    onRowsPerPageChange: action('rows-per-page-changed'),
  },
};

// Small dataset example
export const SmallDataSet: Story = {
  args: {
    page: 1,
    rowsPerPage: 10,
    totalCount: 15,
    onPageChange: action('page-changed'),
    onRowsPerPageChange: action('rows-per-page-changed'),
  },
};

export const PageExample: Story = {
  render: () => {
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(20);
    const mockFilteredItems = Array.from({ length: 87 }, (_, i) => ({
      id: i + 1,
      name: `OAuth Account ${i + 1}`,
    }));

    return (
      <div style={{ width: '600px', padding: '20px' }}>
        <h3 style={{ marginBottom: '10px' }}>Page Example</h3>
        <div style={{ marginBottom: '20px' }}>
        {/* Table content simulation */}
          <div style={{ border: '1px solid #e0e0e0', borderRadius: '4px', padding: '10px', minHeight: '200px' }}>
            {mockFilteredItems
              .slice((page - 1) * rowsPerPage, page * rowsPerPage)
              .map((item) => (
                <div key={item.id} style={{ padding: '8px', borderBottom: '1px solid #f0f0f0' }}>
                  {item.name}
                </div>
              ))}
          </div>
        </div>
        <TablePagination
          totalCount={mockFilteredItems.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={(page) => setPage(page)}
          onRowsPerPageChange={(rowsPage) => setRowsPerPage(rowsPage)}
        />
      </div>
    );
  },
};