import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import './ProductTable.scss'
import { ProductData } from '@models/createCampaign/CreateCampaign'
import { stripHtmlTags } from '@utils/StringUtil'
import VirtualizedTable from '@components/common/table/VirtualizedTable'
import { convertAdReviewStatus, convertAdStatus, convertProductStatus, isProductEnabled } from '@utils/createCampaign'

interface ProductTableProps {
  products?: ProductData[]
  selectedProducts: string[]
  onProductSelection: (product: ProductData) => void
  onSelectAll: () => void
  onDeselectAll: () => void
  isLoading?: boolean // Add loading prop
  // Remove pagination-related props as VirtualizedTable doesn't support them
}

const ProductTable: React.FC<ProductTableProps> = ({
  products,
  selectedProducts,
  onProductSelection,
  onSelectAll,
  onDeselectAll,
  isLoading = false
}) => {
  const { t } = useTranslation()

  const [currentSort, setCurrentSort] = useState<{ field: keyof ProductData; order: 'asc' | 'desc' }>({ field: 'price', order: 'desc' })
  
  const sortFields = ['stock', 'price', 'adRevenue', 'totalRevenue']

 const compareAmount = (a: ProductData, b: ProductData): number => {
    const valA = a[currentSort.field] as number;
    const valB = b[currentSort.field] as number;

    if (currentSort.order === "asc") {
      return valA - valB;
    } else if (currentSort.order === "desc") {
      return valB - valA;
    }
    return 0;
  };

  products?.sort(compareAmount);

  const onOrderChange = (field: keyof ProductData) => {
    setCurrentSort(prev => ({
      field: field,
      order: prev.field !== field ? 'desc' : prev.order === 'desc' ? 'asc' : 'desc'
    }))
  }
  
  const columns = [
    {
      key: 'productImageUrl',
      label: t('createCampaign.label.productTable.productImage'),
      align: 'center' as const,
      width: 150,
      render: (product: ProductData) => (
        <div className="product-image">
          <img src={product.productImageUrl} alt="product" />
        </div>
      )
    },
    {
      key: 'productId',
      label: t('createCampaign.label.productTable.id'),
      align: 'center' as const,
      width: 120
    },
    {
      key: 'categoryName',
      label: t('createCampaign.label.productTable.category'),
      align: 'center' as const,
      // Flexible column - will expand to fill available space
    },
    {
      key: 'productName',
      label: t('createCampaign.label.productTable.productName'),
      align: 'center' as const,
      // Flexible column - will expand to fill available space
      render: (product: ProductData) => stripHtmlTags(product.productName)
    },
    {
      key: 'price',
      label: t('createCampaign.label.productTable.price'),
      align: 'right' as const,
      width: 120,
      defaultSort: "desc",
      render: (product: ProductData) =>
        `${product.price.toLocaleString('ko-KR')} ${t('createCampaign.createModal.productFilter.currency')}`,
    },
    {
      key: 'stock',
      label: t('createCampaign.label.productTable.inventory'),
      align: 'right' as const,
      width: 130
    },
    {
      key: 'adRevenue',
      label: t('createCampaign.label.productTable.advertisingRevenue'),
      align: 'right' as const,
      width: 140,
      render: (product: ProductData) =>
        `${product.adRevenue.toLocaleString('ko-KR')} ${t('createCampaign.createModal.productFilter.currency')}`,
    },
    {
      key: 'totalRevenue',
      label: t('createCampaign.label.productTable.totalRevenue'),
      align: 'right' as const,
      width: 150,
      render: (product: ProductData) =>
        `${product.totalRevenue.toLocaleString('ko-KR')} ${t('createCampaign.createModal.productFilter.currency')}`,
    },
    {
      key: 'status',
      label: t('createCampaign.label.productTable.productStatus'),
      align: 'center' as const,
      width: 150,
      render: (product: ProductData) =>
        convertProductStatus(product.status, t),
    },
    {
      key: 'adInspectStatus',
      label: t('createCampaign.label.productTable.adInspectStatus'),
      align: 'center' as const,
      width: 150,
      render: (product: ProductData) => convertAdReviewStatus( product.adInspectStatus , t)
    },
    {
      key: 'adStatus',
      label: t('createCampaign.label.productTable.advertisingStatus'),
      align: 'center' as const,
      width: 100,
      render: (product: ProductData) => convertAdStatus( product.adStatus, t)
    }
  ]

  const handleItemSelection = (productId: string) => {
    const product = products?.find((p) => p.productId === productId)

    if (product && isProductEnabled(product)) {
      onProductSelection(product)
    }
  }

  const handleSelectAll = () => {
    onSelectAll()
  }

  const handleDeselectAll = () => {
    onDeselectAll()
  }

  return (
    <div className="product-table-container flex-grow font-pretendard">
      <VirtualizedTable
        data={products || []}
        columns={columns}
        getItemId={(product: ProductData) => product.productId}
        isItemDisabled={(product: ProductData) => !isProductEnabled(product)}
        tableOptions={{
          height: '100%',
          width: '100%',
          rowHeight: 50,
          headerHeight: 50,
          enableHorizontalScroll: true,
          stickyHeader: true,
          className: 'border border-gray-300 rounded-lg'
        }}
        selectionOptions={{
          showCheckbox: true,
          selectedItems: selectedProducts,
          onItemSelection: handleItemSelection,
          onSelectAll: handleSelectAll,
          onDeselectAll: handleDeselectAll
        }}
        messageOptions={{
          emptyMessage: t('createCampaign.message.noProducts'),
          loadingMessage: t('common.message.loading')
        }}
        isLoading={isLoading}
        sortFields={sortFields}
        currentSort={currentSort}
        onOrderChange={onOrderChange}
      />
    </div>
  )
}

export default ProductTable
