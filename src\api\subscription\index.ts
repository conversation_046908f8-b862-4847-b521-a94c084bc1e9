import { callApi, Method } from '@utils/ApiUtil';
import { Service } from "@models/common/Service";
import * as SubscriptionModel from "@models/subscription";
import CommonResponse from '@models/common/CommonResponse';


export const createCustomerSession = async (advertiserId: string, isLoading = true) => {
  const response: CommonResponse<SubscriptionModel.CustomerSessionResponse> = await callApi({
    service: Service.MOP_BE, 
    url: `/v1/stripe/customer-session/${advertiserId}`,
    method: Method.POST,
    config: { isLoading }
  });

  return response;
};