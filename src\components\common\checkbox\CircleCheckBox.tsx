import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@assets/icon/round-checkbox.svg';
import React from 'react'
import './CircleCheckBox.scss'

interface Props {
  label?: string|React.ReactNode
  fill?: string
  size?: number
  isChecked: boolean
  onChange: () => void
}

const CircleCheckBox = ({ label, fill = '#5E81F4', size = 18, isChecked, onChange }: Props) => {
  return (
    <div className="circle-check-box" style={{ '--fill': fill } as React.CSSProperties}>
      <input
        type="checkbox"
        id="checkbox"
        className="hidden"
        checked={isChecked}
        onChange={onChange}
      />
      <label
        htmlFor="checkbox"
        className={`
          ${isChecked ? 'checked' : null}
          flex gap-1.5 items-center text-primary-300 font-medium text-sm
        `}>
        <RoundCheckBox width={size} height={size} />
        {label ? label : null}
      </label>
    </div>
  )
}

export default CircleCheckBox