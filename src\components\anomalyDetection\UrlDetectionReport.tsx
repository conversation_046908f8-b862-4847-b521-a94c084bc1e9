import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';

import { downloadUrlAnomalyDetection } from '@api/dashboard/AbnomalyDetection'
import DetectionHeader from '@components/anomalyDetection/DetectionHeader';
import UrlDetectionTableFilter from '@components/anomalyDetection/UrlDetectionTableFilter';
import UrlDetectionTable from '@components/anomalyDetection/UrlDetectionTable';

import { useToast } from "@hooks/common";

interface Props {
  advertiserId: number;
  isEmpty: boolean;
}

const UrlDetectionReport: React.FC<Props> = ({advertiserId, isEmpty}: Props): ReactElement => {
  const { openToast } = useToast()
  const { t } = useTranslation();
  const downloadData = async () => {
    try {
      await downloadUrlAnomalyDetection(advertiserId);
      openToast(t('anomalyDetection.message.download.success'))
    } catch (err) {
      openToast(t('anomalyDetection.message.download.failed'))
    }
  }
  return (
    <>
      <DetectionHeader
        showDownload={!isEmpty}
        downloadFn={downloadData}
        tooltipId="url"
        tooltipHtml={t('anomalyDetection.tooltip.url')}
        headTitle={t('anomalyDetection.reportTitle.url')}
        subTitle={``}
      />
      <UrlDetectionTableFilter />
      <UrlDetectionTable />
    </>
  )
}

export default UrlDetectionReport;