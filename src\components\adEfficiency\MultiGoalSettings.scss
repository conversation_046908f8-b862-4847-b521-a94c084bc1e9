#budget-opt-config-multi-goal {
  display: flex;
  justify-content: space-between;
  .kpi-label {
    .MuiFormControlLabel-label {
      padding-left: 6px;
      font-size: 16px;
      font-weight: 400;
      color: var(--point_color);
    }
    .MuiButtonBase-root {
      width: 10px;
      height: 10px;
      .MuiIconButton-label {
        svg {
          display: none;
        }
        &::before {
          content: '';
          width: 10px;
          height: 10px;
          position: absolute;
          top: 0;
          left: 0;
          border: 1px solid #dedfe4;
          background-color: #fff;
          box-sizing: content-box;
        }
      }
      &.Mui-checked {
        .MuiIconButton-label::after {
          content: '';
          position: absolute;
          top: 2px;
          left: 2px;
          width: 8px;
          height: 8px;
          background-color: var(--status-active);
        }
        &.Mui-disabled .MuiIconButton-label::after {
          background-color: #bdbdbd;
        }
      }
    }
  }

  .MuiFormControlLabel-root {
    margin: 0px;
  }

  .MuiCheckbox-root {
    display: flex;
    align-items: center;
    padding: 0;
    color: #dedfe4;
  }

  svg {
    width: 16px;
    height: 16px;
  }

  .Mui-checked {
    svg {
      fill: var(--status-active);
    }
  }

  .MuiButtonBase-root {
    &.MuiIconButton-colorSecondary {
      .Mui-checked {
        color: var(--status-active);
      }
      &:hover {
        background-color: rgba(51, 134, 200, 0.04);
      }
    }

    .MuiFormControlLabel-label {
      width: 90px;
      font-size: 13px;
      font-weight: 400;
      color: #8285a3;
    }

    .MuiInputBase-root {
      width: 160px;
      height: 24px;
      font-size: 13px;
      font-weight: 300;
      color: var(--point_color);
      border-radius: 11px;
      background-color: var(--bg-gray-light);

      input {
        height: 23px;
        padding: 1px 0px 1px 15px;
        width: 100%;
        text-align: right;

        p {
          font-size: 13px;
          color: var(--point_color);
        }
      }

      fieldset {
        border: none;
      }

      &.MuiInput-underline {
        &::before,
        &::after {
          display: none;
        }
      }

      .MuiInputBase-input {
        font-size: 14px;
        font-weight: 300;
        color: var(--point_color);
        box-sizing: border-box;

        &:focus {
          background: none;
        }
      }
    }
  }
}
