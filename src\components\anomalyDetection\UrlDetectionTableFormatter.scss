#UrlAnomalyDetectionTable {
  & > * {
    box-shadow: none;
    transition: none;
  }
  .MuiTableBody-root {
    border-bottom: 1px solid var(--mop20-filter-toggle-bg);
  }

  .MuiTableCell-root.MuiTableCell-head {
    background-color: var(--bg-table-main);
    border-top: 1px solid var(--border-table-main);
    border-bottom: 1px solid var(--border-table-main);
    color: var(--text-base);
    font-size: 16px;
    padding: 16px 0;
  }
  .MuiTableRow-root {
    .MuiTableCell-root.MuiTableCell-body {
      padding: 0;
      border-bottom: none;
    }
  }
  .cell-body-box {
    width: 100%;
    padding: 16px 0;
    color: var(--text-base);
    &.media[data-title] {
      position: relative;
      &:hover:after {
        opacity: 1;
        transition: all 0.1s ease 0.5s;
        visibility: visible;
      }
      &:after {
        content: attr(data-title);
        color: var(--point_color);
        background-color: white;
        position: absolute;
        padding: 4px 8px;
        bottom: -11px;
        font-size: 12px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        opacity: 0;
        border: 1px solid var(--point_color);
        border-radius: 9999px;
        z-index: 99999;
        visibility: hidden;
      }
    }

    &.name {
      padding: 4px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .title {
        font-weight: 700;
      }
    }
    &.url {
      padding: 4px 16px;
      text-align: left;
    }
    .chip {
      border-radius: 6px;
      padding: 4px 16px;
      margin: 0 auto;
      width: fit-content;
      &.keyword {
        background-color: #e2f6ec;
        color: #1da152;
      }
      &.ad {
        background-color: #e8f2fe;
        color: var(--color-active-blue);
      }
    }
  }

  .MuiTableBody-root .MuiTableRow-root:last-child {
    .cell-body-box.media[data-title]:after {
      bottom: unset;
      top: -11px;
    }
  }
}
