import { memo, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { MopSearch, MopSwitch } from '@components/common'
import MopSelect from '@components/common/mopUI/MopSelect'
import { useToast } from '@hooks/common'
import useDebounce from '@hooks/common/useDebounce'
import { InputAdornment } from '@material-ui/core'
import {
  AdvertisingStatus,
  BusinessChannel,
  FilterControls,
  FilterDebounceControls,
  MAX_PRODUCTS,
  NaverAdsNaverCommerceAccount,
  ProductData,
  ProductStatus,
} from '@models/createCampaign/CreateCampaign'
import './ProductListFilterControl.scss'
import ProductTable from './ProductTable'
import { getProducts } from '@api/creationCampaign'
import { isProductEnabled } from '@utils/createCampaign'
import { MuiInput } from '@components/common/mopUI'

interface Props {
  mediaSelected: NaverAdsNaverCommerceAccount
  chanelSelected: BusinessChannel
  onSetSelectedProducts: (products: ProductData[]) => void
}

const initFilters = {
  productCondition: '',
  adStatus: ''
}

const initFiltersDebounce: FilterDebounceControls = {
  category: '',
  minPrice: 0,
  maxPrice: 0,
  searchKeyword: ''
}

const ProductListFilterControl: React.FC<Props> = memo(
  ({ mediaSelected, chanelSelected, onSetSelectedProducts }: Props) => {
    const { t } = useTranslation()
    const { openToast } = useToast()

    const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(new Set());

    const [filters, setFilters] = useState<FilterControls>(initFilters)
    const [filterInputs, setFilterInputs] = useState<FilterDebounceControls>(initFiltersDebounce)
    const { debounceValue: debouncedFilterInput, flush } = useDebounce(filterInputs, 300)
    const [switchSelectedProducts, setSwitchSelectedProducts] = useState(false)

    const [products, setProducts] = useState<ProductData[]>([])
    const [isLoading, setIsLoading] = useState(false)

    const optionsProductStatus = [
      { value: ProductStatus.ALL, label: t('createCampaign.createModal.statusFilter.all') },
      { value: ProductStatus.WAIT, label: t('createCampaign.createModal.statusFilter.wait') },
      { value: ProductStatus.SALE, label: t('createCampaign.createModal.statusFilter.sale') },
      { value: ProductStatus.OUTOFSTOCK, label: t('createCampaign.createModal.statusFilter.outOfStock') },
      { value: ProductStatus.UNADMISSION, label: t('createCampaign.createModal.statusFilter.unAdmission') },
      { value: ProductStatus.REJECTION, label: t('createCampaign.createModal.statusFilter.rejection') },
      { value: ProductStatus.SUSPENSION, label: t('createCampaign.createModal.statusFilter.suspension') },
      { value: ProductStatus.CLOSE, label: t('createCampaign.createModal.statusFilter.close') },
      { value: ProductStatus.PROHIBITION, label: t('createCampaign.createModal.statusFilter.prohibition') },
      { value: ProductStatus.DELETE, label: t('createCampaign.createModal.statusFilter.delete') },
      { value: ProductStatus.NOHISTORY, label: t('createCampaign.createModal.statusFilter.noHistory') }
    ];

    const optionsAdvertisingStatus = [
      { value: AdvertisingStatus.ALL, label: t("createCampaign.createModal.adStatusFilter.all") },
      { value: AdvertisingStatus.ELIGIBLE, label: t("createCampaign.createModal.adStatusFilter.on") },
      { value: AdvertisingStatus.INELIGIBLE, label: t("createCampaign.createModal.adStatusFilter.off") },
      { value: AdvertisingStatus.NOHISTORY, label: t("createCampaign.createModal.adStatusFilter.noHistory") },
    ]

    const getProductList = async () => {
      try {
        setIsLoading(true)
        const response = await getProducts(mediaSelected.customerId, mediaSelected.sellerAccountId, chanelSelected.businessChannelId)
        setProducts(response ?? [])
      } catch (error) {
        openToast(t('common.message.systemError'))
      } finally {
        setIsLoading(false)
      }
    }

    useEffect(() => {
      if (mediaSelected.customerId && chanelSelected.businessChannelId) {
        getProductList()
        setFilters(initFilters)
        setFilterInputs(initFiltersDebounce)
        setSwitchSelectedProducts(false)
        setSelectedProductIds(new Set())
        onSetSelectedProducts([])
      }
    }, [mediaSelected.customerId, chanelSelected.businessChannelId])

    const { productCondition, adStatus } = filters
    const { category, searchKeyword, minPrice, maxPrice } = debouncedFilterInput

    const normalizedKeyword = searchKeyword.toLowerCase()
    const normalizedCategory = category.toLowerCase()
    const min = minPrice ? minPrice : null
    const max = maxPrice ? maxPrice : null


    const matchesFilter = (product: ProductData) => {
      const matchesSearch =
        product.productName.toLowerCase().includes(normalizedKeyword) ||
        product.productId.toLowerCase().includes(normalizedKeyword)

      const matchesPriceMin = min !== null ? product.price >= min : true
      const matchesPriceMax = max !== null ? product.price <= max : true

      const matchesCondition =
        productCondition === ProductStatus.NOHISTORY
          ? product.status === null
          : productCondition && productCondition !== ProductStatus.ALL
            ? product.status === productCondition
            : true

      const matchesAdStatus =
        adStatus === AdvertisingStatus.NOHISTORY
        ? product.adStatus === null
        : adStatus && adStatus !== AdvertisingStatus.ALL ? product.adStatus === adStatus : true

      const matchesCategory =
        product.categoryName.toLowerCase().includes(normalizedCategory)

      return matchesSearch && matchesPriceMin && matchesPriceMax &&
        matchesCondition && matchesAdStatus && matchesCategory
    }

    const filteredProducts = useMemo(() => {
      return products.filter(matchesFilter)
    }, [products, normalizedKeyword, normalizedCategory, min, max, productCondition, adStatus])


    const selectedProducts = useMemo(() => {
      if (selectedProductIds.size === 0) {
        return [];
      }
      return products.filter(p => selectedProductIds.has(p.productId));
    }, [products, selectedProductIds]);

    useEffect(() => {
      onSetSelectedProducts(selectedProducts);
    }, [selectedProducts, onSetSelectedProducts]);

    const displayedProducts = switchSelectedProducts ? selectedProducts : filteredProducts;

    const enabledProductIds = useMemo(() => {
      return products
        .filter(isProductEnabled)
        .map(p => p.productId);
    }, [products]);

    const handleSelectAllProducts = () => {
      setSelectedProductIds(new Set(enabledProductIds));
    };

    const handleDeselectAllProducts = () => {
      setSelectedProductIds(new Set());
    };

    const handleProductSelection = (product: ProductData) => {
      setSelectedProductIds(prev => {
        const newSelected = new Set(prev);

        if (newSelected.has(product.productId)) {
          newSelected.delete(product.productId);
        } else {
          if (newSelected.size >= MAX_PRODUCTS) {
            openToast(t('createCampaign.createModal.toast.maxProducts'));
          }
          newSelected.add(product.productId);
        }

        return newSelected;
      });
    };

    const handleFilterFieldChange = (name: string, value: string) => {
      setFilters((prev) => ({
        ...prev,
        [name]: value
      }))
    }

    const handleFilterFieldDebounceChange = (name: string, value: string) => {
      setFilterInputs((prev) => ({
        ...prev,
        [name]: value
      }))
    }

    return (
      <>
        <div id="product-list-filter-control-wrapper" className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="text-[20px] font-bold leading-[30px] text-[#333333] font-pretendard">
              {t('createCampaign.createModal.productFilter.label')}
            </div>
            <div className="px-4 py-1 rounded-3xl bg-campaign-background">
              <span className={`font-bold ${selectedProducts.length > MAX_PRODUCTS ? 'text-red-allert' : ''} `}>
                {selectedProducts.length}
              </span>
              <span className="font-light">{`/${products.length}`}</span>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="view-selected-products">{t('createCampaign.createModal.viewSelectedProducts')}</div>
            <MopSwitch
              checked={switchSelectedProducts}
              onChange={() => {
                setSwitchSelectedProducts((prev) => !prev)
              }}
            />
          </div>
        </div>
        <div
          className={`flex flex-nowrap bg-campaign-background px-5 py-3 mb-2 gap-3 border border-campaign-border-light border-solid border-1 rounded font-pretendard ${switchSelectedProducts ? 'hidden' : 'block'
            } `}
          id="ProductListFilterControl"
        >
          <div className="flex basis-[20%] flex-col gap-2">
            <div className="product-filter-title">{t('createCampaign.createModal.productFilter.category')} </div>
            <MopSearch
              id="optimizationCategoryInput"
              data-testid="optimizationCategoryInput"
              onChange={(event) => {
                handleFilterFieldDebounceChange('category', event.target.value)
              }}
              onSearch={flush}
              placeholder={t('createCampaign.createModal.productFilter.placeholderCategory')}
              size={16}
              type="commerce"
              value={filterInputs.category}
              visibleIcon
            />
          </div>
          <div className="flex basis-[20%] flex-col gap-2">
            <div className="product-filter-title">{t('createCampaign.createModal.productFilter.keyword')}</div>
            <MopSearch
              id="productKeywordInput"
              data-testid="productKeywordInput"
              onChange={(event) => {
                handleFilterFieldDebounceChange('searchKeyword', event.target.value)
              }}
              onSearch={flush}
              placeholder={t('createCampaign.createModal.productFilter.placeholderKeyword')}
              size={16}
              type="commerce"
              value={filterInputs.searchKeyword}
              visibleIcon
            />
          </div>
          <div className="flex basis-[32%] flex-col gap-2">
            <div className="product-filter-title">{t('createCampaign.createModal.productFilter.price')} </div>
            <div className="flex gap-2 items-end font-pretendard wrapper-input">
              <MuiInput
                hasFocus
                numberFormatProps={{
                  allowNegative: false,
                  decimalSeparator: '.',
                  thousandSeparator: true
                }}
                onChange={(e) => { handleFilterFieldDebounceChange("minPrice", e.target.value) }}
                endAdornment={
                  <InputAdornment position="end">
                    {t('createCampaign.createModal.productFilter.currency')}
                  </InputAdornment>
                }
                useNumberFormat
                value={filterInputs.minPrice === 0 ? '' : filterInputs.minPrice}
              />
              <span className="self-center">~</span>
              <MuiInput
                hasFocus
                numberFormatProps={{
                  allowNegative: false,
                  decimalSeparator: '.',
                  thousandSeparator: true
                }}
                onChange={(e) => { handleFilterFieldDebounceChange("maxPrice", e.target.value) }}
                endAdornment={
                  <InputAdornment position="end">
                    {t('createCampaign.createModal.productFilter.currency')}
                  </InputAdornment>
                }
                useNumberFormat
                value={filterInputs.maxPrice === 0 ? '' : filterInputs.maxPrice}
              />
            </div>
          </div>
          <div className="flex basis-[14%] flex-col gap-2 font-pretendard">
            <div className="product-filter-title">{t('createCampaign.createModal.productFilter.inventory')}</div>
            <MopSelect
              id="select-productCondition"
              data-testid="productConditionSelect"
              options={optionsProductStatus}
              value={filters.productCondition}
              onChange={(newValue) => {
                handleFilterFieldChange('productCondition', newValue as string)
              }}
              placeholder={t('createCampaign.createModal.productFilter.all')}
            />
          </div>
          <div className="flex basis-[14%] flex-col gap-2 font-pretendard">
            <div className="product-filter-title">{t('createCampaign.createModal.productFilter.adStatus')}</div>
            <MopSelect
              id="select-adStatus"
              data-testid="adStatusSelect"
              options={optionsAdvertisingStatus}
              value={filters.adStatus}
              onChange={(newValue) => {
                handleFilterFieldChange('adStatus', newValue as string)
              }}
              placeholder={t('createCampaign.createModal.productFilter.all')}
            />
          </div>
        </div>
        <div className='product-table-wraper'>
          <ProductTable
            products={displayedProducts}
            selectedProducts={Array.from(selectedProductIds)}
            onProductSelection={handleProductSelection}
            onSelectAll={handleSelectAllProducts}
            onDeselectAll={handleDeselectAllProducts}
            isLoading={isLoading}
          />
        </div>
      </>
    )
  }
)
export default ProductListFilterControl
