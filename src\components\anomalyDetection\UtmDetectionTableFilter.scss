#UtmAbnormalLandingUrlModal {
  .search-label-container,
  .search-input-container {
    width: 100%;

    .MuiGrid-item {
      flex: 1;
    }

    .MuiBox-root {
      text-align: center;
      text-transform: uppercase;

      .MuiFormLabel-root {
        font-size: 12px;
        font-weight: 300;
        color: var(--color-white);
      }
    }
  }

  .max-width {
    // display: flex;
    // max-width: 380.75px;
  }
  .search-label {
    width: 100%;
    height: 100%;
    min-width: 0;
    max-width: 380.75px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    border-right: 1px solid var(--point_color);

    .ellipsis {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    &.dropdown {
      padding-right: 44px;
    }

    &-container {
      height: 22px;
      background-color: #575b7c;
      display: flex;
      justify-content: space-around;

      .MuiBox-root {
        letter-spacing: 1px;
      }
    }
  }

  .search-input {
    min-width: 0;
    max-width: 380.75px;
    height: 100%;

    &-select {
      width: 100%;
      height: 100%;

      &.MuiInput-underline:before,
      &.MuiInput-underline:after {
        content: none;
      }

      .MuiSelect-select {
        padding-right: 44px;

        &:focus {
          background-color: transparent;
        }
      }
    }

    &-dropdown {
      height: 100%;
      width: 44px;
      position: absolute;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-left: 1px solid var(--point_color);
      border-right: 1px solid var(--point_color);
      box-sizing: border-box;
      pointer-events: none;

      &-open {
        background-color: var(--point_color);

        svg {
          transform: rotate(180deg);

          path {
            stroke: white;
          }
        }
      }
    }

    &-container {
      height: 36px;
      border: 1px solid var(--point_color);
      border-top: none;

      .MuiGrid-item {
        background-color: #fff;
        color: var(--point_color);

        &:last-child {
          .search-input-dropdown {
            border-right: none;
          }
        }
      }
    }
  }
}

.url-anomaly-filter-popover {
  .MuiPopover-paper {
    margin-left: -1px;
    margin-top: 2px;
    box-sizing: border-box;
  }

  .MuiPopover-paper .MuiMenu-list .MuiMenuItem-root {
    margin-top: 0;
    width: 100%;
    height: auto;
    font-size: 12px !important;
    font-weight: 200 !important;
    color: var(--point_color);
    text-align: center;
    border-radius: 0px;

    &.Mui-selected {
      font-weight: 500 !important;
      background-color: transparent !important;
    }
  }

  .MuiListItem-button:hover {
    font-weight: 500;
  }

  .MuiMenu-list {
    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
  }
  .fix-height {
    height: 34px;
  }
  .filter-item {
    appearance: none;
    border: 0;
    padding: 0;
    background: unset;
    width: 100%;
    font-size: 12px !important;
    font-weight: 200 !important;
    color: var(--point_color);
    text-align: center;
    white-space: pre-line;
    cursor: pointer;
    &.none {
      display: none;
    }
    &.name {
      display: flex;
      flex-direction: column;
      .title {
        font-weight: 700;
        text-align: left;
      }
    }
    &.optName {
      .optName-id {
        font-weight: 700;
        display: inline-block;
        margin-right: 4px;
      }
    }
  }
}
