import React, { ReactElement, useEffect, useState } from 'react';
import { Checkbox, FormControlLabel } from '@material-ui/core';

import { t } from 'i18next';
import { DvKpiType } from '@models/optimization/Kpi';
import './MultiGoalSettings.scss';
import { ActionType } from '@models/common/CommonConstants';

export interface Props {
  kpis: Map<DvKpiType, number>;
  setKpis: (_key: DvKpiType) => void;
  actionType: ActionType;
}

type SettingChecked = {
  [key in DvKpiType]?: boolean;
};

const MultiGoalSettings: React.FC<Props> = ({ kpis, setKpis, actionType }: Props): ReactElement => {
  const [settingChecked, setSettingChecked] = useState<SettingChecked>({
    [DvKpiType.IMPRESSIONS]: false,
    [DvKpiType.VIEWS]: false,
    [DvKpiType.CLICKS]: false,
    [DvKpiType.REVENUE]: false,
    [DvKpiType.CONVERSIONS]: false,
  });

  const disabledKpis = actionType === ActionType.READ;

  const handleKpiCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSettingChecked({ ...settingChecked, [e.target.name]: e.target.checked })
    setKpis(e.target.name as DvKpiType)
  }

  useEffect(() => {
    const existingKpis = { ...settingChecked }
    for(const [kpiKey, _kpiValue] of kpis) {
      existingKpis[kpiKey] = kpis.has(kpiKey)
    }
    setSettingChecked(existingKpis)
  }, []); //eslint-disable-line

  return (
    <section id="budget-opt-config-multi-goal">
      {
        Object.keys(DvKpiType).map((kpi, i) => (
          <FormControlLabel
            key={i}
            className="kpi-label"
            control={
              <Checkbox
                name={kpi}
                checked={settingChecked[kpi as keyof SettingChecked]}
                disabled={disabledKpis}
                onChange={handleKpiCheck}
              />
            }
            label={t(`common.code.kpis.${kpi}`)}
          />
        ))
      }
    </section>
  );
};

export default MultiGoalSettings;
