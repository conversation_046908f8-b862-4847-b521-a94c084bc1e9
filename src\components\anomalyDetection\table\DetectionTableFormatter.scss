#AnomalyDetectionTable {
  & > * {
    box-shadow: none;
    transition: none;
  }
  .MuiTableBody-root {
    border-bottom: 1px solid var(--mop20-table-border);
  }

  .MuiTableCell-root.MuiTableCell-head {
    background-color: var(--bg-table-main);
    border-top: 1px solid var(--border-table-main);
    border-bottom: 1px solid var(--border-table-main);
    color: var(--text-base);
    font-size: 16px;
    padding: 16px 0;
  }
  .MuiTableRow-root {
    .MuiTableCell-root.MuiTableCell-body {
      padding: 0;
      border-bottom: none;
    }
  }
  .cell-body-box {
    width: 100%;
    padding: 16px 0;
    color: var(--text-base);
    &.media[data-title] {
      position: relative;
      &:hover:after {
        opacity: 1;
        transition: all 0.1s ease 0.5s;
        visibility: visible;
      }
      &:after {
        content: attr(data-title);
        color: var(--color-black);
        background-color: white;
        position: absolute;
        padding: 4px 8px;
        bottom: -11px;
        font-size: 12px;
        left: 50%;
        transform: translateX(-50%);
        white-space: nowrap;
        opacity: 0;
        border: 1px solid var(--color-black);
        border-radius: 9999px;
        z-index: 99999;
        visibility: hidden;
      }
    }

    &.name {
      padding: 4px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .title {
        font-weight: 700;
        text-align: left;
      }
    }
    &.url {
      padding: 4px 16px;
      text-align: left;
      overflow-wrap: break-word;
      line-height: 1.2;
      cursor: pointer;
      text-decoration: underline;
    }
    .chip {
      border-radius: 6px;
      padding: 4px 16px;
      margin: 0 auto;
      width: fit-content;
      &.CATALOG,
      &.keyword {
        background-color: #e2f6ec;
        color: #1da152;
      }
      &.SHOPPING,
      &.ad {
        background-color: #e8f2fe;
        color: var(--mop20-active-blue);
      }
    }
  }

  .MuiTableBody-root .MuiTableRow-root:last-child {
    .cell-body-box.media[data-title]:after {
      bottom: unset;
      top: -11px;
    }
  }
}

// common sorting
.MuiTableSortLabel-root {
  .MuiTableSortLabel-icon {
    display: none;
  }

  &.MuiTableSortLabel-active {
    .MuiTableSortLabel-icon {
      position: relative;
      display: inline-block;
      width: 12px;
      height: 10px;
      opacity: 1;
      text-indent: -9999px;
      &::after {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        position: absolute;
        top: 0;
        right: 0;
        border-style: solid;
        border-width: 10px 6px 0px 6px;
        border-color: var(--color-blue-darker) transparent transparent transparent;
      }
    }
  }
}
