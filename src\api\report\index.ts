/* istanbul ignore file */

import { AxiosResponse } from 'axios';
import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import {
  GetReportDetailResponse,
  GetReportQueryParam,
  GetReportTableQueryParam,
  GetReportTableResponse,
  GetReportSummaryResponse,
} from '@models/report/SearchReport';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import { UpdateConfigurationsRequest } from '@models/report/Settings';

interface APIParams {
  url: string;
}

export default class ReportAPI {
  baseURL: string;
  constructor(params: APIParams) {
    this.baseURL = `/v1/report/${params.url}`;
  }

  defaultGetBody = {
    service: Service.MOP_BE,
    method: Method.GET,
  };

  /* istanbul ignore next */
  async getReports(advertiserId: number, isLoading = true) {
    const response: CommonResponse = await callApi({
      ...this.defaultGetBody,
      url: `${this.baseURL}`,
      params: {
        queryParams: {
          advertiserId,
        },
      },
      config: {
        isLoading,
      },
    });

    return response.successOrNot === 'Y' ? response.data : [];
  }

  async getReportSummary(
    reportId: string,
    param: GetReportQueryParam,
    isLoading = true
  ): Promise<GetReportSummaryResponse> {
    const response: CommonResponse = await callApi({
      ...this.defaultGetBody,
      url: `${this.baseURL}/${reportId}/summary`,
      params: {
        queryParams: {
          ...param,
        },
      },
      config: {
        isLoading,
      },
    });
    return response.successOrNot === 'Y' ? response.data : null;
  }

  /* istanbul ignore next */
  async getReportDetail(
    reportId: string,
    param: GetReportQueryParam,
    isLoading = true
  ): Promise<GetReportDetailResponse> {
    const response: CommonResponse = await callApi({
      ...this.defaultGetBody,
      url: `${this.baseURL}/${reportId}/detail`,
      params: {
        queryParams: {
          ...param,
        },
      },
      config: {
        isLoading: isLoading,
      },
    });
    return response.successOrNot === 'Y' ? response.data : null;
  }

  /* istanbul ignore next */
  async getReportTable(
    reportId: string,
    param: GetReportTableQueryParam,
    isLoading = true
  ): Promise<GetReportTableResponse> {
    const response: CommonResponse = await callApi({
      ...this.defaultGetBody,
      url: `${this.baseURL}/${reportId}/table`,
      params: {
        queryParams: {
          ...param,
        },
      },
      config: {
        isLoading,
      },
    });
    return response.successOrNot === 'Y' ? response.data : null;
    // return (response.successOrNot === 'Y' ? response.data : null) as GetReportTableResponse;
  }

  /* istanbul ignore next */
  async downloadRawData(reportId: string, param: GetReportTableQueryParam, isLoading = true) {
    const response: CommonResponse | AxiosResponse = await downloadByteArray({
      ...this.defaultGetBody,
      url: `${this.baseURL}/${reportId}/raw-data`,
      config: {
        isLoading,
      },
      params: {
        queryParams: {
          ...param,
        },
      },
    });

    if (response.data) {
      openDownloadLink(response);

      return null;
    } else {
      return response as unknown as CommonResponse;
    }
  }

  /* istanbul ignore next */
  async getConfiguration<T>(reportId: string, isLoading = true): Promise<T> {
    const response: CommonResponse = await callApi({
      ...this.defaultGetBody,
      url: `${this.baseURL}/${reportId}/configuration`,
      config: {
        isLoading,
      },
    });
    return response.successOrNot === 'Y' ? response.data : null;
  }

  /* istanbul ignore next */
  async updateConfiguration<MediaType>(
    reportId: string,
    params: UpdateConfigurationsRequest<MediaType>,
    isLoading = true
  ) {
    return callApi({
      service: Service.MOP_BE,
      url: `${this.baseURL}/${reportId}/configuration`,
      method: Method.PATCH,
      params: {
        bodyParams: params,
      },
      config: {
        isLoading,
      },
    });
  }

  /* istanbul ignore next */
  async getAdgroups(advertiserId: number, isLoading = true) {
    const response: CommonResponse = await callApi({
      ...this.defaultGetBody,
      url: `${this.baseURL}/adgroups`,
      params: {
        queryParams: {
          advertiserId,
        },
      },
      config: {
        isLoading,
      },
    });
    return response.successOrNot === 'Y' ? response.data.media : [];
  }
}
