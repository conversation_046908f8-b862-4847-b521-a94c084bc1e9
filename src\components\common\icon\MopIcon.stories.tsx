// src/components/common/icon/MopIcon.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import MopIcon from './MopIcon';
import TextIcon from './TextIcon';
import { MOPIcon } from '@models/common';

const meta: StorybookMeta<typeof MopIcon> = {
  title: 'Components/Common/Icon/MopIcon',
  component: MopIcon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'select',
      options: Object.values(MOPIcon),
      description: 'Icon name',
    },
    size: {
      control: { type: 'range', min: 16, max: 128, step: 4 },
      description: 'Icon size (px)',
    },
    bgColor: {
      control: 'color',
      description: 'Background color',
    },
    fill: {
      control: 'color',
      description: 'Fill color',
    },
    isDisabled: {
      control: 'boolean',
      description: 'Disabled state',
    },
    customClass: {
      control: 'text',
      description: 'Custom CSS class',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    name: MOPIcon.STAR,
    size: 32,
  },
};

// SVG icon stories
export const Advice: Story = {
  args: {
    name: MOPIcon.ADVICE,
    size: 32,
  },
};

export const ArrowRight: Story = {
  args: {
    name: MOPIcon.ARROW_RIGHT,
    size: 32,
  },
};

export const ArrowLeft: Story = {
  args: {
    name: MOPIcon.ARROW_LEFT,
    size: 32,
  },
};

export const ArrowDown: Story = {
  args: {
    name: MOPIcon.ARROW_DOWN,
    size: 32,
  },
};

export const ArrowBack: Story = {
  args: {
    name: MOPIcon.ARROW_BACK,
    size: 32,
  },
};

export const Close: Story = {
  args: {
    name: MOPIcon.CLOSE,
    size: 32,
  },
};

export const ChevronDown: Story = {
  args: {
    name: MOPIcon.CHEVRON_DOWN,
    size: 32,
  },
};

export const Edit: Story = {
  args: {
    name: MOPIcon.EDIT,
    size: 32,
  },
};

export const Erager: Story = {
  args: {
    name: MOPIcon.ERAGER,
    size: 32,
  },
};

export const FilledCheck: Story = {
  args: {
    name: MOPIcon.FILLED_CHECK,
    size: 32,
  },
};

export const FilledCircleWarning: Story = {
  args: {
    name: MOPIcon.FILLED_CIRCLE_WARNING,
    size: 32,
  },
};

export const Delete: Story = {
  args: {
    name: MOPIcon.DELETE,
    size: 32,
  },
};

export const NoticeTriangle: Story = {
  args: {
    name: MOPIcon.NOTICE_TRIANGLE,
    size: 32,
  },
};

export const Plus: Story = {
  args: {
    name: MOPIcon.PLUS,
    size: 32,
  },
};

export const Search: Story = {
  args: {
    name: MOPIcon.SEARCH,
    size: 32,
  },
};

export const SettingCog: Story = {
  args: {
    name: MOPIcon.SETTING_COG,
    size: 32,
  },
};

export const Download: Story = {
  args: {
    name: MOPIcon.DOWNLOAD,
    size: 32,
  },
};

export const EmptyNotice: Story = {
  args: {
    name: MOPIcon.EMPTY_NOTICE,
    size: 32,
  },
};

// Switch icons group
export const SwitchOn: Story = {
  args: {
    name: MOPIcon.SWITCH_ON,
    size: 32,
  },
};

export const SwitchOff: Story = {
  args: {
    name: MOPIcon.SWITCH_OFF,
    size: 32,
  },
};

export const SwitchDisable: Story = {
  args: {
    name: MOPIcon.SWITCH_DISABLE,
    size: 32,
  },
};

export const Copy: Story = {
  args: {
    name: MOPIcon.COPY,
    size: 32,
  },
};

export const CheckDocument: Story = {
  args: {
    name: MOPIcon.CHECK_DOCUMENT,
    size: 32,
  },
};

export const BoxChecked: Story = {
  args: {
    name: MOPIcon.BOX_CHECKED,
    size: 32,
  },
};

export const BoxChecked2: Story = {
  args: {
    name: MOPIcon.BOX_CHECKED2,
    size: 32,
  },
};

export const BoxUnchecked: Story = {
  args: {
    name: MOPIcon.BOX_UNCHECKED,
    size: 32,
  },
};

export const NoticeSpeechBox: Story = {
  args: {
    name: MOPIcon.NOTICE_SPEECH_BOX,
    size: 32,
  },
};

export const BlinkSpeechBox: Story = {
  args: {
    name: MOPIcon.BLINK_SPEECH_BOX,
    size: 32,
  },
};

export const Blink: Story = {
  args: {
    name: MOPIcon.BLINK,
    size: 32,
  },
};

export const More: Story = {
  args: {
    name: MOPIcon.MORE,
    size: 32,
  },
};

export const RawDataCampaign: Story = {
  args: {
    name: MOPIcon.RAW_DATA_CAMPAIGN,
    size: 32,
  },
};

export const RawDataOptimization: Story = {
  args: {
    name: MOPIcon.RAW_DATA_OPTIMIZATION,
    size: 32,
  },
};

export const LeftRightArrow: Story = {
  args: {
    name: MOPIcon.LEFT_RIGHT_ARROW,
    size: 32,
  },
};

export const DropDownArrow: Story = {
  args: {
    name: MOPIcon.DROP_DOWN_ARROW,
    size: 32,
  },
};

export const CopyClipboard: Story = {
  args: {
    name: MOPIcon.COPY_CLIPBOARD,
    size: 32,
  },
};

export const Warning: Story = {
  args: {
    name: MOPIcon.WARNING,
    size: 32,
  },
};

export const Visible: Story = {
  args: {
    name: MOPIcon.VISIBLE,
    size: 32,
  },
};

export const Invisible: Story = {
  args: {
    name: MOPIcon.INVISIBLE,
    size: 32,
  },
};

export const Star: Story = {
  args: {
    name: MOPIcon.STAR,
    size: 32,
  },
};

export const ShoppingBag: Story = {
  args: {
    name: MOPIcon.SHOPPING_BAG,
    size: 32,
  },
};

export const CheckCircle: Story = {
  args: {
    name: MOPIcon.CHECK_CIRCLE,
    size: 32,
  },
};

// Icons rendered as TextIcon (Currency and Authority)
// These icons are rendered with TextIcon component, not SVG.

export const USD: Story = {
  args: {
    name: MOPIcon.USD,
    size: 32,
  },
  parameters: {
    docs: {
      description: {
        story: 'USD currency icon - Rendered as TextIcon displaying "$" character on a circular background.',
      },
    },
  },
};

export const KRW: Story = {
  args: {
    name: MOPIcon.KRW,
    size: 32,
  },
  parameters: {
    docs: {
      description: {
        story: 'KRW currency icon - Rendered as TextIcon displaying "￦" character on a circular background.',
      },
    },
  },
};

export const Administrate: Story = {
  args: {
    name: MOPIcon.ADMINISTRATE,
    size: 32,
  },
  parameters: {
    docs: {
      description: {
        story: 'Administrator authority icon - Rendered as TextIcon displaying "A" character on a circular background with admin color.',
      },
    },
  },
};

export const Operate: Story = {
  args: {
    name: MOPIcon.OPERATE,
    size: 32,
  },
  parameters: {
    docs: {
      description: {
        story: 'Operator authority icon - Rendered as TextIcon displaying "O" character on a circular background with operator color.',
      },
    },
  },
};

export const Read: Story = {
  args: {
    name: MOPIcon.READ,
    size: 32,
  },
  parameters: {
    docs: {
      description: {
        story: 'Read authority icon - Rendered as TextIcon displaying "V" character on a circular background with read permission color.',
      },
    },
  },
};

// TextIcon group story
export const TextIconGroup: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '24px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={MOPIcon.USD} size={32} type="currency" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>USD ($)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Currency</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={MOPIcon.KRW} size={32} type="currency" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>KRW (￦)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Currency</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={MOPIcon.ADMINISTRATE} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>Admin (A)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Administrator</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={MOPIcon.OPERATE} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>Operate (O)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Operator</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <TextIcon code={MOPIcon.READ} size={32} type="authorize" />
        <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>Read (V)</div>
        <div style={{ fontSize: '10px', color: '#666' }}>Viewer</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Group of icons rendered as TextIcon. These are text-based icons (not SVG) with unique colors and characters.',
      },
    },
  },
};

// Story showing all icons in a grid (distinguishing SVG and TextIcon)
export const AllIcons: Story = {
  render: () => {
    const textIcons = [MOPIcon.USD, MOPIcon.KRW, MOPIcon.ADMINISTRATE, MOPIcon.OPERATE, MOPIcon.READ];
    const svgIcons = Object.values(MOPIcon).filter(icon => !textIcons.includes(icon));

    return (
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>SVG Icons</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
          gap: '16px',
          padding: '16px',
          marginBottom: '32px',
          border: '1px solid #e0e0e0',
          borderRadius: '8px'
        }}>
          {svgIcons.map((iconName) => (
            <div key={iconName} style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center',
              textAlign: 'center' 
            }}>
              <MopIcon name={iconName} size={32} />
              <div style={{ marginTop: '8px', fontSize: '12px' }}>{iconName}</div>
            </div>
          ))}
        </div>

        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>TextIcon Icons</h3>
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
          gap: '16px',
          padding: '16px',
          backgroundColor: '#f8f9fa',
          border: '1px solid #e0e0e0',
          borderRadius: '8px'
        }}>
          {textIcons.map((iconName) => (
            <div key={iconName} style={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center',
              textAlign: 'center' 
            }}>
              <TextIcon code={iconName} size={32} />
              <div style={{ marginTop: '8px', fontSize: '12px', fontWeight: 'bold' }}>{iconName}</div>
              <div style={{ fontSize: '10px', color: '#666' }}>TextIcon</div>
            </div>
          ))}
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Displays all icons categorized as SVG icons and TextIcons. TextIcons are text-based circular icons.',
      },
    },
  },
};

// Size variations story
export const SizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={16} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>16px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={24} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>24px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>32px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={48} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>48px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={64} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>64px</div>
      </div>
    </div>
  ),
};

// TextIcon size variations story
export const TextIconSizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.ADMINISTRATE} size={16} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>16px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.ADMINISTRATE} size={24} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>24px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.ADMINISTRATE} size={32} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>32px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.ADMINISTRATE} size={48} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>48px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.ADMINISTRATE} size={64} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>64px</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows various size variations of TextIcon. Text automatically adjusts according to the size.',
      },
    },
  },
};

// Color variations story
export const ColorVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Default</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} fill="#ff0000" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Red Fill</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} bgColor="#e0e0e0" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Gray Background</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} fill="#0066cc" bgColor="#f0f8ff" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Blue Fill + Background</div>
      </div>
    </div>
  ),
};

// Disabled state story
export const DisabledState: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Enabled</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopIcon name={MOPIcon.STAR} size={32} isDisabled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Disabled</div>
      </div>
    </div>
  ),
}; 