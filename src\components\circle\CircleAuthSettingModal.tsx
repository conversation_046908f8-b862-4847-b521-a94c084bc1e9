import React, { type ReactElement, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  getAuthorities,
  deleteAuthority,
  editAuthority,
  createInvitation,
  getInvitationItems,
  deleteInvitationItems
} from '@api/circle'
import type { SessionInfo } from '@models/common/Session'
import type { AuthorityListItem, NewInvitationItem, InvitationItem } from '@models/circle'
import { Dialog, DialogContent, Select, MenuItem, TextField, Button } from '@material-ui/core'
import CommonTooltip from '@components/common/CommonTooltip'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { add, sub } from 'date-fns'
import { TextIcon } from '@components/common/icon'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { MopIcon, BaseChip, EllipsisText } from '@components/common'
import { MOPIcon, AuthorityType } from '@models/common'
import { useDialog, useToast, useAuthority } from '@hooks/common'
import * as yup from 'yup'
import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import CommonResponse, { StatusCode } from '@models/common/CommonResponse'

import './CircleAuthSettingModal.scss'
import TagManager from 'react-gtm-module'
import { useRecoilValue } from 'recoil'
import { getFunctionValue } from '@store/Advertiser'
import { FunctionId } from '@models/common/Advertiser'

export interface Props {
  advertiser: { advertiserId: number; advertiserName: string }
  member: SessionInfo
  onClose: any
  open: boolean
  callback: () => void
}

const AuthorityInfo = () => {
  const { t } = useTranslation()

  return (
    <>
      <h4>
        {t('circle.authSettingModal.tooltip.authority.title')}
        &nbsp;<span>{t('circle.authSettingModal.tooltip.authority.tip')}</span>
      </h4>
      <div className="content">
        <p>
          {t('circle.authSettingModal.tooltip.authority.description1')}
          <br />
          <strong className="administrator">{t('circle.authSettingModal.tooltip.authority.administrator')}</strong>
          ,&nbsp;
          <strong className="Operator">{t('circle.authSettingModal.tooltip.authority.operator')}</strong>
          ,&nbsp;
          <strong className="Viewer">{t('circle.authSettingModal.tooltip.authority.viewer')}</strong>
        </p>
        <p>
          {t('circle.authSettingModal.tooltip.authority.description2')}
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;{t('circle.authSettingModal.tooltip.authority.onlyOneAdmin')}
        </p>
        <table className="authority-table">
          <colgroup>
            <col width="80px" />
            <col width="114px" />
            <col width="114px" />
            <col width="114px" />
          </colgroup>
          <thead>
            <tr>
              <th scope="col">{t('circle.authSettingModal.tooltip.authority.table.category')}</th>
              <th scope="col" className="type">
                <div>
                  <div className="rowspan">
                    <TextIcon code="ADMINISTRATE" size={20} />
                  </div>
                  <div>{t('circle.authSettingModal.tooltip.authority.administrator').split('(')[0]}</div>
                  <div>
                    <small>Administrator</small>
                  </div>
                </div>
              </th>
              <th scope="col" className="type">
                <div>
                  <div className="rowspan">
                    <TextIcon code="OPERATE" size={20} />
                  </div>
                  <div>{t('circle.authSettingModal.tooltip.authority.operator').split('(')[0]}</div>
                  <div>
                    <small>Operator</small>
                  </div>
                </div>
              </th>
              <th scope="col" className="type">
                <div>
                  <div className="rowspan">
                    <TextIcon code="READ" size={20} />
                  </div>
                  <div>{t('circle.authSettingModal.tooltip.authority.viewer').split('(')[0]}</div>
                  <div>
                    <small>Viewer</small>
                  </div>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th scope="row">{t('circle.authSettingModal.tooltip.authority.table.manageIntegration')}</th>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
            </tr>
            <tr>
              <th scope="row">{t('circle.authSettingModal.tooltip.authority.table.manageAdcircle')}</th>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
            </tr>
            <tr>
              <th scope="row">{t('circle.authSettingModal.tooltip.authority.table.manageUserAuth')}</th>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
            </tr>
            <tr>
              <th scope="row">{t('circle.authSettingModal.tooltip.authority.table.mopOperate')}</th>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>{t('circle.authSettingModal.tooltip.authority.table.unavailable')}</td>
            </tr>
            <tr>
              <th scope="row">{t('circle.authSettingModal.tooltip.authority.table.mopView')}</th>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
              <td>
                <strong className="permission">{t('circle.authSettingModal.tooltip.authority.table.available')}</strong>
              </td>
            </tr>
          </tbody>
        </table>
        <p>
          <strong>{t('circle.authSettingModal.tooltip.authority.tableNote')}</strong>
        </p>
        <p>{t('circle.authSettingModal.tooltip.authority.multipleAuth')}</p>
      </div>
    </>
  )
}

interface TabButtonProps {
  selected: boolean
}

const TabButton = styled.button<TabButtonProps>`
  ${tw`text-center basis-1/2`};
  ${({ selected }) => {
    return selected
      ? tw`border-t-2 border-landing-black bg-white text-landing-black font-bold`
      : tw`bg-gray-light shadow-[6px_-2px_20px_0px_rbga(143,143,143,0.05)_inset] text-[#B8B8B8] font-semibold`
  }}
`

interface StatusButtonProps {
  isExpired: boolean
}

const StatusButton = styled.span<StatusButtonProps>`
  ${tw`py-1 px-2.5 rounded font-bold text-xs`};
  ${({ isExpired }) => {
    return isExpired ? tw`bg-[#E6E6E6] text-[#909090]` : tw`bg-[#E8F2FE] text-[#3B5EC9]`
  }}
`

const AuthorityModal: React.FC<Props> = (props: Props): ReactElement => {
  const { t } = useTranslation()
  const { openToast } = useToast()
  const { openDialog, openFormDialog } = useDialog()
  const [authorities, setAuthorities] = useState<AuthorityListItem[] | undefined>(undefined)
  const [invitationItems, setInvitaionItems] = useState<InvitationItem[]>([])
  const [newInvitationItem, setNewInvitationItem] = useState<NewInvitationItem>({
    inviteeEmail: '',
    authorityType: ''
  })
  const [countDelete, updateCountDelete] = useState(0)
  const [currentTab, setCurrentTab] = useState<string>('list')
  const { advertiserList } = useAuthority()
  const authLimitCount = advertiserList
    ?.find((adv) => adv.advertiserId === props.advertiser.advertiserId)
    ?.subscriptionFunctions?.find((func) => func.functionId === FunctionId.MEMBER_AUTHORITY)?.functionValue

  const getAuthorityList = async (advertiserId: number) => {
    const data = await getAuthorities(advertiserId)
    const allowedAuthority = data ? data.filter((auth) => !auth.email.includes('@mop.co.kr')) : []
    setAuthorities(allowedAuthority)
  }

  const handleClose = () => {
    if (countDelete) props.callback()
    props.onClose()
  }

  const handleEdit = ({ advertiserId, memberId, authorityType }: AuthorityListItem) => {
    openFormDialog<{ selectValue: AuthorityType }>({
      title: t('circle.editAuthModal.title'),
      selectOptions: {
        initialValue: authorityType,
        itemOptions: [
          {
            label: 'Operator',
            value: AuthorityType.OPERATE,
            icon: MOPIcon.OPERATE,
            iconSize: 20,
            disabled: authorityType === AuthorityType.OPERATE
          },
          {
            label: 'Viewer',
            value: AuthorityType.READ,
            icon: MOPIcon.READ,
            iconSize: 20,
            disabled: authorityType === AuthorityType.READ
          }
        ]
      },
      onAction: async ({ selectValue: authorityType }) => {
        if (!authorityType) return
        const result = await editAuthority(advertiserId, { authorityType, advertiserId, memberId })
        if (result.successOrNot === 'N') openToast(t('circle.toast.FAILED_EDIT_AUTH'))
        else {
          openToast(t('circle.toast.SUCCESS_EDIT_AUTH'))
          await getAuthorityList(props.advertiser.advertiserId)
        }
      }
    })
  }

  const handleDelete = async ({ advertiserId, memberId }: AuthorityListItem) => {
    openDialog({
      title: t('common.modal.title.notice'),
      message: t('common.message.deleteConfirm'),
      cancelLabel: t('common.label.button.cancel-k'),
      actionLabel: t('common.label.button.delete-k'),
      onAction: async () => {
        await deleteAuthority({ advertiserId, memberId })
        updateCountDelete(countDelete + 1)
        getAuthorityList(advertiserId)
        openToast(t('circle.toast.SUCCESS_DELETE_AUTH'))
      }
    })
  }

  const getInvitationList = async (advertiserId: number): Promise<InvitationItem[]> => {
    try {
      const data: InvitationItem[] = (await getInvitationItems(advertiserId)) || []
      setInvitaionItems(data)
      return data
    } catch (error) {
      console.error('Failed to fetch invitation items:', error)
      return []
    }
  }

  const handleChange = (key: keyof NewInvitationItem, value: string) => {
    setNewInvitationItem((prev) => ({ ...prev, [key]: value }))
  }

  const handleInvitationDelete = async (item: InvitationItem) => {
    if (!item.invitationId) return

    openDialog({
      title: t('common.modal.title.notice'),
      message: t('common.message.deleteConfirm'),
      cancelLabel: t('common.label.button.cancel'),
      actionLabel: t('common.label.button.delete'),
      onAction: async () => {
        await deleteInvitationItems(item.invitationId as number)
        updateCountDelete(countDelete + 1)
        const updatedItems = await getInvitationList(props.advertiser.advertiserId)
        setInvitaionItems(updatedItems)
        openToast(t('circle.toast.SUCCESS_DELETE_INVITATION'))
      }
    })
  }

  const validationSchema = yup.object().shape({
    authorityType: yup.string().required(t('권한 타입을 설정해 주세요.')),
    inviteeEmail: yup.string().required(t('이메일 주소를 입력하세요.')).email(t('contact.validation.validEmail'))
  })

  const handleAddInvitation = (e: React.MouseEvent<HTMLButtonElement>) => {
    const plan = advertiserList?.find(
      (adv) => adv.advertiserId === props.advertiser.advertiserId
    )?.subscriptionProductType
    if (authorities && authorities.length + invitationItems.length >= Number(authLimitCount)) {
      openToast(t('circle.toast.limitAuth', { plan: plan, num: authLimitCount }))
      return
    }

    sendDataLayer(e.currentTarget.dataset.gtmId)

    validationSchema
      .validate(newInvitationItem)
      .then((value) => {
        openDialog({
          title: t('common.modal.title.notice'),
          message: t('circle.authSettingModal.message.confirmInvite'),
          cancelLabel: t('common.label.button.cancel-k'),
          actionLabel: t('common.label.button.confirm-k'),
          onAction: async () => {
            sendInvitation()
          }
        })
      })
      .catch((err) => {
        openToast(err.message)
      })
  }

  const sendInvitation = async () => {
    if (newInvitationItem.authorityType && newInvitationItem.inviteeEmail) {
      try {
        const newItem: InvitationItem = {
          invitationId: null,
          advertiserId: props.advertiser.advertiserId,
          advertiserName: props.advertiser.advertiserName,
          inviterMemberId: props.member.memberId,
          inviterMemberName: props.member.memberName,
          inviteeMemberId: null,
          inviteeEmail: newInvitationItem.inviteeEmail,
          authorityType: newInvitationItem.authorityType as AuthorityType,
          approveYn: null,
          inviteDateTime: new Date().toISOString(),
          expireDateTime: null,
          approveDateTime: null
        }

        setInvitaionItems((prev) => (prev ? [...prev, newItem] : [newItem]))

        // 초대 API 호출
        const response = await createInvitation({
          advertiserId: props.advertiser.advertiserId,
          authorityType: newInvitationItem.authorityType as AuthorityType,
          inviteeEmail: newInvitationItem.inviteeEmail
        })

        setNewInvitationItem({ inviteeEmail: '', authorityType: '' })
        const updatedItems = await getInvitationList(props.advertiser.advertiserId)
        setInvitaionItems(updatedItems)

        // 상태 코드에 따른 다른 메시지 표시
        openToast(t(`circle.toast.${response.statusCode}`))
      } catch (error) {
        if (error instanceof yup.ValidationError) {
          openToast(t('common.message.systemError'))
        }
      }
    }
  }

  const getInvitationStatus = (item: InvitationItem) => {
    const now = new Date()
    let expiredDatetime = add(new Date(item.inviteDateTime), { days: 8 })
    expiredDatetime = sub(expiredDatetime, { seconds: 1 })
    const expired = !item.inviteDateTime && now > expiredDatetime

    if (expired) return { type: 'Expired', label: t('circle.authSettingModal.label.inviteStatus.expired') }
    return { type: 'Invited', label: t('circle.authSettingModal.label.inviteStatus.pending') }
  }

  const sendDataLayer = (gtmId: string | undefined) => {
    if (!gtmId) return
    TagManager.dataLayer({
      dataLayer: {
        event: 'click',
        gtm_id: gtmId
      }
    })
  }

  useEffect(() => {
    if (props.open) {
      getAuthorityList(props.advertiser.advertiserId)
      getInvitationList(props.advertiser.advertiserId)
      setCurrentTab('list')
    }
  }, [props.open, props.advertiser])

  useEffect(() => {
    getAuthorityList(props.advertiser.advertiserId)
    getInvitationList(props.advertiser.advertiserId)
  }, [currentTab])
  const permissionContents = t('circle.authSettingModal.tooltip.permissionInvitation.content', {
    returnObjects: true
  }) as string[]
  const statusInvitationContents = t('circle.authSettingModal.tooltip.statusInvitation.contents', {
    returnObjects: true
  }) as Record<string, string[]>
  return (
    <Dialog id="CircleAuthSettingModal" className="circle-auth-setting-modal" open={props.open} onClose={handleClose}>
      <section className="circle-auth-setting-modal__header">
        <BaseChip size="md" bgColor="#F2F3F6">
          {t('circle.authSettingModal.label.title.chip')}
        </BaseChip>
        <EllipsisText className="circle-auth-setting__advertiser text-center" title={props.advertiser.advertiserName}>
          {props.advertiser.advertiserName}
        </EllipsisText>
        <MopIcon name={MOPIcon.CLOSE} onClick={handleClose} size={20} bgColor="#f3f3f6" />
      </section>
      <DialogContent>
        <div className="border-[#E8E8E8] border-r border-b border-l h-full relative">
          <div className="w-full h-[45px] flex items-stretch">
            <TabButton
              selected={currentTab === 'list'}
              onClick={(e) => {
                setCurrentTab('list')
                sendDataLayer(e.currentTarget.dataset.gtmId)
              }}
              data-gtm-id="adcircle-member-list-click"
            >
              <CommonTooltip id={`authority-info-tooltip`} title={<AuthorityInfo />} placement="right-start" arrow>
                <AdviceMarkIcon className="mb-[-2px]" />
              </CommonTooltip>
              &nbsp;{t('circle.authSettingModal.label.title.memberList')}
            </TabButton>
            <TabButton
              selected={currentTab === 'invite'}
              onClick={(e) => {
                setCurrentTab('invite')
                sendDataLayer(e.currentTarget.dataset.gtmId)
              }}
              data-gtm-id="adcircle-invite-member-list-click"
            >
              <CommonTooltip
                title={
                  <>
                    <h1>{t('circle.authSettingModal.tooltip.permissionInvitation.title')}</h1>
                    <div className="common-style">
                      <p>{t('circle.authSettingModal.tooltip.permissionInvitation.subtitle')}</p>
                      {permissionContents.map((text, idx) => (
                        <p key={idx} className="indent2">
                          {text}
                        </p>
                      ))}
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="mb-[-2px]" />
              </CommonTooltip>
              &nbsp;{t('circle.authSettingModal.label.title.inviteMember')}
            </TabButton>
          </div>
          {currentTab === 'list' ? (
            <section className="py-5 px-2">
              <div className="authority-table max-h-[450px]">
                <div className="authority-table__header">
                  <span>{t('circle.authSettingModal.label.column.authority')}</span>
                  <span>{t('circle.authSettingModal.label.column.name')}</span>
                  <span>{t('circle.authSettingModal.label.column.mail')}</span>
                  <span>{t('circle.authSettingModal.label.column.change')}</span>
                  <span>{t('circle.authSettingModal.label.column.delete')}</span>
                </div>
                <div className="authority-table__body">
                  {authorities &&
                    authorities.map((item) => (
                      <div className="authority-table__row">
                        <div className="authority-table__row-cell">
                          <TextIcon code={item.authorityType} size={20} />
                          {t(`setting.authority.name.${item.authorityType}`)}
                        </div>
                        <div className="authority-table__row-cell justify-center">
                          {item.memberName}
                          {props.member.memberId === item.memberId && (
                            <BaseChip size="sm" bgColor="#3B5EC9" textColor="white">
                              ME
                            </BaseChip>
                          )}
                        </div>
                        <div className="authority-table__row-cell justify-center">{item.email}</div>
                        <div className="authority-table__row-cell">
                          {item.authorityType !== AuthorityType.ADMINISTRATE && (
                            <MopIcon name={MOPIcon.EDIT} onClick={() => handleEdit(item)} />
                          )}
                        </div>
                        <div className="authority-table__row-cell">
                          {item.authorityType !== AuthorityType.ADMINISTRATE && (
                            <MopIcon name={MOPIcon.DELETE} onClick={() => handleDelete(item)} />
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </section>
          ) : (
            <section className="py-5 px-2">
              <div className="authority-table max-h-[366px]">
                <div className="authority-table__header column_4">
                  <span>{t('circle.authSettingModal.label.column.authority')}</span>
                  <span>{t('circle.authSettingModal.label.column.mail')}</span>
                  <span>
                    <CommonTooltip
                      title={
                        <>
                          <h1>{t('circle.authSettingModal.tooltip.statusInvitation.title')}</h1>
                          <div className="common-style">
                            <dl className="w20_w80">
                              {Object.keys(statusInvitationContents).map((key) => (
                                <>
                                  <dt key={`dt-${key}`}>{statusInvitationContents[key][0]}</dt>
                                  <dd key={`dd-${key}`}>{statusInvitationContents[key][1]}</dd>
                                </>
                              ))}
                            </dl>
                          </div>
                        </>
                      }
                      placement="right-start"
                      arrow
                    >
                      <AdviceMarkIcon className="mb-[-2px]" />
                    </CommonTooltip>
                    &nbsp;{t('circle.authSettingModal.label.column.invitationStatus')}
                  </span>
                  <span>{t('circle.authSettingModal.label.column.delete')}</span>
                </div>
                <div className="authority-table__body column_4">
                  {invitationItems &&
                    invitationItems.map((item) => (
                      <div className="authority-table__row">
                        <div className="authority-table__row-cell">
                          <TextIcon code={item.authorityType} size={20} />
                          {t(`setting.authority.name.${item.authorityType}`)}
                        </div>
                        <div className="authority-table__row-cell justify-center">{item.inviteeEmail}</div>
                        <div className="authority-table__row-cell justify-center">
                          <StatusButton isExpired={getInvitationStatus(item)?.type === 'Expired'}>
                            {getInvitationStatus(item)?.label}
                          </StatusButton>
                        </div>
                        <div className="authority-table__row-cell">
                          {item.authorityType !== AuthorityType.ADMINISTRATE && (
                            <MopIcon
                              name={MOPIcon.DELETE}
                              customClass="cursor-pointer"
                              onClick={() => handleInvitationDelete(item)}
                            />
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
              <table className="circle-modal-table row-height-large">
                <colgroup>
                  <col width="142px" />
                  <col width="*" />
                </colgroup>
                <tbody>
                  <tr>
                    <td>
                      <Select
                        name="authorityType"
                        value={newInvitationItem.authorityType}
                        onChange={(e) => handleChange('authorityType', e.target.value as string)}
                        displayEmpty
                        variant="outlined"
                        className="outline-select"
                      >
                        <MenuItem value="" disabled>
                          {t('circle.authSettingModal.label.placeholder.authSelect')}
                        </MenuItem>
                        <MenuItem value="OPERATE">
                          <div className="menu-item-icon">
                            <TextIcon code="OPERATE" size={23} />
                          </div>
                          <div className="menu-item-text">Operator</div>
                        </MenuItem>
                        <MenuItem value="READ">
                          <div className="menu-item-icon">
                            <TextIcon code="READ" size={23} />
                          </div>
                          <div className="menu-item-text">Viewer</div>
                        </MenuItem>
                      </Select>
                    </td>
                    <td>
                      <TextField
                        name="email"
                        value={newInvitationItem.inviteeEmail}
                        onChange={(e) => handleChange('inviteeEmail', e.target.value)}
                        size="small"
                        variant="outlined"
                        placeholder={t('circle.authSettingModal.label.placeholder.mailInput')}
                      />
                      <Button
                        type="button"
                        variant="contained"
                        disableElevation
                        // onClick={sendInvitation}
                        onClick={handleAddInvitation}
                        className="send-button"
                        data-gtm-id="adcircle-invite-member"
                      >
                        {t('circle.authSettingModal.label.button.send')}
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </section>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default AuthorityModal
