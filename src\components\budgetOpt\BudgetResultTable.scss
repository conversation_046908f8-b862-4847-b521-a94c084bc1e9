#BudgetResultTable {
  position: relative;
  margin-top: 60px;
  width: 100%;
  .download {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 4px 0;
    .MuiCheckbox-root {
      padding: 0;
      transform: scale(0.75);
    }
    label {
      font-size: 20px;
      font-weight: 700;
      margin: 0 4px;
    }
    .mop-icon-box {
      cursor: pointer;
    }
  }
  .wrapper {
    position: relative;
    overflow-x: auto;
    table {
      min-width: 100%;
      border-collapse: collapse;
      thead {
        background-color: #f2f3f6;
        tr {
          &:nth-child(1) {
            height: 24px;
            th:not([rowspan]) {
              border-bottom: 1px solid #d9d9d9;
            }
          }
          &:nth-child(2) {
            height: 38px;
          }
          th {
            position: relative;
            color: var(--point_color);
            text-align: center;
            font-size: 14px;
            font-weight: 700;
            svg {
              transform: translateY(-2px);
              vertical-align: text-bottom;
              cursor: pointer;
              &.rounded {
                border: 1px solid #b3b8c5;
                border-radius: 50%;
              }
            }
          }
        }
      }
      tbody {
        tr {
          height: 30px;
          border-bottom: 1px solid #f2f3f6;
          td {
            color: var(--point_color);
            text-align: left;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
            padding: 0 8px;
            &.toggle {
              text-align: center;
              svg {
                vertical-align: text-bottom;
                cursor: pointer;
                &.rounded {
                  border: 1px solid #b3b8c5;
                  border-radius: 50%;
                }
              }
            }
            &.parent {
              svg {
                vertical-align: text-bottom;
                cursor: pointer;
                margin-right: 16px;
                &.rounded {
                  border: 1px solid #b3b8c5;
                  border-radius: 50%;
                }
              }
              .adType {
                display: inline-block;
                width: 40px;
                text-align: center;
                color: #fff;
                font-size: 10px;
                font-weight: 700;
                background-color: var(--point_color);
                padding: 1px 9px;
                border-radius: 9999px;
                margin-right: 8px;
                transform: translateY(-2px);
              }
            }
            &.campaignName {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            &.budget {
              text-align: right;
              padding: 0 6px;
            }
            &.budgetRatio {
              background-color: #edf6f6;
              position: relative;
              .budgetRatioBar {
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                background-color: #c0dfdf;
              }
              .budgetRatioLabel {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                padding: 0 6px;
                display: flex;
                align-items: center;
              }
            }
            &.changeRate {
              font-size: 14px;
              font-weight: 700;
              text-align: center;
              padding: 0 6px;
              &.increase {
                color: #eb414c;
              }
              &.decrease {
                color: var(--color-active-blue);
              }
            }
            &.impactValue {
              font-size: 14px;
              text-align: right;
              padding: 0 6px;
              &.increase {
                color: #eb414c;
              }
              &.decrease {
                color: var(--color-active-blue);
              }
            }
            &.impactChangeRate {
              text-align: center;
              div {
                display: inline-block;
                font-size: 14px;
                font-weight: 700;
                border-radius: 3px;
                width: 60px;
                height: 24px;
                line-height: 24px;
                text-align: center;
                &.increase {
                  color: #eb414c;
                  background-color: #ffe1e3;
                }
                &.decrease {
                  color: var(--color-active-blue);
                  background-color: #d5dfff;
                }
              }
            }
          }
        }
      }
    }
  }

  .togglePopper {
    appearance: none;
    background: transparent;
    border: 0 none;
    position: absolute;
    top: 30px;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    svg path {
      stroke-width: 1;
    }
    &.open {
      svg {
        transform: rotate(180deg);
      }
    }
  }
}
