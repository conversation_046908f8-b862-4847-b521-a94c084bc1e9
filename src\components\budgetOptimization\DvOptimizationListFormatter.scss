#dvOptimizationList {
  .MuiChip-root {
    width: 100%;
    height: 25px;

    &.MuiChip-outlinedSecondary {
      border: none;
    }

    svg {
      display: none;
      height: 16px;
    }

    span {
      font-family: 'NotoSans';
      font-weight: 500;
      font-size: 13px;
    }

    &.errorStatusLabel-red {
      span {
        font-weight: 700;
        color: var(--error_color);
      }
    }

    &.errorStatusLabel-green {
      span {
        font-weight: 700;
        color: #37a29a;
      }
    }
  }

  .optimization-result {
    .optimization-result-button {
      width: 30px;
      height: 30px;
      cursor: pointer;
    }
  }

  .no-sorting-advice {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
  }

  #adviceIcon {
    svg {
      display: flex;
      margin-right: 3px;
      fill: #707070;

      &:hover {
        fill: #66a8e0;
      }
    }
  }

  .icon {
    svg {
      margin-left: 10px;
      margin-right: 10px;
    }
  }

  .delete-modify-icons {
    svg {
      cursor: pointer;
      display: inline;
    }
  }
}

#dv-optimization-advice-tooltip {
  border: 1px solid var(--point_color);
  background-color: #fff;
  .MuiTooltip-tooltip {
    padding: 0px;
    margin: 0px;
    background-color: transparent;
    > div {
      > div {
        color: var(--point_color);
        font-size: 12px;
        font-weight: 500;
        &:first-child {
          padding: 12px 0px;
          border-bottom: 1px solid #9196a4;
          font-size: 14px;
          font-weight: 700;
          display: flex;
          justify-content: center;
        }
        &:nth-child(2) {
          padding: 12px;

          .allocation-desc {
            margin-bottom: 5px;
            line-height: 22px;
          }

          > div {
            display: flex;

            > div:first-child {
              width: 90px;
              font-weight: 700;
            }
            > div:last-child {
              flex: 1;

              .error-color {
                color: var(--error_color);
              }
            }

            .allocation-img {
              width: 42px !important;
              height: auto !important;
              margin-right: 10px;
              background-repeat: no-repeat;
              background-size: 100% auto;
              background-position: center center;
            }
            .allocation-disable {
              background-image: url(~@images/switch_disable.svg);
            }
            .allocation-on {
              background-image: url(~@images/switch_on.svg);
            }
            .allocation-off {
              background-image: url(~@images/switch_off.svg);
            }
          }
        }
      }
    }
    .MuiTooltip-arrow::before {
      border-color: var(--point_color);
    }
  }
}
