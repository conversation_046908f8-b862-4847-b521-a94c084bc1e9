// src/components/common/mopUI/MopSelectOptions.tsx
import React, { Fragment } from 'react'
import { Listbox, Transition } from '@headlessui/react'
import { cn } from '@utils/index'

interface MopSelectOptionsProps {
  children: React.ReactNode
  className?: string
  maxHeight?: string
}

const MopSelectOptions = React.forwardRef<HTMLUListElement, MopSelectOptionsProps>(
  ({ children, className, maxHeight = 'max-h-60' }, ref) => {
    return (
      <Transition
        as={Fragment}
        leave="transition ease-in duration-100"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <Listbox.Options
          ref={ref}
          className={cn(
            'absolute top-full left-0 right-0 z-50 mt-0',
            'bg-white border border-[#efefef] rounded-[4px]',
            'shadow-lg overflow-auto',
            'py-1',
            maxHeight,
            className
          )}
          static={false}
        >
          {children}
        </Listbox.Options>
      </Transition>
    )
  }
)

MopSelectOptions.displayName = 'MopSelectOptions'

export default MopSelectOptions
