import { Button } from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import TagManager from 'react-gtm-module'
import './ContainedCreateButton.scss'

interface Props {
  label: string
  onClick: () => void
  disabled?: boolean
  gtmId?: string
}

const ContainedCreateButton = ({ label, onClick, disabled, gtmId }: Props) => {
  const handleClick: React.MouseEventHandler<HTMLButtonElement> = (event) => {
    const gtmEventId = event.currentTarget.dataset.gtmId

    if (gtmEventId) {
      try {
        TagManager.dataLayer({
          dataLayer: {
            event: 'click',
            gtm_id: gtmEventId
          }
        })
      } catch (error) {
        console.error('GTM event error:', error)
      }
    }

    onClick()
  }

  return (
    <Button
      id="createButton"
      variant="contained"
      onClick={handleClick}
      color="primary"
      endIcon={<AddIcon />}
      disabled={disabled}
      data-gtm-id={gtmId}
    >
      {label}
    </Button>
  )
}

export default ContainedCreateButton