import CommonResponse from '@models/common/CommonResponse';
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import {
  AbnormalDetection,
  AbnormalPerformance,
  Bidding,
  CollectionItems,
  CollectionPerformance,
  CollectionStatus,
  Planning,
  Prediction,
  RankMaintenance,
} from '@models/dashboard/DashboardStatus';
import { GetReportSummaryResponse } from '@models/report/SearchReport';

export const getSaReport = async (advertiserId: number, chkDate: string[], isLoading = false) => {
  const response: CommonResponse<GetReportSummaryResponse> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/report',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        startDate: chkDate[0],
        endDate: chkDate[1],
        compareStartDate: chkDate[2],
        compareEndDate: chkDate[3],
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaCollectionStatus = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<CollectionStatus> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/collection/status',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaAbnormalUrls = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalDetection> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/abnormal/urls',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaAbnormalUtms = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalDetection> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/abnormal/utms',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaAbnormalPerformance = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalPerformance[]> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/abnormal/performances',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaCollectionItem = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<CollectionItems> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/collection/items',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaCollectionPerformance = async (advertiserId: number, lastDays: number, isLoading = false) => {
  const response: CommonResponse<CollectionPerformance> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/collection/performances',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        lastDays: lastDays,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaProjectionPredictions = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Prediction> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/projection/predictions',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaProjectionPlanning = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Planning> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/projection/planning',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaMaintenance = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<RankMaintenance> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/flight/rank-maintenance',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaFlightBid = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Bidding> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/flight/bids',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};
