import React, { ReactElement, useEffect, useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  Typography,
} from '@material-ui/core';
import './DvOptimizationResultModal.scss';
import { t } from 'i18next';
import DvOptimizationResultChart from './DvOptimizationResultChart';
import DvOptimizationResultTable from './DvOptimizationResultTable';
import { downloadMediaAndCampaignBudgetData, getDvOptimizationResult } from '@api/budgetOptimization/DvOptimization';
import { DvReportTableType, GetDvReportResponse } from '@models/budgetOptimization/DvReport';
import CloseIcon from '@components/assets/images/icon_close.png';
import { ReactComponent as DownloadIcon } from '@components/assets/images/icon_download.svg';

import ReportModalTitle from '@components/common/optimization/ReportModalTitle';
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_DVA.svg';
import { ActionType } from '@models/common/CommonConstants';

export interface Props {
  onClose: () => void;
  optimizationId?: number;
  type: ActionType;
  open: boolean;
}

const DvOptimizationResultModal: React.FC<Props> = (props: Props): ReactElement => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const initOptimizationInfo = async (optimizationId: number) => {
    setIsLoading(true);

    const optimizationInfo = await getDvOptimizationResult(optimizationId);

    setIsLoading(false);
    setOptimizationInfo(optimizationInfo);
  };

  const [optimizationInfo, setOptimizationInfo] = useState<GetDvReportResponse>({
    predictionDate: '',
    media: [],
    campaign: [],
  });

  useEffect(() => {
    if (props.optimizationId != undefined) {
      initOptimizationInfo(props.optimizationId);
    }
  }, [props.optimizationId]); // eslint-disable-line

  const getMediaCampaignBudgetDownload = async () => {
    if (props.optimizationId) {
      downloadMediaAndCampaignBudgetData(props.optimizationId);
    }
  };

  return (
    <div>
      <Dialog id="DvOptimizationResultModal" open={props.open} fullScreen scroll="body" onClose={() => props.onClose()}>
        <DialogTitle>
          <IconButton className="modal-close" aria-label="close" onClick={() => props.onClose()}>
            <img alt="close-image" src={CloseIcon} />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Grid container direction="column">
            <Box id="title">
              {/* TODO */}
              <ReportModalTitle title="Display & Video Ad" icon={<TitleLabelIcon />}>
                <span>{t('optimization.label.DvOptimizationResultModal.title')}</span>
                <span className="predict-date">
                  {t('optimization.label.DvOptimizationResultModal.predictDate')}
                  <strong>{optimizationInfo.predictionDate}</strong>
                </span>
              </ReportModalTitle>
            </Box>
            {!isLoading && (
              <>
                <div id="result-by-media">
                  <Grid container id="result-by-media-container">
                    <Grid item id="result-chart">
                      {optimizationInfo.media?.length > 0 ? (
                        <DvOptimizationResultChart report={optimizationInfo} />
                      ) : (
                        <Typography className={'DvOptimizationNoData'} align={'center'}>{`${t(
                          'optimization.label.DvOptimizationResultModal.noData'
                        )} `}</Typography>
                      )}
                    </Grid>
                    <Grid item id="result-table-container" direction="column">
                      <Grid item className="result-table-name">
                        {t('optimization.label.DvOptimizationResultModal.budgetByMedia')}
                      </Grid>
                      <Grid item id="result-table">
                        {optimizationInfo.media?.length > 0 ? (
                          <DvOptimizationResultTable reportData={optimizationInfo} target={DvReportTableType.MEDIA} />
                        ) : (
                          <Typography className={'DvOptimizationNoData'} align={'center'}>{`${t(
                            'optimization.label.DvOptimizationResultModal.noData'
                          )} `}</Typography>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                </div>
                <Grid item id="result-by-campaign-container" direction="column">
                  <Grid item className="result-table-name" id="result-by-campaign-name">
                    <Box id="result-table-name">
                      {t('optimization.label.DvOptimizationResultModal.budgetByCampaign')}
                    </Box>
                    <Box id="result-download-button">
                      <Button
                        id="download-button"
                        data-testid="RawDataDownloadButton"
                        onClick={() => getMediaCampaignBudgetDownload()}
                        disabled={!optimizationInfo.campaign?.length}
                      >
                        <DownloadIcon />
                      </Button>
                    </Box>
                  </Grid>
                  <Grid item id="result-table">
                    {optimizationInfo.campaign?.length > 0 ? (
                      <DvOptimizationResultTable
                        target={DvReportTableType.MEDIA_AND_CAMPAIGN}
                        reportData={optimizationInfo}
                      />
                    ) : (
                      <Typography className={'DvOptimizationNoData'} align={'center'}>{`${t(
                        'optimization.label.DvOptimizationResultModal.noData'
                      )} `}</Typography>
                    )}
                  </Grid>
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
      </Dialog>
    </div>
  );
};
export default DvOptimizationResultModal;
