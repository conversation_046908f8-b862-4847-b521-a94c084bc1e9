import TruncatedText from '../../common/TruncatedText'

const STATUS_COLORS = {
  Live: 'bg-[#6EC7C2]',
  OFF: 'bg-[#CCCCCC]'
}

const TagDisplay = ({
  values,
  getLabel,
  onRemove,
  campaigns,
  isOptimization
}: {
  values: string | string[]
  getLabel: (v: string) => string
  onRemove?: (value: string) => void
  campaigns?: any[]
  isOptimization?: boolean
}) => {
  const valuesArray = Array.isArray(values) ? values : [values]
  const MAX_VISIBLE = 1

  const getCampaignData = (value: string, campaigns: any[]) => {
    return campaigns.find((campaign) => campaign.value === value)
  }

  if (valuesArray.length === 0) return null

  const visibleItems = valuesArray.slice(0, MAX_VISIBLE)
  const remainingCount = valuesArray.length - MAX_VISIBLE

  return (
    <div className="flex items-center gap-1 ">
      {visibleItems.map((value, index) => {
        const itemData = campaigns ? getCampaignData(value, campaigns) : null

        return (
          <div
            key={value}
            className="flex items-center bg-[#F9F9FB] border border-[#EFEFEF] rounded-md px-2 py-1 text-xs min-h-[28px] gap-1"
          >
            {isOptimization ? (
              <>
                <div className="text-[10px] text-[#5E81F4] border border-[#5E81F4] rounded-full px-2 py-1 flex flex-row gap-2">
                  Opt ID <p className="text-[#5E81F4] font-semibold">{itemData?.code || 'N/A'}</p>
                </div>

                <div className="overflow-hidden w-24 ml-2">
                  <TruncatedText className="font-medium text-xs text-gray-800 truncate block">
                    {itemData?.label || getLabel(value)}
                  </TruncatedText>
                </div>

                {itemData?.status && (
                  <p
                    className={`px-2 py-1 rounded-full text-[8px] font-medium ${
                      STATUS_COLORS[itemData.status as keyof typeof STATUS_COLORS]
                    }`}
                  >
                    {itemData.status}
                  </p>
                )}
              </>
            ) : (
              <>
                <span className="opacity-50 font-medium text-xs mr-1">{itemData?.id || getLabel(value)}</span>
                <div className="overflow-hidden w-40">
                  <TruncatedText className="font-medium text-xs text-gray-800 truncate block">
                    {itemData?.label || getLabel(value)}
                  </TruncatedText>
                </div>
              </>
            )}

            {onRemove && (
              <div>
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onRemove(value)
                  }}
                  className="ml-1 text-gray-400 hover:text-gray-600 text-sm leading-none w-4 h-4 flex items-center justify-center rounded-full hover:bg-gray-200 "
                  title="Remove item"
                >
                  ×
                </button>
              </div>
            )}
          </div>
        )
      })}
      {remainingCount > 0 && (
        <div className="flex items-center gap-1">
          <span className="flex items-center self-center">...</span>
          <div className="flex items-center bg-[#F9F9FB]  rounded-md px-2 py-1 text-xs min-h-[28px]">
            <span className="text-gray-700">+{remainingCount}</span>
            {onRemove && (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  valuesArray.forEach((v) => onRemove(v))
                }}
                className="ml-1 text-gray-400 hover:text-gray-600 text-sm leading-none w-4 h-4 flex items-center justify-center rounded-full hover:bg-gray-200"
                title="Remove all remaining items"
              >
                ×
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
export default TagDisplay
