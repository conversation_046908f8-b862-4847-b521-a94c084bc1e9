import React from 'react'
import { useTranslation } from 'react-i18next'
import { Dialog, DialogActions, DialogContent, Divider } from '@material-ui/core'
import { MopButton } from '@components/common/buttons'
import VirtualizedTable from '@components/common/table/VirtualizedTable'
import { CampaignFormData, DeviceTypeEnum, ProductData, UseDailyBudgetTypeEnum } from '@models/createCampaign/CreateCampaign'
import { ColumnDefinition } from '@models/common/VirtualizedTable'
import './CompletedCamPaignModal.scss'
import { stripHtmlTags } from '@utils/StringUtil'
import { EllipsisText } from '@components/common'

interface CampaignCompleteModalProps {
  open: boolean
  onClose: () => void
  campaignDetail: CampaignFormData
  handleModalSubmit: (campaignData: CampaignFormData) => void
}

const CampaignCompleteModal: React.FC<CampaignCompleteModalProps> = ({
  open,
  onClose,
  handleModalSubmit,
  campaignDetail
}) => {
  const { t } = useTranslation()

  const { mediaAccount, businessChannel, basicInfo, selectedProducts } = campaignDetail ?? {}

  const campaignInfo = {
    basic: [
      { label: t('createCampaign.completedModal.basicInfo.setting.name'), value: basicInfo.campaignName },
      {
        label: t('createCampaign.completedModal.basicInfo.setting.device'),
        value:
          basicInfo.deviceType === DeviceTypeEnum.ALL
            ? 'PC, Mobile'
            : basicInfo.deviceType === DeviceTypeEnum.PC
            ? 'PC'
            : 'Mobile'
      },
      {
        label: t('createCampaign.completedModal.basicInfo.setting.budget'),
        value:
          basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.UNLIMITED
            ? t('createCampaign.completedModal.basicInfo.setting.noRestriction')
            : `${basicInfo.dailyBudget?.toLocaleString('ko-KR')} ${t('createCampaign.createModal.productFilter.currency')}`
      }
    ],
    accountChannel: [
      { label: t('createCampaign.completedModal.basicInfo.media.account'), value: mediaAccount?.customerName },
      { label: t('createCampaign.completedModal.basicInfo.media.channel'), value: businessChannel?.businessChannelName }
    ],
    product: [
      {
        label: t('createCampaign.completedModal.basicInfo.product.number'),
        value: `${selectedProducts?.length} ${
          selectedProducts.length > 1
            ? t('createCampaign.completedModal.basicInfo.product.items')
            : t('createCampaign.completedModal.basicInfo.product.item')
        }`
      }
    ]
  }

  // Define columns for VirtualizedTable
  const columns: ColumnDefinition<any>[] = [
    {
      key: 'productImageUrl',
      label: t('createCampaign.label.productTable.productImage'),
      width: 134,
      align: 'center',
      render: (product: ProductData) => (
        <div className="product-image">
          <img src={product.productImageUrl} alt="product" />
        </div>
      )
    },
    {
      key: 'productName',
      label: t('createCampaign.label.productTable.productName'),
      align: 'center',
      render: (product: ProductData) => (
        <div className='text-left'>
          {stripHtmlTags(product.productName)}
        </div>
      )
    },
    {
      key: 'productId',
      label: 'ID',
      width: 140,
      align: 'center'
    }
  ]

  const renderInfoSection = (title: string, data: { label: string; value: string | number | undefined }[]) => (
    <div className="px-5 py-6">
      <h3 className="title-info">{title}</h3>
      <div className="flex flex-col gap-3">
        {data.map((item, idx) => (
          <div key={idx} className="flex gap-3">
            <div className="title-detail">{item.label}</div>
            <EllipsisText className="flex-1 font-medium text-[#171717]" title={item.value as string}>
              {item.value}
            </EllipsisText>
          </div>
        ))}
      </div>

      {title === t('createCampaign.completedModal.basicInfo.product.title') && (
        <div className="mt-10">
          <div className="text-xs space-y-2">
            <div className="flex items-start">
              <span className="mr-2">•</span>
              <span>{t('createCampaign.completedModal.basicInfo.product.info1')}</span>
            </div>
            <div className="flex items-start">
              <span className="mr-2">•</span>
              <span>
                {t('createCampaign.completedModal.basicInfo.product.info2Start')}{' '}
                <strong>{t('createCampaign.completedModal.basicInfo.product.bold1')}</strong>{' '}
                {t('createCampaign.completedModal.basicInfo.product.info2Middle')}{' '}
                <strong>{t('createCampaign.completedModal.basicInfo.product.bold2')}</strong>
                {t('createCampaign.completedModal.basicInfo.product.info2End')}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      className="CampaignCompleteModal font-pretendard"
      data-testid="completed-campaign-modal"
    >
      <div className="title-modal-completed-create-campaign"> {t('createCampaign.completedModal.title')}</div>

      <DialogContent className="flex py-5 px-8 gap-4 w-full">
        <div className="campaign-completed-list-products">
          <VirtualizedTable
            data={campaignDetail.selectedProducts}
            columns={columns}
            getItemId={(product) => product.productId}
            tableOptions={{
              rowHeight: 50,
              headerHeight: 56,
              stickyHeader: true
            }}
            messageOptions={{
              emptyMessage: 'No products available'
            }}
          />
        </div>
        <div className="campaign-completed-basic-info">
          {renderInfoSection(t('createCampaign.completedModal.basicInfo.setting.title'), campaignInfo.basic)}
          <Divider className="my-4" />
          {renderInfoSection(t('createCampaign.completedModal.basicInfo.media.title'), campaignInfo.accountChannel)}
          <Divider className="my-4" />
          {renderInfoSection(t('createCampaign.completedModal.basicInfo.product.title'), campaignInfo.product)}
        </div>
      </DialogContent>

      <DialogActions>
        <MopButton
          label={t('createCampaign.completedModal.button.check')}
          customStyle={{ padding: '10px 30px', fontSize: '14px' }}
          bgColor="#171717"
          textColor="#ffffff"
          contained={true}
          onClick={() => {
            handleModalSubmit(campaignDetail)
          }}
        />

        <MopButton
          bgColor="#ffffff"
          textColor="#333333"
          customStyle={{ padding: '10px 30px', fontSize: '14px', border: '1px solid #171717' }}
          label={t('createCampaign.completedModal.button.cancel')}
          contained={true}
          onClick={onClose}
        />
      </DialogActions>
    </Dialog>
  )
}

export default CampaignCompleteModal
