import { useEffect, useState } from 'react'
import { Dialog, MenuItem } from '@material-ui/core'
import { MopActionButton } from '@components/common/buttons'
import { MediaIcon, MopIcon, RoundedDropdown } from '@components/common'
import { MOPIcon } from '@models/common'
import circleApi from '@api/circle'
import { ConversionTool, ConversionMetric, ExistingUnit, EditCircleUnit, ConversionMetricQuery } from '@models/circle'
import { useToast } from "@hooks/common"
import './EditCircleUnitModal.scss'

export interface Props {
  onClose: any
  open: boolean
  advertiserId: string
  unit: ExistingUnit
  callback: () => void
}

const EditCircleUnitModal = ({
  onClose, open, advertiserId, unit, callback
}: Props) => {
  const [conversionTools, setConversionTools] = useState<ConversionTool[]>([])
  const [selectedTool, setSelectedTool] = useState('')
  const [conversionMetrics, setConversionMetrics] = useState<ConversionMetric[]>([])
  const [selectedMetric, setSelectedMetric] = useState('')
  const { openToast } = useToast()
  const [editInfo, setEditInfo] = useState({
    analyticsMetric: '',
    analyticsType: ''
  })

  const editCircleUnit = async () => {
    const editUnit: EditCircleUnit = {
      campaignId: unit.campaignId,
      mediaAccountId: unit.mediaAccountId,
      mediaType: unit.mediaType,
      analyticsMetric: editInfo.analyticsMetric,
      analyticsType: editInfo.analyticsType
    }
    await circleApi.editCircleUnit(advertiserId, unit.unitId, editUnit)
    callback()
    onClose()
    openToast('수정 되었습니다.')
  }

  const handleChange = (event: any) => {
    const { name, value } = event.target
    if (name === 'conversionTool') setSelectedTool(value)
    if (name === 'conversionMetric') setSelectedMetric(value)
  }

  const getConversionTools = async () => {
    const response = await circleApi.getConversionTools(advertiserId)
    setConversionTools(response ?? [])
  }

  const getConversionMetrics = async (tool: ConversionTool) => {
    const query = {
      mediaType: unit.mediaType,
      accountId: unit.mediaAccountId,
      analyticsType: tool.analyticsType,
      analyticsAccountId: tool.analyticsAccountId,
      analyticsSubId: tool.analyticsSubId
    } as ConversionMetricQuery
    const response = await circleApi.getConversionMetrics(advertiserId, query)
    setConversionMetrics(response ?? [])
  }

  useEffect(() => {
    if (selectedTool) {
      const tool = conversionTools[Number(selectedTool)]
      setEditInfo({ ...editInfo, analyticsType: tool.analyticsType })
      getConversionMetrics(tool)
    }
  }, [selectedTool])

  useEffect(() => {
    if (selectedMetric) {
      const metric = conversionMetrics[Number(selectedMetric)]
      setEditInfo({ ...editInfo, analyticsMetric: metric.analyticsMetric })
    }
  }, [selectedMetric])

  useEffect(() => {
    getConversionTools()
  }, []) //eslint-disable-line
  return (
    <Dialog className="edit-circle-unit-modal" open={open} onClose={onClose}>
      <section className="edit-circle-unit-modal__header">
        <MopIcon customClass="icon-close" name={MOPIcon.CLOSE} onClick={onClose} />
        <MopIcon name={MOPIcon.EDIT} />
        <span>전환값 수정</span>
      </section>
      <section className="edit-circle-unit-modal__body">
        <section className="edit-circle-unit-modal__content">
          <RoundedDropdown
            name="conversionTool"
            label="전환툴"
            all={false}
            font={12}
            dropdownSize={32}
            value={selectedTool}
            onChange={handleChange}
          >
            {conversionTools.map(({ analyticsAccountId, analyticsSubId, analyticsType }, index) => (
              <MenuItem key={index} value={String(index)}>
                <div className="media-account-item">
                  <MediaIcon mediaType={analyticsType}/>
                  <span className="media-account-item__name">{ analyticsAccountId }</span>
                  <span>{ analyticsSubId }</span>
                </div>
              </MenuItem>
            ))}
          </RoundedDropdown>
          <RoundedDropdown
            name="conversionMetric"
            label="전환값"
            all={false}
            font={12}
            dropdownSize={32}
            value={selectedMetric}
            onChange={handleChange}
          >
            {conversionMetrics.map(({ analyticsMetricName }, index) => (
              <MenuItem key={index} value={String(index)}>
                <span>{ analyticsMetricName }</span>
              </MenuItem>
            ))}
          </RoundedDropdown>
        </section>
        <section className="edit-circle-unit-modal__footer">
          <MopActionButton label="SAVE" onClick={editCircleUnit} />
        </section>
      </section>
    </Dialog>
  )
}

export default EditCircleUnitModal