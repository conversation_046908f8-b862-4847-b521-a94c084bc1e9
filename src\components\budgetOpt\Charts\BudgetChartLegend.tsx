import React, { CSSProperties } from 'react';
import tw from 'twin.macro'
import { VarianceChip, BaseChip } from '@components/common/chip'
import { useTranslation } from 'react-i18next';

import './BudgetChartLegend.scss';

const varianceStyle = tw`w-[60px] h-[20px] text-xxs leading-5`
const platformStyle = tw`w-[40px] h-[18px] text-xxs`

interface RegendInfoItem {
    budget: number;
    budgetRatio?: Number;
    ratio?: number;
    color: string;
}

interface RegendInfo {
    [key: string]: RegendInfoItem;
}

interface Props {
    regendInfo: RegendInfo;
    type: string;
}

const BudgetChartLegend: React.FC<Props> = ({ regendInfo, type }) => {
    const { t } = useTranslation();

    return (
        <ul className="budget-chart-legend-label-list p-0 my-0">
            { Object.entries(regendInfo).map(([key, { budget, budgetRatio, ratio, color }]) => {
                const [mediaType, accountId, adType] = key.split('-');
                return (
                    <li key={key} className="flex items-center gap-3.5">
                        <div><span className="media-type" style={{"--label-color" : `${color}`} as CSSProperties}>{ t(`common.code.media.${mediaType}`) }</span></div>
                        <div><BaseChip customStyle={platformStyle}>{adType}</BaseChip></div>
                        { type==="recommend" && <VarianceChip customStyle={varianceStyle} value={ratio ?? 0} />
                            // : <div><span className="none">{ Number(Number(budgetRatio)*100).toFixed(2) }%</span></div>
                        }
                    </li>
                )
            })}
        </ul>
    )
}

export default BudgetChartLegend;