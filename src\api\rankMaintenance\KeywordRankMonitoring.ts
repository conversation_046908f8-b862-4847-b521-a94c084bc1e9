/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  CompetitorInfo,
  CompetitorsInfoUpdateRequest,
  KeywordRankMonitoringChartRequest,
  KeywordRankMonitoringChartResult,
  KeywordRankMonitoringTablePerDate,
  KeywordRankMonitoringTableRequest,
  KeywordRankMonitoringViewType,
} from '@models/rankMaintenance/KeywordRankMonitoring';

export const getChartTypeKeywordRankMonitorings = async (
  param: KeywordRankMonitoringChartRequest,
  isLoading = true
): Promise<KeywordRankMonitoringChartResult[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/monitoring/keyword/${param.keywordMonitoringId}`,
    method: Method.GET,
    params: {
      queryParams: {
        viewType: KeywordRankMonitoringViewType.GRAPH,
        startDate: param.startDate,
        endDate: param.endDate,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as KeywordRankMonitoringChartResult[];
};

export const getTableTypeKeywordRankMonitorings = async (
  param: KeywordRankMonitoringTableRequest,
  isLoading = true
): Promise<KeywordRankMonitoringTablePerDate[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/monitoring/keyword/${param.keywordMonitoringId}`,
    method: Method.GET,
    params: {
      queryParams: {
        viewType: KeywordRankMonitoringViewType.TABLE,
        startDate: param.startDate,
        endDate: param.endDate,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as KeywordRankMonitoringTablePerDate[];
};

export const getKeywordRankMonitoringCompetitors = async (
  keywordMonitoringId: number,
  isLoading = true
): Promise<CompetitorInfo[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/monitoring/keyword/${keywordMonitoringId}/competitors`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as CompetitorInfo[];
};

/* istanbul ignore next */
export const updateKeywordRankMonitoringCompetitors = async (param: CompetitorsInfoUpdateRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/monitoring/keyword/${param.keywordMonitoringId}/competitors`,
    method: Method.POST,
    params: {
      bodyParams: param.competitors,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

/* istanbul ignore next */
export const deleteKeywordRankMonitoringCompetitor = async (
  keywordMonitoringId: number,
  displayUrl: string,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/monitoring/keyword/${keywordMonitoringId}/competitors`,
    method: Method.DELETE,
    params: {
      queryParams: {
        displayUrl: displayUrl,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};
