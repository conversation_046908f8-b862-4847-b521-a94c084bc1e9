.panelTable {
  width: 100%;
  height: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: #ffffff;
}

.panelTable th,
.panelTable td {
  padding: 10px 12px;
  border-bottom: 1px solid #e5e7eb;
  text-align: center;
  min-width: 120px;
  width: 120px;
}

/* Make first column (Division) wider */
.panelTable th:first-child,
.panelTable td:first-child {
  min-width: 120px;
  width: 120px;
}

.stickyHeader{
  text-align: center !important;
}

/* Override PinnedTable styles for row panel */
.rowPanelCell {
  background: #f9fafb;
  white-space: normal;
  padding: 0;
  height: 100%;
  vertical-align: top;
}

.rowPanelInner {
  height: 100%;
  display: flex;
  flex-direction: column;
}



*::-webkit-scrollbar {
  width: 13px;
  height: 13px;
}