import StackedBarChart from '@components/common/chart/StackedBarChart';
import useCustomTooltip from '@components/common/hook/UseCustomTooltip';
import { BarChartData } from '@models/common/ChartData';
import { KpiItem } from '@models/budgetOpt/BudgetOpt';

type ImpactData = (number|null)[]
const generateBarColor = (data: ImpactData) => {
  return data.map(item => {
    if (!item) return ''
    if (item > 0) return '#EB424C';
    return '#3B5EC9'
  })
}

const generateAnnotationLabels = (data: ImpactData) => {
  // @ts-ignore
  return data.reduce((total, curr, index) => {
    if(!curr) return total
    const impactLabel = {
      type: 'label',
      xValue: index,
      yValue: curr > 0 ? curr + 10 : curr - 10  ,
      color: curr > 0 ? '#FF6363' : '#5E81F4',
      content: curr > 0 ? `+${curr}%` : `${curr}%`,
      font: {
        size: 14,
        weight: 700
      }
    }
    return total = {
      ...total,
      [`impactLabel${index}`] : impactLabel
    }
  }, {})
}
interface Props {
  kpiImpact: {[key: string]: number};
  selectedKpis: KpiItem[];
}

const ImpactKeys = {
  "IMPRESSIONS": "Impressions",
  "VIEWS": "Views",
  "CLICKS": "Clicks",
  "CONVERSIONS": "Conversions",
  "REVENUE": "Revenue",
}

const EstimatedImpactChart = ({ kpiImpact, selectedKpis }:Props) => {
  const labels = selectedKpis.map(kpi => ImpactKeys[kpi.kpiType])

  const chartData = labels.reduce((acc, curr) => {
    const key = curr.toLocaleLowerCase()
    const value = kpiImpact[key]
    const hasKpi = selectedKpis.find((kpi: any) => kpi.kpiType === key.toLocaleUpperCase())
    acc.push(!hasKpi ? null : Number(value.toFixed(2)))
    return acc
  }, [] as (number|null)[])

  const { tooltipInfo } = useCustomTooltip({});
  const options: any = {
    maintainAspectRatio: false,
    plugins: {
      title: {
        display: false,
      },
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
      },
      annotation: {
        clip: false,
        annotations: {
          line1: {
            type: 'line',
            yMin: 0,
            yMax: 0,
            borderColor: '#CCCCCC',
            borderWidth: 1,
          },
          ...generateAnnotationLabels(chartData),
        },
      },
    },
    scales: {
      x: {
        grid: {
          borderColor: 'transparent',
          tickColor: 'transparent',
          display: false,
        },
        ticks: {
          autoSkip: false,
          maxRotation: 0,
          minRotation: 0,
          color: '#040a45',
          font: {
            size: 10,
            weight: 300
          }
        },
        barThickness: 6,
      },
      y: {
        ticks: {
          display: false
        },
        grid: {
          borderColor: 'transparent',
          tickColor: 'transparent',
          display: false,
        },
        max: 70,
        min: -70
      }
    },
    responsive: true,
  };
  const data = {
    labels: labels,
    datasets: [
      {
        data: chartData,
        backgroundColor: generateBarColor(chartData),
        barThickness: 30
      }
    ]
  } as unknown as BarChartData
  return (
    <StackedBarChart chartData={data} options={options} tooltipInfo={tooltipInfo} />
  )
}

export default EstimatedImpactChart