import * as ApiUtil from '@utils/ApiUtil';
import { convertDateToStr, getBeforeDate } from '@utils/DateUtil';
import {
  getSaShoppingAbnormalPerformance,
  getSaShoppingAbnormalLock,
  getSaShoppingCollectionItem,
  getSaShoppingCollectionPerformance,
  getSaShoppingCollectionStatus,
  getSaShoppingFlightBid,
  getSaShoppingMaintenance,
  getSaShoppingProjectionPlanning,
  getSaShoppingProjectionPredictions,
  getSaShoppingReport,
} from '@api/dashboard/SaShoppingOptDashboard';
import { SaShoppingType } from '@models/dashboard/DashboardStatus';

describe('getSaShoppingReport', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 조회기간 지난 1일-7일 & 비교기간 지난 8일-14일 SaShoppingReport를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        salesAmount: { value: "16202120", compareValue: "17668882", ratio: "-8.3" },
        impressions: { value: "3420459", compareValue: "3871641", ratio: "-11.65" },
        clicks: { value: "43833", compareValue: "49087", ratio: "-10.7" },
        transactions: { value: "275", compareValue: "843", ratio: "-67.38" },
        transactionRevenue: { value: "23485000", compareValue: "71661800", ratio: "-67.23" },
        ctr: { value: "1.28", compareValue: "1.27", ratio: "0.79" },
        cvr: { value: "0.63", compareValue: "1.72", ratio: "-63.37" },
        cpc: { value: "369.6329", compareValue: "359.9503", ratio: "2.69" },
        cpa: { value: "58916.8000", compareValue: "20959.5279", ratio: "181.1" },
        roas: { value: "144.95", compareValue: "405.58", ratio: "-64.26" }
      }
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingReport(
      1,
      SaShoppingType.SHOPPING,
      [7, 1, 14, 8].map((diff) => convertDateToStr(getBeforeDate(diff)))
    );
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingReport(
      1,
      SaShoppingType.SHOPPING,
      [7, 1, 14, 8].map((diff) => convertDateToStr(getBeforeDate(diff)))
    );
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingAbnormalPerformance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: [{
        mediaType: "NAVER",
        accountId: "356547",
        accountName: "ABC마트",
        campaignId: "cmp-a001-02-***************",
        campaignName: "1_쇼핑검색광고_PC",
        adgroupId: "grp-a001-02-***************",
        adgroupName: "1. 반스_운동화_3",
        message: "ctr is high, and impression decreases about 4.2% and click increases about 100% compared to the last week.",
        createdDatetime: "2023.04.27 23:41:01"
      }, {
        mediaType: "NAVER",
        accountId: "356547",
        accountName: "ABC마트",
        campaignId: "cmp-a001-02-***************",
        campaignName: "1_쇼핑검색광고_모바일",
        adgroupId: "grp-a001-02-***************",
        adgroupName: "1. 반스_운동화_3",
        message: "Impression is 51.0 and it decreases 87.09% compared to the last week.",
        createdDatetime: "2023.04.27 23:41:01"
      }, {
        mediaType: "NAVER",
        accountId: "356547",
        accountName: "ABC마트",
        campaignId: "cmp-a001-02-***************",
        campaignName: "1_쇼핑검색광고_모바일",
        adgroupId: "grp-a001-02-***************",
        adgroupName: "1. 라코스테_운동화_1",
        message: "Impression is 51.0 and it decreases 87.09% compared to the last week.",
        createdDatetime: "2023.04.27 23:41:01"
      }, {
        mediaType: "NAVER",
        accountId: "356547",
        accountName: "ABC마트",
        campaignId: "cmp-a001-02-***************",
        campaignName: "1_쇼핑검색광고_PC",
        adgroupId: "grp-a001-02-***************",
        adgroupName: "1. 반스_운동화_3",
        message: "ctr is high, and impression decreases about 4.2% and click increases about 100% compared to the last week.",
        createdDatetime: "2023.04.28 00:29:43"
      }, {
        mediaType: "NAVER",
        accountId: "356547",
        accountName: "ABC마트",
        campaignId: "cmp-a001-02-***************",
        campaignName: "1_쇼핑검색광고_모바일",
        adgroupId: "grp-a001-02-***************",
        adgroupName: "1. 반스_운동화_3",
        message: "Impression is 51.0 and it decreases 87.09% compared to the last week.",
        createdDatetime: "2023.04.28 00:29:43"
      }, {
        mediaType: "NAVER",
        accountId: "356547",
        accountName: "ABC마트",
        campaignId: "cmp-a001-02-***************",
        campaignName: "1_쇼핑검색광고_모바일",
        adgroupId: "grp-a001-02-***************",
        adgroupName: "1. 라코스테_운동화_1",
        message: "Impression is 51.0 and it decreases 87.09% compared to the last week.",
        createdDatetime: "2023.04.28 00:29:43"
      }]
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingAbnormalPerformance(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingAbnormalPerformance(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingAbnormalLock', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        lastUpdated: "2023.04.28 03:41:43",
        abnormals: [{
          mediaType: "NAVER",
          accountId: "356547",
          accountName: "ABC마트",
          adId: "nad-a001-02-***************",
          productTitle: "나이키 NIKE 나이키 오니온타 샌들 DJ6603-200",
          campaignName: "1_쇼핑검색광고_모바일",
          adgroupName: "1. 나이키_샌들",
          adUpdated: "2023.04.26 17:36:04"
        }, {
          mediaType: "NAVER",
          accountId: "356547",
          accountName: "ABC마트",
          adId: "nad-a001-02-***************",
          productTitle: "나이키 NIKE 나이키 오니온타 샌들 DJ6603-200",
          campaignName: "1_쇼핑검색광고_PC",
          adgroupName: "1. 나이키_샌들",
          adUpdated: "2023.04.26 17:36:04"
        }, {
          mediaType: "NAVER",
          accountId: "356547",
          accountName: "ABC마트",
          adId: "nad-a001-02-***************",
          productTitle: "나이키 NIKE 나이키 오니온타 넥스트 네이처 샌들 DJ6603-002",
          campaignName: "1_쇼핑검색광고_PC",
          adgroupName: "1. 나이키_샌들",
          adUpdated: "2023.04.26 03:34:27"
        }, {
          mediaType: "NAVER",
          accountId: "356547",
          accountName: "ABC마트",
          adId: "nad-a001-02-***************",
          productTitle: "나이키 NIKE 나이키 오니온타 넥스트 네이처 샌들 DJ6603-002",
          campaignName: "1_쇼핑검색광고_모바일",
          adgroupName: "1. 나이키_샌들",
          adUpdated: "2023.04.26 03:34:27"
        }]
      }
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingAbnormalLock(1, SaShoppingType.SHOPPING);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingAbnormalLock(1, SaShoppingType.SHOPPING);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingCollectionStatus', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Collection의 상태를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        media: [{
          type: "NAVER",
          useYn: "Y",
          status: "OK"
        }]
      }
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingCollectionStatus(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingCollectionStatus(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingCollectionItem', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Collection Item 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        optimizations: 1,
        campaigns: 2,
        adgroups: 204,
        ads: 3770
      }
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingCollectionItem(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingCollectionItem(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingCollectionPerformance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 주어진 기간의 Collection Performance를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        lastUpdated: "2023.04.28 11:41:49",
        status: "OK",
        performances: [956913, 939135, 1010441, 1056633, 1019513, 931560, 885741, 861209, 840800, 885279, 85389, 93850, 48880, 53751]
      }
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingCollectionPerformance(1, 14);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingCollectionPerformance(1, 14);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingProjectionPlanning', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Planning을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        optimizations: {
          lastUpdated: "2023.04.28 00:30:10",
          status: "OK",
          totalCount: 1,
          runCount: 1
        },
        ads: {
          bidsCount: 506
        }
      }
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingProjectionPlanning(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingProjectionPlanning(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingProjectionPredictions', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Prediction을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        models: {
          lastUpdated: "2023.04.26 18:04:31",
          status: "OK",
          totalCount: 24
        },
        predictions: {
          status: "ERROR"
        }
      }
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingProjectionPredictions(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingProjectionPredictions(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingMaintenance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Maintenance을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        adCount: 1,
        rankTotalCount: 9,
        rankAchivementCount: 9,
        reachMaxCpc: [],
        monitoring: [{
          date: "20230422",
          values: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
        }, {
          date: "20230423",
          values: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
        }, {
          date: "20230424",
          values: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
        }, {
          date: "20230425",
          values: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
        }, {
          date: "20230426",
          values: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]
        }, {
          date: "20230427",
          values: [8, 8, 8, 8, 8, 5, 5, 5, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        }, {
          date: "20230428",
          values: [2, 2, 2, 2, 2, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
        }]
      }
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingMaintenance(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingMaintenance(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaShoppingFlightBid', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, bid 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: "Y",
      statusCode: "SUCCESS",
      data: {
        bidsCount: [505, 0, 505, 0, 522, 519],
        bidsHours: [0, 5, 10, 15, 20],
        lastUpdated: "2023.04.28 08:00:51",
        currentTime: "2023.04.28 12:24:38",
        nextBidTime: "2023.04.28 15:00:00"
      }
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingFlightBid(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaShoppingFlightBid(1);
    expect(response).toEqual(undefined);
  });
});
