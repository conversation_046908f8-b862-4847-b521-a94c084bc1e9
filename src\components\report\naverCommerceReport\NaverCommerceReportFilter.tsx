import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Checkbox, FormLabel, Popover } from '@material-ui/core'
import RangeCalendar from '@components/common/RangeCalendar'
import { DateRange, ReportCategoryType } from '@models/report/Common'
import { useRecoilValue } from 'recoil'
import { advertiserState } from '@store/Advertiser'
import { getBeforeDate, convertStrToDate, getGapInDays, getMaxDate } from '@utils/DateUtil'
import { ActiveDate } from '@models/report/ActiveDate'
import { format, areIntervalsOverlapping } from 'date-fns'
import { DateFnsFormat, QueryKeys } from '@models/common/CommonConstants'
import { ReactComponent as CalendarIcon } from '@components/assets/images/icon_calendar.svg'
import { useToast, useDialog } from '@hooks/common'
import './NaverCommerceReportFilter.scss'
import dayjs from 'dayjs'
import { FunctionId, SubscriptionProductType } from '@models/common/Advertiser'
import MopSelect from '@components/common/mopUI/MopSelect'
import MopSelectVirtual from '@components/common/mopUI/MopSelectVirtual'
import MopSelectOption from '@components/common/mopUI/MopSelectOption'
import MuiInput from '@components/common/mopUI/MuiInput'
import TagDisplay from './TagDisplay'
import {
  getNaverCommerceOptimizations,
  getNaverCommerceProducts,
  getNaverCommerceReportSummary,
  getNaverCommerceMinMaxDate,
  getNaverCommerceGraph
} from '@api/report/NaverCommerce'
import {
  NaverCommerceIndicatorSummary,
  NaverCommerceOptimization,
  NaverCommerceProduct,
  NaverCommerceGraphResponse
} from '@models/report/NaverCommerce'
import { useQuery } from '@tanstack/react-query'
import TruncatedText from '@components/common/TruncatedText'
import { MopIcon } from '@components/common'
import { MOPIcon } from '@models/common'
import { generateNaverCommerceBodyParams } from '@utils/createCampaign'

// #region Types
const STATUS_COLORS = {
  Live: 'bg-[#6EC7C2]',
  OFF: 'bg-[#CCCCCC]'
}

interface NaverCommerceReportFilterProps {
  activeDate: ActiveDate
  variance: boolean
  setAppliedProducts: React.Dispatch<React.SetStateAction<string[]>>
  setAppliedOptimizations: React.Dispatch<React.SetStateAction<string[]>>
  setActiveDate: React.Dispatch<React.SetStateAction<ActiveDate>>
  onUpdateSummary: (summary: NaverCommerceIndicatorSummary | undefined) => void
  onUpdateGraphData: (graphData: NaverCommerceGraphResponse | undefined) => void
  setVariance: React.Dispatch<React.SetStateAction<boolean>>
}

type Row = {
  key: string
  index: number
  type: 'item' | 'header'
  platform?: string
}
// #endregion

// #region Helper functions
// Convert API optimization data to display format
const convertOptimizationsToDisplay = (optimizations: NaverCommerceOptimization[]) => {
  return optimizations.map((opt) => ({
    value: opt.optimizationId.toString(),
    label: opt.optimizationName,
    code: opt.optimizationId.toString(),
    platform: 'NAVER',
    status: opt.bidYn === 'Y' ? 'Live' : 'OFF',
    startDate: opt.bidStartDate,
    endDate: opt.bidEndDate || '종료일 미설정'
  }))
}

// Convert API product data to display format
const convertProductsToDisplay = (products: NaverCommerceProduct[]) => {
  return products.map((item) => ({
    value: item.channelProductId,
    label: item.channelProductName,
    platform: 'NAVER',
    code: item.channelProductId,
    imageUrl: item.channelProductImageUrl
  }))
}
// #endregion

const NaverCommerceReportFilter: React.FC<NaverCommerceReportFilterProps> = ({
  activeDate,
  setActiveDate,
  onUpdateSummary,
  onUpdateGraphData,
  setAppliedProducts,
  setAppliedOptimizations,
  setVariance
}: NaverCommerceReportFilterProps) => {
  const { t } = useTranslation()
  const { openToast } = useToast()
  const { openDialog } = useDialog()
  const advertiser = useRecoilValue(advertiserState)
  const selectablePeriodLimit = DateRange.DEFAULT

  const [startDate, setStartDate] = useState<Date>(convertStrToDate(activeDate?.startDate) || new Date())
  const [endDate, setEndDate] = useState<Date>(convertStrToDate(activeDate?.endDate) || new Date())
  const [isCompare, setIsCompare] = useState<boolean>(false)
  const [compareStartDate, setCompareStartDate] = useState<Date>(getBeforeDate(7, startDate))
  const [compareEndDate, setCompareEndDate] = useState<Date>(getBeforeDate(1, startDate))
  const [anchorEl1, setAnchorEl1] = useState<HTMLElement | null>(null)
  const [anchorEl2, setAnchorEl2] = useState<HTMLElement | null>(null)

  const [minDate, setMinDate] = useState<Date>(dayjs().subtract(90, 'day').toDate())
  const [maxDate, setMaxDate] = useState<Date>(dayjs().toDate())
  const [isNotAvailableUnit, setIsNotAvailableUnit] = useState<boolean>(false)

  // Optimization selection state and logic
  const [selectedOptimizations, setSelectedOptimizations] = useState<string[]>([])
  const [tempSelectedOptimizations, setTempSelectedOptimizations] = useState<string[]>(selectedOptimizations) // Temporary state for dropdown
  const optimizationToolsRef = useRef<HTMLDivElement | null>(null)
  const [optimizationItemH] = useState(56)

  // Product selection state and logic
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const productToolsRef = useRef<HTMLDivElement | null>(null)
  const [productItemH] = useState(56)
  const [keyword, setKeyword] = useState<string>('')
  const [isSelectingOptimization, setIsSelectingOptimization] = useState(false) // Add handler for optimization dropdown open/close

  const { data: optimizations = [], isLoading: isOptimizationsLoading } = useQuery({
    queryKey: [QueryKeys.NAVER_COMMERCE_OPTIMIZATIONS, advertiser.advertiserId],
    queryFn: () => getNaverCommerceOptimizations(advertiser.advertiserId, false),
    enabled: !!advertiser.advertiserId
  })

  const { data: products = [], isLoading: isProductsLoading } = useQuery({
    queryKey: [
      QueryKeys.NAVER_COMMERCE_PRODUCTS,
      {
        advertiserId: advertiser.advertiserId,
        selectedOptimizations: selectedOptimizations
      }
    ],
    queryFn: () =>
      getNaverCommerceProducts(advertiser.advertiserId, false, {
        optimizationIds: selectedOptimizations
      }),
    enabled: !!advertiser.advertiserId
  })

  useEffect(() => {
    if (isProductsLoading) return
    setSelectedProducts([])
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [products])

  const handleChangeOptimization = (val: string[]) => {
    setTempSelectedOptimizations(val)
    setIsSelectingOptimization(true)
  }

  const handleOptimizationDropdownClose = () => {
    setIsSelectingOptimization(false)
    setSelectedOptimizations(tempSelectedOptimizations)
  }

  const summarizeTempOptimizationSelection = ({
    values,
    getLabel
  }: {
    values: string | string[]
    getLabel: (v: string) => string
  }) => {
    const displayOptimizations = convertOptimizationsToDisplay(optimizations)
    const handleRemove = (value: string) => {
      setTempSelectedOptimizations((prev) => prev.filter((v) => v !== value))
      setSelectedOptimizations((prev) => prev.filter((v) => v !== value))
    }
    return (
      <TagDisplay
        values={values}
        getLabel={getLabel}
        onRemove={handleRemove}
        campaigns={displayOptimizations}
        isOptimization
      />
    )
  }

  const summarizeProductSelection = ({
    values,
    getLabel
  }: {
    values: string | string[]
    getLabel: (v: string) => string
  }) => {
    const displayProducts = convertProductsToDisplay(products)
    const handleRemove = (value: string) => {
      setSelectedProducts((prev) => prev.filter((v) => v !== value))
    }
    return <TagDisplay values={values} getLabel={getLabel} onRemove={handleRemove} campaigns={displayProducts} />
  }

  const getMinMaxDate = useCallback(async (): Promise<boolean> => {
    const result = await getNaverCommerceMinMaxDate(advertiser.advertiserId, ReportCategoryType.REPORT_PRODUCT)

    if (result) {
      setMinDate(dayjs(result.minDate).toDate())
      setMaxDate(dayjs(result.maxDate).toDate())
      setIsNotAvailableUnit(false)
      return true
    } else {
      setIsNotAvailableUnit(true)
      return false
    }
  }, [advertiser.advertiserId])
  const fetchAllData = async (queryParams: ActiveDate, productIds: string[]) => {
    try {
      const bodyParams = generateNaverCommerceBodyParams(productIds, selectedOptimizations)
      const [summaryResult, graphResult] = await Promise.allSettled([
        getNaverCommerceReportSummary(advertiser.advertiserId, queryParams, bodyParams),
        getNaverCommerceGraph(advertiser.advertiserId, queryParams, bodyParams)
      ])

      // Handle summary result
      if (summaryResult.status === 'fulfilled') {
        onUpdateSummary(summaryResult.value)
      } else {
        console.error('Error fetching summary:', summaryResult.reason)
        openToast(t('naverReport.filter.toastMessages.summaryError'))
        onUpdateSummary(undefined)
      }

      // Handle graph result
      if (graphResult.status === 'fulfilled') {
        onUpdateGraphData(graphResult.value)
      } else {
        console.error('Error fetching graph:', graphResult.reason)
        openToast(t('naverReport.filter.toastMessages.graphError'))
        onUpdateGraphData(undefined)
      }
    } catch (error) {
      console.error('Unexpected error in fetchData:', error)
      openToast(t('naverReport.filter.toastMessages.unexpectedError'))
      onUpdateSummary(undefined)
      onUpdateGraphData(undefined)
    }
  }
  const fetchData = async () => {
    if (isCompare) {
      const primaryPeriod =
        getGapInDays(format(startDate, DateFnsFormat.ISO_DATE), format(endDate, DateFnsFormat.ISO_DATE)) + 1
      const comparePeriod =
        getGapInDays(format(compareStartDate, DateFnsFormat.ISO_DATE), format(compareEndDate, DateFnsFormat.ISO_DATE)) +
        1

      // Check if periods have different lengths
      if (primaryPeriod !== comparePeriod) {
        openDialog({
          title: t('naverReport.filter.dialogTitle'),
          message: t('report.label.ReportFilter.toastMessage.differentPeriodLength'),
          actionLabel: t('common.button.confirm'),
          onAction: () => {},
          onClose: () => {}
        })
        return
      }

      // Check if periods overlap
      if (
        areIntervalsOverlapping(
          { start: startDate, end: endDate },
          { start: compareStartDate, end: compareEndDate },
          { inclusive: true }
        )
      ) {
        openToast(t('report.label.ReportFilter.toastMessage.invalidPeriod'))
        return
      }
    }

    setAppliedProducts(selectedProducts)
    setAppliedOptimizations(selectedOptimizations)

    if (compareStartDate && compareEndDate) {
      setVariance(true)
    } else {
      setVariance(false)
    }

    // Use dynamic values instead of hardcoded data
    const queryParams = {
      startDate: format(startDate, DateFnsFormat.ISO_DATE),
      endDate: format(endDate, DateFnsFormat.ISO_DATE),
      ...(isCompare
        ? {
            compareStartDate: format(compareStartDate, DateFnsFormat.ISO_DATE),
            compareEndDate: format(compareEndDate, DateFnsFormat.ISO_DATE)
          }
        : {})
    }

    const newActiveDate = {
      startDate: format(startDate, DateFnsFormat.ISO_DATE),
      endDate: format(endDate, DateFnsFormat.ISO_DATE),
      ...(isCompare
        ? {
            compareStartDate: format(compareStartDate, DateFnsFormat.ISO_DATE),
            compareEndDate: format(compareEndDate, DateFnsFormat.ISO_DATE)
          }
        : {})
    }
    setActiveDate(newActiveDate)
    fetchAllData(queryParams, selectedProducts)
  }
  useEffect(() => {
    const initializeData = async () => {
      const hasMinMaxDate = await getMinMaxDate()
      if (!hasMinMaxDate) {
        openToast(t('report.label.ReportCards.toastMessage.noAvaliabilityUnit'))
      }
      fetchAllData(activeDate, [])
    }
    initializeData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if (isCompare) {
      const period =
        getGapInDays(format(startDate, DateFnsFormat.ISO_DATE), format(endDate, DateFnsFormat.ISO_DATE)) + 1
      setCompareStartDate(getMaxDate(getBeforeDate(period, startDate), dayjs(minDate).toDate()))
      setCompareEndDate(getMaxDate(getBeforeDate(1, startDate), dayjs(minDate).toDate()))
    }
  }, [isCompare, startDate, endDate, minDate])

  // Optimization filtering and selection logic
  const displayOptimizations = useMemo(() => convertOptimizationsToDisplay(optimizations), [optimizations])
  const filteredOptimizations = displayOptimizations // No filtering for optimizations currently
  const visibleOptimizationValues = filteredOptimizations.map((o) => o.value)
  const allOptimizationsSelectedVisible =
    visibleOptimizationValues.length > 0 &&
    visibleOptimizationValues.every((v) => tempSelectedOptimizations.includes(v))
  const someOptimizationsSelectedVisible =
    visibleOptimizationValues.some((v) => tempSelectedOptimizations.includes(v)) && !allOptimizationsSelectedVisible

  const toggleAllOptimizationsVisible = () => {
    if (allOptimizationsSelectedVisible) {
      setTempSelectedOptimizations((prev) => prev.filter((v) => !visibleOptimizationValues.includes(v)))
    } else {
      setTempSelectedOptimizations((prev) => Array.from(new Set([...prev, ...visibleOptimizationValues])))
    }
  }

  // Product filtering and selection logic
  const displayProducts = useMemo(() => convertProductsToDisplay(products), [products])
  const filteredProducts = useMemo(() => {
    if (!keyword.trim()) return displayProducts
    return displayProducts.filter(
      (product) =>
        product.label.toLowerCase().includes(keyword.toLowerCase()) ||
        product.code.toLowerCase().includes(keyword.toLowerCase())
    )
  }, [displayProducts, keyword])
  const enabledVisibleProductValues = filteredProducts.map((p) => p.value)
  const allProductsSelectedVisible =
    enabledVisibleProductValues.length > 0 && enabledVisibleProductValues.every((v) => selectedProducts.includes(v))
  const someProductsSelectedVisible =
    (enabledVisibleProductValues.some((v) => selectedProducts.includes(v)) && !allProductsSelectedVisible) ||
    (selectedProducts.length > 0 && enabledVisibleProductValues.every((v) => !selectedProducts.includes(v)))

  const toggleAllProductsVisible = () => {
    if (allProductsSelectedVisible) {
      setSelectedProducts((prev) => prev.filter((v) => !enabledVisibleProductValues.includes(v)))
    } else {
      setSelectedProducts((prev) => Array.from(new Set([...prev, ...enabledVisibleProductValues])))
    }
  }

  const optimizationRows: Row[] = useMemo(() => {
    const rows: Row[] = []
    let currentPlatform = ''

    filteredOptimizations.forEach((optimization, idx) => {
      if (optimization.platform !== currentPlatform) {
        rows.push({
          key: `header-${optimization.platform}`,
          index: -1,
          type: 'header' as const,
          platform: optimization.platform
        })
        currentPlatform = optimization.platform
      }

      // Add optimization item
      rows.push({
        key: optimization.value,
        index: idx,
        type: 'item' as const
      })
    })

    return rows
  }, [filteredOptimizations])

  const optimizationHeaderTools = (
    <div ref={optimizationToolsRef} className="sticky top-0 z-10 bg-white border-b border-[#efefef]">
      <div className="px-4 py-3 flex items-center justify-start gap-2">
        <Checkbox
          checked={allOptimizationsSelectedVisible}
          indeterminate={someOptimizationsSelectedVisible}
          onChange={toggleAllOptimizationsVisible}
          color="primary"
        />
        <div className="text-[#5E81F4] text-xs font-bold" onClick={toggleAllOptimizationsVisible}>
          {allOptimizationsSelectedVisible
            ? t('naverReport.filter.selectAll.unselectAll')
            : t('naverReport.filter.selectAll.selectAll')}
        </div>
      </div>
    </div>
  )

  const productHeaderTools = (
    <div ref={productToolsRef} className="sticky top-0 z-10 bg-white border-b border-[#efefef]">
      <div className="px-4 pt-3 pb-2">
        <MuiInput
          placeholder="상품 검색하기 (이름/코드)"
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
        />
      </div>
      <div className="px-4 py-3 flex items-center justify-start gap-2">
        <Checkbox
          checked={allProductsSelectedVisible}
          indeterminate={someProductsSelectedVisible}
          onChange={toggleAllProductsVisible}
          color="primary"
        />
        <div className="text-[#5E81F4] text-xs font-bold" onClick={toggleAllProductsVisible}>
          {allProductsSelectedVisible
            ? t('naverReport.filter.selectAll.unselectAll')
            : t('naverReport.filter.selectAll.selectAll')}
        </div>
      </div>
    </div>
  )

  // Build product rows: include one header row then items
  const productRows = useMemo(() => {
    const rows: { key: string; index: number; type: 'item' | 'header'; platform?: string }[] = []
    rows.push({ key: 'product-header-NAVER', index: -1, type: 'header', platform: 'NAVER' })
    filteredProducts.forEach((_, idx) => rows.push({ key: `product-${idx}`, index: idx, type: 'item' }))
    return rows
  }, [filteredProducts])
  return (
    <div className="report-settings-wrapper">
      <div className="report-settings-left">
        <div className="report-settings-container">
          <div className="select-form" style={{ display: 'flex', flex: 1 }}>
            <div className="select-label">
              <FormLabel>{t('report.label.ReportFilterName.reportPeriod')}</FormLabel>
            </div>
            <div className="select-content">
              <div
                onClick={(e) => setAnchorEl1(e.currentTarget)}
                className={`relative cursor-pointer flex items-center justify-between w-full h-[42px] border border-[#efefef] rounded-[4px] px-5 gap-2.5 bg-white text-left text-sm text-[#333] focus:outline-none focus:border-[#17171780] hover:border-[#17171780] 
                `}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    setAnchorEl1(e.currentTarget as any)
                  }
                }}
              >
                <div className="flex items-center gap-2 justify-center w-full">
                  <CalendarIcon className="w-5 h-5" />
                  {format(startDate, DateFnsFormat.ISO_DATE)}&emsp;~&emsp;{format(endDate, DateFnsFormat.ISO_DATE)}
                </div>
                <MopIcon name={MOPIcon.ARROW_DOWN} size={16} customClass="flex-shrink-0" />
              </div>
              <Popover
                className="range-calendar-popover"
                open={!!anchorEl1}
                onClose={() => setAnchorEl1(null)}
                anchorEl={anchorEl1}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'center'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'center'
                }}
              >
                <RangeCalendar
                  startDate={startDate}
                  setStartDate={(date) => setStartDate(date)}
                  endDate={endDate}
                  setEndDate={(date) => setEndDate(date)}
                  minDate={minDate}
                  maxDate={maxDate}
                  onClose={() => setAnchorEl1(null)}
                  isDisplayLastDays={
                    advertiser.subscriptionProductType === SubscriptionProductType.PRO
                      ? [{ 60: false }]
                      : [{ 60: false }, { 0: false }]
                  }
                  isTooltip={{
                    isUsed: true,
                    period: DateRange.DEFAULT,
                    isCompare: false,
                    isPro: false,
                    functionId: FunctionId.REPORT_CAMPAIGN,
                    maxRange: DateRange.DEFAULT
                  }}
                  selectablePeriodLimit={selectablePeriodLimit}
                />
              </Popover>
            </div>
          </div>
          <div className="select-form" style={{ display: 'flex', flex: 1 }}>
            <div className="select-label" style={{ width: 'fit-content' }}>
              <Checkbox
                checked={isCompare}
                onChange={(e) => setIsCompare(e.target.checked)}
                id="isCompare"
                // disabled={isNotAvailableUnit}
                color="primary"
              />
              <FormLabel className={isCompare ? 'on' : 'off'} htmlFor="isCompare">
                {t('report.label.ReportFilterName.comparePeriod')}
              </FormLabel>
            </div>
            <div className="select-content">
              <div
                onClick={(e) => isCompare && setAnchorEl2(e.currentTarget)}
                className={`relative cursor-pointer flex items-center justify-between w-full h-[42px] border border-[#efefef] rounded-[4px] px-5 gap-2.5 bg-white text-left text-sm text-[#333] focus:outline-none focus:border-[#17171780] hover:border-[#17171780] ${
                  !isCompare ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (isCompare && (e.key === 'Enter' || e.key === ' ')) {
                    e.preventDefault()
                    setAnchorEl2(e.currentTarget as any)
                  }
                }}
              >
                <div className="flex items-center gap-2 justify-center w-full">
                  <CalendarIcon className="w-5 h-5" />
                  {format(compareStartDate, DateFnsFormat.ISO_DATE)}&emsp;~&emsp;
                  {format(compareEndDate, DateFnsFormat.ISO_DATE)}
                </div>
                <MopIcon name={MOPIcon.ARROW_DOWN} size={16} customClass="flex-shrink-0" />
              </div>
              <Popover
                className="range-calendar-popover"
                open={!!anchorEl2}
                onClose={() => setAnchorEl2(null)}
                anchorEl={anchorEl2}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'center'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'center'
                }}
              >
                <RangeCalendar
                  startDate={compareStartDate}
                  setStartDate={(date) => setCompareStartDate(date)}
                  endDate={compareEndDate}
                  setEndDate={(date) => setCompareEndDate(date)}
                  minDate={minDate}
                  maxDate={maxDate}
                  onClose={() => setAnchorEl2(null)}
                  refStartDate={startDate}
                  refEndDate={endDate}
                  isTooltip={{
                    isUsed: true,
                    period: DateRange.DEFAULT,
                    isCompare: true,
                    isPro: false,
                    functionId: FunctionId.REPORT_CAMPAIGN,
                    maxRange: DateRange.DEFAULT
                  }}
                  selectablePeriodLimit={selectablePeriodLimit}
                />
              </Popover>
            </div>
          </div>
        </div>
        {/* List Selection */}
        <div className="report-settings-container" style={{ marginTop: '20px' }}>
          <div className="select-form" style={{ display: 'flex', flex: 1 }}>
            <div className="select-label">
              <FormLabel>{t('naverReport.filter.formLabels.optimization')}</FormLabel>
            </div>
            <div className="select-content">
              <MopSelect
                multiple
                value={tempSelectedOptimizations}
                onChange={(val) => handleChangeOptimization(val as string[])}
                placeholder={'None'}
                onClose={handleOptimizationDropdownClose}
                renderSelected={({ values, getLabel }) => summarizeTempOptimizationSelection({ values, getLabel })}
                displayClassName={
                  tempSelectedOptimizations.length > 0
                    ? ''
                    : 'text-[#040A45] h-[72%] flex justify-center bg-[#F2F3F6] text-center items-center w-[53%] ml-5 my-0 -mb-px rounded-[50px]'
                }
                className="h-full"
                containerClassName="h-full"
                listboxClassName="h-full rounded-none"
                disabled={isOptimizationsLoading}
              >
                <MopSelectVirtual
                  rowCount={optimizationRows.length}
                  estimateSize={optimizationItemH}
                  overscan={12}
                  getRowKey={(i) => optimizationRows[i].key}
                  maxHeight="max-h-[420px]"
                  header={optimizationHeaderTools}
                  viewportHeight={420}
                  renderRow={(i) => {
                    const row = optimizationRows[i]

                    // Render group header
                    if (row.type === 'header') {
                      return (
                        <div key={row.key} className="px-4 py-3 bg-[#F2F3F6] border-t border-t-[#5E81F4]">
                          <div className="flex items-center gap-2">
                            <span className="px-3 py-1 bg-transparent border border-[#5E81F4] text-[#5E81F4] rounded-full text-[10px] font-semibold">
                              SPA
                            </span>
                            <span className="text-[#5E81F4] font-bold text-sm">{row.platform}</span>
                          </div>
                        </div>
                      )
                    }

                    // Render optimization item
                    const item = filteredOptimizations[row.index]

                    return (
                      <MopSelectOption
                        key={item.value}
                        value={item.value}
                        render={({ active, selected: isChecked }) => (
                          <li
                            className={`list-none relative cursor-pointer select-none px-4 py-3 transition-colors ${
                              active ? 'bg-[#f9f9fb]' : ''
                            } ${isChecked ? 'bg-[#f9f9fb]' : ''}`}
                          >
                            <div className="grid grid-cols-[20px,1fr,auto] items-center gap-3">
                              <Checkbox checked={isChecked} color="primary" size="small" />
                              <div className="min-w-0 flex gap-3 items-center ">
                                <div className="text-[10px] text-[#5E81F4] border border-[#5E81F4] rounded-full px-2 py-1">
                                  {' '}
                                  Opt ID <span className="text-[#5E81F4] font-semibold">{item.code}</span>
                                </div>
                                <div>
                                  <TruncatedText className="text-xs text-[#333333] truncate font-bold">
                                    {item.label}
                                  </TruncatedText>
                                  <div className="text-xs text-[#040A4591] opacity-55 font-bold">
                                    {item.startDate} - {item.endDate}
                                  </div>
                                </div>
                              </div>
                              <div className="flex flex-col items-end gap-1 ">
                                <span
                                  className={`px-2 py-1 rounded-full text-[8px] font-medium ${
                                    STATUS_COLORS[item.status as keyof typeof STATUS_COLORS]
                                  }`}
                                >
                                  {item.status}
                                </span>
                              </div>
                            </div>
                          </li>
                        )}
                      >
                        {item.label}
                      </MopSelectOption>
                    )
                  }}
                />
              </MopSelect>
            </div>
          </div>
          <div className="select-form" style={{ display: 'flex', flex: 1 }}>
            <div className="select-label" style={{ width: 'fit-content' }}>
              <FormLabel>{t('naverReport.filter.formLabels.product')}</FormLabel>
            </div>
            <div className="select-content">
              <MopSelect
                multiple
                value={selectedProducts}
                onChange={(val) => setSelectedProducts(val as string[])}
                placeholder={'None'}
                renderSelected={summarizeProductSelection}
                displayClassName={
                  selectedProducts.length > 0
                    ? 'text-[#333]'
                    : 'text-[#040A45] h-[72%] flex justify-center bg-[#F2F3F6] text-center items-center w-[53%] ml-5 my-0 -mb-px rounded-[50px]'
                }
                className="h-full"
                containerClassName="h-full"
                listboxClassName="h-full rounded-none"
                disabled={isProductsLoading}
              >
                <MopSelectVirtual
                  rowCount={productRows.length}
                  estimateSize={productItemH}
                  overscan={12}
                  getRowKey={(i) => productRows[i].key}
                  maxHeight="max-h-[420px]"
                  header={productHeaderTools}
                  viewportHeight={420}
                  renderRow={(i) => {
                    const row = productRows[i]
                    if (row.type === 'header') {
                      return (
                        <div key={row.key} className="px-4 py-3 bg-[#F2F3F6] border-t border-t-[#5E81F4]">
                          <div className="flex items-center gap-2">
                            <span className="px-3 py-1 bg-transparent border border-[#5E81F4] text-[#5E81F4] rounded-full text-[10px] font-semibold">
                              SPA
                            </span>
                            <span className="text-[#5E81F4] font-bold text-sm">{row.platform}</span>
                          </div>
                        </div>
                      )
                    }
                    const item = filteredProducts[row.index]
                    return (
                      <MopSelectOption
                        key={item.value}
                        value={item.value}
                        render={({ active, selected: isChecked }) => (
                          <li
                            className={`list-none relative cursor-pointer select-none px-4 py-3 transition-colors ${
                              active ? 'bg-[#F2F5FF]' : ''
                            } ${isChecked ? 'bg-[#F2F5FF]' : ''}`}
                          >
                            <div className="grid grid-cols-[20px,56px,1fr] items-center gap-3">
                              <Checkbox checked={isChecked} color="primary" size="small" />
                              <div className="w-11 h-11 rounded bg-[#f3f4f6] flex items-center justify-center overflow-hidden">
                                <img
                                  src={item.imageUrl}
                                  alt={item.label}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement
                                    target.style.display = 'none'
                                    target.parentElement!.innerHTML =
                                      '<div class="w-full h-full flex items-center justify-center text-gray-400 text-xs">No Image</div>'
                                  }}
                                />
                              </div>
                              <div className="min-w-0 flex items-center gap-2">
                                <div className="text-xs ">{item.code}</div>
                                <TruncatedText className="text-xs font-semibold text-[#333] truncate">
                                  {item.label}
                                </TruncatedText>
                              </div>
                            </div>
                          </li>
                        )}
                      >
                        {item.label}
                      </MopSelectOption>
                    )
                  }}
                />
              </MopSelect>
            </div>
          </div>
        </div>
      </div>
      <div className="report-settings-right">
        <button
          id="SearchButton"
          onClick={fetchData}
          disabled={isNotAvailableUnit || isSelectingOptimization}
          style={{
            cursor: isNotAvailableUnit || isSelectingOptimization ? 'not-allowed' : '',
            opacity: isNotAvailableUnit || isSelectingOptimization ? 0.6 : 1
          }}
        >
          {t('report.label.ReportFilter.button.search')}
        </button>
      </div>
    </div>
  )
}

export default NaverCommerceReportFilter
