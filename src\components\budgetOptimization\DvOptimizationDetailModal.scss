#DvOptimizationDetailModal {
  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.4);
  }

  .MuiDialog-scrollBody {
    overflow-x: auto;
  }

  .MuiDialogTitle-root {
    height: 0px;
    padding: 0px;

    .modal-close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      border-radius: 50%;
      background: #f3f3f6;
    }
  }

  .MuiDialog-paper {
    width: 1600px;
    max-width: 1600px;
    height: 870px;
    box-sizing: border-box;

    .MuiDialogContent-root {
      height: 100%;
      padding: 0;

      & > div {
        height: 100%;
        flex-wrap: nowrap;
      }

      .MuiPaper-root {
        height: 100%;
        box-shadow: none;
        box-sizing: border-box;
        background-color: transparent;
      }

      .LeftGrid {
        width: 680px;
        padding: 20px 30px;
        background-color: var(--bg-gray-light);
        flex: 0 0 auto;

        .MuiBox-root {
          display: flex;
          align-items: center;

          .MuiFormLabel-root {
            display: flex;
            align-items: center;
            flex: 1;
            font-size: 16px;
            font-weight: 700;
            color: var(--point_color);

            & > span {
              padding-left: 10px;
              font-size: 11px;
              font-weight: 400;
              color: var(--point_color);
            }
          }
        }

        #select-media-row {
          margin-bottom: 10px;

          .MuiInputBase-root {
            width: 275px;
            height: 35px;
            font-size: 16px;
            font-weight: 700;
            color: var(--point_color);
            border: 1px solid #999cb3;
            border-radius: 22.5px;
            background-color: #fff;

            &.MuiInput-underline {
              &::before,
              &::after {
                display: none;
              }
            }

            .MuiSelect-icon {
              top: calc(50% - 15px);
              right: 15px;
              display: inline-block;
              width: 16px;
              height: 16px;
              border-left: 1px solid var(--point_color);
              border-top: 1px solid var(--point_color);
              transform: rotate(-135deg);
              opacity: 0.6;

              &.MuiSelect-iconOpen {
                transform: rotate(45deg);
                top: calc(50% - 3px);
              }

              path {
                display: none;
              }
            }
          }

          .MuiInputBase-input {
            font-size: 17px;
            font-weight: 700;
            color: var(--point_color);
            box-sizing: border-box;
            text-align: center;

            &:focus {
              background: none;
            }
          }

          #select-media {
            padding: 0px;
          }
        }

        #BidPeriod {
          margin-bottom: 10px;
          .Mui-error.MuiFormHelperText-filled {
            font-size: 10px;
            position: absolute;
            white-space: nowrap;
            bottom: -6px;
            left: 50%;
            transform: translateX(-50%);
          }
        }

        #select-campaigns-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        #select-campaigns-info {
          display: block;
          margin: 5px 0 0 0;

          & > div {
            display: flex;
            align-items: center;
            margin: 4px 0;

            .select-mark-blue,
            .select-mark-gray,
            .select-mark-red {
              display: inline-block;
              width: 22px;
              height: 4px;
              margin: 0 8px;
            }

            .select-mark-blue {
              background-color: var(--color-blue-darker);
            }

            .select-mark-gray {
              background-color: #909090;
            }

            .select-mark-red {
              background-color: #b51b32;
            }

            .select-mark-new {
              display: inline-block;
              position: relative;
              height: 16px;
              width: 36px;
              margin-right: 4px;
              &::after {
                content: 'NEW';
                display: flex;
                align-items: center;
                justify-content: center;
                position: absolute;
                height: 16px;
                width: 36px;
                font-size: 8px;
                font-weight: 700;
                color: white;
                background-color: var(--color-active-blue);
                border-radius: 8px;
              }
            }

            div {
              font-size: 10px;
              font-weight: 400;
              color: var(--point_color);
            }
          }
        }

        #select-campaigns-count {
          position: relative;

          .MuiTextField-root {
            margin-left: auto;
          }

          .MuiInputBase-root {
            width: 200px;
            height: 34px;
            padding-right: 12px;
            padding-bottom: 4px;
            font-size: 14px;
            font-weight: 300;
            color: var(--point_color);
            border-bottom: 1px solid var(--point_color);

            input {
              height: 23px;
              padding: 1px 0px 1px 15px;
              width: 120px;
            }

            fieldset {
              border: none;
            }

            &.MuiInput-underline {
              &::before,
              &::after {
                display: none;
              }
            }

            .MuiInputBase-input {
              font-size: 20px;
              font-weight: 700;
              color: var(--point_color);
              box-sizing: border-box;

              &:focus {
                background: none;
              }
            }

            &.MuiInputBase-adornedStart {
              & > svg {
                width: 27px;
                height: 27px;
              }
            }
          }

          input {
            text-align: center;
          }

          p {
            font-size: 16px;
            font-weight: 700;
            color: var(--point_color);
          }

          .MuiInputAdornment-positionStart {
            & > div {
              width: 24px;
              height: 24px;
              background-color: var(--point_color);
              margin-left: 4px;
              border-radius: 50%;
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }

          .unit {
            position: absolute;
            right: 0;
            font-size: 10px;
            font-weight: 700;
            color: var(--point_color);
          }
          &.disabled {
            filter: grayscale(1);
            .unit {
              opacity: 0.4;
            }
          }
        }
      }

      .RightGrid {
        width: 920px;
        min-width: 885px;
        padding: 20px 16px;
        background-color: #fff;

        .MuiBox-root {
          display: flex;
          align-items: center;

          & + .MuiBox-root {
            margin-top: 10px;
          }

          .MuiFormLabel-root {
            display: flex;
            align-items: center;
            flex: 1;
            font-size: 16px;
            font-weight: 700;
            color: var(--point_color);

            & > span {
              padding-left: 10px;
              font-size: 11px;
              font-weight: 400;
              color: var(--point_color);
            }
            & > span.icon {
              padding-left: 0;
              padding-right: 5px;
            }
          }
        }

        .MuiInputBase-root {
          width: 410px;
          height: 31px;
          padding-right: 12px;
          font-size: 14px;
          font-weight: 300;
          color: var(--point_color);
          border: 1px solid #bbbdcd;
          border-radius: 22.5px;
          background-color: #fff;

          input {
            height: 23px;
            padding: 1px 0px 1px 15px;
            width: 100%;
          }

          fieldset {
            border: none;
          }

          &.MuiInput-underline {
            &::before,
            &::after {
              display: none;
            }
          }

          .MuiInputBase-input {
            font-size: 14px;
            font-weight: 300;
            color: var(--point_color);
            box-sizing: border-box;

            &:focus {
              background: none;
            }
          }
        }

        #header {
          margin-bottom: 20px;

          .MuiFormLabel-root {
            font-size: 25px;

            & > span {
              font-size: 14px;
            }
          }
        }

        #setting-group {
          padding: 15px 15px 15px 0;
          background-color: var(--bg-gray-light);
        }

        #optimization-name,
        #budget,
        #budget-variance,
        #contribution {
          background-color: var(--bg-gray-light);

          .MuiInputBase-input {
            font-size: 16px;
            font-weight: 700;
          }
        }
        #budget,
        #budget-variance,
        #contribution {
          margin-top: 10px;
        }

        #optimization-name {
          input {
            text-align: center;
            &.Mui-disabled {
              color: rgba(0, 0, 0, 0.38);
            }
          }
        }

        #budget {
          input {
            text-align: right;
            &.Mui-disabled {
              color: rgba(0, 0, 0, 0.38);
            }
          }

          .MuiInputAdornment-positionEnd {
            margin: 0px 13px 0px 20px;

            p {
              font-size: 11px;
              font-weight: 300;
              color: var(--point_color);
            }
          }
        }

        #daily-average-spending {
          position: relative;
          padding-bottom: 20px;
          .dropdown {
            padding: 8px;
            position: relative;
          }
          .slider-wrapper {
            position: relative;
            width: 380px;
            height: 26px;
            border-radius: 13px;
            box-shadow: inset 0 0 5px 0 rgba(0, 0, 0, 0.3);

            .slider {
              height: 8px;
              margin-top: -8px;
              width: calc(100% - 23px);

              .MuiSlider-thumb {
                height: 46px;
                width: 46px;
                background-color: transparent;
                background-image: url(~@images/slider-thumb.svg);
                background-size: 46px 46px;
                margin-top: -10px;
                margin-left: -23px;
                z-index: 1;

                &.Mui-focusVisible,
                &:focus,
                &:hover,
                &:active {
                  box-shadow: none !important;
                }
                .MuiSlider-valueLabel {
                  transition: none;
                  > span {
                    width: 0;
                    height: 0;
                    border-radius: 0;
                    transform: none;
                    transform: translate(48px, 32px);
                    > span {
                      display: inline-block;
                      padding: 4px 8px;
                      border-radius: 4px;
                      background-color: var(--color-active-blue);
                      transform: none;
                      &::after {
                        content: '';
                        position: absolute;
                        left: 50%;
                        top: 10px;
                        width: 0;
                        height: 0;
                        margin-left: -7px;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-top: 10px solid var(--color-active-blue);
                        clear: both;
                      }
                    }
                  }
                }
              }

              .MuiSlider-track {
                height: 18px;
                border-radius: 9px;
                background-image: linear-gradient(90deg, #d3e0ff 3.17%, #3b5ec9 47.64%, #040a45 103.62%);
              }

              .MuiSlider-rail {
                height: 18px;
                border-radius: 9px;
                background-color: transparent;
              }

              &.Mui-disabled {
                filter: grayscale(1);
                .MuiSlider-track {
                  background: #909090;
                }
              }
            }

            .slider::after {
              content: '';
              position: absolute;
              left: 50%;
              bottom: 4px;
              width: 0;
              height: 0;
              margin-left: -9px;
              border-left: 6px solid transparent;
              border-right: 6px solid transparent;
              border-bottom: 10px solid #fff;
              clear: both;
            }

            .rows {
              display: flex;
              flex-flow: column;
              width: 100%;
              position: absolute;
              bottom: 0;
              .row {
                display: flex;
                width: 100%;
                height: 0;
                flex-wrap: wrap;
                justify-content: space-between;
                div {
                  font-size: 0.625rem;
                }
              }
            }
          }
        }

        #budget-variance {
          .budget-variance-input {
            margin-left: 14px;
            .MuiInputBase-root {
              width: 105px;
              height: 26px;
              padding-right: 12px;
              font-size: 14px;
              font-weight: 300;
              color: var(--point_color);
              border: 1px solid #bbbdcd;
              border-radius: 22.5px;
              background-color: #fff;

              input {
                height: 23px;
                padding: 1px 0px 1px 15px;
                width: 100%;
              }

              fieldset {
                border: none;
              }

              &.MuiInput-underline {
                &::before,
                &::after {
                  display: none;
                }
              }

              .MuiInputBase-input {
                font-size: 14px;
                font-weight: 500;
                color: var(--point_color);
                box-sizing: border-box;
                text-align: right;

                &:focus {
                  background: none;
                }
              }

              .MuiTypography-root {
                font-size: 11px;
                font-weight: 300;
                color: var(--point_color);
              }
            }
          }
        }

        #contribution {
          .contribution-settings {
            width: 410px;
            .contributionYnSwitch {
              &.MuiSwitch-root {
                width: auto;
                height: auto;
                padding: 0;
                margin: 0;
                align-items: center;
                display: flex;

                .MuiSwitch-switchBase {
                  left: 2px;
                  padding: 0;

                  .MuiSwitch-thumb {
                    position: relative;
                    top: 2px;
                    width: 14px;
                    height: 14px;
                    padding: 0;
                    background-color: #ffffff;
                    opacity: 1;
                    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
                  }

                  &.Mui-checked {
                    transform: translateX(18px);
                  }
                }

                .MuiSwitch-track {
                  width: 36px;
                  height: 18px;
                  padding: 0;
                  border-radius: 10px;
                  background-color: #707395;
                  opacity: 0.6;
                  border: 1px solid #707395;
                }

                .Mui-checked {
                  & + .MuiSwitch-track {
                    background-color: var(--status-active);
                    opacity: 1;
                    border: 1px solid var(--status-active);
                  }
                }

                .Mui-disabled {
                  .MuiSwitch-thumb {
                    background-color: #ccc;
                    opacity: 1;
                  }

                  & + .MuiSwitch-track {
                    background-color: #fff;
                    opacity: 1;
                    border: 1px solid #959595;
                  }
                }
              }
            }
            .customRadioGroup {
              margin-left: 48px;
            }
          }
        }

        .customRadioGroup {
          .MuiRadio-root {
            padding: 7px;
          }
          .MuiButtonBase-root {
            .MuiIconButton-label > div {
              svg {
                display: none;
              }
              &::before {
                content: '';
                width: 12px;
                height: 12px;
                border: 1px solid #dedfe4;
                border-radius: 50%;
                background-color: var(--bg-gray-light);
              }
            }
            &.Mui-checked {
              .MuiIconButton-label > div {
                &::after {
                  position: absolute;
                  top: 2px;
                  left: 2px;
                  content: '';
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background-color: var(--status-active);
                }
              }
            }
            &.Mui-disabled {
              .MuiIconButton-label > div::after {
                background-color: rgba(0, 0, 0, 0.38);
              }
            }
          }

          [class*='PrivateRadioButtonIcon-checked'] {
            &::after {
              position: absolute;
              top: 2px;
              left: 2px;
              content: '';
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: var(--status-active);
            }
          }
          .MuiIconButton-colorSecondary:hover {
            background-color: rgba(51, 134, 200, 0.04);
          }

          .MuiFormControlLabel-root {
            margin: 0px;
            &:not(:first-child) {
              margin-left: 3px;
            }
          }
          .MuiFormControlLabel-label {
            font-size: 12px;
            color: var(--point_color);
            font-weight: 500;
          }
        }

        #optimization-goal {
          padding: 10px 0 5px 0;
        }

        #optimization-goals {
          margin-bottom: 15px;

          .MuiToggleButtonGroup-root {
            width: 100%;
            display: flex;
            justify-content: space-between;
            gap: 6px;
            border-radius: 0;
          }

          .optimization-goal {
            flex: 1;
            height: 160px;
            background-color: var(--bg-gray-light);
            border: none;
            padding: 25px 0;
            border: 1px solid transparent;
            border-top: 2px solid var(--point_color);

            &.Mui-selected {
              border-color: #c1415f;
              background-color: #fff;
              border-radius: 0;
              &.Mui-disabled {
                border-color: rgba(0, 0, 0, 0.38);
              }
              &[value='KPIS'] .upper {
                display: none !important;
              }
            }
            &:not(.Mui-selected).Mui-disabled {
              filter: grayscale(1);
              opacity: 0.4;
            }
            .MuiToggleButton-label {
              display: flex;
              flex-direction: column;
              .upper {
                display: flex;
                align-items: center;
                .optimization-icon {
                  line-height: 0;
                  > #optimization-icon {
                    padding-right: 10px;
                  }
                }
              }
            }

            .optimization-name-kor {
              font-size: 18px;
              font-weight: 400;
              color: var(--point_color);
              text-align: left;
              line-height: 1.4;
            }

            .optimization-name-eng {
              font-size: 13px;
              font-weight: 700;
              color: var(--color-active-blue);
              text-align: left;
              line-height: 1;
            }

            .optimization-description-container {
              margin-top: 20px;

              &.optimization-kpis-settings {
                margin-top: 0;
              }

              .optimization-description {
                font-size: 12px;
                font-weight: 400;
                color: var(--point_color);
                line-height: 1.3;

                em {
                  font-style: normal;
                  font-weight: 700;
                  color: var(--color-active-blue);
                }
              }

              .MuiTableCell-root {
                border: none;
              }
            }
          }
        }

        #negative-adgroups {
          margin-top: 0px;
          line-height: 1;
        }

        #bid-exception-adgroups {
          #LeftShuttle,
          #RightShuttle {
            width: 436px;
            height: 295px;
            padding: 15px 20px;
            background-color: var(--bg-gray-light);

            & > div {
              width: 100%;
              height: 100%;
              overflow-y: auto;
            }

            .MuiPaper-root {
              height: 100%;
              box-shadow: none;
              box-sizing: border-box;
              background-color: transparent;
              border-radius: 0;

              &::-webkit-scrollbar-thumb {
                background-color: var(--point_color);
              }

              &::-webkit-scrollbar-track {
                background-color: var(--bg-gray-light);
              }

              .MuiList-padding {
                padding: 0px;
              }

              .MuiListItem-root {
                padding: 0 8px;
                height: 44px;
                &:nth-child(odd):not(.empty) {
                  background-color: #fff;
                }
                &.MuiListItem-button.Mui-selected {
                  background-color: #eee !important;
                }
                span.MuiTouchRipple-root {
                  display: none;
                }
              }
            }

            span,
            p {
              font-size: 13px;
              font-weight: 400;
              color: var(--point_color);
              text-align: left;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          #LeftShuttle {
            & > .MuiGrid-container {
              width: 100%;
              flex-wrap: nowrap;
            }

            #search-keyword {
              width: 100%;
              margin-bottom: 8px;

              .MuiFormControl-root {
                width: 100%;
              }

              input {
                padding: 1px 15px 1px 15px;
              }

              .MuiInputBase-adornedEnd .search-icon {
                width: 21px;
                height: 21px;
                cursor: pointer;
              }
            }

            #searching-adgroups {
              width: 100%;

              & > div {
                width: 100%;
                height: 222px;
                overflow-y: auto;
              }
              // FIXME: background color
              & .Mui-selected {
                background-color: #eee;
              }
            }

            .MuiInputBase-root {
              width: 100%;
              height: 35px;
              padding-right: 12px;
              font-size: 16px;
              font-weight: 300;
              color: var(--point_color);
              border: 1px solid #bbbdcd;
              border-radius: 17.5px;
              background-color: #fff;

              input {
                height: 23px;
                padding: 1px 0px 1px 15px;
                width: 100%;
              }

              fieldset {
                border: none;
              }

              &.MuiInput-underline {
                &::before,
                &::after {
                  display: none;
                }
              }

              .MuiInputBase-input {
                font-size: 12px;
                font-weight: 300;
                color: var(--point_color);
                box-sizing: border-box;

                &:focus {
                  background: none;
                }
              }
            }

            .MuiPaper-root {
              width: 100%;
              border-top: 1px solid var(--point_color);
              border-bottom: 1px solid var(--point_color);
              overflow-y: scroll;

              .MuiListItem-root {
                &:not(:first-child) {
                  border-top: 1px solid #c8cbd5;
                }
                span.MuiTouchRipple-root {
                  display: none;
                }
              }
            }
          }

          #RightShuttle {
            .MuiPaper-root {
              border-top: 1px solid var(--point_color);
              border-bottom: 1px solid var(--point_color);

              div.MuiListItem-root {
                padding: 0 15px;
                font-size: 11px;
                font-weight: 400;
                color: var(--point_color);
                text-align: left;
                background-color: transparent;
                border-bottom: 1px solid #c8cbd5;

                span.MuiTouchRipple-root {
                  display: none;
                }
              }
            }
          }

          #ShuttleButton {
            width: 0;
            .MuiIconButton-root {
              margin-left: -14px;
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background: #fff;
              box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);

              &.Mui-disabled {
                background: #ccc;
              }
            }

            #move-left {
              margin-top: 20px;
            }
          }
        }

        #save {
          width: 100%;
          margin-top: 17px;

          .DialogSaveButton {
            width: 170px;
            height: 30px;
            margin-left: auto;
            font-size: 15px;
            font-weight: 900;
            color: var(--text-white);
            border-radius: 17.5px;
            background-color: var(--point_color);
          }
        }
      }
    }

    .MuiDialogActions-root {
      padding: 0;
      margin-top: 10px;
      justify-content: flex-start;
    }
  }
}

#dv-optimization-advice-tooltip-optimization-name {
  .MuiTooltip-tooltip {
    min-width: 210px;
  }
}

#dv-optimization-advice-tooltip-optimization-name,
#dv-optimization-advice-tooltip-budget,
#dv-optimization-advice-tooltip-daily-average-spending,
#dv-optimization-advice-tooltip-optimization-goal,
#dv-optimization-advice-tooltip-negative-adgroups,
#dv-optimization-advice-tooltip-budget-variance,
#dv-optimization-advice-tooltip-contribution-type {
  border: 1px solid var(--point_color);
  background-color: #fff;
  .MuiTooltip-tooltip {
    padding: 0 0 12px 0;
    margin: 0;
    background-color: transparent;
    .border-bottom {
      border-bottom: 1px solid #9196a4;
    }
    .indent1 {
      padding: 0 12px 0 14px;
      text-indent: -14px;
    }
    .indent2 {
      padding: 0 12px 0 30px;
      text-indent: -14px;
    }
    .indent3 {
      padding: 0 12px 0 38px;
      text-indent: -38px;
    }
    > div {
      color: var(--point_color);
      font-size: 12px;
      font-weight: normal;
      > h1,
      > h2,
      > h3,
      > h4,
      > h5,
      > h6 {
        font-size: 12px;
        font-weight: bold;
        margin: 0;
        padding: 12px 0px;
        border-bottom: 1px solid #9196a4;
        font-weight: 700;
        text-align: center;
      }
      > div {
        padding: 8px 12px 6px;
        line-height: 1.5;
        ul {
          margin: 0;
          padding-inline-start: 2em;
        }
      }
    }
    > div > ul {
      color: var(--point_color);
      font-size: 12px;
      font-weight: 500;
    }
    .MuiTooltip-arrow::before {
      border-color: var(--point_color);
    }
  }
}

#dv-optimization-table-popper {
  z-index: 1400;
  background-color: #fff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  &.disabled {
    filter: grayscale(1);
    th {
      &:first-child {
        border-top-color: gray;
      }
      border-top-color: lightgray;
    }
    td:first-child {
      background-color: gray;
    }
    .recommended {
      background-color: gray;
    }
  }
  .bottom {
    font-size: 8px;
    text-align: right;
    padding: 2px 4px;
  }
}

#dv-optimization-table-caption {
  font-size: 0.5rem;
  text-align: right;
  padding: 4px;
  .reload {
    font-size: 0.875rem;
    padding: 0;
    margin: 0 4px;
    background: #f2f3f6;
    border: 1px solid gray;
    border-radius: 20px;
    .MuiIconButton-label {
      padding: 2px 6px 2px 2px;
      svg {
        width: 20px;
        height: 20px;
      }
      &[disabled] {
        filter: grayscale(1);
        opacity: 0.4;
      }
    }
  }
}

#dv-optimization-table {
  position: relative;
  width: 380px;
  table-layout: fixed;
  th {
    font-size: 1rem;
    font-weight: normal;
    border: 0 none;
    border-top: 3px solid var(--color-blue-darker);
    border-right: 1px solid #000;
    background-color: #f2f3f6;
    text-align: center;
    padding: 6px 16px;

    &:first-child {
      border-top: 3px solid var(--color-active-blue);
      color: var(--color-active-blue);
    }
    &:last-child {
      border-right: 0 none;
    }
  }
  td {
    font-size: 0.875rem;
    border: 0 none;
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    background-color: #fff;
    text-align: center;
    padding: 6px 16px;

    &:first-child {
      background-color: var(--color-active-blue);
      color: #fff;
    }
    &:last-child {
      border-right: 0 none;
    }
  }
  .recommended {
    font-size: 0.5rem;
    background-color: var(--color-active-blue);
    color: #fff;
    position: absolute;
    padding: 2px 4px;
    border-radius: 9999px;
    top: -8px;
    left: 50px;
    line-height: 1;
  }
}
