// FIXME
.circle-auth-setting-modal {
  .MuiDialog-paper {
    padding: 20px 57px;
    width: 765px;
    height: 660px;
    max-width: unset;
    .MuiDialogContent-root {
      padding: 0px;
    }
  }
  &__header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 0 0 20px 0;

    .mop-chip {
      color: var(--point_color);
      background-color: var(--bg-gray-light);
      font-size: 10px;
    }

    .mop-icon-box {
      position: absolute;
      top: 12px;
      right: 12px;
    }

    .circle-auth-setting__advertiser {
      margin-top: 15px;
      font-size: 18px;
      color: var(--point_color);
      font-weight: 600;
    }
  }

  &__sub-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 25px 0 14px;
    padding: 0 24px;
    color: var(--point_color);

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 900;
    }

    span {
      font-size: 12px;
      font-weight: 400;
    }
  }

  .authority-table {
    display: grid;
    grid-template-rows: 40px 1fr;

    &__header,
    &__body {
      display: grid;
      grid-template-columns: 21% 21% 38% 10% 10%;
      &.column_4 {
        grid-template-columns: 30% 46% 15% 10%;
      }
    }

    &__header {
      grid-template-rows: 40px;
      background-color: var(--bg-gray-light);
      align-items: center;
      justify-items: center;
      font-weight: 600;
      color: var(--point_color);
      font-size: 14px;
      padding-right: 6px;

      .tooltip-icon {
        margin: 0 2px -2px 0;
        display: inline;
        vertical-align: baseline;
      }
    }

    &__body {
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 8px;
      }
      &::-webkit-scrollbar-thumb {
        border: 2px solid transparent;
        border-radius: 3px;
        background-color: #eeeeee;
      }
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
      .authority-table__row {
        display: contents;

        &-cell {
          border-bottom: 1px solid var(--gray-light);
          height: 50px;
          padding: 0 10px;
          font-size: 12px;
          color: var(--point_color);
          font-weight: 400;
          display: flex;
          align-items: center;
          gap: 4px;

          .mop-icon-box {
            margin: 0 auto;
          }

          .text-icon {
            margin: 0;
          }

          &.Registered,
          &.Invited,
          &.Expired {
            .mop-chip {
              font-weight: 700;
              font-size: 10px;
              width: 100%;
            }
          }

          &.Registered {
            .mop-chip {
              background-color: #e8f2fe;
              --chip-font-color: var(--color-active-blue);
            }
          }
          &.Invited {
            .mop-chip {
              background-color: #e2fcd9;
              --chip-font-color: #47b35d;
            }
          }
          &.Expired {
            .mop-chip {
              background-color: #ffe0db;
              --chip-font-color: #dd4a33;
            }
          }
        }
      }
    }
  }

  .MuiPaper-rounded {
    border-radius: 0px;
  }
}

#CircleAuthSettingModal {
  .MuiSelect-outlined.MuiSelect-outlined {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    justify-content: center;
    color: var(--gray-dark);
    .menu-item-text {
      color: var(--point_color);
    }
  }
  .MuiTooltip-tooltip {
    width: 490px;
  }

  .circle-modal-table {
    border-top: 1px solid #b8b8b8;
    border-spacing: 0px;
    position: absolute;
    right: 8px;
    bottom: 10px;
    left: 8px;
    &.tbody-scroll {
      display: block;
      table-layout: fixed;
      thead {
        th {
          &:nth-of-type(1) {
            width: 114px;
          }
          &:nth-of-type(2) {
            width: 95px;
          }
          &:nth-of-type(3) {
            width: 162px;
          }
          &:nth-of-type(4) {
            width: 104px;
          }
          &:last-child {
            width: 65px;
          }
        }
      }
      tbody {
        max-height: 224px;
        display: block;
        overflow: auto;
        td {
          > div {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          &:nth-of-type(1) {
            width: 114px;
            > div {
              width: 114px;
            }
          }
          &:nth-of-type(2) {
            width: 95px;
            > div {
              width: 95px;
            }
          }
          &:nth-of-type(3) {
            width: 162px;
            > div {
              width: 162px;
            }
          }
          &:nth-of-type(4) {
            width: 104px;
            > div {
              width: 104px;
            }
          }
          &:last-child {
            width: 50px;
            > div {
              width: 50px;
            }
          }
        }
      }
    }
    &.row-height-large {
      tbody {
        tr {
          height: 48px;
          line-height: 48px;
        }
      }
    }
    .align-left {
      text-align: left;
      .text-icon {
        margin: 0 5px 0 12px;
      }
    }
    thead {
      tr {
        height: 36px;
      }
      th {
        font-size: 12px;
        font-weight: 700;
        color: var(--point_color);
        background-color: var(--bg-gray-light);
      }
    }
    tbody {
      tr {
        height: 36px;
      }
      td {
        padding: 0 10px;
        text-align: center;
        font-size: 10px;
        font-weight: 400;

        .MuiChip-sizeSmall {
          height: 20px;
        }
        .MuiChip-labelSmall {
          font-size: 10px;
          font-weight: 700;
        }

        .outline-select {
          min-width: 162px;
          height: 36px;
          font-size: 10px;
          border-radius: 0px;
        }

        .MuiTextField-root {
          width: 320px;
          margin-top: 6px;

          input[type='text'] {
            padding: 0;
            width: 380px;
            height: 36px;
            text-align: center;
            font-size: 12px;
          }
        }
        .delete-button {
          cursor: pointer;
        }
        .add-button {
          margin-left: 6px;
          vertical-align: middle;
          cursor: pointer;
        }
        .send-button {
          margin: -2px 0 0 5px;
          padding: 5px 20px;
          background-color: var(--point_color);
          color: white;
          font-weight: bold;
          border-radius: 4px;
        }
      }
    }
  }
}

.MuiPopover-paper {
  &.MuiMenu-paper {
    width: 160px;
  }
  .MuiMenu-list .MuiMenuItem-root {
    font-family: Noto Sans Korean, sans-serif;
    .menu-item-icon {
      display: inline-block;
    }
    .menu-item-text {
      width: 80%;
      text-align: center;
    }
  }
}

#authority-info-tooltip {
  .MuiTooltip-tooltip {
    max-width: inherit;
    margin: 0;
    padding: 0;
    width: 490px;
    > h4 {
      padding: 20px 30px;
      color: var(--point_color);
      font-size: 18px;
      font-weight: 700;
      text-align: left;
      border-bottom: 0px;
      > span {
        color: var(--color-active-blue);
      }
    }
    .content {
      padding: 30px;
      background-color: var(--bg-gray-light);
      > p {
        padding: 0px;
        line-height: 1.7;
        font-weight: 350;
      }
      .administrator {
        color: var(--color-admin);
      }
      .Operator {
        color: var(--color-operator);
      }
      .Viewer {
        color: var(--color-viewer);
      }

      .authority-table {
        margin: 15px 0px;
        border-top: 2px solid var(--point_color);
        border-bottom: 2px solid var(--point_color);
        border-spacing: 0px;

        th {
          background-color: transparent;
        }
        thead {
          tr {
            height: 40px;
          }
          th {
            border-bottom: 1px solid #909090;
          }
          .type {
            font-size: 8px;
            text-align: left;
            line-height: 1.1;
            > div {
              display: inline-flex;
              flex-wrap: wrap;
              flex-direction: column;
              height: 24px;
              > div {
                text-align: left;
                flex: 0 0 50%;
                &.rowspan {
                  text-align: center;
                  vertical-align: middle;
                  flex: 0 0 100%;
                }
                .text-icon {
                  margin: 2px 5px 0px 20px;
                }
              }
            }
          }
        }
        tbody {
          tr {
            height: 37px;
            border: none;
          }
          th {
            border-bottom: 1px solid #ffffff;
          }
          td {
            text-align: center;
            background-color: #ffffff;
            border: none;
            border-bottom: 1px solid var(--bg-gray-light);
            .permission {
              color: #dd4a33;
            }
          }
        }
      }
    }
  }
}
