#campaignCreationListFilter {
  .MuiGrid-item {
    display: flex;
    align-items: center;
    flex-flow: column;
    justify-content: space-between;
    padding: 0;

    &:last-child {
      justify-content: flex-end;
      margin: 0;
    }
  }

  .search-label-container {
    background: linear-gradient(180deg, var(--mop20-gray-lighter) 38px, #FFFFFF 50%);
    .page-list-filter__select{
      border-left: 1px solid var(--mop20-table-border);
    }
  }

  .search-label {
    height: 37px;
    display: flex;
    align-self: center;
    justify-content: center;
    padding-right: 40px;

    svg {
      width: 20px;
      height: 20px;
      display: none;
      margin-right: 5px;
    }

    label {
      display: flex;
      align-items: center;
      height: 35px;
      font-size: 13px;
      font-weight: 300;
      color: var(--mop20-text-color);
    }
  }

  .search-media {
    width: 150px;
    box-sizing: border-box;
  }

  .search-input-container {
    margin-bottom: 32px !important;
    .search-input {
      display: flex;
      height: 40px;
      border-top: 1px solid var(--mop20-table-border);
      border-right: 1px solid var(--mop20-table-border);
      border-bottom: 1px solid var(--mop20-table-border);
      
      .MuiSelect-select {
        width: 150px;
        padding: 0px 40px 0px 0px;
        font-weight: 300;
      }

      .menuItem {
        .MuiPaper-rounded {
          border-radius: 0px;
          box-shadow: none;
          border: 1px solid var(--mop20-table-border);
        }

        .MuiMenuItem-root {
          width: 100%;
          position: relative;
          font-size: 12px;
          font-weight: 200;
          color: var(--point_color);
          text-align: center;
          border-radius: 0px;

          &.Mui-selected {
            font-weight: 500;
            background-color: transparent;
          }
        }
      }

      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: var(--point_color);

      .MuiInputBase-root {
        height: 100%;

        &.MuiInput-underline {
          &::before,
          &::after {
            display: none;
          }
        }

        .search-input-dropdown {
          width: 40px;
          min-width: 40px;
          height: 40px;
          position: absolute;
          right: 0px;
          border-left: 1px solid var(--mop20-table-border);
          pointer-events: none;

          svg {
            position: absolute;
            top: calc(50% - 12px);
            left: 12px;
            display: inline-block;
            min-width: 16px;
            min-height: 16px;
            width: 16px;
            height: 16px;
            border-left: 1px solid var(--point_color);
            border-top: 1px solid var(--point_color);
            transform: rotate(-135deg);
            opacity: 0.6;

            path {
              display: none;
            }
          }

          &.search-input-dropdown-open {
            background-color: var(--color-black);

            svg {
              transform: rotate(45deg);
              top: calc(50% - 3px);

              border-left: 1px solid var(--color-white);
              border-top: 1px solid var(--color-white);
            }
          }
        }
      }

      .MuiInputBase-input {
        font-size: 14px;
        font-weight: 400;
        color: var(--mop20-text-color);
        box-sizing: border-box;
        text-align: center;

        &:focus {
          background: none;
        }
      }
    }

    .MuiGrid-item:first-child {
      .search-input {
        border-left: 1px solid var(--mop20-table-border);
      }
    }
  }
}

.campaign-creation-filter-popover {
  .MuiPaper-rounded {
    border-radius: 0px !important;
    box-shadow: none !important;
    border: 1px solid var(--mop20-table-border) !important;

    :hover {
      border-radius: 0px !important;
    }
  }

  .MuiMenuItem-root {
    width: 100%;
    height: 35px;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: var(--mop20-text-color);
    text-align: center;
    border-radius: 0px;

    &.Mui-selected {
      font-weight: 600 !important;
      background-color: transparent !important;
    }
  }

  .MuiMenu-list {
    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
  }
} 