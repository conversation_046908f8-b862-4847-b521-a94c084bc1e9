import React, { useState, useMemo } from 'react'
import { UpdateBudgetOptParams } from '@models/budgetOpt/BudgetOpt'

import './BudgetOptConfig.scss'
import CommonTooltip from '@components/common/CommonTooltip'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import CustomRadioGroup from '@components/common/CustomRadioGroup'
import { YNFlag } from '@models/common/YNFlag'
import MultiGoalSettings from './MultiGoalSettings'
import { Box, InputAdornment, Switch, TextField } from '@material-ui/core'

import { BudgetVarianceType, ContributionType, KpiItem } from '@models/budgetOpt/BudgetOpt'
import IntegerNumberFormat from '@components/common/IntegerNumberFormat'
import { DvKpiType } from '@models/optimization/Kpi'
import { Item } from '@models/contributionAnalysis/RadioItem'
import { ActionType } from '@models/common/CommonConstants'
import { Trans, useTranslation } from 'react-i18next'
import TooltipCard from '@components/common/tooltip/TooltipCard'

interface Props {
  enableGA: boolean
  optConfig: UpdateBudgetOptParams
  setOptConfig: (params: Partial<UpdateBudgetOptParams>) => void
  hasAuthority: boolean
}

const getVariaceType = (rate: number | string) => {
  switch (String(rate)) {
    case '5':
      return BudgetVarianceType.FIVE_OR_LESS
    case '10':
      return BudgetVarianceType.TEN_OR_LESS
    case '20':
      return BudgetVarianceType.TWENTY_OR_LESS
    default:
      return BudgetVarianceType.DIRECT
  }
}

const convertKpisMap = (kpis: KpiItem[]) => {
  return kpis.map((item) => [item.kpiType, -1] as [DvKpiType, number])
}

const BudgetOptConfig: React.FC<Props> = ({ enableGA, optConfig, setOptConfig, hasAuthority }: Props) => {
  const [kpis, setKpis] = useState(new Map(convertKpisMap(optConfig.kpis)))
  const [contributionType, setContributionType] = useState<ContributionType>(optConfig.contributionType)
  const [isFixingRateByMedia, setFixingRateByMedia] = useState<boolean>(optConfig.mediaBudgetFix === 'Y')
  const [budgetChangeRateType, setBudgetChangeRateType] = useState<BudgetVarianceType>(
    getVariaceType(optConfig.budgetChangeRate)
  )
  const [budgetChangeRate, setBudgetChangeRate] = useState<number>(optConfig.budgetChangeRate)
  const { t } = useTranslation()
  const contributionTypeCodes = useMemo(() => {
    return Object.keys(ContributionType)
      .filter((x) => x !== ContributionType.NONE)
      .map(
        (item) =>
          ({
            value: item,
            label: (
              <div className="radio-button-label">
                <span className="radio-button-label__en">
                  <Trans i18nKey={`common.code.contributionType.${item}`} />
                </span>
              </div>
            )
          } as Item<ContributionType>)
      )
  }, [t]) //eslint-disable-line

  const budgetChangeRateCodes = useMemo(() => {
    return Object.keys(BudgetVarianceType).map(
      (item) =>
        ({
          value: item,
          label: t(`common.code.budgetChangeRate.${item}`)
        } as Item<BudgetVarianceType>)
    )
  }, [t]) //eslint-disable-line

  const handleContributeSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setContributionType(e.target.checked ? ContributionType.TRANSACTION : ContributionType.NONE)
    setOptConfig({
      contributionType: e.target.checked ? ContributionType.TRANSACTION : ContributionType.NONE
    })
  }

  const changeFixingRateSwitch = (e: any) => {
    setFixingRateByMedia(e.target.checked)
    setOptConfig({
      mediaBudgetFix: e.target.checked ? YNFlag.Y : YNFlag.N
    })
  }

  const handleKpis = (key: DvKpiType) => {
    if (kpis.has(key)) {
      kpis.delete(key)
    } else {
      kpis.set(key, -1)
    }
    setKpis(kpis)
    setOptConfig({
      kpis: Array.from(kpis, ([kpiType, kpiValue]) => ({
        kpiType,
        kpiValue
      }))
    })
  }

  const handleContributionType = (event: React.ChangeEvent<HTMLInputElement>) => {
    const contributionType = (event.target as HTMLInputElement).value as ContributionType
    setContributionType(contributionType)
    setOptConfig({ contributionType })
  }

  const handleBudgetVariance = (event: React.ChangeEvent<HTMLInputElement>) => {
    setBudgetChangeRateType((event.target as HTMLInputElement).value as BudgetVarianceType)
    const rangeType = (event.target as HTMLInputElement).value as BudgetVarianceType
    const varianceOptions = { FIVE_OR_LESS: 5, TEN_OR_LESS: 10, TWENTY_OR_LESS: 20 }

    if (rangeType === BudgetVarianceType.DIRECT) {
      setOptConfig({ budgetChangeRate: budgetChangeRate })
    } else {
      setOptConfig({ budgetChangeRate: varianceOptions[rangeType] })
      setBudgetChangeRate(0)
    }
  }

  const handleDirectBudgetChangeRate = (event: React.ChangeEvent<HTMLInputElement>) => {
    const rangeNum = Number((event.target as HTMLInputElement).value)
    setBudgetChangeRate(rangeNum)
    setOptConfig({ budgetChangeRate: rangeNum })
  }

  return (
    <div id="BudgetOptConfig">
      <Box id="optimization-goal" className="budget-config-section">
        <div className="config-label-container">
          <CommonTooltip
            title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.optimizationGoal'} type={'paragraph'} />}
            placement="right-start"
            arrow
          >
            <AdviceMarkIcon />
          </CommonTooltip>
          <span className="head-label">{t('optimization.label.budgetOpt.config.optimizationGoal')}</span>
          {/* <span className="sub-label">{t('optimization.label.budgetOpt.config.optimizationGoal.ko')}</span> */}
        </div>
        <Box id="optimization-goals">
          <div className="optimization-description-container optimization-kpis-settings">
            <MultiGoalSettings
              kpis={kpis}
              setKpis={handleKpis}
              actionType={hasAuthority ? ActionType.CREATE : ActionType.READ}
            />
          </div>
        </Box>
      </Box>
      <Box id="budget-variance" className="budget-config-section">
        <div className="config-label-container">
          <CommonTooltip
            title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.budgetVariance'} type={'paragraph'} />}
            placement="right-start"
            arrow
          >
            <AdviceMarkIcon />
          </CommonTooltip>
          <span className="head-label">{t('optimization.label.budgetOpt.config.budgetVariance')}</span>
          {/* <span className="sub-label">{t('optimization.label.budgetOpt.config.budgetVariance.ko')}</span> */}
        </div>
        <div className="config-form-container">
          <div>
            <CustomRadioGroup<BudgetVarianceType>
              items={budgetChangeRateCodes}
              onChange={handleBudgetVariance}
              defaultValue={budgetChangeRateType}
              disabled={!hasAuthority}
              disableRipple
              customClass="budget-config-radio"
            >
              <TextField
                className="budget-variance-input"
                data-testid="budgetChangeRate"
                value={budgetChangeRateType === BudgetVarianceType.DIRECT ? budgetChangeRate : 0}
                disabled={budgetChangeRateType !== BudgetVarianceType.DIRECT && !hasAuthority}
                onChange={handleDirectBudgetChangeRate}
                inputProps={{
                  maxLength: 30
                }}
                InputProps={{
                  inputComponent: IntegerNumberFormat,
                  endAdornment: <InputAdornment position="end">{t('common.label.belowPercent')}</InputAdornment>
                }}
              />
            </CustomRadioGroup>
          </div>
          <div className="sub-config-section">
            <div className="config-label-container">
              <CommonTooltip
                title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.fixingRatio'} type={'paragraph'} />}
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon />
              </CommonTooltip>
              <span className="head-label">{t('optimization.label.budgetOpt.config.fixingRatio')}</span>
              {/* <span className="sub-label">{t('optimization.label.budgetOpt.config.fixingRatio.ko')}</span> */}
            </div>
            {/* TODO */}
            <Switch
              className="budget-opt-switch"
              edge="end"
              color="primary"
              onChange={changeFixingRateSwitch}
              disabled={!hasAuthority}
              checked={isFixingRateByMedia}
            />
          </div>
        </div>
      </Box>
      <Box id="contribution" className="budget-config-section">
        <div className="config-label-container">
          <CommonTooltip
            title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.contributionType'} type={'paragraph'} />}
            placement="right-start"
            arrow
          >
            <AdviceMarkIcon />
          </CommonTooltip>
          <span className="head-label">{t('optimization.label.budgetOpt.config.attributionType')}</span>
          {/* <span className="sub-label">{t('optimization.label.budgetOpt.config.attributionType.ko')}</span> */}
        </div>
        <Box className="contribution-settings">
          <Switch
            className="budget-opt-switch"
            edge="end"
            color="primary"
            onChange={handleContributeSwitchChange}
            disabled={!enableGA || !hasAuthority}
            checked={enableGA && contributionType !== 'NONE'}
          />
          <div className="sub-config-section">
            <div className="config-label-container">
              <CommonTooltip
                title={
                  <TooltipCard tKey={'optimization.label.budgetOpt.tooltip.attributionTarget'} type={'paragraph'} />
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon />
              </CommonTooltip>
              <span className="head-label">{t('optimization.label.budgetOpt.config.attributionTarget')}</span>
              {/* <span className="sub-label">{t('optimization.label.budgetOpt.config.attributionTarget.ko')}</span> */}
            </div>
            <CustomRadioGroup<ContributionType>
              items={contributionTypeCodes}
              onChange={handleContributionType}
              defaultValue={contributionType}
              disabled={!enableGA || !hasAuthority}
              disableRipple
              customClass="budget-config-radio small"
            />
          </div>
        </Box>
      </Box>
    </div>
  )
}

export default BudgetOptConfig
