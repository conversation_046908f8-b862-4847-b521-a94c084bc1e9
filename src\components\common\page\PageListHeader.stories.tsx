// src/components/common/page/PageListHeader.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import PageListHeader from './PageListHeader';
import TableHeaderRowCount from '@components/common/table/TableHeaderRowCount';
import { action } from '@storybook/addon-actions';

const meta: StorybookMeta<typeof PageListHeader> = {
  title: 'Components/Common/Page/PageListHeader',
  component: PageListHeader,
  parameters: {
    layout: 'padded',
  },
  tags: ['autodocs'],
  argTypes: {
    placeHolder: {
      control: 'text',
      description: 'Placeholder text for search input field',
      defaultValue: 'Search',
    },
    buttonLabel: {
      control: 'text',
      description: 'Label text for create button',
      defaultValue: '신규생성',
    },
    handleCreate: {
      description: 'Function called when create button is clicked',
    },
    handleSearch: {
      description: 'Function called when search is performed',
    },
    rowCountComponent: {
      description: 'Additional component to display in left area (e.g., creation count display)',
    },
    needAuth: {
      control: 'boolean',
      description: 'Whether to check user authority for create button visibility',
      defaultValue: true,
    },
    visibleSearchIcon: {
      control: 'boolean',
      description: 'Whether to display search icon',
      defaultValue: false,
    },
    gtmId: {
      control: 'text',
      description: 'Google Tag Manager ID',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default example
export const Default: Story = {
  args: {
    placeHolder: 'Search',
    buttonLabel: '신규생성',
    handleCreate: action('handleCreate'),
    handleSearch: action('handleSearch'),
    needAuth: true,
    visibleSearchIcon: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Default PageListHeader component with create button and search functionality.',
      },
    },
  },
};

// ReportRawDataPage example
export const ReportRawDataPageExample: Story = {
  args: {
    handleCreate: action('openCreateModal'),
    placeHolder: '리포트명, 광고주명 검색',
    handleSearch: action('handleSearch'),
    rowCountComponent: (
      <TableHeaderRowCount current={1} total={3} text="오늘 생성 가능">
        <>
          <h1>{'보고서 생성 횟수'}</h1>
          <div className="common-style">
            <p style={{ fontWeight: 500 }}>
              하루 생성 가능한 대용량리포트 횟수는 3개로 제한됩니다. 추가 리포트가 필요하실 경우 다음날 다시
              시도해주세요.
            </p>
            <p>
              ※ 3개 제한은 생성을 시도한 횟수를 기준으로 산정합니다. 생성완료 및 삭제 여부 상관없이 횟수에
              포함된다는 점 참고바랍니다.
            </p>
          </div>
        </>
      </TableHeaderRowCount>
    ),
    visibleSearchIcon: true,
    buttonLabel: '신규생성',
  },
  parameters: {
    docs: {
      description: {
        story: 'Example used in ReportRawDataPage. Shows search icon and additional component displaying report creation limit.',
      },
    },
  },
};

// With search icon example
export const WithSearchIcon: Story = {
  args: {
    placeHolder: '검색어를 입력하세요',
    buttonLabel: '추가',
    handleCreate: action('handleCreate'),
    handleSearch: action('handleSearch'),
    needAuth: true,
    visibleSearchIcon: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Example with search icon displayed.',
      },
    },
  },
};