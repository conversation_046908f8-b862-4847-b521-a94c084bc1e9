.CampaignCompleteModal .MuiDialog-paper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 30px;
  gap: 30px;
  width: 1300px;
  max-width: 1300px !important;
  box-sizing: border-box;
  border-radius: 10px;
  border: 1px solid #efefef;
}

.CampaignCompleteModal {
  .title-modal-completed-create-campaign {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.5;
  }
  .campaign-completed-list-products {
    width: 60%;
    border-radius: 4px;

    .virtualized-table {
      height: 100% !important;
    }

    .MuiTableContainer-root {
      width: 100%;
      height: 100%;
    }

    .product-image {
      display: flex;
      align-items: center;
      justify-content: center;
      justify-self: center;
      width: 40px;
      height: auto;
    }

    .MuiTableCell-root {
      padding: 8px 20px !important;
    }
  }
  .campaign-completed-basic-info {
    width: 40%;
    border-radius: 4px;
    border: 1px solid #efefef;
    display: flex;
    flex-direction: column;

    .title-info {
      font-weight: 700;
      font-size: 16px;
      line-height: 1.5;
      color: #171717;
      margin-bottom: 20px;
    }
    .title-detail {
      font-weight: 400;
      font-size: 14px;
      line-height: 1.5;
      color: #999999;
      width: 40%;
    }
  }
}
