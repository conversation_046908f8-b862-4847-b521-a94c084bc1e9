import React, { useEffect } from 'react'
import './App.scss'
import {
  Routes,
  Route,
  BrowserRouter,
  Navigate,
  useLocation,
  useNavigationType,
  createRoutesFromChildren,
  matchRoutes
} from 'react-router-dom'
import Loading from '@components/common/Loading'
import SessionUtil from '@utils/SessionUtil'
import ErrorPage from '@pages/error/ErrorPage'
import SystemMaintenancePage from '@pages/SystemMaintenance/SystemMaintenancePage'
import CommonToast from '@components/common/CommonToast'
import {
  LoginPage,
  RegisterPage,
  RegisterResultPage,
  FindPasswordPage,
  ResetPasswordPage,
  EmailVerificationPage,
  UpdatePasswordPage
} from '@pages/login'
import { Router, RouterIntro, RouterSetting } from '@router/index'
import { CssBaseline, ThemeProvider, unstable_createMuiStrictModeTheme } from '@material-ui/core'
import { isMobile } from 'react-device-detect'
import { PageErrorCode } from '@models/common/PageErrorCode'
import SubscriptionPage from '@pages/subscription/SubscriptionPage'
import CustomOAuthLink from '@pages/customOAuthLink/CustomOAuthLink'
import AcceptInvitation from '@pages/acceptInvitation/AcceptInvitation'
import RedirectPage from '@pages/redirect/RedirectPage'
import * as Sentry from '@sentry/react'
import axios, { AxiosError } from 'axios'
import { CommonDialog } from '@components/common'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import TagManager from 'react-gtm-module'
import { initChannelService } from '@utils/ChannelServiceUtil'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      retry: false
    }
  }
})

if (process.env.REACT_APP_GTM_ID) {
  TagManager.initialize({ gtmId: process.env.REACT_APP_GTM_ID })
}

Sentry.init({
  dsn: 'https://<EMAIL>/4506947522789376',
  integrations: [
    Sentry.reactRouterV6BrowserTracingIntegration({
      useEffect: React.useEffect,
      useLocation,
      useNavigationType,
      createRoutesFromChildren,
      matchRoutes
    }),
    Sentry.replayIntegration()
  ],
  beforeSend(event, hint) {
    const error = hint.originalException
    if (axios.isAxiosError(error)) logSentryError(error as AxiosError)
    return event
  },
  tracesSampleRate: 0.2,
  tracePropagationTargets: [/^https:\/\/be\.stg\.mopapp\.net/, /^https:\/\/be\.mopapp\.net/],
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0
})

function logSentryError(error: AxiosError) {
  if (error.response && error?.response.status === 429) {
    console.log('Too Many Requests Error:', error?.response)
  }
}

const SentryRoutes = Sentry.withSentryReactRouterV6Routing(Routes)

const App: React.FC = () => {
  const sessionUtil = new SessionUtil()
  const theme = unstable_createMuiStrictModeTheme({
    typography: {
      fontFamily: ['Noto Sans Korean', 'sans-serif'].join(',')
    },
    overrides: {
      MuiCssBaseline: {
        '@global': {
          '*::-webkit-scrollbar': {
            width: '13px'
          },
          '*::-webkit-scrollbar-track': {
            width: '9px',
            backgroundColor: '#fff',
            borderRight: '5px solid #f'
          },
          '*::-webkit-scrollbar-thumb': {
            width: '9px',
            borderRadius: '9px',
            border: '2px solid transparent',
            backgroundClip: 'padding-box',
            backgroundColor: '#d5d6e0'
          }
        }
      }
    }
  })

  useEffect(() => {
    return initChannelService({
      memberId: '',
      memberHash: ''
    })
  }, [])

  const redirectToMainPage = () => {
    if (sessionUtil.getSessionId()) {
      return isMobile ? <Navigate to="/error" state={PageErrorCode.NOT_SUPPORT_MOBILE_PAGE} /> : <Router />
    } else {
      return <RouterIntro />
    }
  }

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Loading />
        <BrowserRouter>
          <SentryRoutes>
            <Route path="/subscription" element={<SubscriptionPage />} />
            <Route path="/error" element={<ErrorPage />} />
            {/* <Route path="/login" element={<SystemMaintenancePage />} /> */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />
            <Route path="/email-verification" element={<EmailVerificationPage />} />
            <Route path="/auth-result" element={<RegisterResultPage />} />
            <Route path="/find-password" element={<FindPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/update-password" element={<UpdatePasswordPage />} />
            <Route path="/oauth-link" element={<CustomOAuthLink />} />
            <Route path="/accept-invitation" element={<AcceptInvitation />} />
            <Route path="/redirect" element={<RedirectPage />} />
            <Route path="/setting/*" element={<RouterSetting />} />
            <Route path="/*" element={redirectToMainPage()} />
          </SentryRoutes>
        </BrowserRouter>
        <CommonDialog />
        <CommonToast />
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export default App
