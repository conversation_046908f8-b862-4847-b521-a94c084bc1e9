import { Switch, withStyles, Tooltip } from '@material-ui/core';
import CustomTooltip from '@components/common/CustomTooltip';
import UndefinedChip from '@components/common/UndefinedChip';
import {
  AdEfficiencyAnalysisResultColumn,
  AdEfficiencyAnalysisResultDetails,
} from '@models/adEfficiency/AdEfficiency';
import { useRecoilValue } from 'recoil';
import { useTranslation } from 'react-i18next';
import { xAxisState, yAxisState, sizeState } from './AdEfficiencyChart/store'
import AdEfficiencyTooltip from './AdEfficiencyTooltip';
import { checkAuthority } from '@utils/AuthorityUtil';
import { AuthorityType } from '@models/common/Advertiser';
import { advertiserState } from '@store/Advertiser';
import { mediaActiveStatus } from '@utils/common/MediaType'

import './AdEfficiencyResultTableFormatter.scss';
import { t } from 'i18next';
export default class AdEfficiencyAnalysisResultTableFormatter {
  constructor() {
    this.field = ''
    this.order = 'asc'
  }
  field: string
  order: 'asc' | 'desc'
  handleSwitchOff(item: AdEfficiencyAnalysisResultDetails){}
  getColumnFormat = (): Array<AdEfficiencyAnalysisResultColumn> => {
    const { t } = useTranslation();
    const advertiser = useRecoilValue(advertiserState);
    const hasAuthority = checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
    const xAxis = useRecoilValue(xAxisState)
    const yAxis = useRecoilValue(yAxisState)
    const sizeAxis = useRecoilValue(sizeState)

    const _format = (new Intl.NumberFormat('ko-KR', { maximumFractionDigits: 0 })).format
    const _format2 = (new Intl.NumberFormat('ko-KR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })).format
    const _format4 = (new Intl.NumberFormat('ko-KR', { minimumFractionDigits: 4, maximumFractionDigits: 4 })).format
    const format = (value: string|number) => _format(Number(value))
    const format2 = (value: string|number) => _format2(Number(value))
    const format4 = (value: string|number) => _format4(Number(value))

    const TableTooltip = withStyles((theme) => ({
      tooltip: {
        '&.MuiTooltip-tooltip': {
          backgroundColor: 'transparent',
          maxWidth: 'unset',
        },
        '& .chart-tooltip__box': {
          position: 'relative',
        },
      },
    }))(Tooltip)

    const MyTooltip = withStyles({
      tooltip: {
        minWidth: 'unset !important',
      },
    })(CustomTooltip)

    const columnCreative = () => {
      return {
        title: 'Creative',
        field: 'creativeName',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'creativeName' ? this.order : undefined,
        cellStyle: {
          width: '*',
          minWidth: 90
        },
        render: (rowData) => {
          const imageUrl = rowData.mediaType === 'KAKAO' ? JSON.parse(rowData.image.replace(/&quot;/g, '"'))?.url : rowData.image
          return (
            <TableTooltip title={ <AdEfficiencyTooltip
                x={Number(rowData[xAxis])}
                y={Number(rowData[yAxis])}
                size={Number(rowData[sizeAxis])}
                color="#040A45"
                top={0}
                left={0}
                image={imageUrl}
                campaignId={rowData.campaignId}
                accountId={rowData.accountId}
                adgroupId={rowData.adgroupId}
                mediaType={rowData.mediaType}
                creativeId={rowData.creativeId}
                creativeName={rowData.creativeName}
                campaignName={rowData.campaignName}
                adgroupName={rowData.adgroupName}
              /> }
              placement="right-start"
              PopperProps={{
                modifiers: {
                  preventOverflow: {
                    enabled: true,
                    boundariesElement: 'viewport',
                  }
                },
              }}
            >
              <div className='cell-body-box creative-cell'>
                <strong>{rowData.creativeName}</strong><br/>
                <span>{rowData.creativeId}</span>
              </div>
            </TableTooltip>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnStatus = () => {
      return {
        title: 'Status',
        field: 'onoff',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'onoff' ? this.order : undefined,
        cellStyle: {
          width: '62px',
          minWidth: '62px'
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box' style={{'textAlign': 'center'}}>
              { rowData.mediaType === 'CRITEO' ?
                <MyTooltip title={<p>{t('adEfficiency.label.ResultTable.message.criteoOnoff')}</p>}
                placement="right-start"
                PopperProps={{
                  modifiers: {
                    preventOverflow: {
                      enabled: true,
                      boundariesElement: 'viewport',
                    }
                  },
                }}>
                  <UndefinedChip width={30} />
                </MyTooltip>
                : <Switch
                    edge="end"
                    color="primary"
                    checked={rowData.onoff === 'ON'}
                    onChange={() => this.handleSwitchOff(rowData)}
                    disabled={rowData.onoff !== 'ON' || !hasAuthority}
                />
              }
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnEfficiency = () => {
      return {
        title: 'Efficiency',
        field: 'efficiency',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'efficiency' ? this.order : undefined,
        customSort: (a, b) => Number(a.efficiency) - Number(b.efficiency),
        cellStyle: {
          width: 90,
          minWidth: 90,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span><strong>{ format4(Number(rowData.efficiency).toFixed(4)) }</strong></span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCost = () => {
      return {
        title: 'Cost',
        field: 'cost',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'cost' ? this.order : undefined,
        customSort: (a, b) => Number(a.cost) - Number(b.cost),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2(Number(rowData.cost).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnImpression = () => {
      return {
        title: 'Impression',
        field: 'impressions',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'impressions' ? this.order : undefined,
        customSort: (a, b) => Number(a.impressions) - Number(b.impressions),
        cellStyle: {
          width: 105,
          minWidth: 105,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format(Number(rowData.impressions).toFixed()) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnView = () => {
      return {
        title: 'View',
        field: 'views',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'views' ? this.order : undefined,
        customSort: (a, b) => Number(a.views) - Number(b.views),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format(Number(rowData.views).toFixed()) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnClick = () => {
      return {
        title: 'Click',
        field: 'clicks',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'clicks' ? this.order : undefined,
        customSort: (a, b) => Number(a.clicks) - Number(b.clicks),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format(Number(rowData.clicks).toFixed()) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnConversion = () => {
      return {
        title: 'Conversion',
        field: 'conversions',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'conversions' ? this.order : undefined,
        customSort: (a, b) => Number(a.conversions) - Number(b.conversions),
        cellStyle: {
          width: 105,
          minWidth: 105,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format(Number(rowData.conversions).toFixed()) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnRevenue = () => {
      return {
        title: 'Revenue',
        field: 'revenue',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'revenue' ? this.order : undefined,
        customSort: (a, b) => Number(a.revenue) - Number(b.revenue),
        cellStyle: {
          width: 80,
          minWidth: 80,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format(Number(rowData.revenue).toFixed()) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnVTR = () => {
      return {
        title: 'VTR%',
        field: 'vtr',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'vtr' ? this.order : undefined,
        customSort: (a, b) => Number(a.vtr) - Number(b.vtr),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2((Number(rowData.vtr)*100).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCTR = () => {
      return {
        title: 'CTR%',
        field: 'ctr',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'ctr' ? this.order : undefined,
        customSort: (a, b) => Number(a.ctr) - Number(b.ctr),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2((Number(rowData.ctr)*100).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCVR = () => {
      return {
        title: 'CVR%',
        field: 'cvr',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'cvr' ? this.order : undefined,
        customSort: (a, b) => Number(a.cvr) - Number(b.cvr),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2((Number(rowData.cvr)*100).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCPM = () => {
      return {
        title: 'CPM',
        field: 'cpm',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'cpm' ? this.order : undefined,
        customSort: (a, b) => Number(a.cpm) - Number(b.cpm),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2(Number(rowData.cpm).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCPV = () => {
      return {
        title: 'CPV',
        field: 'cpv',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'cpv' ? this.order : undefined,
        customSort: (a, b) => Number(a.cpv) - Number(b.cpv),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2(Number(rowData.cpv).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCPC = () => {
      return {
        title: 'CPC',
        field: 'cpc',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'cpc' ? this.order : undefined,
        customSort: (a, b) => Number(a.cpc) - Number(b.cpc),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2(Number(rowData.cpc).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnCPA = () => {
      return {
        title: 'CPA',
        field: 'cpa',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'cpa' ? this.order : undefined,
        customSort: (a, b) => Number(a.cpa) - Number(b.cpa),
        cellStyle: {
          width: 72,
          minWidth: 72,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2(Number(rowData.cpa).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }

    const columnROAS = () => {
      return {
        title: 'ROAS%',
        field: 'roas',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'roas' ? this.order : undefined,
        customSort: (a, b) => Number(a.roas) - Number(b.roas),
        cellStyle: {
          width: 75,
          minWidth: 75,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              <span>{ format2((Number(rowData.roas)*100).toFixed(2)) }</span>
            </div>
          )
        }
      } as AdEfficiencyAnalysisResultColumn
    }
    

    const columns: AdEfficiencyAnalysisResultColumn[] = [
      columnCreative(),
      columnStatus(),
      columnEfficiency(),
      columnCost(),
      columnImpression(),
      columnView(),
      columnClick(),
      columnConversion(),
      columnRevenue(),
      columnVTR(),
      columnCTR(),
      columnCVR(),
      columnCPM(),
      columnCPV(),
      columnCPC(),
      columnCPA(),
      columnROAS(),
    ];

    return columns;
  };
}
