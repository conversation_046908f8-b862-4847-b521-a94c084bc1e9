.chart-tooltip {
  &__box {
    --top: 0;
    --left: 0;
    --tooltip-color: #6EC7C2;
    position: absolute;
    width: 250px;
    top: var(--top);
    left: var(--left);
    border-top: 4px solid var(--tooltip-color);
    z-index: 100;
    background-color: white;
    box-shadow: 0 5px 15px 0 rgba(68, 68, 79, 0.7);
  }

  &__header {
    background-color: #F2F3F6;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    gap: 8px;
    line-height: 1.2;

    .title {
      font-size: 16px;
      font-weight: 700;
      color: var(--tooltip-color);
      word-break: break-all;
    }
    .id {
      font-size: 12px;
      font-weight: 500;
      color: var(--point_color);
    }
  }

  &__content {
    padding: 16px;
  }

  &__category {
    .chart-tooltip__header span {
      font-size: 12px;
    }
  }

  &__image {
    img {
      display: block;
      max-width: 100%;
      max-height: 200px;
      object-fit: contain;
      margin: 0 auto;
      padding-bottom: 16px;
    }
    span {
      display: inline-block;
      color: var(--point_color);
      font-size: 10px;
      padding-bottom: 16px;
    }
    padding: 0 16px;
    text-align: center;
  }

  &__value {
    display: grid;
    grid-template-columns: 90px 1fr;
    color: var(--point_color);
    font-weight: 700;
    margin-bottom: 16px;

    span {
      display: inline-block;
      font-size: 10px;
      padding: 4px 8px;
      margin-bottom: 1px;
      &:nth-child(2n - 1) {
        background-color: #F2F3F6;
      }
      &:nth-child(2n) {
        border-bottom: 1px solid #F2F3F6;
      }
      &:last-child {
        border-bottom: none;
      }
    }
  }

  &__media {
    display: flex;
    flex-direction: column;
  }

  &__media-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 8px 0;
    width: 100%;
    border-bottom: 1px solid #F2F3F6;

    &:last-child {
      border-bottom: none;
    }

    span {
      font-size: 10px;
      color: var(--point_color);
    }
    .title {
      font-weight: 700;
      word-break: break-all;
    }
  }

  &__box.side-mode {
    display: grid;
    grid-template-columns: 250px auto;
    width: auto;
    .chart-tooltip__header {
      grid-column: 1/3;
    }
    .chart-tooltip__content {
      grid-column: 1/2;
    }
    .chart-tooltip__image {
      grid-column: 2/3;
      padding: 0;
      img {
        max-height: 300px;
        width: unset;
        padding-right: 16px;
      }
    }
  }
}