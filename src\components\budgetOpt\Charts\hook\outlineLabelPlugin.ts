const outlinedLabel = (reportData: any, chartName: string) => ({
  id: 'outlinedLabel',
  // @ts-ignore
  afterDatasetsDraw(chart, _args, _options) {
    const {
      ctx,
      data,
      chartArea: { width, height },
    } = chart;
    ctx.save()

    let ChartEl = document.body.querySelector(`#${_options.id}`)
    data.datasets[0].data.forEach((datapoint: any, index: number) => {
      const existingEl = document.body.querySelector(`.media-label-container-${chartName}_${index}`)
      if (existingEl) {
        existingEl.remove()
      }

      const { x, y } = chart.getDatasetMeta(0).data[index].tooltipPosition()

      const targetMedia = reportData[index]

      const yCenter = height / 2;
      const xCenter = width / 2;

      const mediaTypeEl = document.createElement('span')
      mediaTypeEl.classList.add('media-label-type')
      mediaTypeEl.style.setProperty('--label-color', data.datasets[0].backgroundColor[index])
      const budgetRatio = targetMedia.budgetRatio >= 1 ? '100' : (targetMedia.budgetRatio * 100).toFixed(2)
      const [mediaType, accountId, adType] = targetMedia.key.split('-')
      mediaTypeEl.innerHTML = chartName === 'spending'
        ? `<span class="adType">${adType}</span><br/>${mediaType}<br/>${budgetRatio}%` : `<span class="adType">${adType}</span><br/>${mediaType}`

      const mediaLabelOuter = document.createElement('div')
      mediaLabelOuter.classList.add(`media-label-container-${chartName}_${index}`)
      mediaLabelOuter.appendChild(mediaTypeEl)

      if (chartName === 'recommendation') {
        const budgetRatio = targetMedia.ratio.toFixed(2)
        if (Math.abs(budgetRatio) !== 0) {
          const sign = budgetRatio > 0 ? '+' : ''
          const budgetRatioEl = document.createElement('span')
          budgetRatioEl.classList.add('media-budget-ratio')
          budgetRatioEl.classList.add(budgetRatio > 0 ? 'plus' : 'minus')
          budgetRatioEl.innerHTML = `${sign}${budgetRatio}%`
          mediaLabelOuter.appendChild(budgetRatioEl)
        }
      }

      mediaLabelOuter.style.position = 'absolute';

      mediaLabelOuter.style.visibility = 'hidden'
      ChartEl?.appendChild(mediaLabelOuter)
      const { width: labelWidth, height: labelHeight } = mediaLabelOuter.getBoundingClientRect()
      const dx = x - xCenter
      const dy = y - yCenter
      const radian = Math.atan2(dy, dx)
      const r = Math.sqrt(dx*dx + dy*dy)
      const r1 = r + Math.sqrt(labelWidth*labelWidth + labelHeight*labelHeight) / 2 + (chartName === 'spending' ? 10 : 20)
      const x1 = xCenter + r1 * Math.cos(radian)
      const y1 = yCenter + r1 * Math.sin(radian)
      mediaLabelOuter.style.left = `${x1}px`
      mediaLabelOuter.style.top = `${y1}px`
      mediaLabelOuter.style.visibility = 'visible'

    })
  }
})

export default outlinedLabel