import { GeneralChartData } from '@models/common/ChartData';

interface Props {
  reports?: any;
  targetDataKey: string
}

const useDoughnutsChart = ({
  reports,
  targetDataKey
}: Props): {
  chartData: GeneralChartData;
} => {
  const chartData: GeneralChartData = {
    labels: [],
    datasets: [{ data: [], backgroundColor: [] }],
  };

  reports.forEach((report: any, idx: number) => {
    chartData.labels.push(report.key);
    chartData.datasets[0].backgroundColor!.push(report.color);
    chartData.datasets[0].data.push(report[targetDataKey].toFixed(6));
  });

  return { chartData };
};

export default useDoughnutsChart;
