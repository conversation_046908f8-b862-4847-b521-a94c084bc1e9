/// <reference types="Cypress" />

import AdvertiserMock from '@mock/AdvertiserMock'
import OauthMock from '@mock/OauthMock'
import OauthPage from '@pages/oauth/OauthPage'

const page = new OauthPage()
const oauthService = new OauthMock()
const advertiserService = new AdvertiserMock()

const sessionInfo = {
  sessionId: 'test-session-id',
  memberId: 1,
  memberName: '홍길동'
}

describe('CommerceAccountDetailModal - 커머스 계정 상세 모달', () => {
  describe('모달 열기 및 기본 표시', () => {
    it.guide('OAuth 목록 페이지로 이동한다.', {
      mockFunc: () => {
        advertiserService.successWhenGetAdvertisers()
        oauthService.successWhenGetOAuthList()
      },
      actionFunc: () => {
        page.fakeSession(sessionInfo)
        page.visit('/setting/connect-data')
      },
      waitFunc: () => {
        cy.wait(['@successWhenGetOAuthList', '@successWhenGetAdvertisers'])
      },
      assertFunc: () => {
        page.assertOAuthPageLoaded()
      }
    })

    it.guide('NAVER 미디어의 커머스 연동 버튼을 클릭하면 커머스 계정 상세 모달이 열린다.', {
      mockFunc: () => {
        oauthService.initialData()
        oauthService.successWhenGetCommerceAccounts('2232364')
      },
      actionFunc: () => {
        page.clickCommerceLinkedButton('2232364')
      },
      waitFunc: () => {
        cy.wait('@getCommerceAccounts')
      },
      assertFunc: () => {
        page.assertCommerceAccountModalIsVisible()
        page.assertCommerceAccountTableIsVisible()
        page.assertCommerceAccountAddButtonIsVisible()
      }
    })

    it.guide('모달이 열리면 테이블 컬럼 헤더가 올바르게 표시된다.', {
      mockFunc: () => {},
      actionFunc: () => {},
      waitFunc: () => {},
      assertFunc: () => {
        page.assertCommerceAccountTableColumns()
      }
    })
  })

  describe('계정 추가 기능', () => {
    it.guide('커머스 계정 연동 버튼을 클릭하면 새 창이 열린다.', {
      mockFunc: () => {
        cy.window().then((win) => {
          cy.stub(win, 'open').as('windowOpen')
        })
      },
      actionFunc: () => {
        page.clickCommerceAccountAddButton()
      },
      waitFunc: () => {},
      assertFunc: () => {
        cy.get('@windowOpen').should('have.been.calledOnce')
        cy.get('@windowOpen').should(
          'have.been.calledWith',
          Cypress.sinon.match(/oauth-link\?type=.*&customerId=.*/)
        )
      }
    })
  })

  describe('계정 삭제 기능', () => {
    it.guide('삭제 아이콘을 클릭하면 확인 다이얼로그가 표시된다.', {
      mockFunc: () => {},
      actionFunc: () => {
        page.clickDeleteIconForAccount('1a')
      },
      waitFunc: () => {},
      assertFunc: () => {
        page.assertDeleteConfirmationDialog()
      }
    })

    it.guide('삭제 확인 다이얼로그에서 취소를 클릭하면 삭제가 취소된다.', {
      mockFunc: () => {},
      actionFunc: () => {
        page.clickCancelDeleteButton()
      },
      waitFunc: () => {},
      assertFunc: () => {
        page.assertCommerceAccountTableHasData(2)
      }
    })

    it.guide('삭제 확인 다이얼로그에서 확인을 클릭하면 계정이 삭제된다.', {
      mockFunc: () => {
        oauthService.successWhenDeleteCommerceAccount('2232364', '1a')
        oauthService.successWhenGetCommerceAccounts('2232364')
      },
      actionFunc: () => {
        page.clickDeleteIconForAccount('1a')
        page.clickConfirmDeleteButton()
      },
      waitFunc: () => {
        cy.wait(['@deleteCommerceAccount', '@getCommerceAccounts'])
      },
      assertFunc: () => {
        page.assertCommerceAccountTableHasData(1)
      }
    })
  })
})
