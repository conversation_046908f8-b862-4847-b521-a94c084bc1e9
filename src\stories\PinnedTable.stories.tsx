import type { Meta, StoryObj } from '@storybook/react';
import React, { useCallback, useMemo, useState, useEffect, useRef } from 'react';
import PinnedTable from '../components/common/table/PinnedTable';
import Popover from '@material-ui/core/Popover';
import IconButton from '@material-ui/core/IconButton';

interface PersonRow {
  name: string;
  age: number;
  city: string;
  job: string;
  company: string;
  email: string;
  phone?: string;
  address?: string;
}

const meta: Meta<typeof PinnedTable> = {
  title: 'Table/PinnedTable',
  component: PinnedTable,
  tags: ['autodocs'],
  parameters: {
    layout: 'fullscreen',
  },
};

export default meta;

const columns = [
  {
    header: '이름',
    accessorKey: 'name',
    size: 180,
    meta: { subHeader: '서브-이름', headerRowSpan: 2 },
    cell: ({ row, getValue, tableApi }: any) => (
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <button
          onClick={() => tableApi?.toggleRowPanel?.()}
          aria-label={tableApi?.isRowPanelOpen ? '패널 닫기' : '패널 열기'}
          style={{
            width: 28,
            height: 28,
            borderRadius: 14,
            border: '1px solid #d1d5db',
            background: '#ffffff',
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 16,
            lineHeight: 1,
            cursor: 'pointer',
          }}
        >
          {tableApi?.isRowPanelOpen ? '−' : '+'}
        </button>
        <span>{getValue()}</span>
      </div>
    ),
  },
  { header: '나이', accessorKey: 'age', size: 120, meta: { subHeader: '서브-나이', headerRowSpan: 2 } },
  { header: '도시', accessorKey: 'city', size: 180, meta: { subHeader: '서브-도시', headerColSpan: { span: 2, content: '도시+직업' } } },
  { header: '직업', accessorKey: 'job', size: 220, meta: { subHeader: '서브-직업' } },
  { header: '회사', accessorKey: 'company', size: 240, meta: { subHeader: '서브-회사' } },
  { header: '이메일', accessorKey: 'email', size: 280, meta: { subHeader: '서브-이메일' } },
  { header: '전화번호', accessorKey: 'phone', size: 220, meta: { subHeader: '서브-전화번호' } },
  { header: '주소', accessorKey: 'address', size: 400, meta: { subHeader: '서브-주소' } },
] as const;

function makeRows(offset: number, limit: number): PersonRow[] {
  return Array.from({ length: limit }).map((_, idx) => {
    const i = offset + idx;
    return {
      name: `홍길동 ${i + 1}`,
      age: 20 + ((i * 7) % 25),
      city: ['서울', '부산', '대구', '대전', '광주'][i % 5],
      job: ['개발자', '기획자', '디자이너', '마케터'][i % 4],
      company: ['LG CNS', '삼성SDS', '카카오', '네이버', '쿠팡'][i % 5],
      email: `user${i + 1}@example.com`,
      phone: `010-${(1000 + (i % 9000)).toString().padStart(4, '0')}-${(1000 + ((i * 13) % 9000)).toString().padStart(4, '0')}`,
      address: `서울특별시 강남구 테헤란로 ${10 + (i % 90)}길 ${(i % 200) + 1}`,
    } as PersonRow;
  });
}

export const Default: StoryObj<typeof PinnedTable> = {
  render: () => {
    const [rows, setRows] = useState<PersonRow[]>(() => makeRows(0, 60));
    const [isLoading, setIsLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);

    const onLoadMore = useCallback(() => {
      if (isLoading || !hasMore) return;
      setIsLoading(true);
      // 비동기 로딩 시뮬레이션
      setTimeout(() => {
        const nextOffset = rows.length;
        // 총 500개까지만 로드
        if (nextOffset >= 500) {
          setHasMore(false);
          setIsLoading(false);
          return;
        }
        const next = makeRows(nextOffset, 40);
        setRows((prev) => [...prev, ...next]);
        setIsLoading(false);
      }, 600);
    }, [rows.length, isLoading, hasMore]);

    const tableData = useMemo(() => rows, [rows]);

    return (
      <div style={{ padding: 16 }}>
        <div style={{ width: 720 }}>
          <PinnedTable<PersonRow>
            columns={columns as any}
            data={tableData}
            height={340}
            maxWidth={720}
            onLoadMore={onLoadMore}
            hasMore={hasMore}
            isLoading={isLoading}
            loadMoreOffset={200}
            pinned={{ left: 2 }}
            rowPanelRenderers={{
              leftPanel: (row) => (
                <div>
                  <strong>Left Panel</strong>
                  <div>이름: {row.name}</div>
                  <div>나이: {row.age}</div>
                </div>
              ),
              rightPanel: (row) => (
                <div>
                  <strong>Right Panel</strong>
                  <div>도시: {row.city}</div>
                  <div>직업: {row.job}</div>
                  <div>회사: {row.company}</div>
                </div>
              ),
            }}
          />
        </div>
      </div>
    );
  },
};

export const ComplexCase: StoryObj<typeof PinnedTable> = {
  render: () => {
    type DetailRow = {
      optId: string;
      campaign: string;
      adGroup: string;
      creativeId: string;
      division: string;
    };

    type ProductRow = {
      product: string;
      divisionLabel: string;
      totalSales: number;
      grossRevenue: number;
      refundAmount: number;
      netRevenue: number;
      adCost: number;
      impressions: number;
      clicks: number;
      conversions: number;
      ctr: number; // CTR(%)
      cvr: number; // CVR(%)
      cpc: number; // CPC
      cpa: number; // CPA
      roas: number; // ROAS(%)
      details: DetailRow[];
    };

    const number = (min: number, max: number) =>
      Math.floor(Math.random() * (max - min + 1)) + min;

    const makeProductRows = (count: number): ProductRow[] =>
      Array.from({ length: count }).map((_, i) => {
        const details: DetailRow[] = Array.from({ length: 3 }).map((__, j) => ({
          optId: `${1000 + ((i + 1) * (j + 1))}`,
          campaign: `NEW tyrja`,
          adGroup: `#01.메인-PC`,
          creativeId: `nad-${number(100000000000, 999999999999)}`,
          division: '조회기간\n비교기간',
        }));
        const totalSales = number(1200, 9000);
        const grossRevenue = number(5000000, 20000000);
        const refundAmount = number(0, Math.floor(grossRevenue * 0.2));
        const netRevenue = grossRevenue - refundAmount;
        const adCost = number(100000, 900000);
        const impressions = number(50000, 400000);
        const clicks = number(1000, 15000);
        const conversions = number(10, 300);
        // 파생 지표
        const ctr = +(clicks / Math.max(1, impressions) * 100).toFixed(2);
        const cvr = +(conversions / Math.max(1, clicks) * 100).toFixed(2);
        const cpc = +(adCost / Math.max(1, clicks)).toFixed(0);
        const cpa = +(adCost / Math.max(1, conversions)).toFixed(0);
        const roas = +((netRevenue / Math.max(1, adCost)) * 100).toFixed(2);
        return {
          product: `뉴발란스 운동화 ${i + 1}`,
          divisionLabel: '조회기간\n비교기간',
          totalSales,
          grossRevenue,
          refundAmount,
          netRevenue,
          adCost,
          impressions,
          clicks,
          conversions,
          ctr,
          cvr,
          cpc,
          cpa,
          roas,
          details,
        } as ProductRow;
      });

    const rows = useMemo(() => makeProductRows(25), []);

    // 지표 컬럼 묶음 정의(상품실적 / 광고실적)
    const complexColumns = useMemo(
      () => [
        {
          header: '상품정보',
          accessorKey: 'product',
          size: 500,
          meta: { headerRowSpan: 2 },
          cell: ({ getValue, tableApi }: any) => (
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <button
                onClick={() => tableApi?.toggleRowPanel?.()}
                aria-label={tableApi?.isRowPanelOpen ? '패널 닫기' : '패널 열기'}
                style={{
                  width: 28,
                  height: 28,
                  borderRadius: 14,
                  border: '1px solid #d1d5db',
                  background: '#ffffff',
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 16,
                  lineHeight: 1,
                  cursor: 'pointer',
                }}
              >
                {tableApi?.isRowPanelOpen ? '−' : '+'}
              </button>
              <span>{getValue()}</span>
            </div>
          ),
        },
        {
          header: 'Division',
          accessorKey: 'divisionLabel',
          size: 120,
          meta: { headerRowSpan: 2 },
          cell: ({ getValue }: any) => (
            <div style={{ lineHeight: 1.2, whiteSpace: 'pre-wrap' }}>{getValue()}</div>
          ),
        },
        // 상품실적(4)
        {
          header: '총 판매수량',
          accessorKey: 'totalSales',
          size: 140,
          meta: { headerColSpan: { span: 4, content: '상품실적' }, subHeader: '총 판매수량' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: '총 매출',
          accessorKey: 'grossRevenue',
          size: 140,
          meta: { subHeader: '총 매출' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: '환불 금액',
          accessorKey: 'refundAmount',
          size: 140,
          meta: { subHeader: '환불 금액' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: '순 매출',
          accessorKey: 'netRevenue',
          size: 140,
          meta: { subHeader: '순 매출' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        // 광고실적(9)
        {
          header: '광고비(VAT+)',
          accessorKey: 'adCost',
          size: 160,
          meta: { headerColSpan: { span: 9, content: '광고실적' }, subHeader: '광고비(VAT+)' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: '노출',
          accessorKey: 'impressions',
          size: 120,
          meta: { subHeader: '노출' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: '클릭',
          accessorKey: 'clicks',
          size: 120,
          meta: { subHeader: '클릭' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: '전환',
          accessorKey: 'conversions',
          size: 120,
          meta: { subHeader: '전환' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>
              {getValue().toLocaleString()}
            </div>
          ),
        },
        {
          header: 'ROAS(%)',
          accessorKey: 'roas',
          size: 120,
          meta: { subHeader: 'ROAS(%)' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>{getValue().toLocaleString()}</div>
          ),
        },
        {
          header: 'CTR(%)',
          accessorKey: 'ctr',
          size: 120,
          meta: { subHeader: 'CTR(%)' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>{getValue().toLocaleString()}</div>
          ),
        },
        {
          header: 'CVR(%)',
          accessorKey: 'cvr',
          size: 120,
          meta: { subHeader: 'CVR(%)' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>{getValue().toLocaleString()}</div>
          ),
        },
        {
          header: 'CPC',
          accessorKey: 'cpc',
          size: 120,
          meta: { subHeader: 'CPC' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>{getValue().toLocaleString()}</div>
          ),
        },
        {
          header: 'CPA',
          accessorKey: 'cpa',
          size: 120,
          meta: { subHeader: 'CPA' },
          cell: ({ getValue }: any) => (
            <div style={{ width: '100%', display: 'flex', justifyContent: 'flex-end' }}>{getValue().toLocaleString()}</div>
          ),
        },
      ] as const,
      []
    );

    // 컬럼 가시성 제어 상태
    const [visibility, setVisibility] = useState<any>({});

    // 지표설정 패널 항목 정의
    const productMetricItems = [
      { key: 'totalSales', label: '총 판매 수량' },
      { key: 'grossRevenue', label: '총 매출' },
      { key: 'refundAmount', label: '환불 금액' },
      { key: 'netRevenue', label: '순 매출' },
    ];
    const adMetricItems = [
      { key: 'adCost', label: '광고비(VAT+)' },
      { key: 'impressions', label: '노출' },
      { key: 'clicks', label: '클릭' },
      { key: 'conversions', label: '전환' },
      { key: 'roas', label: 'ROAS(%)' },
      { key: 'ctr', label: 'CTR(%)' },
      { key: 'cvr', label: 'CVR(%)' },
      { key: 'cpc', label: 'CPC' },
      { key: 'cpa', label: 'CPA' },
    ];

    const toggleMetric = (key: string) => {
      setVisibility((prev: any) => ({ ...prev, [key]: !(prev?.[key] ?? true) }));
    };

    const isChecked = (key: string) => (visibility[key] ?? true);

    // metric defs dependent on visibility
    const allMetricDefs = useMemo(() => (complexColumns as any).slice(2) as any[], [complexColumns]);
    const visibleMetricDefs = useMemo(() => allMetricDefs.filter((def: any) => isChecked(def.accessorKey as string)), [allMetricDefs, visibility]);
    const metricColSizes = useMemo(() => visibleMetricDefs.map((def: any, i: number) => (def as any).size ?? 120), [visibleMetricDefs]);

    // 마스터 체크박스(상품/광고)
    const productAll = productMetricItems.every((m) => isChecked(m.key));
    const productSome = productMetricItems.some((m) => isChecked(m.key));
    const adAll = adMetricItems.every((m) => isChecked(m.key));
    const adSome = adMetricItems.some((m) => isChecked(m.key));

    const productMasterRef = useRef<HTMLInputElement | null>(null);
    const adMasterRef = useRef<HTMLInputElement | null>(null);

    useEffect(() => {
      if (productMasterRef.current) productMasterRef.current.indeterminate = !productAll && productSome;
    }, [productAll, productSome]);
    useEffect(() => {
      if (adMasterRef.current) adMasterRef.current.indeterminate = !adAll && adSome;
    }, [adAll, adSome]);

    const setGroup = (items: { key: string }[], checked: boolean) => {
      setVisibility((prev: any) => {
        const next = { ...prev };
        items.forEach((m) => {
          next[m.key] = checked;
        });
        return next;
      });
    };

    // Popover 상태
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const open = Boolean(anchorEl);
    const handleOpen = (e: React.MouseEvent<HTMLButtonElement>) => setAnchorEl(e.currentTarget);
    const handleClose = () => setAnchorEl(null);

    return (
      <div style={{ padding: 16 }}>
        <div style={{ position: 'relative' }}>
          <PinnedTable<ProductRow>
            columns={complexColumns as any}
            data={rows}
            height={600}
            maxWidth={'100%'}
            pinned={{ left: 2 }}
            columnVisibility={visibility}
            onColumnVisibilityChange={setVisibility}
            rowPanelRenderers={{
              leftPanel: (row) => (
                <div>
                  <table className="panelTable">
                    <thead>
                      <tr>
                        <th>최적화 ID</th>
                        <th>캠페인</th>
                        <th>애드그룹</th>
                        <th>소재ID</th>
                        <th style={{ width: 120 }}></th>
                      </tr>
                    </thead>
                    <tbody>
                      {row.details.map((d, idx) => (
                        <tr key={idx}>
                          <td style={{ padding: 8 }}>{d.optId}</td>
                          <td style={{ padding: 8 }}>{d.campaign}</td>
                          <td style={{ padding: 8 }}>{d.adGroup}</td>
                          <td style={{ padding: 8 }}>{d.creativeId}</td>
                          <td style={{ padding: 8, whiteSpace: 'pre-wrap' }}>{d.division}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ),
              rightPanel: (row) => (
                <div>
                  <table className="panelTable">
                    <colgroup>
                      {metricColSizes.map((w: number, i: number) => (
                        <col key={i} style={{ width: w, minWidth: w }} />
                      ))}
                    </colgroup>
                    <thead>
                      <tr>
                        <th colSpan={metricColSizes.length} style={{ padding: 8, textAlign: 'left', background: '#f3f4f6' }}>&nbsp;</th>
                      </tr>
                    </thead>
                    <tbody>
                      {row.details.map((_, idx) => (
                        <tr key={idx}>
                          {visibleMetricDefs.map((def: any, ci: number) => {
                            const key = def.accessorKey as string;
                            const isProductMetric = ['totalSales','grossRevenue','refundAmount','netRevenue'].includes(key);
                            const raw = isProductMetric ? '' : (row as any)[key];
                            const display = typeof raw === 'number' ? raw.toLocaleString() : '';
                            return (
                              <td
                                key={key}
                                style={{
                                  padding: 8,
                                  position: ci === 0 ? 'relative' : undefined,
                                  width: metricColSizes[ci],
                                  minWidth: metricColSizes[ci],
                                  textAlign: 'right',
                                }}
                              >
                                {ci === 0 ? (
                                  <>
                                    <div style={{ visibility: 'hidden', whiteSpace: 'pre-wrap', lineHeight: 1.2, minHeight: 48 }}>조회기간{"\n"}비교기간</div>
                                    <div style={{ position: 'absolute', top: 8, left: 8, right: 8, bottom: 8, display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                      {display}
                                    </div>
                                  </>
                                ) : (
                                  display
                                )}
                              </td>
                            );
                          })}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ),
            }}
          />

          {/* 플로팅 설정 버튼 */}
          <IconButton
            onClick={handleOpen}
            aria-label="지표 설정"
            size="small"
            style={{
              position: 'absolute',
              right: 8,
              top: 8,
              zIndex: 20,
              width: 32,
              height: 32,
              borderRadius: 16,
              border: '1px solid #d1d5db',
              background: '#ffffff',
            }}
          >
            …
          </IconButton>
        </div>

        {/* 지표설정 Popover */}
        <Popover
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          transformOrigin={{ vertical: 'top', horizontal: 'right' }}
          PaperProps={{ style: { borderRadius: 8 } }}
        >
          <div style={{ padding: 12, maxWidth: 880 }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, fontWeight: 700, marginBottom: 8 }}>
              <input ref={productMasterRef} type="checkbox" checked={productAll} onChange={(e) => setGroup(productMetricItems, e.target.checked)} />
              상품실적
            </div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16, marginBottom: 8 }}>
              {productMetricItems.map((m) => (
                <label key={m.key} style={{ display: 'inline-flex', alignItems: 'center', gap: 8 }}>
                  <input
                    type="checkbox"
                    checked={isChecked(m.key)}
                    onChange={() => toggleMetric(m.key)}
                  />
                  {m.label}
                </label>
              ))}
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, fontWeight: 700, margin: '12px 0 8px' }}>
              <input ref={adMasterRef} type="checkbox" checked={adAll} onChange={(e) => setGroup(adMetricItems, e.target.checked)} />
              광고실적
            </div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: 16 }}>
              {adMetricItems.map((m) => (
                <label key={m.key} style={{ display: 'inline-flex', alignItems: 'center', gap: 8 }}>
                  <input
                    type="checkbox"
                    checked={isChecked(m.key)}
                    onChange={() => toggleMetric(m.key)}
                  />
                  {m.label}
                </label>
              ))}
            </div>
          </div>
        </Popover>
      </div>
    );
  },
}; 