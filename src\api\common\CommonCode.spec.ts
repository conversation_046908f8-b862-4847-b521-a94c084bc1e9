import { getCommonCode } from './CommonCode';
import * as ApiUtil from '@utils/ApiUtil';

describe('CommonCode', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('91 - 응답이 정상인 경우, CommonCode를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          groupCode: 'group1',
          codeId: 'codeId1',
          codeName: 'codeName1',
        },
        {
          groupCode: 'group1',
          codeId: 'codeId2',
          codeName: 'codeName2',
        },
      ],
    };

    mockCallApi.mockResolvedValue(responseMock);
    const commonCodes = await getCommonCode('test1');
    expect(commonCodes).toEqual(responseMock.data);
  });

  it('91 - 응답이 비정상인 경우, null을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const commonCodes = await getCommonCode('test2');
    expect(commonCodes).toBeNull();
  });
});
