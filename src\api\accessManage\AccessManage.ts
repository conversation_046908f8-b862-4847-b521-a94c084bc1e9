/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  AccessManageAccessKeyList,
  CreateAccessKeyParams,
  UpdateAccessKeyParams,
} from '@models/accessManage/AccessManage';

export const getAccessKeyList = async (isLoading = true): Promise<AccessManageAccessKeyList> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/access/list',
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as AccessManageAccessKeyList;
};

export const getAccessKey = async (advertiserId: number, accessKey: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/access/${advertiserId}/${accessKey}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
};

export const createAccessKey = async (params: CreateAccessKeyParams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/access`,
    method: Method.POST,
    params: {
      bodyParams: {
        ...params,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateAccessKey = async (
  advertiserId: number,
  accessKey: string,
  params: UpdateAccessKeyParams,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/access/${advertiserId}/${accessKey}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        ...params,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const deleteAccessKey = async (advertiserId: number, accessKey: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/access/${advertiserId}/${accessKey}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });
};
