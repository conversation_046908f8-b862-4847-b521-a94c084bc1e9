.report-settings-wrapper {
  display: flex;
  justify-content: center;
  width: 100%;

  .report-settings-left {
    flex: 1;
    max-width: 1200px; 
  }

  .report-settings-right {
    display: flex;
    width: 120px;
    align-items: center;
    justify-content: flex-end;
    #SearchButton {
      justify-self: flex-end;
      width: 90px;
      height: 32px;
      border-radius: 20px;
      border: 1px solid var(--color-active-blue);
      background-color: transparent;
      font-weight: 700;
      font-size: 14px;
      color: var(--color-active-blue);
      cursor: pointer;
    }
  }
}

.report-settings-container {
  display: flex;
  height: 48px;
  margin-bottom: 6px;
  width: 100%;
  
  .select-form {
    &:first-child {
      margin-left: 0px;
    }
    margin-left: 7px;
    padding: 0;
    background: #fff;
    border: 1px solid var(--border-gray-lighter);
    display: flex;
    flex: 1; 

    .select-label {
      min-width: 120px; 
      padding: 0 20px;
      text-align: center;
      border-right: 1px solid var(--border-gray-lighter);
      display: flex;
      justify-content: center;
      align-items: center;

      .MuiFormLabel-root {
        font-size: 12px;
        font-weight: 700;
        line-height: 24px;
        white-space: nowrap;
        &.off {
          color: var(--color-disabled);
        }
        svg {
          transform: translateY(3px);
          margin-right: 2px;
        }
      }
      .MuiCheckbox-root {
        padding: 0 10px 0 0;
      }
    }

    .select-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px; 
      .MuiIconButton-root{
        padding: 0;
      }
      button {
        padding: 0;
        font-size: 12px;
        font-weight: 700;
        width: 100%;
        // Only style the first span inside the button
        span:first-of-type {
          padding-left: 20px;
        }
        
      }
    }
  }
}

.range-calendar-popover {
  min-width: 800px;
  .MuiPopover-paper {
    min-width: 800px;
  }
}
