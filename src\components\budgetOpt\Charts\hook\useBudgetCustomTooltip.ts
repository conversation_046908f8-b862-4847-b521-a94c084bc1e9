import { useState } from 'react';
import { TooltipInfo } from '@models/common/TooltipInfo';
import { numberWithCommas } from "@utils/FormatUtil";
interface Option {
  horizontalOrigin?: 'AUTO' | 'LEFT' | 'CENTER' | 'RIGHT';
  verticalOrigin?: 'TOP' | 'CENTER' | 'BOTTOM';
  reportData: any
}
const useBudgetCustomTooltip = ({ reportData, horizontalOrigin = 'AUTO', verticalOrigin = 'TOP' }: Option) => {
  const [tooltipInfo, setTooltipInfo] = useState<TooltipInfo>({
    visible: false,
    left: '0px',
    top: '0px',
    data: [{ label: '', value: '' }],
  });

  const customTooltip = (context: any) => {
    const { tooltip, chart } = context;

    const position = chart.canvas.getBoundingClientRect();

    if (tooltip.opacity === 0) {
      if (tooltipInfo.visible) {
        setTooltipInfo({ visible: false, left: '0px', top: '0px', data: [{ label: '', value: '' }] });
      }
      return;
    }

    let horizontalCorrection = 0;
    let verticalCorrection = 0;

    switch (horizontalOrigin) {
      case 'CENTER':
        horizontalCorrection = tooltip.width / 4;
        break;
      case 'RIGHT':
        horizontalCorrection = tooltip.width / 2;
        break;
      case 'AUTO':
        const tooltipRight = Number(tooltip.width) + Number(tooltip.caretX) + Number(position.left);
        const positionRight = Number(position.right);
        if (tooltipRight > positionRight) {
          horizontalCorrection = tooltipRight - positionRight;
        }
        break;
    }

    if (verticalOrigin === 'CENTER') {
      verticalCorrection = tooltip.height / 2;
    } else if (verticalOrigin === 'BOTTOM') {
      verticalCorrection = tooltip.height;
    }

    const left = position.left + tooltip.caretX - horizontalCorrection + 'px'; // eslint-disable-line
    const top = position.top + tooltip.caretY - verticalCorrection + 'px'; // eslint-disable-line

    if (tooltipInfo.left === left && tooltipInfo.visible) {
      return;
    }

    const bodyLines = tooltip.body.map((b: any) => b.lines[0])[0].split(':');

    const currentData = reportData.find((report: any) => report.key === bodyLines[0])
    const [mediaType, accountId, adType] = currentData.key.split('-')
    const title = `${adType} ${mediaType}`
    const label = `(${accountId})`
    const ratio = currentData.budgetRatio >= 1 ? '100' : (currentData.budgetRatio * 100).toFixed(2)
    setTooltipInfo({
      visible: true,
      left,
      top,
      data: [{ title: title, label: label, value: numberWithCommas(currentData.budget), ratio: ratio }],
    });
  };

  return { tooltipInfo, customTooltip };
};

export default useBudgetCustomTooltip;
