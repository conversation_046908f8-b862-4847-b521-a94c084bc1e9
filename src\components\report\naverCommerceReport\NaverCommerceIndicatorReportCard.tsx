import React, { useMemo } from 'react'
import '../IndicatorReportCard.scss'
import useNaverCommerceIndicatorFormat from '@components/report/hook/useNaverCommerceIndicatorFormat'
import { useTranslation } from 'react-i18next'
import { ReactComponent as VatIncluded } from '@components/assets/images/vat_included.svg'
import { ReactComponent as VatExcluded } from '@components/assets/images/vat_excluded.svg'
import { NaverCommerceIndicator } from '@utils/naverCommerceReport'
import { Metric } from '@models/report/NaverCommerce'

interface Props {
  indicator: string
  summary?: Metric
  unit?: string
}

const NaverCommerceIndicatorReportCard: React.FC<Props> = ({
  indicator,
  summary = { value: '', compareValue: '', ratio: '' },
  unit
}: Props) => {
  const { indicatorFormat, getCompareRatio, formatCompareValue } = useNaverCommerceIndicatorFormat()
  const { t } = useTranslation()
  const title = useMemo(() => t(`naverReport.indicator.lableWithUnit.${indicator}`), [indicator, t])
  const { value, compareValue, ratio } = summary
  const currIndicatorValue = useMemo(
    () => indicatorFormat(value, indicator as NaverCommerceIndicator),
    [value, indicator, indicatorFormat]
  )
  const compareIndicatorValue = useMemo(
    () => formatCompareValue(compareValue ?? '', indicator as NaverCommerceIndicator),
    [compareValue, indicator, formatCompareValue]
  )
  const compareRatio = useMemo(
    () => getCompareRatio(value, compareValue, ratio),
    [value, compareValue, ratio, getCompareRatio]
  )

  return (
    <div className="indicator-report-card">
      <div className="indicator-report-card__header">
        {title}
        {indicator === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded />}
        {indicator === NaverCommerceIndicator.COST && <VatExcluded />}
      </div>
      <div className="indicator-report-card__content">
        <div className='text-[8px] px-5 py-0.5 bg-[#D7D8E1] rounded-full'>{t('naverReport.last7Days')}</div>
        <div className="indicator__current">
          <span className="indicator__current-value">{currIndicatorValue || '-'}</span>
        </div>
        {compareValue && (
          <div className="indicator__compare">
            <span className="indicator__compare-value">{`vs. ${compareIndicatorValue || '-'}`}</span>{' '}
            {ratio && ratio !== '-' && (
              <span
                className={`indicator__compare-ratio
                ${compareRatio.includes('-') ? 'indicator__compare-ratio--decrease' : ''}
                ${compareRatio.includes('+') ? 'indicator__compare-ratio--increase' : ''}
              `}
              >
                {`(${compareRatio})`}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default NaverCommerceIndicatorReportCard
