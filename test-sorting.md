# NaverCommerceReportTable Sorting Implementation

## Tóm tắt thay đổi

Đã thêm tính năng sorting cho PinnedTable trong NaverCommerceReportTable với các thay đổi sau:

### 1. Cậ<PERSON> nhật NaverCommerceBodyParams interface
- Thêm `orderBy?: string` - tên column để sort
- Thêm `sorting?: 'ASC' | 'DESC'` - hướng sort

### 2. Cập nhật PinnedTable component
- Thêm import `getSortedRowModel`, `SortingState` từ @tanstack/react-table
- Thêm props: `sorting`, `onSortingChange`, `enableSorting`
- Cập nhật useReactTable để hỗ trợ sorting
- Thêm sorting UI (▲/▼ icons) vào header cells

### 3. Cập nhật NaverCommerceReportTable component
- Thêm sorting state: `productSorting`, `dateSorting`
- Cập nhật query keys để include sorting state
- Thêm sorting parameters vào API calls
- Enable sorting cho các indicator columns và date/product columns
- Disable sorting cho division column
- Reset sorting khi chuyển tab

### 4. API Integration
- Sorting parameters được thêm vào query dưới dạng:
  - `orderBy=${columnKey}` 
  - `sorting=ASC/DESC`
- Áp dụng cho cả `getNaverCommerceReportByProduct` và `getNaverCommerceReportByDate`

## Cách sử dụng

1. Click vào header của column để sort
2. Click lần đầu: sort ASC
3. Click lần hai: sort DESC  
4. Click lần ba: remove sort
5. Sorting được reset khi chuyển tab

## Các column có thể sort

### Product Table:
- channelProductName (Product name)
- Tất cả indicator columns (pageView, numPurchases, cost, impressions, etc.)

### Date Table:  
- statDate (Date)
- Tất cả indicator columns

### Không thể sort:
- division column (không có ý nghĩa business)

## Technical Notes

- Sử dụng server-side sorting (không sort ở client)
- Sorting state được quản lý riêng cho từng tab
- Query được invalidate khi sorting thay đổi
- Pagination được reset về page 1 khi sort (cho product table)
