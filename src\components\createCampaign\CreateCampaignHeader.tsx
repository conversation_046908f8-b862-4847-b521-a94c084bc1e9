import React from 'react'
import AddIcon from '@material-ui/icons/Add'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { SearchingTextLimitLength } from '@models/common/CommonConstants'
import './CreateCampaignHeader.scss'
import { MopButton } from '@components/common/buttons'
import { MopSearch } from '@components/common'
import { useAuthority, useToast } from '@hooks/common'
// import AdviceTooltip from '@components/common/AdviceTooltip'
// import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
interface Props {
  searchKeyword: string
  onSearchChange: (value: string) => void
}

const CreateCampaignHeader: React.FC<Props> = ({ searchKeyword, onSearchChange }: Props) => {
  const { t } = useTranslation()
  const { openToast } = useToast()
  const navigate = useNavigate()
  const { isProAdvertiser } = useAuthority()
  const handleSearchChange = (value: string) => {
    if (value.length <= SearchingTextLimitLength) {
      onSearchChange(value)
    }
  }
  const handleCreateCampaign = () => {
   
    navigate('/campaign/create')
  }
  return (
    <div className="createCampaignHeader pb-4  px-8 ">
      {/* <div className="flex items-center space-x-2 mb-3">
        <h1 className="text-2xl font-bold text-gray-900">Create a campaign</h1>
        <AdviceTooltip
          id={`create-campaign-advice-tooltip`}
          title={'Campaign creation is possible only if both Naver Ad and Commerce accounts are fully linked.'}
          placement="bottom-start"
          arrow
        >
          <AdviceMarkIcon />
        </AdviceTooltip>
      </div> */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2" data-testid="createButton">
          <MopButton
            label={t('createCampaign.label.button.create')}
              onClick={handleCreateCampaign}     
            size="md"
            rounded="sm"
            customStyle={{
              fontSize: '14px',
              fontWeight: 'normal',
              height:"30px",
              borderRadius:"0px"
            }}
            disabled={!isProAdvertiser}
            contained={false}
            gtmId="campaign-create-click"
            textColor='#000'
            borderColor='#bfbfbf'           
            rightIcon={<AddIcon />}
          />
        </div>
        <div className="search-input-container flex items-center">
          <MopSearch
            id="outlined-adornment-weight"
            data-testid="campaignNameInput"
            value={searchKeyword}
            placeholder={t('createCampaign.label.placeholder.search')}
            onChange={(e) => handleSearchChange(e.target.value)}
            aria-describedby="outlined-weight-helper-text"
            type='commerce'
          />
        </div>
      </div>
    </div>
  )
}

export default CreateCampaignHeader
