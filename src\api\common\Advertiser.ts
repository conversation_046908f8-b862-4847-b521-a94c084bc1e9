/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import CommonResponse from '@models/common/CommonResponse';
import { Advertiser } from '@models/common/Advertiser';

export const getAdvertisers = async (isLoading = false) => {
  const res = await callApi({
    service: Service.MOP_BE,
    url: '/v1/advertisers',
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return res as CommonResponse<Advertiser[]>;
};

export const createAdvertiserBookmark = async (advertiserId: number, isLoading = false) => {
  const res = await callApi({
    service: Service.MOP_BE,
    url: `/v1/advertisers/${advertiserId}/bookmark`,
    method: Method.POST,
    config: {
      isLoading: isLoading,
    },
  });

  return res as CommonResponse;
};

export const deleteAdvertiserBookmark = async (advertiserId: number, isLoading = false) => {
  const res = await callApi({
    service: Service.MOP_BE,
    url: `/v1/advertisers/${advertiserId}/bookmark`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });

  return res as CommonResponse;
};
