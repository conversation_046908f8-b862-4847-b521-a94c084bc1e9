import React, { useState, useEffect } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { useTranslation } from 'react-i18next';
import {
  getAdEfficiencyAdgroups,
} from '@api/adEfficiency/AdEfficiency';
import {
  AdEfficiencyInfo,
  AdEfficiencyAccount,
  AdEfficiencyCampaign,
  AdEfficiencyAdgroup,
  AdEfficiencyTargetAdgroups,
} from '@models/adEfficiency/AdEfficiency';
import { advertiserState } from '@store/Advertiser';
import { useRecoilValue } from 'recoil';
import TreeView from '@components/common/TreeView';
import { Icon, TextField } from '@material-ui/core';
import { MediaIcon } from '@components/common';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import { SearchingTextLimitLength } from '@models/common/CommonConstants';
import CommonTooltip from '@components/common/CommonTooltip';
import IntegerNumberFormat from '@components/common/IntegerNumberFormat';
import { downloadCSVFile } from '@utils/jsonToCSV';
import { withStyles } from '@material-ui/core/styles';
import InnerHtml from '@components/common/InnerHtml';
import TreeItem from '@material-ui/lab/TreeItem';
import CustomTooltip from '@components/common/CustomTooltip';

import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg';
import { ReactComponent as CountCheckIcon } from '@components/assets/images/icon_count_check.svg';
import { ReactComponent as DownloadIcon } from '@components/assets/images/icon_download.svg';
import AddIcon from '@material-ui/icons/Add';
import RemoveIcon  from '@material-ui/icons/Remove';
import { TreeItemTypes, TreeDataParams } from '@models/adEfficiency/AdEfficiency'
import { mediaActiveStatus } from '@utils/common/MediaType'

import './AdEfficiencyTreeView.scss';
import _ from 'lodash';
import { MediaType } from '@models/common';

interface Props {
  analysis: AdEfficiencyInfo;
  setTargetAdgroups: (value: AdEfficiencyTargetAdgroups[]) => void;
  hasAuthority: boolean;
}

export interface TreeItem {
  mediaType: MediaType
  type: TreeItemTypes
  id: string;
  name: string;
  children?: this[];
  parent?: this;
  data?: AdEfficiencyAccount | AdEfficiencyCampaign | AdEfficiencyAdgroup;
}

const MyTooltip = withStyles({
  tooltip: {
    '& .bar': {
      display: 'inline-block',
      height: 4,
      margin: '0 8px',
      width: 22,
      verticalAlign: 'middle',
      '&.navy': {
        backgroundColor: 'var(--point_color)',
      },
      '&.red': {
        backgroundColor: '#EB414C',
      }
    },
    '& .new': {
      display: 'inline-block',
      position: 'relative',
      height: 16,
      width: 36,
      marginRight: 4,
      verticalAlign: 'text-bottom',
      '&::after': {
        content: '"NEW"',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        height: 16,
        width: 36,
        fontSize: 8,
        fontWeight: 700,
        color: 'white',
        backgroundColor: '#3B5EC9',
        borderRadius: 8,
      },
    },
  },
})(CommonTooltip)

function createNode<T extends TreeItemTypes>(
  type: T, data: TreeDataParams<T>, parent: TreeItem, mediaType: MediaType) {
  return {
    type,
    id: data[`${type}Id` as keyof TreeDataParams<T>],
    name: data[`${type}Name` as keyof TreeDataParams<T>],
    children: [],
    parent,
    data,
    mediaType
  } as unknown as TreeItem
}

const traverse = (parent: TreeItem, _data: any, mediaType: MediaType) => {
    let node: TreeItem|undefined = undefined
    let children: any = undefined
    if (_data.accountId) {
      node = createNode('account', _data as AdEfficiencyAccount, parent, mediaType)
      children = _data.campaigns
    } else if (_data.campaignId) {
      node = createNode('campaign', _data as AdEfficiencyCampaign, parent, mediaType)
      children = _data.adgroups
    } else if (_data.adgroupId) {
      node = createNode('adgroup', _data as AdEfficiencyAdgroup, parent, mediaType)
    }
    if (node && children) {
      children.forEach((item: any) => {
        const child = traverse(node!, item, mediaType)
        child && node!.children!.push(child)
      })
    }
    return node
  }

const AdEfficiencyTreeView: React.FC<Props> = ({ analysis, setTargetAdgroups, hasAuthority }: Props) => {
  const { t } = useTranslation()
  const advertiser = useRecoilValue(advertiserState);
  const [selected, setSelected] = useState<string[]>([])
  const [expanded, setExpanded] = useState<string[]>([])
  const [searchText, setSearchText] = useState<string>('')
  const [treeData, setTreeData] = useState<TreeItem[]>([])

  const root = { id: '', name: '', children: treeData } as TreeItem
  const _flatten = (node: TreeItem): TreeItem[] => [node, ...(node.children?.flatMap(_flatten) || [])]
  const _ancestors = (node: TreeItem, ancestors: TreeItem[] = []): TreeItem[] => (node && node.parent ? _ancestors(node.parent, [...ancestors, node.parent]) : ancestors);
  const nodes = [..._flatten(root)].slice(1)
  const allParents = nodes.filter(node => node.children && node.children.length > 0)
  const targetAdgroups = nodes.filter(node => selected.includes(node.id) && node.type === 'adgroup')
  const targetCampaign = [...new Set(targetAdgroups.map(node => node.parent?.id))]
  const targetAccount = [...new Set(targetAdgroups.map(node => node.parent?.parent?.id))]
  const targetMedia = [...new Set(targetAdgroups.map(node => node.parent?.parent?.parent?.id))]
  const selectedAdgroupIds = analysis.targetList.map(item => item.adgroupId)

  const getSearched = (node: TreeItem) => {
    const matched = node.id.includes(searchText) || node.name.toLowerCase().includes(searchText.toLowerCase())
    const ret: string[] = []
    if (node.children && node.children.length > 0) {
      if (matched) {
        ret.push.apply(ret, _flatten(node).map(_node => _node.id))
      } else {
        const children = node.children.flatMap(getSearched)
        if (children.length > 0) {
          ret.push(node.id)
          ret.push.apply(ret, children)
        }
      }
    } else {
      if (matched) {
        ret.push(node.id)
      }
    }
    return ret
  }
  const searched = getSearched(root)

  const getSelected = (node: TreeItem) => {
    const ret: string[] = []
    if (node.children && node.children.length > 0) {
      const children = node.children.flatMap(getSelected)
      if (node.children.every(node => children.includes(node.id))) {
        ret.push(node.id)
      }
      ret.push.apply(ret, children)
    } else {
      if (selectedAdgroupIds.includes(node.id)) {
        ret.push(node.id)
      }
    }
    return ret
  }

  const getTreeData = async() => {
    const adgroups = await getAdEfficiencyAdgroups(advertiser.advertiserId)
    const uniqueMediaTypes = [...new Set(adgroups.map(item => item.mediaType))]
    const _mediaTreeData = uniqueMediaTypes.map(mediaType => ({
      type: 'media',
      id: mediaType,
      name: mediaType,
      children: [],
      mediaType
    } as TreeItem))
    adgroups.forEach((adgroup) => {
      const matchedMediaTree = _mediaTreeData.find((mediaTreeNode) => mediaTreeNode.id === adgroup.mediaType)
      if (matchedMediaTree) {
        const child = traverse(matchedMediaTree, adgroup, adgroup.mediaType)
        child && matchedMediaTree.children!.push(child)
      }
    })
    unstable_batchedUpdates(() => {
      const root = { id: '', name: '', children: _mediaTreeData } as TreeItem
      const nodes = [..._flatten(root)].slice(1)
      const allParents = nodes.filter(node => node.children && node.children.length > 0)
      setTreeData(_mediaTreeData)
      setExpanded(allParents.map(item => item.id))
      setSelected(getSelected(root))
    })
  }

  const renderTreeItem = (node: TreeItem, depth: number) => {
    return (
      <TreeItem
        key={node.id}
        nodeId={node.id}
        label={
          <CustomTooltip title={node.name} placement="bottom-start">
            <div className="treeItemContent">
              <div className={'labelName'}>
                { node.type === 'media' &&
                  <div className="columns">
                    <MediaIcon mediaType={node.name} size={30} />
                    <div className="mediaName">{node.name}</div>
                  </div>
                }
                { (node.type === 'account' || node.type === 'campaign' || node.type === 'adgroup') &&
                  <div className="rows">
                    <div><b>{node.name}</b></div>
                    <div>{node.id}</div>
                  </div>
                }
                { (node.data as any)?.newlyCreated && <Icon className="new" /> }
              </div>
              <span className="labelStatus">
                <b>{(node.data && 'active' in node.data) ? mediaActiveStatus(node.data.active, node.mediaType) : ''}</b>
              </span>
            </div>
          </CustomTooltip>
        }
        className={`treeItemType-${node.type} ${(searchText && !searched.includes(node.id)) ? 'hidden' : ''}`}
        onLabelClick={(e) => {
          e.preventDefault();
        }}
      >
        {Array.isArray(node.children) ? node.children.map((child) => renderTreeItem(child, depth+1)) : null}
      </TreeItem>
    )
  }

  const onNodeSelect = (nodeId: string) => {
    const node = nodes.find(node => node.id === nodeId)
    if (!node || !hasAuthority) {
      return
    }
    const descendants = _flatten(node).map(node => node.id).slice(1)
    const ancestors = _ancestors(node).map(node => node.id)

    if (selected.includes(nodeId)) {
      const removeTargets = [nodeId].concat(descendants).concat(ancestors)
      setSelected(selected.filter(id => !removeTargets.includes(id)))
    } else {
      const addTargets = [nodeId].concat(descendants)
      let _node = node
      while(_node.parent) {
        if (_node.parent.children?.every(child => child.id === _node.id || selected.includes(child.id))) {
          addTargets.push(_node.parent.id)
        } else {
          break
        }
        _node = _node.parent
      }
      setSelected([...new Set([...selected, ...addTargets])])
    }
  }

  const onNodeToggle = (nodeIds: string[]) => {
    setExpanded(nodeIds)
  }

  const toggleAll = () => {
    if (expanded.length === allParents.length) {
      setExpanded([])
    } else {
      setExpanded(allParents.map(node => node.id))
    }
  }

  const downloadCsv = () => {
    downloadCSVFile(targetAdgroups.map(node => {
      const adgroup = node.data! as AdEfficiencyAdgroup
      const campaign = node.parent!.data! as AdEfficiencyCampaign
      const account = node.parent!.parent!.data! as AdEfficiencyAccount
      return {
        mediaType: account.mediaType,
        accountId: account.accountId,
        accountName: account.accountName,
        campaignId: campaign.campaignId,
        campaignName: campaign.campaignName,
        adgroupId: adgroup.adgroupId,
        adgroupName: adgroup.adgroupName,
        onAndOff: mediaActiveStatus(adgroup.active, account.mediaType),
      }
    }), `${analysis.analysisId}-adgroups`)
  }

  useEffect(() => {
    getTreeData()
  }, []) //eslint-disable-line

  useEffect(() => {
    setTargetAdgroups(targetAdgroups.map(node => {
      const adgroupId = (node.data as AdEfficiencyAdgroup).adgroupId
      const campaignId = (node.parent!.data as AdEfficiencyCampaign).campaignId
      const accountId = (node.parent!.parent!.data as AdEfficiencyAccount).accountId
      const mediaType = node.parent!.parent!.parent!.id
      return {
        adgroupId,
        campaignId,
        accountId,
        mediaType,
      } as AdEfficiencyTargetAdgroups
    }))
  }, [selected])

  return (
    <div id="AdEfficiencyTreeView">
      <div className="flex-container">
        <div className="tree">
          <div className="treeHeader">
            <button className="toggleWrapper" onClick={toggleAll}>
              { expanded.length === allParents.length && <RemoveIcon /> }
              { expanded.length < allParents.length && <AddIcon color="disabled"/> }
            </button>
            <div className="labelName">
              <MyTooltip
                title={
                  <>
                    <h1>{t('optimization.label.budgetOpt.tooltip.targetCampaign.title')}</h1>
                    <p>
                      <div>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.0')}</div>
                      <div>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.1')}</div>
                      <div>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.2')}</div>
                    </p>
                    <p>
                      <div><span className="bar navy"></span><InnerHtml tagName={'span'} innerHTML={t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.3')} /></div>
                      <div><span className="bar red"></span><InnerHtml tagName={'span'} innerHTML={t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.4')} /></div>
                      <div><span className="new"></span>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.5')}</div>
                    </p>
                  </>
                }
                placement="bottom-start"
                className="tooltipContents"
              >
                <span><AdviceMarkIcon />Media / Account / Campaign / Adgroup</span>
              </MyTooltip>
            </div>
            <div className="labelStatus">
              <CommonTooltip
                title={
                  <>
                    <h1>{t('optimization.label.budgetOpt.tooltip.onAndOff.title')}</h1>
                    <p>
                      <div>{t('optimization.label.budgetOpt.tooltip.onAndOff.contents.0')}</div>
                      <div className="indent2">{t('optimization.label.budgetOpt.tooltip.onAndOff.contents.1')}</div>
                      <div className="indent2">{t('optimization.label.budgetOpt.tooltip.onAndOff.contents.2')}</div>
                    </p>
                  </>
                }
                placement="bottom-start"
                className="tooltipContents"
              >
                <span><AdviceMarkIcon />On / Off</span>
              </CommonTooltip>
            </div>
            <button className="download" onClick={downloadCsv}>
              <DownloadIcon />
            </button>
          </div>
          <TreeView
            selected={selected}
            expanded={expanded}
            onNodeSelect={onNodeSelect}
            onNodeToggle={onNodeToggle}
          >
            { treeData.map((node) => renderTreeItem(node, 0)) }
          </TreeView>
        </div>
        <div className="sidebar">
          <OutlinedInput
            className="searchInput"
            onChange={(e) => {
              setSearchText(e.target.value)
            }}
            endAdornment={<SearchIcon className="search-icon" />}
            labelWidth={0}
            inputProps={{
              maxLength: SearchingTextLimitLength,
            }}
            placeholder="Account / Campaign / Adgroup"
          />
          <TextField
            className="counter media"
            value={targetMedia.length}
            InputProps={{
              inputComponent: IntegerNumberFormat,
              startAdornment: <CountCheckIcon />,
              endAdornment: <span className="endAdornment">Media</span>
            }}
            disabled
          />
          <TextField
            className="counter account"
            value={targetAccount.length}
            InputProps={{
              inputComponent: IntegerNumberFormat,
              startAdornment: <div className="empty"></div>,
              endAdornment: <span className="endAdornment">Account</span>
            }}
            disabled
          />
          <TextField
            className="counter campaign"
            value={targetCampaign.length}
            InputProps={{
              inputComponent: IntegerNumberFormat,
              startAdornment: <div className="empty"></div>,
              endAdornment: <span className="endAdornment">Campaigns</span>
            }}
            disabled
          />
          <TextField
            className="counter adgroup"
            value={targetAdgroups.length}
            InputProps={{
              inputComponent: IntegerNumberFormat,
              startAdornment: <div className="empty"></div>,
              endAdornment: <span className="endAdornment">Ad groups</span>
            }}
            disabled
          />
          <div className="info">
            <div>
              <Icon className="select-mark-new" />
              <div><b>MOP 연동 3일 내</b> 캠페인/애드그룹</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdEfficiencyTreeView