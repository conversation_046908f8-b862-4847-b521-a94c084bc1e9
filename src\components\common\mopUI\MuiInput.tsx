// src/components/common/mopUI/MuiInput.tsx
import React, { forwardRef } from 'react'
import { OutlinedInput, OutlinedInputProps } from '@material-ui/core'
import NumberFormat, { NumberFormatProps } from 'react-number-format'
import styled from '@emotion/styled'

// Default style based on MopSearch commerce type (with center-aligned font)
const StyledInput = styled(OutlinedInput)<{ $hasFocus?: boolean; $inputType?: string }>`
  --height: 42px;
  width: 100%;
  height: var(--height);
  background-color: white;
  
  .MuiOutlinedInput-input {
    text-align: center;
    padding: 10px 20px;
    height: calc(var(--height) - 22px); /* Consider border and padding */
    font-family: 'NotoSans';
    font-weight: 400;
    font-size: 14px;
    color: #333;
    
    &::placeholder {
      font-family: 'NotoSans';
      font-weight: 400;
      font-size: 14px;
      color: #707070;
    }
  }

  .MuiOutlinedInput-notchedOutline {
    border: 1px solid #efefef;
    border-radius: 4px;
  }
  
  &:hover .MuiOutlinedInput-notchedOutline {
    border-width: 1px;
    border-color: ${(props) => (props.$hasFocus ? '#333' : '#efefef')};
  }

  &.Mui-focused {
    outline: none;
  }
  &.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-width: 1px !important;
    border-color: ${(props) => (props.$hasFocus ? '#333' : '#efefef')} !important;
  }
  
  &.Mui-disabled {
    background-color: #f6f8f9;
    
    .MuiOutlinedInput-input {
      background-color: #f6f8f9;
    }
  }

  /* default type style (no additional styles as it's the base) */

  /* raw-data type style */
  ${(props) => props.$inputType === 'raw-data' && `
    --height: 27px;
    
    .MuiOutlinedInput-input {
      padding: 4px 8px;
      height: calc(var(--height) - 10px); /* Consider border and padding */
      font-size: 13px;
      
      &::placeholder {
        font-size: 13px;
      }
    }
    
    .MuiOutlinedInput-notchedOutline {
      border: 1px solid #bbbdcd;
      border-radius: calc(var(--height) / 2);
    }
  `}

  /* commerce type style (only right alignment differs) */
  ${(props) => props.$inputType === 'commerce' && `
    .MuiOutlinedInput-input {
      text-align: right;
    }
  `}
`

// NumberFormat related props type definition
interface NumberFormatCustomProps extends Omit<NumberFormatProps, 'onValueChange' | 'value' | 'onChange'> {
  onChange: (event: { target: { name?: string; value: string } }) => void
  name?: string
  value?: string | number
}

// Component that connects NumberFormat with OutlinedInput
const NumberFormatCustom = forwardRef<HTMLInputElement, NumberFormatCustomProps>((props, ref) => {
  const { onChange, ...other } = props

  return (
    <NumberFormat
      {...other}
      getInputRef={ref}
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value,
          },
        })
      }}
    />
  )
})

NumberFormatCustom.displayName = 'NumberFormatCustom'

// MuiInput component props interface
interface MuiInputProps extends Omit<OutlinedInputProps, 'inputComponent'> {
  hasFocus?: boolean
  // Prop to determine whether to use NumberFormat
  useNumberFormat?: boolean
  // NumberFormat related props (only used when useNumberFormat is true)
  numberFormatProps?: Omit<NumberFormatProps, 'onValueChange' | 'value' | 'onChange'>
  // Style type (same approach as MopSearch)
  type?: 'default' | 'raw-data' | 'commerce'
}

/**
 * MuiInput Component
 * 
 * Custom input component based on MUI's OutlinedInput
 * - Supports all OutlinedInput functionality by default
 * - Number formatting can be enabled via useNumberFormat prop
 * - Uses the same style system as MopSearch
 * - Supports various style variations through type prop
 * 
 * @param hasFocus - Styling based on focus state (default: true)
 * @param useNumberFormat - Whether to use react-number-format
 * @param numberFormatProps - Props to pass to NumberFormat component
 * @param type - Style type ('default' | 'raw-data' | 'commerce')
 * @param ...restProps - All other OutlinedInput props
 */
const MuiInput = forwardRef<HTMLInputElement, MuiInputProps>(
  ({ hasFocus = true, useNumberFormat = false, numberFormatProps, type = 'default', ...restProps }, ref) => {
    // When using NumberFormat
    if (useNumberFormat) {
      return (
        <StyledInput
          {...restProps}
          ref={ref}
          $hasFocus={hasFocus}
          $inputType={type}
          inputComponent={NumberFormatCustom as any}
          inputProps={{
            ...restProps.inputProps,
            ...numberFormatProps,
          }}
        />
      )
    }

    // When using regular OutlinedInput
    return <StyledInput {...restProps} ref={ref} $hasFocus={hasFocus} $inputType={type} />
  }
)

MuiInput.displayName = 'MuiInput'

export default MuiInput
export type { MuiInputProps }
