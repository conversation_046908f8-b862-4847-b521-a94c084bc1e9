/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import { LoginRequest } from '@models/common/Session'

export const login = async (loginReqeust: LoginRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/session',
    method: Method.POST,
    params: {
      bodyParams: loginReqeust
    },
    config: {
      isLoading: isLoading
    }
  })
}

export const logout = async (isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/session',
    method: Method.DELETE,
    config: {
      isLoading: isLoading
    }
  })
}
