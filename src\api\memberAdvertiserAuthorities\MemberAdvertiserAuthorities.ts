/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import { MemberAuthorityRequest } from '@models/memberAdvertiserAuthorities/MemberAdvertiserAuthorities';

export const enrollMemberAdvertiserAuthorities = async (
  memberAuthorityRequest: MemberAuthorityRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/admin/authorities`,
    method: Method.POST,
    params: {
      bodyParams: {
        ...memberAuthorityRequest,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const getMembersAdvertiserAuthorities = async (isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/admin/authorities`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
};
