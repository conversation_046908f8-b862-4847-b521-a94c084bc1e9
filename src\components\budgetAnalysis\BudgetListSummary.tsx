import { Badge, MediaIcon, NewBadge, OptimizationIcon } from '@components/common'
import { ReactComponent as CircleArrowRight } from '@components/assets/images/circle_filled_arrow_right.svg'
import { numberWithCommas } from '@utils/FormatUtil'
import { BudgetData } from '@models/budgetAnalysis/Budget'
import { useTranslation } from 'react-i18next'
import { set, parseISO, differenceInDays } from 'date-fns'
import './BudgetListSummary.scss'
import { cn } from '@utils/index'

interface Props {
  data: BudgetData
  handleToggleModal: (_id: number) => void
}
const BudgetListSummary = ({ data, handleToggleModal }: Props) => {
  const { t } = useTranslation()
  const today = set(new Date(), { hours: 0, minutes: 0, seconds: 0, milliseconds: 0 })
  const diffDay = data.bidEndDate ? differenceInDays(parseISO(data.bidEndDate), today) : 9999
  const isBid = data.bidYn === 'Y'
  return (
    <div id="budget-list-summary">
      <div className="budget-analysis-title">
        <Badge size="sm" className={cn(isBid ? 'bg-[#6EC7C2]' : 'bg-[#D7D8E2]')}>
          {isBid ? 'Live' : 'OFF'}
          {isBid && diffDay <= 7 && (
            <span>
              -<b>{diffDay}d</b>
            </span>
          )}
        </Badge>
        <span title={data.optimizationName}>{data.optimizationName}</span>
        {data.newlyCreated && <NewBadge size="sm" />}
      </div>
      <div>
        <MediaIcon mediaType={data.mediaType} />
      </div>
      <div>
        {data.optimizationId}
        <button className="go-opt-button" onClick={() => handleToggleModal(data.optimizationId)}>
          {t('competition.label.button.goOpt')} <CircleArrowRight />
        </button>
      </div>
      <div>
        <span>
          {data.bidStartDate}~
          {data.bidEndDate === '9999.12.31' ? t('common.datePeriodPicker.unsetEndDate') : data.bidEndDate}
        </span>
      </div>
      <div>
        <span className="value">
          {numberWithCommas(data.dailyBudget)} <small>{t('competition.label.detailList.vatExcluded')}</small>
        </span>
      </div>
      <div>
        <OptimizationIcon goalType={data.optimizationGoal} />
        <span>{t(`common.code.optimizationGoal.${data.optimizationGoal}`)}</span>
      </div>
    </div>
  )
}

export default BudgetListSummary
