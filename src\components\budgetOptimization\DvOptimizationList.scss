#dvOptimizationList {
  background-color: #ffffff;
  border-radius: 8px;

  & > .MuiPaper-root {
    box-shadow: none;

    & > .MuiToolbar-root {
      display: none;
    }
  }

  .MuiTableCell-root {
    padding: 0;
    width: auto !important;
  }

  .MuiTableCell-head {
    height: 50px;
    position: relative;
    font-size: 15px !important;
    font-weight: 500 !important;
    color: var(--color-blue-darker) !important;
    border-top: 1px solid #363a6a;
    border-bottom: 1px solid #363a6a;
    background-color: #f1f2f5;
    text-align-last: center;

    .MuiTableSortLabel-root {
      .MuiTableSortLabel-icon {
        display: none;
      }

      &:hover {
        color: var(--color-blue-darker) !important;
      }

      &.MuiTableSortLabel-active {
        color: var(--color-blue-darker);

        .MuiTableSortLabel-icon {
          position: relative;
          display: inline-block;
          width: 12px;
          height: 10px;
          opacity: 1;
          text-indent: -9999px;

          &::after {
            content: '';
            display: inline-block;
            width: 0;
            height: 0;
            position: absolute;
            top: 0;
            right: 0;
            border-style: solid;
            border-width: 10px 6px 0px 6px;
            border-color: var(--color-blue-darker) transparent transparent transparent;
          }
        }
      }
    }
  }

  .MuiTableCell-body {
    height: 50px;
    border-bottom: 1px solid #d1d4dc;
    text-align: center;

    .MuiSwitch-root {
      width: auto;
      height: auto;
      padding: 0;
      margin: 0;
      align-items: center;
      display: flex;

      .MuiSwitch-switchBase {
        left: 2px;
        padding: 0;

        .MuiSwitch-thumb {
          position: relative;
          top: 2px;
          width: 14px;
          height: 14px;
          padding: 0;
          background-color: #ffffff;
          opacity: 1;
          box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
        }

        &.Mui-checked {
          transform: translateX(18px);
        }
      }

      .MuiSwitch-track {
        width: 36px;
        height: 18px;
        padding: 0;
        border-radius: 10px;
        background-color: #707395;
        opacity: 1;
        border: 1px solid #707395;
      }

      .Mui-checked {
        & + .MuiSwitch-track {
          background-color: var(--status-active);
          opacity: 1;
          border: 1px solid var(--status-active);
        }
      }

      .Mui-disabled {
        .MuiSwitch-thumb {
          background-color: #ccc;
          opacity: 1;
        }

        & + .MuiSwitch-track {
          background-color: #fff;
          opacity: 1;
          border: 1px solid #959595;
        }
      }
    }

    .color-on {
      font-weight: 400;
      color: var(--status-active);
    }

    .optimizationName {
      p {
        margin: 0;
      }
    }

    & > div {
      & > span,
      & > p {
        font-size: 13px;
        font-weight: 400;
        color: var(--color-blue-darker);
      }
    }

    .allocationYn {
      display: flex;
      justify-content: center;
    }
  }

  #dva-optimization-list-pagination {
    margin-top: 45px;
  }

  @media screen and (max-width: 1900px) {
    .MuiTableCell-head,
    .MuiTableCell-body {
      height: 50px;
    }
    .fixedLayoutCell-col-ALLOCATION_YN,
    .fixedLayoutCell-col-OPTIMIZATION_ID {
      width: 90px !important;
    }
    .fixedLayoutCell-col-STATUS {
      width: 70px !important;
    }
    .fixedLayoutCell-col-OPTIMIZATION_RESULT {
      width: 100px !important;
    }
    .fixedLayoutCell-col-ALLOCATION_START_DATE,
    .fixedLayoutCell-col-ALLOCATION_END_DATE {
      width: 90px !important;
    }
    .fixedLayoutCell-col-DAILY_BUDGET,
    .fixedLayoutCell-col-OPTIMIZATION_GOAL {
      width: 120px !important;
    }
    .fixedLayoutCell-col-CREATED_DATETIME {
      width: 130px !important;
    }
    .fixedLayoutCell-col-CONTEXT_MENU {
      width: 100px !important;
    }
  }
}
