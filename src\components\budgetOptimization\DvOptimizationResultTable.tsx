import React, { Fragment, ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Table, TableBody, TableCell, TableHead, TableRow, TableContainer } from '@material-ui/core';
import './DvOptimizationResultTable.scss';
import { DvReportMedia, DvReportTableType, GetDvReportResponse } from '@models/budgetOptimization/DvReport';
interface Props {
  target?: DvReportTableType;
  reportData: GetDvReportResponse;
}

const DvOptimizationResultTable: React.FC<Props> = ({ target, reportData }: Props): ReactElement => {
  const { t } = useTranslation();
  const [tableData, setTableData] = useState<any>();

  useEffect(() => {
    if (reportData !== undefined) {
      setTableData(reportData);
    }
  }, [reportData]);

  const renderHeader = (id: string) => {
    return (
      <Fragment key={`listHeader-total-${target?.toLocaleLowerCase()}-${id}`}>
        {tableData?.media && (
          <Fragment>
            <TableRow key={`listHeader-${target?.toLocaleLowerCase()}-${id}-1`} data-testid={`listHeader-${id}`}>
              <TableCell key={`listColHeader-${id}-1-media`} scope="col" rowSpan={2}>
                {t('optimization.label.DvOptimizationResultModal.list.media')}
              </TableCell>
              {target === DvReportTableType.MEDIA_AND_CAMPAIGN && (
                <TableCell key={`listColHeader-${id}-1-campaign`} scope="col" rowSpan={2} width="20%">
                  {t('optimization.label.DvOptimizationResultModal.list.campaign')}
                </TableCell>
              )}
              <TableCell key={`listColHeader-${id}-1-budget`} scope="col" rowSpan={2}>
                {t('optimization.label.DvOptimizationResultModal.list.budget')}
              </TableCell>
              <TableCell key={`listColHeader-${id}-1-budgetRate`} scope="col" rowSpan={2}>
                {t('optimization.label.DvOptimizationResultModal.list.budgetRate')}
              </TableCell>
              <TableCell key={`listColHeader-${id}-1-estimatedPerformance`} scope="col" colSpan={10}>
                {t('optimization.label.DvOptimizationResultModal.list.estimatedPerformance')}
              </TableCell>
            </TableRow>
            <TableRow
              className="header-row-second"
              key={`listHeader-${target?.toLocaleLowerCase()}-${id}-2`}
              data-testid={`listHeader-${id}`}
            >
              {tableData.media.length > 0 &&
                Object.entries(tableData.media[0]).map(([indicator], index) => {
                  const estimatedColumn = [
                    'impression',
                    'view',
                    'click',
                    'conversion',
                    'revenue',
                    'vtr',
                    'ctr',
                    'cpv',
                    'cpc',
                    'cpa',
                  ];
                  const isEstimatedColumn = estimatedColumn.indexOf(indicator) !== -1;
                  return isEstimatedColumn ? (
                    <TableCell
                      key={`listColHeader-${target?.toLocaleLowerCase()}-${id}-2-${index}-${indicator}`}
                      data-testid={`listColHeader-${id}-${indicator}`}
                      scope="col"
                    >
                      {t(`optimization.label.DvOptimizationResultModal.list.${indicator}`)}
                    </TableCell>
                  ) : (
                    <Fragment key={`listColHeader-${target?.toLocaleLowerCase()}-${id}-2-${index}`}></Fragment>
                  );
                })}
            </TableRow>
          </Fragment>
        )}
      </Fragment>
    );
  };

  const renderRow = (row: DvReportMedia, index: number) => {
    return (
      <Fragment key={`list-total-rows-${target?.toLocaleLowerCase()}-${index}`}>
        <TableRow
          key={`listRow-${target?.toLocaleLowerCase()}-${index}`}
          data-testid={`listRow-${target?.toLocaleLowerCase()}-${index}`}
        >
          <TableCell
            key={`listRow-${index}-mediaType`}
            data-testid={`listRow-${index}-mediaType`}
            className="colCenterAlign"
            scope="row"
          >
            {row.mediaType}
          </TableCell>
          {target === DvReportTableType.MEDIA_AND_CAMPAIGN && (
            <TableCell
              key={`listRow-${index}-campaignId`}
              data-testid={`listRow-${index}-campaignId`}
              className="colCenterAlign"
              scope="row"
            >
              {row.campaignName}
            </TableCell>
          )}
          <TableCell
            key={`listRow-${index}-budget`}
            data-testid={`listRow-${index}-budget`}
            className="colCenterAlign"
            scope="row"
          >
            {row.budget.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-budgetRate`}
            data-testid={`listRow-${index}-budgetRate`}
            className="colCenterAlign"
            scope="row"
          >
            {row.budgetRate}
          </TableCell>
          <TableCell
            key={`listRow-${index}-impression`}
            data-testid={`listRow-${index}-impression`}
            className="colCenterAlign"
            scope="row"
          >
            {row.impression.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-view`}
            data-testid={`listRow-${index}-view`}
            className="colCenterAlign"
            scope="row"
          >
            {row.view.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-click`}
            data-testid={`listRow-${index}-click`}
            className="colCenterAlign"
            scope="row"
          >
            {row.click.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-conversion`}
            data-testid={`listRow-${index}-conversion`}
            className="colCenterAlign"
            scope="row"
          >
            {row.conversion.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-revenue`}
            data-testid={`listRow-${index}-revenue`}
            className="colCenterAlign"
            scope="row"
          >
            {row.revenue.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-vtr`}
            data-testid={`listRow-${index}-vtr`}
            className="colCenterAlign"
            scope="row"
          >
            {row.vtr.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-ctr`}
            data-testid={`listRow-${index}-ctr`}
            className="colCenterAlign"
            scope="row"
          >
            {row.ctr.toLocaleString('ko-KR', { maximumFractionDigits: 2 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-cpv`}
            data-testid={`listRow-${index}-cpv`}
            className="colCenterAlign"
            scope="row"
          >
            {row.cpv.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-cpc`}
            data-testid={`listRow-${index}-cpc`}
            className="colCenterAlign"
            scope="row"
          >
            {row.cpc.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
          <TableCell
            key={`listRow-${index}-cpa`}
            data-testid={`listRow-${index}-cpa`}
            className="colCenterAlign"
            scope="row"
          >
            {row.cpa.toLocaleString('ko-KR', { maximumFractionDigits: 4 })}
          </TableCell>
        </TableRow>
      </Fragment>
    );
  };

  return (
    <div
      className="dv-optimization-result-table"
      data-testid={`DvOptimizationResultTable-${target === DvReportTableType.MEDIA ? 'media' : 'campaign'}`}
    >
      {tableData && (
        <TableContainer>
          <Table stickyHeader>
            <TableHead>{renderHeader('report')}</TableHead>
            <TableBody>
              {target === DvReportTableType.MEDIA &&
                tableData.media.map((row: DvReportMedia, index: number) => {
                  return renderRow(row, index);
                })}
              {target === DvReportTableType.MEDIA_AND_CAMPAIGN &&
                tableData.campaign.map((row: DvReportMedia, index: number) => {
                  return renderRow(row, index);
                })}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </div>
  );
};

export default DvOptimizationResultTable;
