import React, { useEffect, useState } from 'react';
import {
  AdEfficiencyInfo,
  AdEfficiencyAnalysisResultColumn,
  adEfficiencyListSortingState,
  AdEfficiencyAnalysisResultDetails,
} from '@models/adEfficiency/AdEfficiency';
import {
  updateAdEfficiencyOff,
} from '@api/adEfficiency/AdEfficiency';
import { AdEfficiencyOffParams } from '@models/adEfficiency/AdEfficiency';
import { downloadCSVFile } from '@utils/jsonToCSV';
import { useTranslation } from 'react-i18next';
import { FixedLayoutTable } from '@components/common/table'
import AdEfficiencyResultTableFormatter from './AdEfficiencyResultTableFormatter'
import EmptyDetection from '@components/anomalyDetection/EmptyDetection';
import { useRecoilState, useRecoilValue, useRecoilTransaction_UNSTABLE } from 'recoil';
import { ReactComponent as DownloadIcon } from '@components/assets/images/icon_download.svg';
import { ReactComponent as EmptyNotice } from '@components/assets/images/icon_notice_outline.svg';
import { adEfficiencyTableData, adEfficiencyChartData } from '@store/AdEfficiency';
import { useToast, useDialog } from "@hooks/common";
import './AdEfficiencyResultTable.scss'

interface Props {
  analysis: AdEfficiencyInfo;
}

const AdEfficiencyResultTableColumns = new AdEfficiencyResultTableFormatter();
const getUpdatedData = (data: AdEfficiencyAnalysisResultDetails[], creativeId: string) => {
  return data.map((item, index) => {
    if(item.creativeId === creativeId) return { ...item, onoff: 'OFF' }
    return item
  })
}

const AdEfficiencyResultTable: React.FC<Props> = ({ analysis }: Props) => {
  const { t } = useTranslation();
  const { openToast } = useToast()
  const { openDialog } = useDialog()
  const tableData = useRecoilValue(adEfficiencyTableData)
  const [cloneData, setCloneData] = useState<AdEfficiencyAnalysisResultDetails[]>([])
  const [listSortingState, setListSortingState] = useRecoilState(adEfficiencyListSortingState);
  const allColumns: AdEfficiencyAnalysisResultColumn[] = AdEfficiencyResultTableColumns.getColumnFormat();
  const [filter, setFilter] = useState<{field: string, order: 'asc' | 'desc'}>({field: 'efficiency', order: 'asc'})
  // const keys: (keyof AdEfficiencyAnalysisResultDetails)[] = ['efficiency', 'cost', 'impressions', 'views', 'clicks', 'conversions', 'revenue', 'vtr', 'ctr', 'cvr', 'cpm', 'cpv', 'cpc', 'cpa', 'roas']

  const formatCsv = (val: string|number, digits=0) => Number(val).toFixed(digits)

  const downloadCsv = () => {
  // TODO: ...Object.fromEntries(keys.map()) 으로 단축하기
    downloadCSVFile(cloneData.map((row) => ({
      status: row.onoff,
      mediaType: row.mediaType,
      campaignId: row.campaignId,
      campaignName: row.campaignName,
      accountId: row.accountId,
      adgroupId: row.adgroupId,
      adgroupName: row.adgroupName,
      creativeId: row.creativeId,
      creativeName: row.creativeName,
      efficiency: formatCsv(row.efficiency, 4),
      cost: formatCsv(row.cost, 2),
      impressions: formatCsv(row.impressions),
      views: formatCsv(row.views),
      clicks: formatCsv(row.clicks),
      conversions: formatCsv(row.conversions),
      revenue: formatCsv(row.revenue),
      'vtr%': formatCsv(Number(row.vtr)*100, 2),
      'ctr%': formatCsv(Number(row.ctr)*100, 2),
      'cvr%': formatCsv(Number(row.cvr)*100, 2),
      cpm: formatCsv(row.cpm, 2),
      cpv: formatCsv(row.cpv, 2),
      cpc: formatCsv(row.cpc, 2),
      cpa: formatCsv(row.cpa, 2),
      'roas%': formatCsv(Number(row.roas)*100, 2)
    }))
    , `ad-efficiency-result-table-${analysis.analysisName}`)
  }

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setListSortingState({
      orderBy: allColumns[orderBy]!.field!,
      sorting: orderDirection,
    });
  };

  const updateSwitch = useRecoilTransaction_UNSTABLE(
    ({ set, get }) => (switchedParams: AdEfficiencyOffParams) => {
      // NOTE: Table data onoff 변경
      const tableData = get(adEfficiencyTableData)
      set(adEfficiencyTableData, getUpdatedData(tableData, switchedParams.creativeId))
      // NOTE: Chart data onoff 변경
      const chartData = get(adEfficiencyChartData)
      set(adEfficiencyChartData, getUpdatedData(chartData, switchedParams.creativeId))
    }
  )
  const handleSwitchOff = async(item: AdEfficiencyAnalysisResultDetails) => {
    const offParam: AdEfficiencyOffParams = {
      accountId: item.accountId,
      adgroupId: item.adgroupId,
      creativeId: item.creativeId,
      isOff: item.onoff === 'ON' ? 'Y' : 'N',
      mediaType: item.mediaType,
    }

    openDialog({
      message: t('adEfficiency.label.ResultTable.message.offConfirm'),
      cancelLabel: t('adEfficiency.label.ResultTable.button.cancel'),
      actionLabel: t('adEfficiency.label.ResultTable.button.off'),
      onAction: async () => {
        const result: any = await updateAdEfficiencyOff(analysis.analysisId, offParam)
        if (result && result.status === 'OK'){
          openToast(t('adEfficiency.label.ResultTable.message.offSuccess'))
          updateSwitch(offParam)
        } else {
          if (result.request.mediaType === 'GOOGLE' && result.description === 'Mutates are not allowed for the requested resource.') {
            openToast('구글 VA는 소재OFF API를 지원하지 않습니다. 매체에서 직접 OFF 해주세요.')
          } else if (result.description) {
            openToast(`소재를 OFF할 수 없습니다. 시스템 운영자에게 문의해주세요.\n${result.request.mediaType}: ${result.description}`)
          } else {
            openToast(t('adEfficiency.label.ResultTable.message.offFail'))
          }
        }
      }
    })
  }

  AdEfficiencyResultTableColumns.handleSwitchOff = handleSwitchOff
  AdEfficiencyResultTableColumns.field = filter.field
  AdEfficiencyResultTableColumns.order = filter.order

  useEffect(() => {
    // NOTE: material-table type error bug
    // https://stackoverflow.com/questions/59648434/material-table-typeerror-cannot-add-property-tabledata-object-is-not-extensibl
    setCloneData(tableData.map(o => ({ ...o })))
  }, [tableData])
  return (
    <div id="AdEfficiencyResultTable">
      <div className="btnArea">
        <button className="right download" onClick={downloadCsv}><DownloadIcon /></button>
      </div>
      <FixedLayoutTable
        id="AdEfficiencyResultTableData"
        columns={allColumns}
        onOrderChange={handleOrderChange}
        data={cloneData}
        localization={{
          body: {
            emptyDataSourceMessage: (
              <div style={{height: '600px'}}>
                <EmptyDetection icon={<EmptyNotice />} content={'no data'}/>
              </div>
            )
          }
        }}
      />
    </div>
  )
}

export default AdEfficiencyResultTable