.detection-utm-report {
  .utm-accordion {
    --accordion-border: #D7D8E2;
    position: relative;
    margin-top: 0;
    margin-bottom: 16px;
    border-top: 2px solid var(--accordion-border);
    box-shadow: none;
    border-radius: 0;
    background-color: #F2F3F6;

    &:first-child, &:last-child {
      border-radius: 0;
    }
    &-summary {
      position: relative;
      z-index: 10;
      padding: 0 16px;
      &.Mui-expanded {
        box-shadow: 0px 4px 4px 0px #00000040;
        .summary.arrow-down svg {
          transform: rotate(180deg);
        }
      }
      .MuiAccordionSummary-content {
        margin: 0;
        padding: 12px 0;
        justify-content: space-between;
      }
      .side {
        &-left, &-right {
          display: flex;
          align-items: center;
          padding: 0;
          p {
            color: var(--point_color);
            margin: 0 8px;
            font-weight: 500;
          }
        }
      }
      .summary {
        &.media[data-title] {
          padding: 0 16px;
          position: relative;
          &:hover:after {
            opacity: 1;
            transition: all 0.1s ease 0.5s;
            visibility: visible;
          }
          &:after {
            content: attr(data-title);
            color: var(--point_color);
            background-color: white;
            position: absolute;
            padding: 4px 8px;
            bottom: -32px;
            font-size: 12px;
            left: 50%;
            transform: translateX(-50%);
            white-space: nowrap;
            opacity: 0;
            border: 1px solid var(--point_color);
            border-radius: 9999px;
            z-index: 99999;
            visibility: hidden;
          }

        }
        &.bold {
          font-weight: 700;
        }
        &.platform, &.name, &.count {
          font-size: 20px;
        }
        &.count {
          min-width: 100px;
          text-align: right;
        }
        &.cases {
          font-size: 12px;
          color: #909090;
        }
        &.arrow-down {
          margin-left: 20px;
        }
      }
    }

    &-details {
      position: relative;
      z-index: 0;
      padding: 0;
      background-color: #F2F3F6;
      border-bottom: 2px solid var(--accordion-border);

      max-height: 700px;
      overflow-y: auto;

      &::-webkit-scrollbar-thumb {
        background-color: var(--point_color);
        border: 3px solid transparent;
      }
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
    }
  }
}