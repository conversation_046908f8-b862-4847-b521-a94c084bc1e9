import * as ApiUtil from '@utils/ApiUtil';
import { getDVaReportTable } from './DVaReport';

describe('getDVaReportTable', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('749 - 대시보드 리포트 조회 응답이 정상인 경우, 리포트 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        value: {
          salesAmount: '145',
          impressions: '402078',
          views: '78271',
          clicks: '3230',
          transactions: '10',
          transactionRevenue: '888',
          vtr: '19.47',
          ctr: '0.8',
          cvr: '0.31',
          cpv: '0.19',
          cpc: '0',
          cpa: '15',
          roas: '612.21',
        },
        dates: [
          {
            date: '2022-11-11',
            division: 'asdfasdf',
            value: {
              salesAmount: '145',
              impressions: '402078',
              views: '78271',
              clicks: '3230',
              transactions: '10',
              transactionRevenue: '888',
              vtr: '19.47',
              ctr: '0.8',
              cvr: '0.31',
              cpv: '0.19',
              cpc: '0',
              cpa: '15',
              roas: '612.21',
            },
          },
          {
            date: '2022-11-10',
            division: 'qwerqwer',
            value: {
              salesAmount: '421',
              impressions: '983609',
              views: '196812',
              clicks: '7742',
              transactions: '11',
              transactionRevenue: '399',
              vtr: '20.01',
              ctr: '0.79',
              cvr: '0.14',
              cpv: '0.21',
              cpc: '0',
              cpa: '38',
              roas: '94.67',
            },
          },
        ],
      },
    };

    const requestParam = {
      startDate: '20220522',
      endDate: '20220523',
    };

    const response = responseMock.data;

    mockCallApi.mockResolvedValue(responseMock);
    const reportResponse = await getDVaReportTable('reportId', requestParam, true);
    expect(reportResponse).toEqual(response);
  });

  it('749 - 대시보드 리포트 조회 응답이 비정상인 경우, null을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const requestParam = {
      startDate: '20220522',
      endDate: '20220523',
      compareStartDate: '20220520',
      compareEndDate: '20220521',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const reportResponse = await getDVaReportTable('reportId', requestParam, true);
    expect(reportResponse).toEqual(null);
  });
});
