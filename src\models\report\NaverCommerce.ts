export enum BidYn {
  Y = 'Y',
  N = 'N'
}
export interface NaverCommerceOptimization {
  optimizationId: number
  optimizationName: string
  bidStartDate: string
  bidEndDate: string
  bidYn: BidYn
}
export interface NaverCommerceProduct {
  channelProductImageUrl: string
  channelProductId: string
  channelProductName: string
  selectOptYn: BidYn
}
export interface NaverCommerceIndicatorSummary extends BaseProductMetrics<Metric> {}

export interface NaverCommerceGraphData {
  statDate: string
  division: string
  pageView: string
  numPurchases: string
  productQuantity: string
  payAmount: string
  refundNumPurchases: string
  refundProductQuantity: string
  refundPayAmount: string
  productCouponDiscountAmount: string
  orderCouponDiscountAmount: string
  salesAmount: string
  cost: string
  impressions: string
  clicks: string
  conversion: string
  directConversion: string
  conversionRevenue: string
  directConversionRevenue: string
  ctr: string
  cvr: string
  cpm: string
  cpc: string
  cpa: string
  roas: string
}

export interface NaverCommerceGraphResponseItem {
  date: string
  value: NaverCommerceGraphData[]
}

export interface NaverCommerceGraphResponse extends Array<NaverCommerceGraphResponseItem> {}
export interface NaverCommerceBodyParams {
  pageSize?: number
  pageIndex?: number
  channelProductIds?: string[]
  optimizationIds?: number[]
}

export interface Metric {
  value: string
  compareValue?: string
  ratio?: string
}

type MetricString = string

interface BaseProductMetrics<T> {
  pageView: T
  numPurchases: T
  productQuantity: T
  payAmount: T
  refundNumPurchases: T
  refundProductQuantity: T
  refundPayAmount: T
  productCouponDiscountAmount: T
  orderCouponDiscountAmount: T
  salesAmount: T
  cost: T
  impressions: T
  clicks: T
  conversion: T
  directConversion: T
  conversionRevenue: T
  directConversionRevenue: T
  ctr: T
  cvr: T
  cpm: T
  cpc: T
  cpa: T
  roas: T
}

export interface ProductOptimization extends BaseProductMetrics<Metric> {
  adId: string
  adgroupName: string
  campaignName: string
  optimizationId: number
}
export enum PRODUCT_STATUS_TYPE {
  SALE = 'SALE',
  WAIT = 'WAIT',
  UNADMISSION = 'UNADMISSION',
  REJECTION = 'REJECTION',
  SUSPENSION = 'SUSPENSION',
  CLOSE = 'CLOSE',
  PROHIBITION = 'PROHIBITION',
  OUTOFSTOCK = 'OUTOFSTOCK',
  DELETE = 'DELETE'
}

export interface ProductWithDetail extends BaseProductMetrics<Metric> {
  channelProductImageUrl: string
  channelProductName: string
  channelProductId: string
  statusType: PRODUCT_STATUS_TYPE
  optCount: number
}

export interface ProductWithDate extends BaseProductMetrics<MetricString> {
  statDate: string
  division: string
}

export interface ProductWithDateTotalRowData extends NaverCommerceIndicatorSummary {
  statDate: string
  division: string
}

export interface ProductWithDetailTotalRowData extends BaseProductMetrics<Metric> {
  channelProductName: string
  division: string
}

export interface StatsByProduct {
  products: ProductWithDetail[]
  totalCount: number
}

export interface StatsByDate {
  dailyData: ProductWithDate[]
}

export interface ProductDetailResponse {
  optimizations: ProductOptimization[]
}
