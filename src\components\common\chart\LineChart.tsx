import React, { useMemo } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Line } from 'react-chartjs-2';
import ChartCustomTooltip, { TooltipOption } from '@components/common/chart/ChartCustomTooltip';
import type { ChartData, ChartOptions } from 'chart.js';
import { TooltipInfo } from '@models/common/TooltipInfo';
import annotationPlugin from 'chartjs-plugin-annotation';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, annotationPlugin);

interface Props {
  chartData: ChartData<'line'>;
  options: any;
  plugins?: any;
  tooltipInfo: TooltipInfo;
  tooltipOption?: TooltipOption;
  className?: string;
  width?: number;
  height?: number;
}

const LineChart = ({ chartData, options, plugins, tooltipInfo, tooltipOption, className, width, height }: Props) => {
  // const toolTipColor = useMemo(() => {
  //   const targetChartData = chartData.datasets.find((item) => item.label === tooltipInfo.data[0].label);
  //   return targetChartData?.backgroundColor || '';
  // }, [chartData, tooltipInfo]);

  return (
    <>
      <Line options={options} data={chartData} plugins={plugins} className={className} width={width} height={height} />
      {tooltipInfo.visible && (
        <ChartCustomTooltip
          left={tooltipInfo.left}
          top={tooltipInfo.top}
          label={tooltipInfo.data[0].label}
          value={tooltipInfo.data[0].value}
          title={tooltipInfo.data[0].xTitle}
          // indexColor={toolTipColor}
          option={tooltipOption}
        />
      )}
    </>
  );
};

export default LineChart;
