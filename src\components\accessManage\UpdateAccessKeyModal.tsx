import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { deleteAccessKey, getAccessKey, updateAccessKey } from '@api/accessManage/AccessManage';
import { Button, Dialog, DialogContent, Grid } from '@material-ui/core';
import { CreateAccessKeyResult, UpdateAccessKeyParams } from '@models/accessManage/AccessManage';
import { MopIcon } from '@components/common';
import { MOPIcon, YNFlag } from '@models/common';
import { StatusCode } from '@models/common/CommonResponse';
import { useToast, useDialog } from '@hooks/common';
import './UpdateAccessKeyModal.scss';

interface Props {
  open: boolean;
  advertiserId: number;
  accessKey: string;
  onClose: () => void;
  callback: () => void;
}

const UpdateAccessKeyModal: React.FC<Props> = ({
  open,
  advertiserId,
  accessKey,
  onClose,
  callback,
}: Props): ReactElement => {
  const { openToast } = useToast();
  const { openDialog } = useDialog();
  const { t } = useTranslation();
  const [isDisabledDelete, setIsDisabledDelete] = useState(true);
  const [accessKeyInfo, setAccessKeyInfo] = useState<CreateAccessKeyResult | null>(null);

  const getAccessKeyInfo = async () => {
    try {
      const response = await getAccessKey(advertiserId, accessKey);

      if (response.statusCode === StatusCode.SUCCESS) {
        setAccessKeyInfo(response.data);

        if (response.data.useYn === YNFlag.Y) {
          setIsDisabledDelete(true);
        } else {
          setIsDisabledDelete(false);
        }
      } else {
        openToast(t('common.message.systemError'));
      }
    } catch (error) {
      openToast(t('common.message.systemError'));
    }
  };

  const handleUpdate = async () => {
    try {
      const updateParam: UpdateAccessKeyParams = {
        accessKey: accessKeyInfo?.accessKey ?? '',
        useYn: accessKeyInfo?.useYn === YNFlag.Y ? YNFlag.N : YNFlag.Y,
      };

      const response = await updateAccessKey(advertiserId, accessKey, updateParam);

      if (response.statusCode === StatusCode.SUCCESS) {
        openToast(t('common.message.saveSuccess'));
        setAccessKeyInfo(response.data);
        callback();

        if (response.data.useYn === YNFlag.Y) {
          setIsDisabledDelete(true);
        } else {
          setIsDisabledDelete(false);
        }
      } else {
        openToast(t('common.message.systemError'));
      }
    } catch (error) {
      openToast(t('common.message.systemError'));
    }
  };

  const handleDelete = async () => {
    openDialog({
      title: t('common.message.title.notice'),
      message: t('accessManage.message.modal.delete.confirm'),
      cancelLabel: t('common.label.button.cancel'),
      actionLabel: t('common.label.button.confirm'),
      onAction: async () => {
        try {
          const response = await deleteAccessKey(advertiserId, accessKey);

          if (response.statusCode === StatusCode.SUCCESS) {
            openToast(t('common.message.deleteSuccess'));
            handleModalClose();
          } else {
            openToast(t('common.message.systemError'));
          }
        } catch (error) {
          openToast(t('common.message.systemError'));
        }
      },
    });
  };

  const handleModalClose = () => {
    callback();
    onClose();
  };

  useEffect(() => {
    if (open) {
      getAccessKeyInfo();
      setIsDisabledDelete(true);
    }
  }, [open]);

  return (
    <Dialog open={open} id="update-access-key-modal" onClose={handleModalClose}>
      <section className="update-access-key-modal__header">
        <span className="update-access-key__title">{t('accessManage.label.modal.update.titleLabel')}</span>
        <MopIcon
          name={MOPIcon.CLOSE}
          size={20}
          customClass="access-key-modal-close"
          bgColor="#f3f3f6"
          onClick={handleModalClose}
        />
      </section>
      {accessKeyInfo && (
        <DialogContent>
          <Grid className="grid-row">
            {t('accessManage.message.modal.update.notice1')}
            <br />
            {t('accessManage.message.modal.update.notice2')}
          </Grid>
          <Grid container className="access-key-modal-grid">
            <Grid className="grid-row grid-row__header">
              <Grid className="grid-row__label">{t('accessManage.label.modal.update.tableHeader.advertiserName')}</Grid>
              <Grid className="grid-row__value">{accessKeyInfo.advertiserName}</Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.update.tableHeader.accessKey')}</Grid>
              <Grid className="grid-row__value">{accessKeyInfo.accessKey}</Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.update.tableHeader.description')}</Grid>
              <Grid className="grid-row__value">{accessKeyInfo.description}</Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.update.tableHeader.useYn')}</Grid>
              <Grid className="grid-row__value">
                {accessKeyInfo.useYn === YNFlag.Y
                  ? t('accessManage.label.modal.update.useYes')
                  : t('accessManage.label.modal.update.useNo')}
              </Grid>
            </Grid>
          </Grid>
          <Grid className="grid-row">
            <Button
              className="modal-delete-button"
              variant="contained"
              onClick={handleDelete}
              disabled={isDisabledDelete}
            >
              {t('accessManage.button.modal.update.delete')}
            </Button>
            {accessKeyInfo && (
              <Button className="modal-update-button" variant="contained" onClick={handleUpdate}>
                {accessKeyInfo.useYn === YNFlag.Y
                  ? t('accessManage.button.modal.update.useNo')
                  : t('accessManage.button.modal.update.useYes')}
              </Button>
            )}
          </Grid>
        </DialogContent>
      )}
    </Dialog>
  );
};

export default UpdateAccessKeyModal;
