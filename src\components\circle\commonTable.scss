.fixedLayout-table-root.list-table {
  .cell-body-box {
    font-weight: 500;
    &.PREPROCESSED_BID_00 {
      color: var(--status-available);
    }
    &.MANUAL_UPLOAD_NEEDED {
      color: var(--status-warning);
      cursor: pointer;
    }
    &.DELETED {
      color: var(--status-delete);
    }
    &.ERROR {
      color: var(--status-error);
    }
  }
  .media-account-cell {
    display: grid;
    grid-template-columns: repeat(2, auto);
    align-items: center;
    justify-content: start;
    column-gap: 8px;
    text-align: start;
    width: 100%;
    padding: 0 8px;
    padding-left: 24px;

    .media-icon-box {
      grid-row: span 2;
    }

    &__name {
      font-weight: 700;
    }
  }

  .circle-cell__campaign {
    display: flex;
    padding-left: 0.5rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
    &-name {
      font-weight: 700;
    }
  }

  .circle-cell__campaign-name {
    text-align: left;
    display: inline-block;
    width: 100%;
    line-break: anywhere;
  }

  .tooltip-icon {
    margin: 0 2px -2px 0;
    display: inline;
    vertical-align: baseline;
  }
}

#common-table-tooltip {
  .MuiTooltip-tooltip {
    min-width: 360px;
  }

  &--medium {
    .MuiTooltip-tooltip {
      min-width: 380px;
    }
  }

  &--large {
    .MuiTooltip-tooltip {
      min-width: 420px;
    }
  }
}
