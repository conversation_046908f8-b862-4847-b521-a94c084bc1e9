import React, { use<PERSON>emo, useCallback } from 'react'
import { Step<PERSON>, Step, Step<PERSON>abel, Toolt<PERSON> } from '@material-ui/core'
import { useTranslation } from 'react-i18next'
import './StatusStepper.scss'
import { CAMPAIGN_CREATION_STATUS, CampaignInfo } from '@models/createCampaign/CreateCampaign'

// Constants
const STATUS_COLORS = {
  [CAMPAIGN_CREATION_STATUS.SUCCESS]: '#34C759',
  [CAMPAIGN_CREATION_STATUS.PARTIAL_FAIL]: '#FF9500',
  [CAMPAIGN_CREATION_STATUS.FAIL]: '#FF3B30',
  [CAMPAIGN_CREATION_STATUS.PENDING]: '#C7C7CC',
  null: '#C7C7CC'
} as const


interface StatusStepperProps {
  campaignInfo: CampaignInfo
}

// Utility functions
const getStatusColor = (status: CAMPAIGN_CREATION_STATUS | null): string => {
  return STATUS_COLORS[status as keyof typeof STATUS_COLORS] // STATUS_COLORS.null
}


const isStepCompleted = (status: CAMPAIGN_CREATION_STATUS | null): boolean => {
  return status === CAMPAIGN_CREATION_STATUS.SUCCESS
}

const isStepError = (status: CAMPAIGN_CREATION_STATUS | null): boolean => {
  return status === CAMPAIGN_CREATION_STATUS.FAIL
}

const isStepPartialFail = (status: CAMPAIGN_CREATION_STATUS |null): boolean => {
  return status === CAMPAIGN_CREATION_STATUS.PARTIAL_FAIL
}

// Memoized CustomStepIcon component
const CustomStepIcon: React.FC<{ status: CAMPAIGN_CREATION_STATUS | null }> = React.memo(({ status }) => {
  const backgroundColor = useMemo(() => getStatusColor(status), [status])
  
  return <div className="custom-step-icon" style={{ backgroundColor }} />
})

CustomStepIcon.displayName = 'CustomStepIcon'

const StatusStepper: React.FC<StatusStepperProps> = ({ campaignInfo }) => {
  const { t } = useTranslation()
  
  const { 
    campaignCreationStatus, 
    adgroupCreationStatus, 
    adsCreationStatus, 
    adgroupCreationSuccessCount = 0, 
    adgroupCreationFailCount = 0,
    adsCreationSuccessCount = 0, 
    adsCreationFailCount = 0,
  } = campaignInfo

  // Memoized steps with i18n
  const steps = useMemo(() => [
    t('createCampaign.statusStepper.campaignCreation'),
    t('createCampaign.statusStepper.adGroupCreation'),
    t('createCampaign.statusStepper.creativeCreation')
  ], [t])

  // Memoized step statuses
  const stepStatuses = useMemo(() => [
    campaignCreationStatus,
    adgroupCreationStatus,
    adsCreationStatus
  ], [campaignCreationStatus, adgroupCreationStatus, adsCreationStatus])

  // Memoized active step calculation
  const activeStep = useMemo(() => {
    const pendingIndex = stepStatuses.findIndex(
      status => status === null // status === CAMPAIGN_CREATION_STATUS.PENDING
    ) 
    return pendingIndex === -1 ? 3 : pendingIndex
  }, [stepStatuses])

  // Memoized CSS variables for connector colors
  const connectorColors = useMemo(() => ({
    '--step-1-color': getStatusColor(campaignCreationStatus),
    '--step-2-color': getStatusColor(adgroupCreationStatus)
  }), [campaignCreationStatus, adgroupCreationStatus])

  // Memoized tooltip content generator
  const getTooltipContent = useCallback((stepIndex: number) => {
    switch (stepIndex) {
      case 0: {
        const statusLabel = t(`createCampaign.statusStepper.status.${campaignCreationStatus?.toLowerCase() || 'pending'}`)
        return (
          <div className='text-center'>
            {t('createCampaign.statusStepper.campaignCreation')} <br/> {statusLabel}
          </div>
        )
      }
      case 1: {
        return (
          <div className='text-center'>
            {t('createCampaign.statusStepper.adGroupCreation')} <br/> 
            {t('createCampaign.statusStepper.success')} {adgroupCreationSuccessCount} / {t('createCampaign.statusStepper.failed')} {adgroupCreationFailCount}
          </div>
        )
      }
      case 2: {
        return (
          <div className='text-center'>
            {t('createCampaign.statusStepper.creativeCreation')} <br/> 
            {t('createCampaign.statusStepper.success')} {adsCreationSuccessCount} / {t('createCampaign.statusStepper.failed')} {adsCreationFailCount}
          </div>
        )
      }
      default:
        return ''
    }
  }, [
    t,
    campaignCreationStatus, 
    adgroupCreationSuccessCount, 
    adgroupCreationFailCount,
    adsCreationSuccessCount, 
    adsCreationFailCount
  ])

  // Memoized step rendering
  const renderStep = useCallback((label: string, index: number) => {
    const status = stepStatuses[index]
    const isCompleted = isStepCompleted(status)
    const hasError = isStepError(status)
    const hasPartialFail = isStepPartialFail(status)

    return (
      <Step
        key={label}
        completed={isCompleted}
        className={hasError ? 'MuiStep-error' : hasPartialFail ? 'MuiStep-partial-fail' : ''}
      >
        <Tooltip 
          title={getTooltipContent(index)} 
          arrow 
          placement="top"
        >
          <StepLabel
            error={hasError}
            className={`step-label ${status ? status.toLowerCase() : 'pending'}`}
            StepIconComponent={() => <CustomStepIcon status={status} />}
          />
        </Tooltip>
      </Step>
    )
  }, [stepStatuses, getTooltipContent])

  return (
    <div
      className="mui-status-stepper"
      style={connectorColors as React.CSSProperties}
    >
      <Stepper activeStep={activeStep} className="custom-stepper">
        {steps.map(renderStep)}
      </Stepper>
    </div>
  )
}

export default React.memo(StatusStepper)
