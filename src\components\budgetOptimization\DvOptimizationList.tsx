import React, { ReactElement } from 'react';
import './DvOptimizationList.scss';
import { pageSizeOptions } from '@models/common/CommonConstants';
import { ContextMenuFunctions, DvOptimizationInfo } from '@models/budgetOptimization/DvOptimization';
import { useTranslation } from 'react-i18next';
import { FixedLayoutTable, TablePagination, FixedLayoutColumn } from '@components/common/table'
import DvOptimizationListFormatter from '@components/budgetOptimization/DvOptimizationListFormatter';
import { useRecoilState, useRecoilValue } from 'recoil';
import { dvoListActiveFilterState, dvoListState } from '@store/DvOptimization'

interface Props {
  contextMenuFunctions?: ContextMenuFunctions;
}

const dvOptimizationListFormatter = new DvOptimizationListFormatter();

const DvOptimizationList: React.FC<Props> = ({ contextMenuFunctions }: Props): ReactElement => {
  const { t } = useTranslation();

  const dvoList = useRecoilValue(dvoListState);
  const [dvoListActiveFilter, setDvoListActiveFilter] = useRecoilState(dvoListActiveFilterState);
  const allColumns: Array<FixedLayoutColumn<DvOptimizationInfo>> = dvOptimizationListFormatter.getColumnFormat(
    contextMenuFunctions,
    dvoListActiveFilter.orderBy,
    dvoListActiveFilter.sorting
  );

  const handleChangePage = (newPage: number) => {
    if (newPage !== dvoListActiveFilter.pageIndex) {
      setDvoListActiveFilter({ ...dvoListActiveFilter, pageIndex: newPage });
    }
  };

  const handleChangeRowsPerPage = (newRowsPerPage: number) => {
    if (newRowsPerPage !== dvoListActiveFilter.pageSize) {
      setDvoListActiveFilter({ ...dvoListActiveFilter, pageSize: newRowsPerPage, pageIndex: 1 });
    }
  };

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setDvoListActiveFilter({
      ...dvoListActiveFilter,
      orderBy: allColumns[orderBy]?.field,
      sorting: orderDirection.toUpperCase(),
      pageIndex: 1,
    });
  };

  return (
    <div id="dvOptimizationList">
      {dvoList.optimizations && allColumns && (
        <>
          <FixedLayoutTable
            data-testid="dvOptimizationTable"
            onOrderChange={handleOrderChange}
            columns={allColumns}
            data={dvoList.optimizations.map((obj) => Object.create(obj)) || []}
            localization={
              dvoList.optimizations.length === 0
                ? { body: { emptyDataSourceMessage: t('common.message.list.noData') } }
                : { body: { emptyDataSourceMessage: '' } }
            }
          />
          <TablePagination
            id="dva-optimization-list-pagination"
            totalCount={dvoList.totalCount || 0}
            page={dvoListActiveFilter.pageIndex || 1}
            rowsPerPage={dvoListActiveFilter.pageSize || pageSizeOptions[0]}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </>
      )}
    </div>
  );
};

export default DvOptimizationList;
