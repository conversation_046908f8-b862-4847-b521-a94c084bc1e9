// src/components/common/mopUI/MopSwitch.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'
import MopSwitch from './MopSwitch'

const meta: Meta<typeof MopSwitch> = {
  title: 'Components/common/MopUI/MopSwitch',
  component: MopSwitch,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
MopSwitch is a custom switch component based on Material-UI Switch.

**Key Features:**
- **Left/Right Label Support**: Display labels on both sides of the switch using leftLabel and rightLabel
- **Custom Styling**: Brand colors and design applied through SCSS
- **Accessibility Support**: Inherits Material-UI accessibility features
- **Event Propagation Prevention**: stopPropagation applied in onChange events
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    checked: {
      description: 'Controls the checked state of the switch.',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    onChange: {
      description: 'Callback function called when the switch state changes.',
      action: 'changed',
      table: {
        type: { summary: '(checked: boolean) => void' },
        defaultValue: { summary: 'undefined' }
      }
    },
    leftLabel: {
      description: 'Label text displayed on the left side of the switch.',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    leftLabelClass: {
      description: 'CSS class name to apply to the left label.',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    rightLabel: {
      description: 'Label text displayed on the right side of the switch.',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    rightLabelClass: {
      description: 'CSS class name to apply to the right label.',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    disabled: {
      description: 'Sets the disabled state of the switch.',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    color: {
      description: 'Color theme of the Material-UI Switch.',
      control: { type: 'select' },
      options: ['default', 'primary', 'secondary'],
      table: {
        type: { summary: '"default" | "primary" | "secondary"' },
        defaultValue: { summary: '"primary"' }
      }
    },
  }
}

export default meta
type Story = StoryObj<typeof meta>

// Basic switch example
export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Basic usage example of the MopSwitch component. Provides simple toggle functionality without labels.'
      }
    }
  },
  render: () => {
    const [checked, setChecked] = useState(false)
    
    return (
      <div className="p-4">
        <MopSwitch
          checked={checked}
          onChange={(isChecked) => setChecked(isChecked)}
        />
        <p className="mt-2 text-sm text-gray-600">
          현재 상태: {checked ? '켜짐' : '꺼짐'}
        </p>
      </div>
    )
  }
}

// Switch with labels
export const WithLabels: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Example of a switch with labels on both sides. Uses leftLabel and rightLabel properties to clearly convey meaning.'
      }
    }
  },
  render: () => {
    const [checked, setChecked] = useState(false)
    
    return (
      <div className="p-4">
        <MopSwitch
          checked={checked}
          onChange={(isChecked) => setChecked(isChecked)}
          leftLabel="끄기"
          rightLabel="켜기"
        />
        <p className="mt-2 text-sm text-gray-600">
          현재 상태: {checked ? '켜짐' : '꺼짐'}
        </p>
      </div>
    )
  }
}

// Custom label styling
export const CustomLabelStyling: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Example applying custom styles to labels. Uses leftLabelClass and rightLabelClass to apply different styles to each label.'
      }
    }
  },
  render: () => {
    const [checked, setChecked] = useState(true)
    
    return (
      <div className="p-4">
        <MopSwitch
          checked={checked}
          onChange={(isChecked) => setChecked(isChecked)}
          leftLabel="비활성"
          leftLabelClass="text-red-500 font-semibold"
          rightLabel="활성"
          rightLabelClass="text-green-500 font-semibold"
        />
        <p className="mt-2 text-sm text-gray-600">
          현재 상태: {checked ? '활성' : '비활성'}
        </p>
      </div>
    )
  }
}

// Disabled switch
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Example of disabled switches. Setting the disabled attribute to true blocks user interaction.'
      }
    }
  },
  render: () => (
    <div className="p-4 space-y-4">
      <div>
        <h4 className="text-sm font-medium mb-2">Disabled Switch (Off State)</h4>
        <MopSwitch
          checked={false}
          disabled
          leftLabel="끄기"
          rightLabel="켜기"
        />
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">Disabled Switch (On State)</h4>
        <MopSwitch
          checked={true}
          disabled
          leftLabel="끄기"
          rightLabel="켜기"
        />
      </div>
    </div>
  )
}


// Interactive demo
export const InteractiveDemo: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive demo where you can change various settings in real-time.'
      }
    }
  },
  render: () => {
    const [checked, setChecked] = useState(false)
    const [disabled, setDisabled] = useState(false)
    const [leftLabel, setLeftLabel] = useState('끄기')
    const [rightLabel, setRightLabel] = useState('켜기')
    
    return (
      <div className="p-4 space-y-6">
        <div className="border border-gray-200 rounded-lg p-4">
          <h4 className="text-sm font-medium mb-4">Switch Preview</h4>
          <div className="flex justify-center">
            <MopSwitch
              checked={checked}
              onChange={(isChecked) => setChecked(isChecked)}
              disabled={disabled}
              leftLabel={leftLabel}
              rightLabel={rightLabel}
            />
          </div>
          <p className="mt-2 text-center text-sm text-gray-600">
            Current State: {checked ? rightLabel || 'On' : leftLabel || 'Off'}
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Left Label</label>
            <input
              type="text"
              value={leftLabel}
              onChange={(e) => setLeftLabel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="Enter left label"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Right Label</label>
            <input
              type="text"
              value={rightLabel}
              onChange={(e) => setRightLabel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="Enter right label"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="disabled-toggle"
              checked={disabled}
              onChange={(e) => setDisabled(e.target.checked)}
            />
            <label htmlFor="disabled-toggle" className="text-sm">
              Disabled
            </label>
          </div>
          
        </div>
      </div>
    )
  }
}

// Real-world use case examples
export const UseCaseExamples: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Examples of various scenarios that can be used in real applications.'
      }
    }
  },
  render: () => {
    const [notifications, setNotifications] = useState(true)
    const [darkMode, setDarkMode] = useState(false)
    const [autoSave, setAutoSave] = useState(true)
    const [publicProfile, setPublicProfile] = useState(false)
    
    return (
      <div className="p-4 space-y-6 max-w-md">
        <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
          <h4 className="text-lg font-semibold">Settings</h4>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Notifications</span>
            <MopSwitch
              checked={notifications}
              onChange={(checked) => setNotifications(checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Dark Mode</span>
            <MopSwitch
              checked={darkMode}
              onChange={(checked) => setDarkMode(checked)}
              leftLabel="Light"
              rightLabel="Dark"
              leftLabelClass="text-sm text-gray-500"
              rightLabelClass="text-sm text-gray-500"
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm">Auto Save</span>
              <p className="text-xs text-gray-500">Automatically save your work</p>
            </div>
            <MopSwitch
              checked={autoSave}
              onChange={(checked) => setAutoSave(checked)}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <span className="text-sm">Public Profile</span>
              <p className="text-xs text-gray-500">Allow other users to view your profile</p>
            </div>
            <MopSwitch
              checked={publicProfile}
              onChange={(checked) => setPublicProfile(checked)}
              leftLabel="Private"
              rightLabel="Public"
              leftLabelClass="text-sm text-red-600"
              rightLabelClass="text-sm text-green-600"
            />
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="text-sm font-medium mb-2">Current Settings Status</h5>
          <ul className="text-xs text-gray-600 space-y-1">
            <li>Notifications: {notifications ? 'Enabled' : 'Disabled'}</li>
            <li>Theme: {darkMode ? 'Dark Mode' : 'Light Mode'}</li>
            <li>Auto Save: {autoSave ? 'On' : 'Off'}</li>
            <li>Profile: {publicProfile ? 'Public' : 'Private'}</li>
          </ul>
        </div>
      </div>
    )
  }
}
