import CommonResponse from '@models/common/CommonResponse';
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import {
  AbnormalPerformance,
  Bidding,
  CollectionItems,
  CollectionPerformance,
  CollectionStatus,
  Planning,
  Prediction,
  RankMaintenance,
  SaShoppingType,
} from '@models/dashboard/DashboardStatus';
import { GetReportSummaryResponse } from '@models/report/SearchReport';

export const getSaShoppingReport = async (advertiserId: number, saShoppingType: SaShoppingType, chkDate: string[], isLoading = false) => {
  const response: CommonResponse<GetReportSummaryResponse> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/report',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        saShoppingType: saShoppingType,
        startDate: chkDate[0],
        endDate: chkDate[1],
        compareStartDate: chkDate[2],
        compareEndDate: chkDate[3],
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingCollectionStatus = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<CollectionStatus> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/collection/status',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingAbnormalPerformance = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<AbnormalPerformance[]> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/abnormal/performances',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingAbnormalLock = async (advertiserId: number, saShoppingType: SaShoppingType, isLoading = false) => {
  const response: CommonResponse<any> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/abnormal/lock',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        saShoppingType: saShoppingType,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingCollectionItem = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<CollectionItems> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/collection/items',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingCollectionPerformance = async (advertiserId: number, lastDays: number, isLoading = false) => {
  const response: CommonResponse<CollectionPerformance> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/collection/performances',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
        lastDays: lastDays,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingProjectionPredictions = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Prediction> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/projection/predictions',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingProjectionPlanning = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Planning> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/projection/planning',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingMaintenance = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<RankMaintenance> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/flight/rank-maintenance',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getSaShoppingFlightBid = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<Bidding> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/dashboard/sa/shopping/flight/bids',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};
