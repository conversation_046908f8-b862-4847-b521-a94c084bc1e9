#createCampaignTable {
  .tooltip-icon {
    margin: 0 2px -2px 0;
    display: inline;
    vertical-align: baseline;
  }

  .text-icon-wrapper {
    display: flex;
    gap: 2px;
    align-items: center;
    margin: 0 4px;
    border-radius: 9999px;
    border: 1px solid #ccc;
    padding: 2px 4px;

    .count-number {
      width: 2.5ch;
    }
  }

  .setting--disable {
    color: #909090;
    text-align: center;
    font-size: 14px;
    font-weight: 700;

    &::before {
      content: '!';
      width: 16px;
      height: 16px;
      display: inline-block;
      margin-right: 4px;
      color: white;
      background-color: #909090;
      border-radius: 100%;
      font-size: 12px;
    }
  }
  .no-sorting-advice {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
  }
  #adviceIcon {
    svg {
      display: flex;
      margin-right: 3px;
      fill: #707070;

      &:hover {
        fill: #66a8e0;
      }
    }
  }
  .cell-body-box {
    height: 50px;
    &.has-sub-label {
      flex-direction: column;
      gap: 2px;
      .mop-chip.chip-wrapper {
        max-width: 100%;
        span {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden !important;
        }
        strong {
          padding-left: 4px;
        }
      }
    }

    &.device-type-cell {
      .device-type {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        .device-icon {
          width: 16px;
          height: 16px;
        }
      }
    }

    &.state-cell {
      .state-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
      }
    }

    &.status-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 0;
    }

    &.creative-review-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 0;
    }
  }

  .MuiTableBody-root {
    border-bottom: 1px solid var(--mop20-table-border);
  }

  .MuiTableCell-root.MuiTableCell-head {
    background-color: var(--bg-table-main);
    border-top: 1px solid var(--border-table-main);
    border-bottom: 1px solid var(--border-table-main);
    color: var(--text-base);
    font-size: 16px;
    height: 50px;
  }

  .MuiTableRow-root {
    .MuiTableCell-root.MuiTableCell-body {
      height: 50px;
      padding: 0px;
      border-bottom: 1px solid #d1d4dc;
      text-align: center;
      font-size: 13px;
      color: var(--text-base) !important;
    }
  }

  .MuiTableCell-body {
    height: 50px;
    border-bottom: 1px solid #d1d4dc;
    text-align: center;
  }
}
#create-campaign-advice-tooltip {
  border: 1px solid var(--point_color);
  background-color: #fff;
  .MuiTooltip-tooltip {
    padding: 0px;
    margin: 0px;
    background-color: transparent;
    > div {
      > div {
        color: var(--point_color);
        font-size: 12px;
        font-weight: 500;
        &:first-child {
          padding: 12px 0px;
          border-bottom: 1px solid #9196a4;
          font-size: 14px;
          font-weight: 700;
          display: flex;
          justify-content: center;
        }
        &:nth-child(2) {
          padding: 15px 20px;
        }
        &:nth-child(3) {
          border-radius: 4px;
          margin-top: 8px;
          padding: 0px 20px 20px 20px;
          .status-legend {
            background-color: #f5f5f5;
            display: flex;
            flex-direction: column;
            padding: 15px;
            gap: 8px;

            .status-item {
              display: flex;
              align-items: center;
              gap: 8px;

              .status-color {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                flex-shrink: 0;

                &.status-grey {
                  background-color: #c7c7cc !important;
                }

                &.status-blue {
                  background-color: #007aff !important;
                }

                &.status-green {
                  background-color: #34c759 !important;
                }

                &.status-yellow {
                  background-color: #ff9500 !important;
                }

                &.status-red {
                  background-color: #ff3b30 !important;
                }
              }

              .status-text {
                font-size: 12px;
                color: var(--text-base);
                line-height: 1.4;
              }
            }
          }
        }
      }
    }
    span.red {
      color: var(--error_color);
    }
    .MuiTooltip-arrow::before {
      border-color: var(--point_color);
    }
  }
}
