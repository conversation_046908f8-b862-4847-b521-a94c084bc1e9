import {
  getNaverCommerceProductOptimizations,
  getNaverCommerceReportByDate,
  getNaverCommerceReportByProduct
} from '@api/report/NaverCommerce'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { ReactComponent as VatExcluded } from '@components/assets/images/vat_excluded.svg'
import { ReactComponent as VatIncluded } from '@components/assets/images/vat_included.svg'
import { TruncatedText } from '@components/common'
import CommonTooltip from '@components/common/CommonTooltip'
import PinnedTable from '@components/common/table/PinnedTable'
import TablePagination from '@components/common/table/TablePagination'
import NaverCommerceGroupIndicatorFilter from '@components/report/naverCommerceReport/NaverCommerceGroupIndicatorFilter'
import useNaverCommerceIndicatorFormat from '@components/report/hook/useNaverCommerceIndicatorFormat'
import { Checkbox, FormControlLabel, Tab, Tabs } from '@material-ui/core'
import { pageSizeOptions, QueryKeys } from '@models/common/CommonConstants'
import { NaverReportAdIndicator, NaverReportProductIndicator } from '@models/common/Indicator'
import { ReportTableType } from '@models/report/Common'
import {
  NaverCommerceBodyParams,
  NaverCommerceIndicatorSummary,
  ProductOptimization,
  ProductWithDate,
  ProductWithDateTotalRowData,
  ProductWithDetail,
  ProductWithDetailTotalRowData
} from '@models/report/NaverCommerce'
import { advertiserState } from '@store/Advertiser'
import { ColumnDef, PaginationState, SortingState } from '@tanstack/react-table'
import { cn } from '@utils/index'
import {
  generateNaverCommerceBodyParams,
  NaverCommerceIndicator,
  SORTING_NAVER_COMMERCE_INDICATORS_MAPPING
} from '@utils/naverCommerceReport'
import { NAVER_COMMERCE_INITIAL_INDICATORS } from '@utils/reports'
import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRecoilValue } from 'recoil'
import { useQuery } from '@tanstack/react-query'
import './NaverCommerceReportTable.scss'

interface NaverCommerceReportTableProps {
  activeDate: any
  appliedProducts: string[]
  appliedOptimizations: string[]
  totalRowData?: NaverCommerceIndicatorSummary
  variance: boolean
  setVariance: React.Dispatch<React.SetStateAction<boolean>>
}

type DateTableData = ProductWithDate | ProductWithDateTotalRowData
type ProductTableData = ProductWithDetail | ProductWithDetailTotalRowData

export const NaverCommerceReportTable: React.FC<NaverCommerceReportTableProps> = ({
  activeDate,
  appliedProducts,
  appliedOptimizations,
  totalRowData,
  variance,
  setVariance
}) => {
  const { t } = useTranslation()
  const [rowPanelIds, setRowPanelIds] = useState<string[]>([])
  const { indicatorFormat } = useNaverCommerceIndicatorFormat()
  const advertiser = useRecoilValue(advertiserState)
  const [tableTab, setTableTab] = useState<ReportTableType>(ReportTableType.PRODUCT)
  const [columnVisibility, setColumnVisibility] = useState<Record<string, boolean>>({})
  const [productOptimizations, setProductOptimizations] = useState<Record<string, ProductOptimization[]>>({})
  const advertisingIndicators = Object.values(NaverReportAdIndicator) as string[]
  const productIndicators = Object.values(NaverReportProductIndicator) as string[]
  const [productPagination, setProductPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: pageSizeOptions[0]
  })
  const [productSorting, setProductSorting] = useState<SortingState>([])
  const [dateSorting, setDateSorting] = useState<SortingState>([])

  const isCompared = activeDate?.compareStartDate && activeDate?.compareEndDate
  const queryParams = {
    startDate: activeDate.startDate,
    endDate: activeDate.endDate,
    ...(activeDate.compareStartDate &&
      activeDate.compareEndDate && {
        compareStartDate: activeDate.compareStartDate,
        compareEndDate: activeDate.compareEndDate
      })
  }
  const baseBodyParams: NaverCommerceBodyParams = generateNaverCommerceBodyParams(appliedProducts, appliedOptimizations)
  // State for table columns
  const storedTableIndicator = localStorage.getItem('mop-naver-commerce-report-table-indicator')?.split(',')
  const [tableColumn, setTableColumn] = useState<string[]>(storedTableIndicator ?? NAVER_COMMERCE_INITIAL_INDICATORS)
  const updateMediaTableColumns = (value: string[]) => {
    localStorage.setItem('mop-naver-commerce-report-table-indicator', value.join(','))
    setTableColumn(value)
  }

  // useQuery for product data
  const { data: productQueryData, isLoading: isProductLoading } = useQuery({
    queryKey: [
      QueryKeys.NAVER_COMMERCE_PRODUCT_TABLE,
      advertiser?.advertiserId,
      queryParams,
      productPagination,
      productSorting,
      appliedProducts,
      tableTab
    ],
    queryFn: async () => {
      if (!advertiser?.advertiserId || !activeDate) return null

      const bodyParams: NaverCommerceBodyParams = {
        ...baseBodyParams,
        pageSize: productPagination.pageSize,
        pageIndex: productPagination.pageIndex + 1
      }

      // Add sorting parameters if sorting is active
      if (productSorting.length > 0) {
        const sort = productSorting[0]
        bodyParams.orderBy = SORTING_NAVER_COMMERCE_INDICATORS_MAPPING[sort.id]
        bodyParams.sorting = sort.desc ? 'DESC' : 'ASC'
      }

      const result = await getNaverCommerceReportByProduct(advertiser.advertiserId, queryParams, bodyParams)
      return result
    },
    enabled: !!advertiser?.advertiserId && !!activeDate && tableTab === ReportTableType.PRODUCT,
    keepPreviousData: true
  })

  // useQuery for date data
  const { data: dateQueryData, isLoading: isDateLoading } = useQuery({
    queryKey: [
      QueryKeys.NAVER_COMMERCE_DATE_TABLE,
      advertiser?.advertiserId,
      queryParams,
      dateSorting,
      appliedProducts,
      tableTab
    ],
    queryFn: async () => {
      if (!advertiser?.advertiserId || !activeDate) return null

      const bodyParams: NaverCommerceBodyParams = { ...baseBodyParams }

      // Add sorting parameters if sorting is active
      if (dateSorting.length > 0) {
        const sort = dateSorting[0]
        bodyParams.orderBy = SORTING_NAVER_COMMERCE_INDICATORS_MAPPING[sort.id]
        bodyParams.sorting = sort.desc ? 'DESC' : 'ASC'
      }

      const result = await getNaverCommerceReportByDate(advertiser.advertiserId, queryParams, bodyParams)
      return result?.dailyData || []
    },
    enabled: !!advertiser?.advertiserId && !!activeDate && tableTab === ReportTableType.DAILY,
    keepPreviousData: true
  })

  // Reset pagination when sorting changes for product table
  useEffect(() => {
    if (productSorting.length > 0) {
      setProductPagination((prev) => ({ ...prev, pageIndex: 0 }))
    }
  }, [productSorting])

  const createHeaderWithTooltip = (indicator: string, isHiddenTooltip: boolean = true) => {
    const baseLabel = t(`indicator.lableWithUnit.${indicator}`)
    const needsVatTooltip = ['salesAmount', 'conversionRevenue', 'revenue', 'cost'].includes(indicator)

    if (!needsVatTooltip) {
      return baseLabel
    }

    const isVatIncluded = ['salesAmount', 'conversionRevenue', 'revenue'].includes(indicator)
    const isVatExcluded = indicator === 'cost'

    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        {isHiddenTooltip && (
          <CommonTooltip
            classes={{
              tooltip: '!min-w-fit'
            }}
            title={<p>VAT {isVatExcluded ? '제외' : '포함'}</p>}
            placement="bottom"
            arrow
          >
            <AdviceMarkIcon style={{ marginRight: '5px' } as React.CSSProperties} />
          </CommonTooltip>
        )}

        <span>{baseLabel}</span>
        {isHiddenTooltip && (
          <>
            {isVatIncluded && <VatIncluded style={{ marginLeft: '5px' } as React.CSSProperties} />}
            {isVatExcluded && <VatExcluded style={{ marginLeft: '5px' } as React.CSSProperties} />}
          </>
        )}
      </div>
    )
  }
  const getListSectionIndicator = (indicators: string[]) => {
    return tableColumn.filter((indicator) => indicators.includes(indicator))
  }

  const productPerformanceList = getListSectionIndicator(productIndicators)
  const adPerformanceList = getListSectionIndicator(advertisingIndicators)

  const isZeroOrEmpty = (val: any): boolean => {
    if (!val) return true
    if (typeof val === 'string') {
      return val.trim() === '' || parseFloat(val) === 0
    }
    if (typeof val === 'number') {
      return val === 0
    }
    return false
  }

  // Helper functions for value validation
  const isValidValue = (val: any): boolean => val && val !== '-' && val !== ''
  const isValidRatio = (ratio: any, compareValue: any): boolean =>
    isValidValue(ratio) && ratio !== '0' && compareValue !== '-'
  const formatRatio = (ratio: string): string => {
    const numRatio = parseFloat(ratio)
    return `${numRatio > 0 ? '+' : ''}${numRatio.toFixed(2)}%`
  }
  const getRatioClassName = (ratio: string): string => {
    return `ratio ${parseFloat(ratio) > 0 ? 'increase' : 'decrease'}`
  }

  const renderValueCell = ({ value, indicator }: { value: any; indicator: string }): React.ReactNode => {
    // Handle case where value is a string (for ProductWithDate)
    if (typeof value === 'string') {
      if (isZeroOrEmpty(value)) {
        return <div style={{ textAlign: 'center' }}>-</div>
      }
      return <div style={{ textAlign: 'center' }}>{indicatorFormat(value, indicator as NaverCommerceIndicator)}</div>
    }

    // Handle case where value is a Metric object (for ProductWithDetail)
    if (!value) {
      return <div style={{ textAlign: 'center' }}>-</div>
    }

    // Rest of the existing logic for Metric objects...
    const hasCompareValue = isValidValue(value.compareValue)
    const hasRatio = isValidRatio(value.ratio, value.compareValue)
    return (
      <div className={cn('text-center flex flex-col items-end', variance ? 'w-[155px]' : '')}>
        <div className="flex items-center gap-2 w-full justify-center">
          <div>
            <div className={hasCompareValue ? 'bold' : ''}>
              {indicatorFormat(value.value, indicator as NaverCommerceIndicator)}
            </div>

            {hasCompareValue && (
              <div style={{ fontSize: '0.9em', color: '#666' }}>
                {indicatorFormat(value.compareValue!, indicator as NaverCommerceIndicator)}
              </div>
            )}
          </div>

          {hasRatio && variance && (
            <div className={getRatioClassName(String(value.ratio))}>{formatRatio(String(value.ratio))}</div>
          )}
        </div>
      </div>
    )
  }
  const createIndicatorColumns = <T,>(filteredColumns: string[], groupName: string): ColumnDef<T, unknown>[] => {
    return filteredColumns
      .map((indicator, index) => ({
        header: createHeaderWithTooltip(indicator),
        accessorKey: indicator,
        size: 120,
        enableSorting: true,
        meta:
          index === 0
            ? ({
                headerColSpan: { span: filteredColumns.length, content: groupName },
                subHeader: createHeaderWithTooltip(indicator)
              } as any)
            : ({
                subHeader: createHeaderWithTooltip(indicator)
              } as any),
        cell: ({ getValue }: any) => {
          const value = getValue()
          return renderValueCell({
            value,
            indicator
          })
        }
      }))
      .filter(Boolean) as ColumnDef<T, unknown>[]
  }

  const createTableColumns = <T,>(firstColumn: ColumnDef<T, unknown>, isDate?: boolean): ColumnDef<T, unknown>[] => {
    const columns: ColumnDef<T, unknown>[] = [firstColumn]

    columns.push({
      header: t('naverReport.tableHeader.division'),
      accessorKey: 'division',
      size: 120,
      enableSorting: false,
      meta: { headerRowSpan: 2 } as any,
      cell: ({ getValue }: any) => {
        const value = getValue()
        if ((value === 'total' || !isDate) && isCompared) {
          return (
            <div>
              <p className="bold">{t('naverReport.period.reportPeriod')}</p>
              <p>{t('naverReport.period.comparePeriod')}</p>
            </div>
          )
        } else if (value === 'COMPARE_DATE') {
          return t('naverReport.period.comparePeriod')
        }

        return t('naverReport.period.reportPeriod')
      }
    })

    // Product Performance indicators
    const productColumns = createIndicatorColumns<T>(productPerformanceList, t('naverReport.group.productPerformance'))
    columns.push(...productColumns)
    // Advertising Performance indicators
    const advertisingColumns = createIndicatorColumns<T>(
      adPerformanceList,
      t('naverReport.group.advertisingPerformance')
    )
    columns.push(...advertisingColumns)
    return columns
  }

  const dateColumns = useMemo((): ColumnDef<DateTableData, unknown>[] => {
    const dateColumn: ColumnDef<DateTableData, unknown> = {
      header: t('naverReport.tableHeader.date'),
      accessorKey: 'statDate',
      size: 120,
      enableSorting: true,
      meta: { headerRowSpan: 2 } as any,
      cell: ({ getValue }: any) => (
        <div style={{ fontWeight: getValue() === t('naverReport.total') ? 'bold' : 'normal' }}>{getValue()}</div>
      )
    }

    return createTableColumns(dateColumn, true)

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableColumn, t, variance, isCompared])
  const productColumns = useMemo((): ColumnDef<ProductTableData, unknown>[] => {
    const productColumn: ColumnDef<ProductTableData, unknown> = {
      header: t('naverReport.tableHeader.product'),
      accessorKey: 'channelProductName',
      size: 600,
      enableSorting: true,
      meta: { headerRowSpan: 2 } as any,
      cell: ({ row, tableApi, getValue }: any) => {
        const data = row.original as ProductWithDetail
        let displayText = data.channelProductName || ''

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {displayText !== 'Total' && !!data.optCount ? (
              <button
                onClick={() => tableApi?.toggleRowPanel?.()}
                aria-label={tableApi?.isRowPanelOpen ? t('naverReport.panel.close') : t('naverReport.panel.open')}
                className="w-7 h-7 rounded-full border border-gray-300 bg-white inline-flex items-center justify-center text-base leading-none cursor-pointer"
              >
                {tableApi?.isRowPanelOpen ? '-' : '+'}
              </button>
            ) : null}
            {getValue() === t('naverReport.total') ? (
              <span style={{ fontWeight: 'bold' }}>{displayText}</span>
            ) : (
              <div className="flex items-center gap-2">
                <div className="w-10 h-10">
                  <img src={data.channelProductImageUrl} className="w-10 h-10 object-cover" alt={displayText} />
                </div>
                <div>
                  <div className="text-sm">
                    <TruncatedText className="bold">{displayText}</TruncatedText>
                  </div>
                  <div className="text-xs">
                    {t(`naverReport.statusType.${data.statusType}`)} | {data.channelProductId}{' '}
                  </div>
                </div>
              </div>
            )}
          </div>
        )
      }
    }

    return createTableColumns(productColumn)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tableColumn, t, variance, isCompared])
  const mergeDateData = useMemo(() => {
    return [
      {
        ...totalRowData,
        statDate: t('naverReport.total'),
        division: 'total'
      } as ProductWithDateTotalRowData,
      ...(dateQueryData || [])
    ]
  }, [dateQueryData, totalRowData, t])
  const mergeProductData = useMemo(() => {
    return [
      {
        ...totalRowData,
        channelProductName: t('naverReport.total'),
        division: 'total'
      } as ProductWithDetailTotalRowData,
      ...(productQueryData?.products || [])
    ]
  }, [productQueryData, totalRowData, t])

  // Reset row panel IDs when product data changes
  useEffect(() => {
    setRowPanelIds([])
  }, [productQueryData])

  const onToggleRowPanel = async (rowId: string, row: any) => {
    const productDetail = row.original as ProductWithDetail
    const channelProductId = productDetail.channelProductId
    const result = await getNaverCommerceProductOptimizations(
      advertiser.advertiserId,
      productDetail.channelProductId,
      queryParams
    )
    setRowPanelIds((prev) => {
      if (prev.includes(rowId)) {
        return prev.filter((id) => id !== rowId)
      } else {
        return [...prev, rowId]
      }
    })
    setProductOptimizations((prev) => ({ ...prev, [channelProductId]: result?.optimizations || [] }))
  }

  return (
    <section className="campaign-report__table">
      <div className="campaign-report__table-tabs">
        <Tabs
          value={tableTab}
          onChange={(_e, newTab) => {
            setTableTab(newTab)
            if (newTab === ReportTableType.PRODUCT) {
              setProductPagination({ pageIndex: 0, pageSize: pageSizeOptions[0] })
              setProductSorting([])
            } else if (newTab === ReportTableType.DAILY) {
              setDateSorting([])
            }
          }}
        >
          <Tab label={t('report.label.tab.byProduct')} value={ReportTableType.PRODUCT} />
          <Tab label={t('report.label.tab.byDate')} value={ReportTableType.DAILY} />
        </Tabs>
      </div>
      <div className="campaign-report__table-content">
        <div className="campaign-report__table-tools">
          <FormControlLabel
            className="table-tools__variance"
            disabled={!isCompared}
            control={
              <Checkbox
                checked={variance && !!activeDate?.compareStartDate && !!activeDate?.compareEndDate}
                onChange={(e) => setVariance(e.target.checked)}
              />
            }
            label={t('report.label.checkbox.variacnce')}
          />
          <NaverCommerceGroupIndicatorFilter
            initialValue={NAVER_COMMERCE_INITIAL_INDICATORS}
            existingValue={tableColumn}
            getMultiChecks={updateMediaTableColumns}
            hasMinimum={true}
          />
          {/* <Button className="table-tools__download" onClick={downloadTable}>
            <DownloadIcon />
          </Button> */}
        </div>
        {tableTab === ReportTableType.DAILY && (
          <PinnedTable<DateTableData>
            columns={dateColumns}
            data={mergeDateData}
            height={500}
            maxWidth="100%"
            pinned={{ left: 2 }}
            columnVisibility={columnVisibility}
            onColumnVisibilityChange={setColumnVisibility}
            sorting={dateSorting}
            onSortingChange={setDateSorting}
            enableSorting={true}
            isLoading={isDateLoading}
          />
        )}
        {tableTab === ReportTableType.PRODUCT && (
          <>
            <PinnedTable<ProductTableData>
              className="mb-2"
              columns={productColumns}
              data={mergeProductData || []}
              height={500}
              maxWidth="100%"
              pinned={{ left: 1 }}
              columnVisibility={columnVisibility}
              onColumnVisibilityChange={setColumnVisibility}
              sorting={productSorting}
              onSortingChange={setProductSorting}
              enableSorting={true}
              isLoading={isProductLoading}
              openRowPanelIds={rowPanelIds}
              onToggleRowPanel={onToggleRowPanel}
              rowPanelRenderers={{
                leftPanel: (row) => {
                  return (
                    <div className="h-full flex flex-col">
                      <table className="panelTable flex-1">
                        <thead className="relative">
                          <tr>
                            <th
                              className="text-xs font-normal whitespace-nowrap bg-[#F9F9FB] "
                              style={{ paddingLeft: '40px' }}
                            >
                              {t('naverReport.panel.optId')}
                            </th>
                            <th className="text-xs font-normal whitespace-nowrap bg-[#F9F9FB] ">
                              {t('naverReport.panel.campaign')}
                            </th>
                            <th className="text-xs font-normal whitespace-nowrap bg-[#F9F9FB] ">
                              {t('naverReport.panel.adgroup')}
                            </th>
                            <th className="text-xs font-normal whitespace-nowrap bg-[#F9F9FB] ">
                              {t('naverReport.panel.creativeId')}
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {productOptimizations[(row as ProductWithDetail).channelProductId]?.map(
                            (optimizationDetail: ProductOptimization, idx: any) => (
                              <tr key={idx}>
                                <td className="whitespace-nowrap !text-center !pl-[40px] bg-[#F9F9FB] h-[40px]">
                                  <div className=" h-[40px] flex items-center justify-center">
                                    <div className="text-[10px] rounded-full border-[1px] border-solid border-[#5E81F4] text-[#5E81F4] px-2 py-1 inline-block">
                                      {optimizationDetail.optimizationId}
                                    </div>
                                  </div>
                                </td>
                                <td className="whitespace-nowrap !text-center bg-[#F9F9FB] h-[40px]">
                                  {optimizationDetail.campaignName}
                                </td>
                                <td className="whitespace-nowrap !text-center bg-[#F9F9FB] h-[40px]">
                                  {optimizationDetail.adgroupName}
                                </td>
                                <td className="whitespace-nowrap !text-center bg-[#F9F9FB] h-[40px]">
                                  {optimizationDetail.adId}
                                </td>
                              </tr>
                            )
                          )}
                        </tbody>
                      </table>
                    </div>
                  )
                },
                rightPanel: (row) => (
                  <div>
                    <table className="panelTable">
                      <colgroup>
                        <col span={productColumns.length - 1} className="w-[120px] min-w-[120px]" />
                      </colgroup>
                      <thead>
                        <tr>
                          <th className="p-2 text-left bg-[#f3f4f6]"></th>
                          {productPerformanceList.map((indicator) => (
                            <th
                              key={indicator}
                              className="p-2 text-center bg-[#f3f4f6] whitespace-nowrap text-xs text-[#f3f4f6] "
                            >
                              {createHeaderWithTooltip(indicator, false)}
                            </th>
                          ))}
                          {adPerformanceList.map((indicator) => (
                            <th
                              key={indicator}
                              className="p-2 text-center bg-[#f3f4f6] whitespace-nowrap text-xs text-[#f3f4f6]"
                            >
                              {createHeaderWithTooltip(indicator, false)}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {productOptimizations[(row as ProductWithDetail).channelProductId]?.map(
                          (productDetail: ProductOptimization, idx: any) => (
                            <tr key={idx}>
                              <td className="bg-[#F9F9FB]">
                                <div className="h-[40px] flex text-left  flex-col justify-center">
                                  <p className={cn({ 'font-bold': isCompared }, 'whitespace-nowrap')}>
                                    {t('naverReport.period.reportPeriod')}
                                  </p>
                                  {isCompared && (
                                    <p className="whitespace-nowrap">{t('naverReport.period.comparePeriod')}</p>
                                  )}
                                </div>
                              </td>
                              {productPerformanceList.map((indicator, index) => {
                                const isOdd = productPerformanceList.length % 2 === 1
                                const isEven = productPerformanceList.length % 2 === 0
                                const middleIndex = Math.floor(productPerformanceList.length / 2)

                                // For odd arrays: middle item has no background color
                                // For even arrays: last item in the left middle has left alignment and no background color
                                const shouldRemoveBackground = isOdd
                                  ? index === middleIndex
                                  : isEven && index === middleIndex - 1
                                const shouldAlignLeft = isEven && index === middleIndex - 1

                                return (
                                  <th
                                    key={indicator}
                                    className={`
                                      ${shouldAlignLeft ? 'text-left' : 'text-center'}
                                      bg-[#F9F9FB]
                                      whitespace-nowrap
                                      p-2
                                      ${shouldRemoveBackground ? '' : 'text-[#F9F9FB]'}
                                    `}
                                  >
                                    <div className={variance ? 'w-[155px]' : ''}>-</div>
                                  </th>
                                )
                              })}
                              {adPerformanceList.map((item) => {
                                const otpProduct = productDetail[item as keyof ProductOptimization]
                                return (
                                  <td className="bg-[#F9F9FB]">
                                    {renderValueCell({
                                      value: otpProduct,
                                      indicator: item
                                    })}
                                  </td>
                                )
                              })}
                            </tr>
                          )
                        )}
                      </tbody>
                    </table>
                  </div>
                )
              }}
            />
            <TablePagination
              totalCount={productQueryData?.totalCount || 0}
              page={productPagination.pageIndex + 1}
              rowsPerPage={productPagination.pageSize}
              onPageChange={(newPage) => {
                setProductPagination((prev) => ({
                  ...prev,
                  pageIndex: newPage - 1
                }))
              }}
              onRowsPerPageChange={(newPageSize) => {
                setProductPagination(() => ({
                  pageIndex: 0,
                  pageSize: newPageSize
                }))
              }}
            />
          </>
        )}
      </div>
    </section>
  )
}
