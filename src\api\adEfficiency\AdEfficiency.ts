/* istanbul ignore file */

import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  CreateAdEfficiencyParams,
  CreateAdEfficiencyResult,
  AdEfficiencyListItem,
  AdEfficiencyInfo,
  UpdateAdEfficiencyParams,
  AdEfficiencyAccount,
  AdEfficiencyTargetAdgroups,
  AdEfficiencyAnalysisParams,
  AdEfficiencyAnalysisResult,
  AdEfficiencyOffParams,
  AdEfficiencyAnalysisOffHistoryItem,
} from '@models/adEfficiency/AdEfficiency';
import { AxiosResponse } from 'axios';

export const getAdEfficiencies = async (advertiserId: number, isLoading = true): Promise<AdEfficiencyListItem[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/efficiency/ad',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as AdEfficiencyListItem[];
};

export const createAdEfficiency = async (params: CreateAdEfficiencyParams, isLoading = true): Promise<CreateAdEfficiencyResult|null> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad`,
    method: Method.POST,
    params: {
      bodyParams: params,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as CreateAdEfficiencyResult;
};

export const getAdEfficiency = async (analysisId: number, isLoading = true): Promise<AdEfficiencyInfo> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as AdEfficiencyInfo;
};

export const updateAdEfficiency = async (analysisId: number, params: UpdateAdEfficiencyParams, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        ...params,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data as {} : null;
};

export const deleteAdEfficiency = async (analysisId: number, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data as {} : null;
};

export const copyAdEfficiency = async (analysisId: number, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}`,
    method: Method.POST,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as AdEfficiencyInfo;
};

export const getAdEfficiencyAdgroups = async (advertiserId: number, isLoading = true): Promise<AdEfficiencyAccount[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${advertiserId}/adgroups`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as AdEfficiencyAccount[];
}

export const updateAdEfficiencyTargetAdgroups = async (analysisId: number, targets: AdEfficiencyTargetAdgroups[], isLoading = true): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}/targets`,
    method: Method.POST,
    params: {
      bodyParams: {
        targets: targets,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as any;
}

export const getAdEfficiencyAnalysisResult = async (analysisId: number, params?: AdEfficiencyAnalysisParams, isLoading = true): Promise<AdEfficiencyAnalysisResult> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}/result`,
    method: Method.GET,
    params: {
      queryParams: {
        ...params,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as AdEfficiencyAnalysisResult;
}

export const downloadAdEfficiencyResultTableCsv = async (
  analysisId: number,
  rangeType: string,
  isLoading = true
) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}/result/raw-data`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
    params: {
      queryParams: {
        rangeType,
      },
    },
  });

  if (response.data) {
    openDownloadLink(response);
    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};

export const updateAdEfficiencyOff = async ( analysisId: number, params?: AdEfficiencyOffParams, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${analysisId}/off`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        ...params,
      },
    },
  });

  return response.successOrNot === 'Y' ? response.data as {} : null;
};

export const getAdEfficiencyOffHistory = async (advertiserId: number, isLoading = true): Promise<AdEfficiencyAnalysisOffHistoryItem[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/efficiency/ad/${advertiserId}/onoff/history`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as AdEfficiencyAnalysisOffHistoryItem[];
}