import React from "react"
import BudgetAnalysisChart from './BudgetAnalysisChart'
import './BudgetListDetails.scss'

import { useTranslation } from 'react-i18next';
import { useAuthority } from '@hooks/common'
import { useNavigate } from 'react-router-dom'
import { MopIcon } from '@components/common/icon'
import { MOPIcon } from '@models/common/Icon'
import { TransText } from '@components/common'

interface Props {
  optimizationId: number
  chartData:{
    data: string,
    date_idx: number,
    prediction_date: string
  }[]
  toggleHidden: (_id:number, _name:string) => void
  hiddenToggle: any
}
const BudgetListDetails = ({ chartData, optimizationId, hiddenToggle, toggleHidden }: Props) => {
  const { t } = useTranslation();
  const { isBasicAdvertiser, advertiser } = useAuthority();
  const navigate = useNavigate();
  const toggleData = hiddenToggle[optimizationId] || {};
  const toggleChartLine = (event: React.MouseEvent<HTMLButtonElement>) => {
    toggleHidden(optimizationId, event.currentTarget.name)
  }
  const reversed = chartData.slice().reverse()
  return (
    <section className="budget-list-details">
      <div className="toggle-wrapper">
        <div className="toggle-title">
          <span>{t(`rankMaintenance.label.RankMonitoringModal.chart.label.view`)}</span>
        </div>
        <button
          className={`toggle impressions ${toggleData.impressions ? 'on' : 'off'}`}
          name="impressions"
          onClick={toggleChartLine}
        >Impressions</button>
        <button
          className={`toggle clicks ${toggleData.clicks ? 'on' : 'off'}`}
          name="clicks"
          onClick={toggleChartLine}
        >Clicks</button>
        <button
          className={`toggle revenue ${toggleData.revenues ? 'on' : 'off'}`}
          name="revenues"
          onClick={toggleChartLine}
        >Revenue</button>
        <button
          className={`toggle conversions ${toggleData.conversions ? 'on' : 'off'}`}
          name="conversions"
          onClick={toggleChartLine}
        >Conversions</button>
        <button
          className={`toggle top-impression-share ${toggleData.top_imps ? 'on' : 'off'}`}
          name="top_imps"
          onClick={toggleChartLine}
        >Top Impression Share</button>
      </div>
      <div className="chart-panel-wrapper">
        {reversed.map(((datum, idx) => (
          <BudgetAnalysisChart key={idx} toggleData={toggleData} datum={datum} />
        )))}
        {isBasicAdvertiser && (
          <div className="flex flex-col gap-y-6 w-full h-full justify-center items-center bg-white/30 backdrop-blur-sm relative min-w-[400px]">
            <MopIcon name={MOPIcon.BLINK} size={80} />
            <TransText i18nKey="common.message.upgradeBasicToPaidPlan" className="font-bold text-lg text-center px-4" />
            <TransText
              as="a"
              href="#"
              onClick={()=>{
                const path = `/setting/subscription/${advertiser.advertiserId}`
                  navigate(path, {
                    state: {
                      advertiserId: advertiser.advertiserId,
                      advertiserName: advertiser.advertiserName
                    }
                  })}
              }
              className="bg-black text-white py-2 px-5 rounded-full font-bold hover:bg-gray-800 transition-colors"
              i18nKey="common.message.upgrade"
            />
          </div>
        )}
      </div>
    </section>
  )
}
export default BudgetListDetails
