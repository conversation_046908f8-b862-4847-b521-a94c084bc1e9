.commerce-account-modal {
  --button-color: var(--mop20-active-blue);
  .MuiPaper-root.MuiDialog-paper {
    border-radius: 0px;
    width: 770px;
  }

  &__title {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 30px 0;

    &-head {
      font-weight: 900;
      font-size: 24px;
    }

    &-sub {
      font-size: 14px;
    }
  }

  &__content {
    background-color: var(--bg-gray-light);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 60px;
    gap: 16px;
  }

  &__table-action {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__table-wrapper {
    width: 100%;
    border: 1px solid var(--gray-dark);
    border-top: 2px solid var(--point_color);
    background-color: white;

    .fixedLayout-table-root {
      &.list-table {
        margin: 0;

        .MuiTableCell-head {
          background-color: white !important;
          font-weight: 700 !important;
        }

        .MuiTableRow-root {
          &:hover {
            background-color: var(--bg-gray-light) !important;
          }
        }
      }
    }
  }

  .delete-icon {
    display: flex;
    justify-content: center;

    svg {
      cursor: pointer;
      display: inline;
    }
  }
}
