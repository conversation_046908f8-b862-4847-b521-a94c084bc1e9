/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import {
  SignInParams,
  SignInResponse,
  SignUpParams,
  SignUpResponse,
  ResendEmailParams,
  ResetPasswordParams
} from '@models/member/Session'

export const signIn = async (loginReqeust: SignInParams, isLoading = true) => {
  return callApi<SignInResponse>({
    service: Service.MOP_BE,
    url: '/v1/members/session/signin',
    method: Method.POST,
    params: {
      bodyParams: loginReqeust
    },
    config: { isLoading }
  })
}

export const signOut = async (isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/session/signout',
    method: Method.DELETE,
    config: { isLoading }
  })
}

export const signUp = async (bodyParams: SignUpParams, isLoading = true) => {
  return callApi<SignUpResponse>({
    service: Service.MOP_BE,
    url: '/v1/members/session/signup',
    method: Method.POST,
    params: {
      bodyParams
    },
    config: { isLoading }
  })
}

export const migrateMember = async (bodyParams: SignUpParams, token: string, isLoading = true) => {
  return callApi<SignUpResponse>({
    service: Service.MOP_BE,
    url: '/v1/members/session/migrate',
    method: Method.POST,
    params: {
      queryParams: { token },
      bodyParams
    },
    config: { isLoading }
  })
}

export const checkVerifyToken = (token: string, emailType: string, isLoading = true) => {
  return callApi<SignInResponse>({
    service: Service.MOP_BE,
    url: '/v1/members/session/check-token',
    method: Method.PUT,
    params: {
      queryParams: { token, emailType }
    },
    config: { isLoading }
  })
}

export const resendEmail = async (bodyParams: ResendEmailParams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/session/email',
    method: Method.POST,
    params: {
      bodyParams
    },
    config: { isLoading }
  })
}

export const checkMemberVerify = async (emailType: string, memberInfoToken: string) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/session/verify',
    method: Method.PUT,
    params: {
      queryParams: { emailType, memberInfoToken }
    },
    config: {
      isLoading: true
    }
  })
}

export const resetPassword = async (bodyParams: ResetPasswordParams, memberInfoToken: string) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/session/reset-pw',
    method: Method.PUT,
    params: {
      bodyParams,
      queryParams: { memberInfoToken }
    },
    config: {
      isLoading: true
    }
  })
}

// temp
export const changeMemberStatus = async (bodyParams: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/members/session/temp/change/status',
    method: Method.PUT,
    params: {
      bodyParams: {
        memberId: 5,
        memberStateCode: bodyParams
      }
    },
    config: { isLoading }
  })
}
