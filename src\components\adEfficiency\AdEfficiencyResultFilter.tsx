import React, { ReactElement, useEffect, useState } from 'react';
import { MenuItem, type MenuProps } from '@material-ui/core';
import _ from 'lodash';
import { t } from 'i18next';
import SelectBottom from '@components/common/SelectBottom';
import { getOptions } from '@utils/CodeUtil';
import { useRecoilValue, useSetRecoilState } from 'recoil'
import { adEfficiencyMutableAllData, adEfficiencyFilteredDataBySelect } from '@store/AdEfficiency'
import { AdEfficiencyAnalysisParams, AdEfficiencyInfo, AdEfficiencyTab,
  AdEfficiencyFiltered, AdEfficiencyAnalysisResultDetails } from '@models/adEfficiency/AdEfficiency';
import { ExpandLess } from '@material-ui/icons';
import { ReactComponent as SettingIcon } from '@components/assets/images/icon_setting.svg';
import { ReactComponent as SwitchOn } from '@components/assets/images/switch_on.svg';
import { ReactComponent as SwitchOff } from '@components/assets/images/switch_off.svg';
import UndefinedChip from '@components/common/UndefinedChip';
import AdEfficiencySettingModal from './AdEfficiencySettingModal';
import { groupAnalysisByKeys, groupAnalysisKey } from './AdEfficiencyChart/utils'
import './AdEfficiencyResultFilter.scss';
import { MediaType } from '@models/common';
import { ChartIndicator } from '@models/adEfficiency/AdEfficiency';
import { YNFlag } from '@models/common/YNFlag'

interface Props {
  range: AdEfficiencyTab;
  analysis: AdEfficiencyInfo;
  getResult: (_setting: AdEfficiencyAnalysisParams) => void
  updateMutableAllData: () => void
  settingValues: {indicator: ChartIndicator, analysisValue: number, isGreater: YNFlag}
  setSettingValues: ({indicator, analysisValue, isGreater}: {indicator: ChartIndicator, analysisValue: number, isGreater: YNFlag}) => void;
}

interface FilterSelectProps<T> {
  value: T;
  setValue: React.Dispatch<React.SetStateAction<T>>;
  options: AdEfficiencyOption[]
  setFilter: (_key: groupAnalysisKey, _selected: string) => void
}

type AdEfficiencyOption = {
  value: string
  name: string
  adgroupName: string
  campaignName: string
}

type FilterChangeEvent = React.ChangeEvent<{
  name?: string | undefined;
  value: any;
}>

const FilterOptionsProps: Partial<MenuProps> = {
  className: 'filter-options-popover',
  anchorOrigin: { vertical: 26, horizontal: 'left' }
}

const getFilter = (data: AdEfficiencyAnalysisResultDetails[], key: groupAnalysisKey): {
  keys: string[]
  value: AdEfficiencyFiltered
  options: AdEfficiencyOption[]
} => {
  const group = data && groupAnalysisByKeys(data, key)
  const keys = group && Object.keys(group)
  return {
    keys,
    value: group,
    options: keys.map(item => ({
      value: item,
      adgroupName: group[item][0].adgroupName,
      campaignName: group[item][0].campaignName,
      name: item
    }))
  }
}

const renderDropdownIcon = (props: any) => {
  const isOpened = props.className.includes('MuiSelect-iconOpen') ? 'search-input-dropdown-open' : ''
  return (
    <div className={`search-input-dropdown ${isOpened}`}>
      <ExpandLess />
    </div>
  );
};

const StatusSelect = ({ value, setValue, options, setFilter }: FilterSelectProps<string>) => {
  const handleChange = (e: FilterChangeEvent) => {
    setValue(e.target.value)
    setFilter('onoff', e.target.value)
  }
  return (
    <SelectBottom
      displayEmpty
      value={value}
      onChange={handleChange}
      MenuProps={FilterOptionsProps}
      IconComponent={(props) => renderDropdownIcon(props)}
    >
      <MenuItem value={'ALL'}>{t('common.label.filter.all')}</MenuItem>
      {options.map((item) => (
        <MenuItem key={item.value} value={item.value}>
          {item.value==='ON' && <SwitchOn style={{ width: '30px', height: 'auto' }} />}
          {item.value==='OFF' && <SwitchOff style={{ width: '30px', height: 'auto' }} />}
          {item.value==='null' && <UndefinedChip width={30} />}
          {/* {item.name} */}
        </MenuItem>
      ))}
    </SelectBottom>
  )
}

const MediaSelect = ({value, setValue, options, setFilter}: FilterSelectProps<string>) => {
  const handleChange = (e: FilterChangeEvent) => {
    setValue(e.target.value)
    setFilter('mediaType', e.target.value)
  }
  return (
    <SelectBottom
      displayEmpty
      value={value}
      onChange={handleChange}
      MenuProps={FilterOptionsProps}
      IconComponent={(props) => renderDropdownIcon(props)}
    >
      {options.map((item) => (
        <MenuItem key={item.value} value={item.value}>{item.name}</MenuItem> ))}
    </SelectBottom>
  )
}

const AdGroupSelect = ({value, setValue, options, setFilter}: FilterSelectProps<string>) => {
  const handleChange = (e: FilterChangeEvent) => {
    setValue(e.target.value)
    setFilter('adgroupId', e.target.value)
  }
  return (
    <SelectBottom
      value={value}
      onChange={handleChange}
      displayEmpty
      IconComponent={(props) => renderDropdownIcon(props)}
      MenuProps={FilterOptionsProps}
    >
      {options.map((item, i) => {
        return (
          <MenuItem
            className="filter-item column"
            key={`${item.value}-${i}`}
            value={item.value}
          >
            <span className="item-id">{item.value}</span>
            <span className="item-name break-all">{item.adgroupName}</span>
          </MenuItem>
        );
      })}
    </SelectBottom>
  )
}

const CampaignSelect = ({value, setValue, options, setFilter}: FilterSelectProps<string>) => {
  const handleChange = (e: FilterChangeEvent) => {
    setValue(e.target.value)
    setFilter('campaignId', e.target.value)
  }

  return (
    <SelectBottom
      value={value}
      onChange={handleChange}
      displayEmpty
      IconComponent={(props) => renderDropdownIcon(props)}
      MenuProps={FilterOptionsProps}
    >
      {options.map((item, i) => {
        return (
          <MenuItem
            className="filter-item column"
            key={`${item.value}-${i}`}
            value={item.value}
          >
            <span className="item-id">{item.value}</span>
            <span className="item-name break-all">{item.campaignName}</span>
          </MenuItem>
        );
      })}
    </SelectBottom>
  )
}

const AdEfficiencyResultFilter: React.FC<Props> = ({ getResult, range, updateMutableAllData, settingValues, setSettingValues }: Props): ReactElement => {
  const [status, setStatus] = useState('ALL')
  const [media, setMedia] = useState('')
  const [campaign, setCampaign] = useState('')
  const [adgroup, setAdgroup] = useState('')
  const [show, setShow] = useState(false)
  const mutableAllData = useRecoilValue(adEfficiencyMutableAllData)
  const [mediaOptions, setMediaOptions] = useState<AdEfficiencyOption[]>([])
  const [campaignOptions, setCampaignOptions] = useState<AdEfficiencyOption[]>([])
  const [adgroupOptions, setAdgroupOptions] = useState<AdEfficiencyOption[]>([])
  const setAdEfficiencyFilter = useSetRecoilState(adEfficiencyFilteredDataBySelect)

  const handleFilter = async (key: groupAnalysisKey, selected: string) => {
    let campaigns: AdEfficiencyFiltered = {};
    let adgroups: AdEfficiencyFiltered = {};
    let adgroup: AdEfficiencyFiltered = {};
    if (key === 'onoff') {
      let detailsByStatus = mutableAllData;
      if (selected === 'ALL') {
        setStatus(selected)
      } else {
        const { selectedDetails } = filterOptions(mutableAllData, 'onoff', selected)
        detailsByStatus = selectedDetails
      }
      const { selectedDetails: detailsByMedia } = filterOptions(detailsByStatus, 'mediaType')
      const { allValues: campaignValues, selectedDetails: detailsByCampaign } = filterOptions(detailsByMedia, 'campaignId')
      const { allValues: adgroupValues, selectedValue, selectedDetails } = filterOptions(detailsByCampaign, 'adgroupId')
      campaigns = campaignValues
      adgroups = adgroupValues;
      adgroup = { [selectedValue]: selectedDetails }
    } else if (key === 'mediaType') {
      const { selectedDetails: detailsByMedia } = filterOptions(mutableAllData, 'mediaType', selected)
      const { allValues: campaignValues, selectedDetails: detailsByCampaign } = filterOptions(detailsByMedia, 'campaignId')
      const { allValues: adgroupValues, selectedValue, selectedDetails } = filterOptions(detailsByCampaign, 'adgroupId')
      campaigns = campaignValues;
      adgroups = adgroupValues;
      adgroup = { [selectedValue]: selectedDetails }
    } else if (key === 'campaignId') {
      const { allValues: campaignValues, selectedDetails: detailsByCampaign } = filterOptions(mutableAllData, 'campaignId', selected)
      const { allValues: adgroupValues, selectedValue, selectedDetails } = filterOptions(detailsByCampaign, 'adgroupId')
      campaigns = campaignValues;
      adgroups = adgroupValues;
      adgroup = { [selectedValue]: selectedDetails }
    } else {
      const { allValues: adgroupValues, selectedValue, selectedDetails } = filterOptions(mutableAllData, 'adgroupId', selected)
      adgroups = adgroupValues;
      adgroup = { [selectedValue]: selectedDetails }
    }
    switch (range) {
      case AdEfficiencyTab.MEDIA: {
        return setAdEfficiencyFilter(campaigns)
      }
      case AdEfficiencyTab.CAMPAIGN: {
        return setAdEfficiencyFilter(adgroups)
      }
      default: {
        return setAdEfficiencyFilter(adgroup)
      }
    }
  }

  const filterOptions = (details: AdEfficiencyAnalysisResultDetails[], key: groupAnalysisKey, selected?: string) => {
    const { value: allValues, options } = getFilter(details, key)
    let selectedOption = selected?.trim() ? options.find((opt) => opt.value === selected) : options[0]
    if (!selectedOption) {
      // NOTE: 필터로 선택한 값이 없을 경우, default 값 반환
      return { selectedDetails: [], selectedValue: "", allValues }
    }
    const { value: selectedValue } = selectedOption
    const selectedDetails = allValues[selectedValue]
    switch (key) {
      case 'onoff':
        setStatus(selectedValue)
        break;
      case 'mediaType':
        setMediaOptions(options)
        setMedia(selectedValue)
        break;
      case 'campaignId':
        setCampaignOptions(options)
        setCampaign(selectedValue)
        break;
      default:
        setAdgroupOptions(options)
        setAdgroup(selectedValue)
        break;
    }
    return { selectedDetails, selectedValue, allValues }
  }

  useEffect(() => {
    let currentAllData = mutableAllData
    if (currentAllData.length === 0) {
      return
    }
    if (status !== 'ALL') {
      const { selectedDetails } = filterOptions(mutableAllData, 'onoff', status)
      currentAllData = selectedDetails
    } else { setStatus(status) }
    const { selectedDetails: detailsByMedia } = filterOptions(currentAllData, 'mediaType', media)
    const { allValues: campaignValues, selectedDetails: detailsByCampaign } = filterOptions(detailsByMedia, 'campaignId', campaign)
    const { allValues: adgroupValues, selectedValue, selectedDetails } = filterOptions(detailsByCampaign, 'adgroupId', adgroup)
    switch (range) {
      case AdEfficiencyTab.MEDIA: {
        return setAdEfficiencyFilter(campaignValues)
      }
      case AdEfficiencyTab.CAMPAIGN: {
        return setAdEfficiencyFilter(adgroupValues)
      }
      default: {
        return setAdEfficiencyFilter({ [selectedValue]: selectedDetails })
      }
    }
  }, [mutableAllData, range])

  return (
    <>
      <section className={`adefficiency-result-filter ${range}`}>
        <div className="adefficiency-result-filter__title">
          <span>STATUS</span>
          <span>MEDIA</span>
          <span className="adefficiency-result-filter__title-campaign">CAMPAIGN</span>
          <span className="adefficiency-result-filter__title-adgroup">AD GROUP</span>
          <span className='setting'>SETTING</span>
        </div>
        <div className="adefficiency-result-filter__select" onClick={() => updateMutableAllData()}>
          <StatusSelect
            value={status}
            setValue={setStatus}
            options={getOptions(['ON', 'OFF', 'null'], "onoff") as AdEfficiencyOption[]}
            setFilter={handleFilter}
          />
          <MediaSelect
            value={media}
            setValue={setMedia}
            options={mediaOptions}
            setFilter={handleFilter}
          />
          <div className="adefficiency-result-filter__select-campaign">
            <CampaignSelect
              value={campaign}
              setValue={setCampaign}
              options={campaignOptions}
              setFilter={handleFilter}
            />
          </div>
          <div className="adefficiency-result-filter__select-adgroup">
            <AdGroupSelect
              value={adgroup}
              setValue={setAdgroup}
              options={adgroupOptions}
              setFilter={handleFilter}
            />
          </div>
          <div className="MuiInputBase-root MuiInput-root">
            <div role="button" className="setting-button" onClick={() => setShow(!show)}>
              <SettingIcon />
            </div>
          </div>
        </div>
      </section>
      <AdEfficiencySettingModal
        open={show}
        onClose={() => setShow(false)}
        save={getResult}
        settingValues={settingValues}
        setSettingValues={setSettingValues}
      />
    </>
  )
}

export default AdEfficiencyResultFilter