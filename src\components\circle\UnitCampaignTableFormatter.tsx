import { CircleCampaignData } from '@models/circle'
import { Badge } from '@components/common'
import { TableTitle, FixedLayoutColumn } from '@components/common/table'
import { useTranslation } from 'react-i18next'
import CommonTooltip from '@components/common/CommonTooltip'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import './CircleListTableFormatter.scss'
import './commonTable.scss'
import { mediaActiveStatus } from '@utils/common/MediaType'

type TableRow = FixedLayoutColumn<CircleCampaignData>

export default class TableFormatter {
  constructor() {
    this.field = ''
    this.order = 'asc'
  }
  field: string
  order: 'asc' | 'desc'
  getColumnFormat = (): Array<TableRow> => {
    const { t } = useTranslation()

    const columnAdType = (): TableRow => {
      return {
        title: (
          // FIXME: 중복됨 Platform tooltip
          <TableTitle titleStr={t('circle.createUnit.columnHeader.adType')}>
            <CommonTooltip
              id={`common-table-tooltip`}
              title={
                <>
                  <h1>{t('circle.tooltip.createUnit.adType.title')}</h1>
                  <div className="common-style">
                    <p>{t('circle.tooltip.createUnit.adType.content.0')}</p>
                    <dl className="w20_w80">
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type1"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail1')}</dd>
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type2"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail2')}</dd>
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type3"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail3')}</dd>
                    </dl>
                  </div>
                </>
              }
              placement="right-start"
              arrow
            >
              <AdviceMarkIcon className="tooltip-icon" />
            </CommonTooltip>
          </TableTitle>
        ),
        field: 'platformType',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'platformType' ? this.order : undefined,
        cellStyle: { width: '15%' },
        render: ({ platformType }) => {
          return (
            <div className="cell-body-box">
              {platformType && (
                <Badge
                  size="sm"
                  className="text-active-blue border-active-blue"
                  outlined
                  i18nKey={`common.code.platform.${platformType}`}
                />
              )}
            </div>
          )
        }
      }
    }

    const columnCampaignName = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.createUnit.columnHeader.campaign')} />,
        field: 'campaignName',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'campaignName' ? this.order : undefined,
        cellStyle: { width: '25%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box circle-cell__campaign">
              <span className="circle-cell__campaign-name">{campaign.campaignName}</span>
            </div>
          )
        }
      }
    }

    const columnCampaignId = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.createUnit.columnHeader.campaignId')} />,
        field: 'campaignId',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'campaignId' ? this.order : undefined,
        cellStyle: { width: '25%' },
        render: (campaign) => {
          return <div className="cell-body-box">{campaign.campaignId}</div>
        }
      }
    }

    const columnStatus = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr="ON/OFF"
            children={
              <CommonTooltip
                id={`common-table-tooltip`}
                title={
                  <>
                    <h1>{t('circle.tooltip.createUnit.onOff.title')}</h1>
                    <div className="common-style">
                      <p>{t('circle.tooltip.createUnit.onOff.content.0')}</p>
                      <p className="indent2">{t('circle.tooltip.createUnit.onOff.content.1')}</p>
                      <p className="indent2">{t('circle.tooltip.createUnit.onOff.content.2')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'active',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'active' ? this.order : undefined,
        cellStyle: { width: '15%' },
        render: (campaign) => {
          return <div className="cell-body-box">{mediaActiveStatus(campaign.active, campaign.mediaType)}</div>
        }
      }
    }

    return [columnAdType(), columnCampaignName(), columnCampaignId(), columnStatus()]
  }
}
