import { BudgetData } from '@models/budgetAnalysis/Budget'
import { Accordion, AccordionSummary, AccordionDetails } from '@material-ui/core/'
import BudgetListSummary from './BudgetListSummary'
import './BudgetOptList.scss'
import BudgetListDetails from './BudgetListDetails'

interface Props {
  budgetData: BudgetData
  handleToggleModal: (_id: any) => void
  hiddenToggle: any
  toggleHidden: (_id: number, _name: string) => void
}
const BudgetOptList = ({ budgetData, handleToggleModal, hiddenToggle, toggleHidden }: Props) => {
  return (
    <Accordion id="budget-listview-accordion">
      <AccordionSummary className="budget-accordion-summary">
        <BudgetListSummary data={budgetData} handleToggleModal={handleToggleModal} />
      </AccordionSummary>
      <AccordionDetails className="budget-accordion-details">
        <BudgetListDetails
          optimizationId={budgetData.optimizationId}
          chartData={budgetData.data}
          toggleHidden={toggleHidden}
          hiddenToggle={hiddenToggle}
        />
      </AccordionDetails>
    </Accordion>
  )
}

export default BudgetOptList
