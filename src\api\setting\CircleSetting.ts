/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import { Circle, CircleUnit, CreateCirclePrarams, CreateCircleResponse, EditCirclePrarams } from '@models/circle'
import CommonResponse from '@models/common/CommonResponse';


export const createCircle = async (params: CreateCirclePrarams, isLoading = true): Promise<CommonResponse<CreateCircleResponse>> => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/circle',
    method: Method.POST,
    params: {
      bodyParams: params,
    },
    config: {
      isLoading: isLoading,
    }
  });
};

export const editCircle = async (advertiserId: number, params: EditCirclePrarams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/${advertiserId}`,
    method: Method.PATCH,
    params: {
      bodyParams: params,
    },
    config: {
      isLoading: isLoading,
    }
  });
};

export const getCircleList = async (isLoading = true) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: '/v1/circle/list',
    method: Method.GET,
    config: {
      isLoading: isLoading,
    }
  });

  return (response.successOrNot === 'Y' ? response.data : null) as Circle[];
};

export const getCircleUnitList = async (advertiserId: number, isLoading = true) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    }
  });

  return (response.successOrNot === 'Y' ? response.data : null) as CircleUnit[];
};


export default {
  createCircle,
  editCircle,
  getCircleList,
  getCircleUnitList
}