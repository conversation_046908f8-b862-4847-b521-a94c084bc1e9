import './UtmAbnormalLandingUrlModal.scss'
import { Dialog, IconButton, Table, TableHead, TableBody, TableRow, TableCell } from '@material-ui/core'
import { useSetRecoilState, useRecoilValue } from 'recoil'
import {
  openAbnormalLandingUrlModal,
  abnormalLandingUrlDetail,
  selectedUtmAnomalySummaryDetails,
  selectedUtmAnomalySummary
} from '@store/AnomalyDetection'
import CloseIcon from '@components/assets/images/icon_close.png'
import DetectionHeader from './DetectionHeader'
import UtmDetectionTableFilter from './UtmDetectionTableFilter'
import InnerHtml from '@components/common/InnerHtml'
import { ReactComponent as IconMore } from '@components/assets/images/icon_more.svg'
import { getUtmAnomalyDetectionRules } from '@api/dashboard/AbnomalyDetection'
import { UTM_TYPE_DEFAULT_VALUE } from '@utils/settings/utmRule'

import { useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import AdviceTooltip from '@components/common/AdviceTooltip'
import { UtmRule, UtmRuleStatus } from '@models/anomalyDetection'
import { getUtmDetectionColumns, getUtmRuleColumns, getUtmByUrl } from '@utils/settings/utmRule'
import UtmRuleStatusIcons from './UtmRuleStatusIcons'
import { AnalyticsType } from '@models/common'
import { useDialog, useToast } from '@hooks/common'
import { needSettingValues } from '@utils/settings/utmRule'
import { UtmConstants } from '@models/setting/UtmRule'
import { UtmRuleTooltip, UtmSettingTooltip } from '@components/utmRules/tooltip'

const highlightedAndDecoded = (url: string, analytics: AnalyticsType) => {
  const matchUtmParams: [string, string][] = Object.entries(getUtmByUrl(url, analytics).matchUtmParams)
  return matchUtmParams.reduce((resultURL, [currKey, _currValue]) => {
    return resultURL.replace(
      `${currKey}=`,
      (matchedStr) => `<span class="highlight ${currKey}">${matchedStr.slice(0, -1)}</span>=`
    )
  }, decodeURIComponent(url))
}

const getUtmRuleType = (utm: any) => {
  const isARule = /\{KEYWORD-ID|AD-ID|AD-KEYWORD-ID\}/
  const isBRule = /\{KEYWORD-NAME|AD-NAME|AD-KEYWORD-NAME\}/
  for (const value of Object.values(utm)) {
    if (isARule.test(value as string)) return 'A'
    if (isBRule.test(value as string)) return 'B'
  }
}

const UtmLabels = ({ rule, labelKey, analytics }: { rule: any; labelKey: string; analytics: AnalyticsType }) => {
  // FIXME
  const { t, i18n } = useTranslation()
  const regex = /{[^{}]*\*\*}|{[^{}]*}|./g
  const symbolPattern = /^[^{}]*$/
  const matchedLabels = rule[labelKey].match(regex) as UtmConstants[]
  if (!matchedLabels) return <></>
  return (
    <>
      {matchedLabels?.map((label, i) => {
        if (symbolPattern.test(label)) return <p key={i}>{label}</p>
        const itemLabel = i18n.exists(`utmRules.${label}`)
          ? t(`utmRules.${label}`)
          : t(`utmRules.${analytics}.${label}`)
        if (needSettingValues.includes(label)) {
          const chipStyle = label.match(/\{(.*?)\-TYPE\}/)?.[1] || ''
          return (
            <div className="utm-label-box">
              <AdviceTooltip
                id="utm-type-settings-tooltip"
                placement="top"
                title={
                  <div className="utm-type-settings">
                    <p className="subTitle">{t(`anomalyDetection.utmRules.settings.${label}-sub`)}</p>
                    <p className="title">{t(`anomalyDetection.utmRules.settings.${label}`)}</p>
                    <div className="content">
                      {Object.keys(rule.terms[label]).map((fieldKey) => {
                        return (
                          <div className="settings-list">
                            <p className={`chip type-title ${chipStyle}`}>{fieldKey}</p>
                            <div className="type-value-box">
                              {Array.isArray(rule.terms[label][fieldKey]) ? (
                                rule.terms[label][fieldKey].map((value: string) => (
                                  <p key={value} className="chip type-value">
                                    {value}
                                  </p>
                                ))
                              ) : (
                                <p className="chip type-value">{rule.terms[label][fieldKey]}</p>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                }
              >
                <span className="icon">
                  <IconMore className="utm-type-icon" />
                </span>
              </AdviceTooltip>
              <p key={i} className="utm-label">
                {itemLabel}
              </p>
            </div>
          )
        }
        return (
          <p key={i} className="utm-label">
            {itemLabel}
          </p>
        )
      })}
    </>
  )
}

interface Props {
  open: boolean
  advertiserId: number
}

const UtmAbnormalLandingUrlModal = ({ open, advertiserId }: Props) => {
  const { t } = useTranslation()
  const toggleModal = useSetRecoilState(openAbnormalLandingUrlModal)
  const currentItem = useRecoilValue(abnormalLandingUrlDetail)
  const currentCampaign = useRecoilValue(selectedUtmAnomalySummaryDetails)
  const currentSummary = useRecoilValue(selectedUtmAnomalySummary)
  const [currentUtmRule, setCurrentUtmRule] = useState<UtmRule>()
  const [isLoaded, setIsLoaded] = useState(false)
  const { openToast } = useToast()
  const { openDialog } = useDialog()
  const analyticsType = useMemo(() => currentSummary.analyticsType, [currentSummary])

  const handleCopyUrls = async () => {
    const copyText = currentItem.landingUrls.join('\n')
    try {
      await navigator.clipboard.writeText(copyText)
      openDialog({
        title: t('common.modal.title.notice'),
        message: t('anomalyDetection.message.toast.copySuccess'),
        actionLabel: t('common.label.button.ok'),
        onAction: () => {}
      })
    } catch (error) {
      openToast(t('anomalyDetection.message.toast.copyFail'))
    }
  }

  const handleClose = () => toggleModal(false)

  useEffect(() => {
    getUtmAnomalyDetectionRules({
      advertiserId,
      campaignId: currentCampaign.campaignId,
      mediaType: currentItem.mediaType
    }).then((res) => {
      const rule = { terms: UTM_TYPE_DEFAULT_VALUE, ...res } as UtmRule
      setCurrentUtmRule(rule)
      setIsLoaded(true)
    })
  }, []) //eslint-disable-line

  return (
    <>
      <Dialog id="UtmAbnormalLandingUrlModal" open={open} onClose={handleClose}>
        <IconButton className="modal-close" aria-label="close" onClick={handleClose}>
          <img alt="close-image" src={CloseIcon} />
        </IconButton>
        <section className="utm-modal header">
          <DetectionHeader headTitle="Abnormal Landing URL" headSize="24px" subTitle="이상감지 URL" subSize="14px" />
          <UtmDetectionTableFilter />
        </section>
        <section className="utm-modal content">
          <section>
            <DetectionHeader
              headTitle="Landing URL"
              tooltipId="landing-url"
              tooltipHtml={t('anomalyDetection.tooltip.landingUrl')}
            />
            <Table className="landing-urls">
              <TableHead>
                <TableRow>
                  <TableCell>{t('anomalyDetection.columnHeader.url.landingUrl')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentItem?.landingUrls.map((url: string) => (
                  <TableRow>
                    <TableCell>
                      <InnerHtml innerHTML={highlightedAndDecoded(url, analyticsType)} />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            <button className="copy-button" onClick={handleCopyUrls}>
              {t('anomalyDetection.label.button.download')}
            </button>
          </section>
          <section>
            {isLoaded && (
              <DetectionHeader
                headTitle={t(`anomalyDetection.utmRules.header.${analyticsType}`)}
                analytics={analyticsType}
                tooltipId="utm"
                gaViewId={currentUtmRule?.gaViewId}
                tooltipHtml={t(`anomalyDetection.tooltip.utmRules.${analyticsType}`)}
              />
            )}
            <Table className="ga-utm-rules-table">
              <TableHead>
                <TableCell className="rule">
                  <p>
                    <UtmRuleTooltip analytics={analyticsType} />
                    {t('anomalyDetection.columnHeader.utm.rule.title')}{' '}
                    {t('anomalyDetection.columnHeader.utm.rule.columnHeader.rule')}
                  </p>
                </TableCell>
                <TableCell className="settings">
                  <div className="utm-rule head">
                    <div className="utm-rule title">
                      <UtmSettingTooltip analytics={analyticsType} showDirect={false} />
                      {t('anomalyDetection.columnHeader.utm.rule.title')}{' '}
                      {t('anomalyDetection.columnHeader.utm.rule.columnHeader.settingValue')}
                    </div>
                    <div className="utm-rule items">
                      {getUtmDetectionColumns(analyticsType).map((column) => (
                        <p>{t(`anomalyDetection.columnHeader.utm.rule.${analyticsType}.${column}`)}</p>
                      ))}
                    </div>
                  </div>
                </TableCell>
              </TableHead>
              <TableBody>
                <TableRow className="rule-result">
                  <TableCell className="rule" />
                  <TableCell className="settings">
                    {getUtmDetectionColumns(analyticsType).map((column) => (
                      <div className="settings-box">
                        <UtmRuleStatusIcons status={currentItem.statusCode[column as keyof UtmRuleStatus]} />
                      </div>
                    ))}
                  </TableCell>
                </TableRow>
                <TableRow>
                  {isLoaded && (
                    <>
                      <TableCell className="rule">
                        {t(`anomalyDetection.utmRules.ruleType.${getUtmRuleType(currentUtmRule)}`)}
                      </TableCell>
                      <TableCell className="settings">
                        {getUtmRuleColumns(analyticsType).map((ruleColumnKey) => (
                          <div className="settings-box">
                            <UtmLabels rule={currentUtmRule} labelKey={ruleColumnKey} analytics={analyticsType} />
                          </div>
                        ))}
                      </TableCell>
                    </>
                  )}
                </TableRow>
              </TableBody>
            </Table>
          </section>
        </section>
      </Dialog>
    </>
  )
}

export default UtmAbnormalLandingUrlModal
