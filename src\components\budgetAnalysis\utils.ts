
export const options = {
  maintainAspectRatio: false,
  responsive: false,
  interaction: {
    mode: 'index' as const,
    intersect: false,
  },
  plugins: {
    legend: {
      display: false,
    },
    annotation: {
      drawTime: 'afterDatasetsDraw',
      annotations: [{
        type: 'line',
        mode: 'vertical',
        scaleID: 'x',
        value: '100',
        borderColor: '#EB424C',
        borderWidth: 1,
        borderDash: [3, 3],
      }, {
        type: 'line',
        borderColor: '#ccc',
        borderDash: [5, 5],
        borderWidth: 1,
        xScaleID: 'x',
        yScaleID: 'y',
        xMin: 0,
        xMax: 150,
        yMin: 0,
        yMax: 150,
      }],
    },
  },
  scales: {
    x: {
      display: true,
      type: 'linear',
      position: 'bottom',
      min: 0,
      suggestedMax: 150,
      ticks: {
        beginAtZero: true,
        stepSize: 25,
        labelOffset: 4,
        padding: 3,
        color: 'rgba(43, 49, 82, 1)',
        maxRotation: 0,
        minRotation: 0,
      },
      grid: {
        display: true,
      },
      title: {
        display: true,
        text: 'Budget %',
        align: 'end',
        color: '#040a45',
        font: {
          size: 10,
          weight: 400,
        },
        padding: 3,
      },
    },
    y: {
      display: true,
      min: 0,
      suggestedMax: 150,
      ticks: {
        beginAtZero: true,
        stepSize: 25,
        labelOffset: 4,
        padding: 3,
        color: 'rgba(43, 49, 82, 1)',
      },
      grid: {
        display: true,
      },
    },
  },
};

export const getChartData = (data: any, toggleData: any) => {
  const base = data.find((item: any) => item.budget === 1);
  const datasets = [{
    label: 'Impressions',
    backgroundColor: '#00359C',
    borderColor: '#00359C',
    pointStyle: 'line',
    hidden: !toggleData.impressions,
    data: data.map((item: any) => ({x: Math.round(item.budget * 100), y: item.value.impressions / base.value.impressions * 100})),
  }, {
    label: 'Clicks',
    backgroundColor: '#FF5C00',
    borderColor: '#FF5C00',
    pointStyle: 'line',
    hidden: !toggleData.clicks,
    data: data.map((item: any) => ({x: Math.round(item.budget * 100), y: item.value.clicks / base.value.clicks * 100})),
  }, {
    label: 'Revenue',
    backgroundColor: '#774CF2',
    borderColor: '#774CF2',
    pointStyle: 'line',
    hidden: !toggleData.revenues,
    data: data.map((item: any) => ({x: Math.round(item.budget * 100), y: item.value.revenues / base.value.revenues * 100})),
  }, {
    label: 'Conversions',
    backgroundColor: '#FFD600',
    borderColor: '#FFD600',
    pointStyle: 'line',
    hidden: !toggleData.conversions,
    data: data.map((item: any) => ({x: Math.round(item.budget * 100), y: item.value.conversions / base.value.conversions * 100})),
  }, {
    label: 'Top Impression Share',
    backgroundColor: '#ADDA2F',
    borderColor: '#ADDA2F',
    pointStyle: 'line',
    hidden: !toggleData.top_imps,
    data: data.map((item: any) => ({x: Math.round(item.budget * 100), y: item.value.top_imp_ratio * 100})),
  }];
  return { datasets };
};