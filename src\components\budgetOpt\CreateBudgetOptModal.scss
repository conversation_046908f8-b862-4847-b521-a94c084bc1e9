#create-budget-opt-modal {
  .MuiPaper-root.MuiDialog-paper {
    width: 450px;
    border-radius: 0;
  }
  p {
    margin: 0;
    padding: 0;
    color: var(--point_color);
  }
  .modal-title-container {
    text-align: center;
    border-bottom: 0.5px solid var(--point_color);
    position: relative;

    .modal-title {
      font-size: 16px;
      font-weight: 700;
    }

    .modal-close-button {
      --size: 24px;
      --half-size: calc(var(--size) / 2);
      position: absolute;
      right: var(--half-size);
      top: calc(50% - var(--half-size));
      width: var(--size);
      height: var(--size);
      border: none;
      background: var(--gray-light);
      border-radius: 100%;
      padding: 0;
      svg {
        width: 16px;
      }
    }
  }
  .modal-content-form {
    display: flex;
    flex-direction: column;
    align-items: center;

    .grid-row {
      display: flex;
      align-items: center;
      width: 90%;
      margin: 16px 0;

      .modal-content-input {
        width: 100%;
        height: 35px;
        border-radius: 35px;
        font-size: 14px;
        .MuiOutlinedInput-input {
          text-align: center;
        }
      }

      .validation-error {
        text-align: center;
        position: absolute;
        left: 0;
        margin-top: 4px;
        color: #EB424C;
        font-size: 12px;
        width: 100%;
      }
    }
  }

  .modal-save-button {
    color: white;
    width: 120px;
    height: 32px;
    border-radius: 20px;
    background-color: var(--point_color);
    margin: 0 auto;
    &:disabled {
      opacity: 0.6;
    }
  }
}
