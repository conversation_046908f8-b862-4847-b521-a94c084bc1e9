/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import { GetAdgroupsQueryParam, GetCampainsQueryParam } from '@models/optimization/GetAdgroupsQueryParam';

import { GetAdgroupsResponse, GetCampaignsResponse } from '@models/optimization/GetAdgroupsResponse';

export const getConversionGoals = async (isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/optimization/conversion-goal',
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
};

export const getAdgroupsForOptimizationSa = async (queryParam: GetAdgroupsQueryParam) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: 'v1/optimizations/sa/adgroups',
    method: Method.GET,
    params: { queryParams: { ...queryParam } },
  });

  return (response.successOrNot === 'Y' ? response.data : {}) as GetAdgroupsResponse;
};

export const getCampaignsForOptimizationDva = async (queryParam: GetCampainsQueryParam) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `v1/optimizations/dva/campaigns/${queryParam.optimizationId !== undefined ? queryParam.optimizationId : ''}`,
    method: Method.GET,
    params: { queryParams: { ...queryParam } },
  });

  return (response.successOrNot === 'Y' ? { campaigns: response.data } : {}) as GetCampaignsResponse;
};
