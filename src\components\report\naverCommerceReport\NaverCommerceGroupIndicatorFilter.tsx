import React, { useState, useEffect } from 'react';
import { Checkbox, FormControlLabel, Popover } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import { ReactComponent as VatIncluded } from '@components/assets/images/vat_included.svg';
import { ReactComponent as VatExcluded } from '@components/assets/images/vat_excluded.svg';
import { ReactComponent as SettingIcon } from '@components/assets/images/gear-icon.svg';
import './NaverCommerceGroupIndicatorFilter.scss';
import { NaverCommerceIndicator } from '@utils/naverCommerceReport';


export interface IndicatorGroup {
  id: string;
  label: string;
  indicators: {
    value: string;
    label: string;
  }[];
}

export interface Props {
  initialValue?: string[];
  existingValue?: string[];
  getMultiChecks: (values: string[]) => void;
  hasMinimum?: boolean;
  minimumMessage?: string;
  minimum?: number;
  direction?: 'vertical' | 'horizontal';
}


const NaverCommerceGroupIndicatorFilter: React.FC<Props> = ({
  initialValue = [],
  existingValue = [],
  getMultiChecks,
  hasMinimum = false,
  minimumMessage = '최소 7개의 지표를 선택해야 합니다.',
  minimum = 0,
  direction = 'horizontal'
}) => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [groupStates, setGroupStates] = useState<{[key: string]: boolean}>({});
  const [indicatorStates, setIndicatorStates] = useState<{[key: string]: boolean}>({});

  // Create indicator groups with i18n labels
  const createIndicatorGroups = (): IndicatorGroup[] => {
    return [
      {
        id: 'product',
        label: t('naverReport.indicatorGroup.product'),
        indicators: [
          { value: NaverCommerceIndicator.PAGE_VIEW, label: t('naverReport.indicator.label.pageView') },
          { value: NaverCommerceIndicator.NUM_PURCHASES, label: t('naverReport.indicator.label.numPurchases') },
          { value: NaverCommerceIndicator.PRODUCT_QUANTITY, label: t('naverReport.indicator.label.productQuantity') },
          { value: NaverCommerceIndicator.PAY_AMOUNT, label: t('naverReport.indicator.label.payAmount') },
          { value: NaverCommerceIndicator.REFUND_NUM_PURCHASES, label: t('naverReport.indicator.label.refundNumPurchases') },
          { value: NaverCommerceIndicator.REFUND_PRODUCT_QUANTITY, label: t('naverReport.indicator.label.refundProductQuantity') },
          { value: NaverCommerceIndicator.REFUND_PAY_AMOUNT, label: t('naverReport.indicator.label.refundPayAmount') },
          { value: NaverCommerceIndicator.PRODUCT_COUPON_DISCOUNT_AMOUNT, label: t('naverReport.indicator.label.productCouponDiscountAmount') },
          { value: NaverCommerceIndicator.ORDER_COUPON_DISCOUNT_AMOUNT, label: t('naverReport.indicator.label.orderCouponDiscountAmount') },
        ]
      },
      {
        id: 'ad',
        label: t('naverReport.indicatorGroup.ad'),
        indicators: [
          { value: NaverCommerceIndicator.SALES_AMOUNT, label: t('naverReport.indicator.label.salesAmount') },
          { value: NaverCommerceIndicator.COST, label: t('naverReport.indicator.label.cost') },
          { value: NaverCommerceIndicator.IMPRESSIONS, label: t('naverReport.indicator.label.impressions') },
          { value: NaverCommerceIndicator.CLICKS, label: t('naverReport.indicator.label.clicks') },
          { value: NaverCommerceIndicator.CONVERSION, label: t('naverReport.indicator.label.conversion') },
          { value: NaverCommerceIndicator.DIRECT_CONVERSION, label: t('naverReport.indicator.label.directConversion') },
          { value: NaverCommerceIndicator.CONVERSION_REVENUE, label: t('naverReport.indicator.label.conversionRevenue') },
          { value: NaverCommerceIndicator.DIRECT_CONVERSION_REVENUE, label: t('naverReport.indicator.label.directConversionRevenue') },
          { value: NaverCommerceIndicator.CTR, label: t('naverReport.indicator.label.ctr') },
          { value: NaverCommerceIndicator.CVR, label: t('naverReport.indicator.label.cvr') },
          { value: NaverCommerceIndicator.CPM, label: t('naverReport.indicator.label.cpm') },
          { value: NaverCommerceIndicator.CPC, label: t('naverReport.indicator.label.cpc') },
          { value: NaverCommerceIndicator.CPA, label: t('naverReport.indicator.label.cpa') },
          { value: NaverCommerceIndicator.ROAS, label: t('naverReport.indicator.label.roas') },
        ]
      }
    ];
  };

  const INDICATOR_GROUPS = createIndicatorGroups();

  // Initialize states from initialValue and existingValue props
  useEffect(() => {
    const combinedValues = [...new Set([...initialValue, ...existingValue])];
    
    // Initialize indicator states
    const newIndicatorStates: {[key: string]: boolean} = {};
    INDICATOR_GROUPS.forEach(group => {
      group.indicators.forEach(indicator => {
        newIndicatorStates[indicator.value] = combinedValues.includes(indicator.value);
      });
    });
    
    setIndicatorStates(newIndicatorStates);
    
    // Initialize group states based on selected indicators
    const newGroupStates: {[key: string]: boolean} = {};
    INDICATOR_GROUPS.forEach(group => {
      const groupIndicators = group.indicators.map(ind => ind.value);
      const selectedInGroup = groupIndicators.filter(ind => newIndicatorStates[ind]);
      newGroupStates[group.id] = selectedInGroup.length === groupIndicators.length;
    });
    
    setGroupStates(newGroupStates);
    
    // Call getMultiChecks with initial values
    if (combinedValues.length > 0) {
      getMultiChecks(combinedValues);
    }
  }, [t]); // Add t as dependency to recreate groups when language changes

  const openIndicatorFilter = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const close = () => { 
    setAnchorEl(null); 
  };

  const open = Boolean(anchorEl);

  const handleGroupToggle = (groupId: string, checked: boolean) => {
    const group = INDICATOR_GROUPS.find(g => g.id === groupId);
    if (!group) return;

    const newIndicatorStates = { ...indicatorStates };
    group.indicators.forEach(indicator => {
      if (!initialValue.includes(indicator.value) || checked) {
        newIndicatorStates[indicator.value] = checked;
      };
    });

    setIndicatorStates(newIndicatorStates);
    setGroupStates(prev => ({ ...prev, [groupId]: checked }));
    const selectedIndicators = Object.keys(newIndicatorStates).filter(key => newIndicatorStates[key]);
    getMultiChecks(selectedIndicators);
  };

  const handleIndicatorToggle = (indicatorValue: string, checked: boolean) => {
    const updated = { ...indicatorStates, [indicatorValue]: checked };
    const newSelectedCount = Object.keys(updated).filter(key => updated[key]).length;
    
    // Check minimum requirement after the change
    if (hasMinimum && newSelectedCount < minimum && !checked) {
      alert(minimumMessage);
      return;
    }

    setIndicatorStates(updated);

    const group = INDICATOR_GROUPS.find(g => 
      g.indicators.some(ind => ind.value === indicatorValue)
    );
    if (group) {
      const groupIndicators = group.indicators.map(ind => ind.value);
      const selectedInGroup = groupIndicators.filter(ind => 
        ind === indicatorValue ? checked : indicatorStates[ind]
      );
      const isGroupFullySelected = selectedInGroup.length === groupIndicators.length;
      setGroupStates(prev => ({ ...prev, [group.id]: isGroupFullySelected }));
    }
    const selectedIndicators = Object.keys(updated).filter(key => updated[key]);
    getMultiChecks(selectedIndicators);
  };

  const renderVatIcon = (indicator: string) => {
    if (indicator === NaverCommerceIndicator.SALES_AMOUNT) {
      return <VatIncluded className="vat-icon" />;
    }
    if (indicator === NaverCommerceIndicator.COST) {
      return <VatExcluded className="vat-icon" />;
    }
    return null;
  };

  return (
    <div >
      <button className="naver-indicator-filter-button" onClick={openIndicatorFilter}>
        <span>{t('naverReport.label.button.metrics')}</span>
        <SettingIcon width={20} height={20} />
      </button>
      <Popover
        id="group-indicator-filter-popover "
        open={open}
        anchorEl={anchorEl}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        onClose={close}
        PaperProps={{
          style: {
            maxHeight: '80vh',
            overflow: 'auto'
          }
        }}
      >
        <div className={`naver-group-indicator-filter multiple-check-box flex flex-col`}>
          {INDICATOR_GROUPS.map(group => (
            <div key={group.id} className="indicator-group">
              <div className="group-header">
                <FormControlLabel
                  className="group-checkbox check-box-label"
                  control={
                    <Checkbox
                      checked={groupStates[group.id] || false}
                      onChange={(e) => handleGroupToggle(group.id, e.target.checked)}
                    />
                  }
                  label={group.label}
                />
              </div>
              <div className={`group-indicators ${direction === 'vertical' ? 'grid-cols-1' : ''}`}>
                {group.indicators.map((indicator, index) => (
                  <FormControlLabel
                    key={`${group.id}-${indicator.value}-${index}`}
                    className="indicator-checkbox check-box-label"
                    control={
                      <Checkbox
                        checked={indicatorStates[indicator.value] || false}
                        onChange={(e) => handleIndicatorToggle(indicator.value, e.target.checked)}
                      />
                    }
                    label={
                      <span className="indicator-label">
                        {indicator.label}
                        {renderVatIcon(indicator.value)}
                      </span>
                    }
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      </Popover>
    </div>
  );
};

export default NaverCommerceGroupIndicatorFilter;


