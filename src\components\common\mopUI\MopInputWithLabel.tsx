// src/components/common/mopUI/MopInputWithLabel.tsx
import React, { useEffect, useState } from 'react'
import { OutlinedInput, OutlinedInputProps } from '@material-ui/core'
import tw from 'twin.macro'
import styled from '@emotion/styled'

const Wrapper = tw.div`w-full flex items-center justify-between gap-4`
const Label = tw.label`text-primary-black font-bold w-1/5`
const Input = styled(OutlinedInput, {
  shouldForwardProp: (prop) => prop !== '$hasFocus'
})<{ $hasFocus?: boolean }>`
  ${tw`flex-1 rounded-full bg-white py-1.5`};

  .MuiOutlinedInput-input {
    text-align: center;
    padding: 0;
  }

  .MuiOutlinedInput-notchedOutline {
    border-color: var(--border-input);
  }
  &:hover .MuiOutlinedInput-notchedOutline,
  &.Mui-focused .MuiOutlinedInput-notchedOutline {
    border-width: 1px;
    border-color: ${(props) => (props.$hasFocus ? 'var(--blue-active)' : 'var(--border-input)')};
  }
`

interface Props extends OutlinedInputProps {
  name: string
  handleChange: (_name: string, _value: any) => void
  replaceValue?: (_name: string, _value: any) => any
  hasFocus?: boolean
  originValue?: any
}
const MopInputWithLabel = ({
  name,
  defaultValue,
  label,
  originValue,
  hasFocus = true,
  handleChange,
  replaceValue,
  ...restProps
}: Props) => {
  const [value, setValue] = useState(originValue)

  const onChange = ({ target: { name, value } }: React.ChangeEvent<HTMLInputElement>) => {
    if (value === undefined || value === null) return
    if (replaceValue) {
      value = replaceValue(name, value).trim()
    }
    value = value === typeof 'string' ? value.trim() : value
    setValue(value)
    handleChange(name, value)
  }

  useEffect(() => {
    if (originValue !== undefined) setValue(originValue)
  }, [originValue])

  return (
    <Wrapper className="mop-input-wrapper">
      {label && <Label className="mop-input-label">{label}</Label>}
      <Input {...restProps} className="mop-input" name={name} value={value} onChange={onChange} $hasFocus={hasFocus} />
    </Wrapper>
  )
}

export default MopInputWithLabel
