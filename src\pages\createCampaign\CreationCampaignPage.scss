.CreationCampaignPage {
  display: flex;
  box-sizing: border-box;
  height: calc(100vh - 125px);
  // min-height: 850px;
  // height: 100%;
  width: 100%;
  position: relative;

  .left-site-create-new-campaign {
    padding: 20px 30px 6px 30px;
    width: 80%;
    display: flex;
    flex-direction: column;
  }

  .right-site-create-new-campaign {
    padding: 40px 24px 90px 24px;
    max-height: calc(100vh - 125px);
    width: 20%;
    min-width: 420px;
    background-color: #f9f9fb;
  }

  .mop-input {
    width: 100%;
    height: 42px;
    border-radius: 4px !important;

    fieldset {
      border: 1px solid #efefef;
    }
  }

  .view-selected-products {
    font-size: 14px;
    color: #7e7e7e;
  }

  .MuiOutlinedInput-input {
    padding: 8px !important;
  }

  .text-field-bg-white {
    & .MuiInputBase-root {
      background-color: #fff;
      box-shadow: '0 2px 6px rgba(0,0,0,0.15)';
      border: none;
    }
  }
}

#select-dropdown-campaign-info-items {
  .MuiPaper-rounded {
    border-radius: 0px;
    box-shadow: none;
    border: 1px solid #b5b7c9;
  }

  .MuiPopover-root .MuiMenu-list {
    padding: 0 !important;
  }

  .MuiMenuItem-root {
    width: 100%;
    height: 46px;
    margin-top: 0 !important;
    position: relative;
    font-size: 14px;
    color: var(--point_color);
    text-align: center;
    box-sizing: border-box;
    padding-left: 16px;
    border-radius: 0px;

    &.Mui-selected {
      background-color: transparent;
      font-weight: 500;
    }

    &.Mui-disabled {
      color: #7f808a;
    }
  }
}
