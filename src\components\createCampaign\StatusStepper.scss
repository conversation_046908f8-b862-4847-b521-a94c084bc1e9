.mui-status-stepper {
  .custom-stepper {
    background: transparent;
    
    .MuiStepLabel-root {
      cursor: pointer;
      padding: 0;
      
      .MuiStepLabel-iconContainer {
        padding: 0;
        
        .custom-step-icon {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          display: block;
        }
      }
    }
    
    .MuiStepConnector-root {
      .MuiStepConnector-line {
        border-top-width: 2px;
        border-top-style: solid;
        border-top-color: #C7C7CC;
        min-width: 20px;
      }
      .MuiStepConnector-lineHorizontal{
        border-top-width: 3px;
      }
    }
    
    .MuiStep-root {
      padding: 0 3px;
    }

    .MuiStepConnector-root {
      &:nth-child(2) {
        .MuiStepConnector-line {
          border-top-color: var(--step-1-color, #C7C7CC);
        }
      }
      
      &:nth-child(4) {
        .MuiStepConnector-line {
          border-top-color: var(--step-2-color, #C7C7CC);
        }
      }
    }
  }
} 