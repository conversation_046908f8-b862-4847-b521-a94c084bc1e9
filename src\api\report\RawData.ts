/* istanbul ignore file */

import { Service } from '@models/common';
import CommonResponse from '@models/common/CommonResponse';
import {
  CreateRawDataReport,
  RawDataAccount,
  ReportRawData,
  RawDataReportTodayCountRes,
  UpdateRawDataReportName,
  RawDataReportDownloadRes,
  CreateRawDataOptimization,
  CreateRawDataQueryParams,
  RawDataOptimization,
} from '@models/report/RawData';
import { callApi, Method } from '@utils/ApiUtil';

// 캠페인 생성
export const createRawDataReport = async ({
  advertiserId,
  params,
  queryParams,
  isLoading = true,
}: {
  advertiserId: number;
  params: CreateRawDataReport;
  queryParams: CreateRawDataQueryParams;
  isLoading?: boolean;
}): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/bulk/${advertiserId}`,
    method: Method.POST,
    params: {
      bodyParams: params,
      queryParams: { ...queryParams },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : response;
};

// 최적화 생성
export const createRawDataOptimizationReport = async ({
  advertiserId,
  params,
  queryParams,
  isLoading = true,
}: {
  advertiserId: number;
  params: CreateRawDataOptimization ;
  queryParams: CreateRawDataQueryParams;
  isLoading?: boolean;
}): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/bulk/${advertiserId}`,
    method: Method.POST,
    params: {
      bodyParams: params,
      queryParams: { ...queryParams }
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : response;
};

// 대용량 캠페인 리스트 조회
export const getRawDataReports = async (advertiserId: number, isLoading = true): Promise<ReportRawData[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : [];
};

// 매체 계정 조회
export const getReportRawDataAccounts = async (
  advertiserId: number,
  isLoading = true
): Promise<RawDataAccount[] | []> => {
  const response: CommonResponse<RawDataAccount[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/accounts`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data || [] : [];
};

// 최적화 리스트 조회
export const getRawDataOptimizations = async (
  advertiserId: number,
  isLoading = true
): Promise<RawDataOptimization[] | []> => {
  const response: CommonResponse<RawDataOptimization[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/optimizations`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data || [] : [];
};

// 상세 조회
export const getRawDataReport = async (
  advertiserId: number,
  reportId: number,
  isLoading = true
): Promise<CreateRawDataReport | CreateRawDataOptimization> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/reports/${reportId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : null;
};

// 삭제
export const deleteRawDataReport = async (advertiserId: number, reportId: number, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/reports/${reportId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });

  return response;
};

// 리포트 이름 변경
export const updateRawDataReportName = async ({
  advertiserId,
  reportId,
  params,
  isLoading = true,
}: {
  advertiserId: number;
  reportId: number;
  params: UpdateRawDataReportName;
  isLoading?: boolean;
}) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/reports/${reportId}/name`,
    method: Method.PATCH,
    params: {
      bodyParams: params,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return response;
};

// 미리보기
export const createRawDataSampleReport = async ({
  advertiserId,
  params,
  queryParams,
  isLoading = true,
}: {
  advertiserId: number;
  params: CreateRawDataReport | CreateRawDataOptimization;
  queryParams: CreateRawDataQueryParams;
  isLoading?: boolean;
}): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/sample-data`,
    method: Method.POST,
    params: {
      bodyParams: params,
      queryParams: { ...queryParams },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : [];
};

/**
 * 금일 생성 보고서 수 조회
 * @param advertiserId
 * @param isLoading
 * @returns
 */
export const getRawDataReportTodayCount = async ({
  advertiserId,
  queryParams,
  isLoading = true,
}: {
  advertiserId: number;
  queryParams: CreateRawDataQueryParams;
  isLoading?: boolean;
}): Promise<RawDataReportTodayCountRes> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/today-count`,
    method: Method.GET,
    params: {
      queryParams: { ...queryParams },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : null;
};

/**
 * 대용량 리포트 다운로드
 * @param advertiserId
 * @param reportId
 * @param isLoading
 * @returns
 */
export const getRawDataReportDownload = async ({
  advertiserId,
  reportId,
  isLoading = true,
}: {
  advertiserId: number;
  reportId: number;
  isLoading?: boolean;
}): Promise<RawDataReportDownloadRes> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/raw-data/${advertiserId}/reports/${reportId}/download`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : null;
};
