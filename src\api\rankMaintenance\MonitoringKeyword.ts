/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import { SearchMonitoringKeywordRequest } from '@models/rankMaintenance/SearchMonitoringKeywordRequest';
import { SearchMonitoringKeywordResponse } from '@models/rankMaintenance/SearchMonitoringKeywordResponse';

export const getMonitoringKeywords = async (
  queryParam: SearchMonitoringKeywordRequest,
  isLoading = true
): Promise<SearchMonitoringKeywordResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/monitoring/keyword',
    method: Method.GET,
    params: { queryParams: { ...queryParam } },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as SearchMonitoringKeywordResponse;
};
