#dvOptimizationListFilter {
  .MuiGrid-item {
    display: flex;
    align-items: center;
    flex-flow: column;
    justify-content: space-between;
    padding: 0;

    &:last-child {
      justify-content: flex-end;
      margin: 0;
    }
  }

  .search-label-container {
    background-color: #707495;
    border-bottom: 2px solid var(--point_color);
    box-sizing: border-box;
  }

  .search-label {
    height: 37px;
    display: flex;
    align-self: center;
    justify-content: center;

    svg {
      width: 20px;
      height: 20px;
      display: none;
      margin-right: 5px;
    }

    label {
      display: flex;
      align-items: center;
      align-items: center;
      height: 35px;
      font-size: 13px;
      font-weight: 300;
      color: #fff;
    }
  }

  .search-allocation-yn,
  .search-status,
  .search-media,
  .search-goal {
    width: 150px;
    box-sizing: border-box;
  }

  .search-schedule {
    width: 340px;
    box-sizing: border-box;
  }

  .search-input-container {
    margin-bottom: 32px !important;
    .search-input {
      display: flex;
      height: 40px;
      border-right: 1px solid #b5b7c9;
      border-bottom: 1px solid #b5b7c9;

      .MuiSelect-select {
        width: 150px;
        padding: 0px 40px 0px 0px;
        font-weight: 500;
      }

      .menuItem {
        .MuiPaper-rounded {
          border-radius: 0px;
          box-shadow: none;
          border: 1px solid #b5b7c9;
        }

        .MuiMenuItem-root {
          width: 100%;
          position: relative;
          font-size: 12px;
          font-weight: 200;
          color: var(--point_color);
          text-align: center;
          border-radius: 0px;

          &.Mui-selected {
            font-weight: 500;
            background-color: transparent;
          }
        }
      }

      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: var(--point_color);

      .MuiInputBase-root {
        height: 100%;

        &.MuiInput-underline {
          &::before,
          &::after {
            display: none;
          }
        }

        .search-input-dropdown {
          width: 40px;
          min-width: 40px;
          height: 40px;
          position: absolute;
          right: 0px;
          border-left: 1px solid #b5b7c9;
          pointer-events: none;

          svg {
            position: absolute;
            top: calc(50% - 12px);
            left: 12px;
            display: inline-block;
            min-width: 16px;
            min-height: 16px;
            width: 16px;
            height: 16px;
            border-left: 1px solid var(--point_color);
            border-top: 1px solid var(--point_color);
            transform: rotate(-135deg);
            opacity: 0.6;

            path {
              display: none;
            }
          }

          &.search-input-dropdown-open {
            background-color: var(--point_color);

            svg {
              transform: rotate(45deg);
              top: calc(50% - 3px);

              border-left: 1px solid var(--color-white);
              border-top: 1px solid var(--color-white);
            }
          }
        }
      }

      .MuiInputBase-input {
        font-size: 14px;
        font-weight: 400;
        color: var(--point_color);
        box-sizing: border-box;
        text-align: center;

        &:focus {
          background: none;
        }
      }
    }

    .MuiGrid-item:first-child {
      .search-input {
        border-left: 1px solid #b5b7c9;
      }
    }
  }

  .optimization-search-area {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 12px;

    .search-area {
      width: 420px;
      height: 35px;
      padding-right: 50px;
    }

    .MuiInputBase-root {
      width: 370px;
      height: 35px;
      padding-right: 7px;
      font-size: 14px;
      font-weight: 300;
      color: var(--point_color);
      border: 1px solid #bbbdcd;
      border-radius: 22.5px;
      background-color: #fff;

      fieldset {
        border: none;
      }

      &.MuiInput-underline {
        &::before,
        &::after {
          display: none;
        }
      }

      .MuiInputBase-input {
        width: 100%;
        height: 23px;
        padding: 1px 15px 1px 15px;
        text-align: right;
        font-size: 14px;
        font-weight: 300;
        color: #333;
        box-sizing: border-box;

        &:focus {
          background: none;
        }

        &::placeholder {
          font-family: 'NotoSans';
          font-weight: 400;
          font-size: 13px;
          color: #707070;
        }
      }

      &.MuiInputBase-adornedEnd .search-icon {
        width: 26px;
        height: 26px;
        cursor: pointer;
      }
    }
  }
}

.dva-optimization-filter-popover {
  .MuiPaper-rounded {
    border-radius: 0px !important;
    box-shadow: none !important;
    border: 1px solid #b5b7c9 !important;

    :hover {
      border-radius: 0px !important;
    }
  }

  .MuiMenuItem-root {
    width: 100%;
    height: 35px;
    font-size: 12px !important;
    font-weight: 400 !important;
    color: var(--point_color);
    text-align: center;
    border-radius: 0px;

    &.Mui-selected {
      font-weight: 600 !important;
      background-color: transparent !important;
    }
  }

  .MuiMenu-list {
    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
  }
}
