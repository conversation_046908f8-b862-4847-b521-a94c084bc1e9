import React, { ChangeEvent, ReactElement, useEffect, useState, useCallback } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  FormLabel,
  Grid,
  Icon,
  IconButton,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  Paper,
  Switch,
  TextField
} from '@material-ui/core';
import {
  getBudgetChangeRateCodes,
  getContributionTypeCodes,
  getDvOptimizationGoalCodes,
  getDvKpiCodes,
  getOptimizationGoalCodes,
} from '@utils/CodeUtil';
import {
  DvOptimizationSaveRequest,
  DvOptimizationDetail,
  ContributionTypeType,
  BudgetChangeRateType,
  DvOptimizationAdGroup,
  DvAdgroupSearchRequest,
  DvOptimizationCostsRequest,
  DvOptimizationCost,
} from '@models/budgetOptimization/DvOptimization';
import { DvOptimizationGoalType } from '@models/optimization/OptimizationGoal';
import './DvOptimizationDetailModal.scss';
import { add, format, max } from 'date-fns';
import { t } from 'i18next';
import { DateFnsFormat, SearchingTextLimitLength } from '@models/common/CommonConstants';
import { array, number, object, string } from 'yup';
import {
  createDvOptimization,
  updateDvOptimization,
  getDvOptimizationDetail,
  getDvCampaignAdgroups,
  getDvOptimizationCosts,
} from '@api/budgetOptimization/DvOptimization';
import { DvKpiType } from '@models/optimization/Kpi';
import IntegerNumberFormat from '@components/common/IntegerNumberFormat';
import { ReactComponent as CountCheckIcon } from '@components/assets/images/icon_count_check.svg';
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg';
import CloseIcon from '@components/assets/images/icon_close.png';
import CustomRadioGroup from '@components/common/CustomRadioGroup';
import { ToggleButton, ToggleButtonGroup } from '@material-ui/lab';
import DvSelectAdGroupList from './DvSelectAdGroupList';
import { cloneDeep, find, isNumber, debounce } from 'lodash';
import { Campaign } from '@models/common/Campaign';
import { StatusCode } from '@models/common/CommonResponse';
import { DatePeriodPicker } from '@components/common/DatePeriodPicker';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import LeftArrowIcon from '@components/assets/images/icon_arrow_left.svg';
import RightArrowIcon from '@components/assets/images/icon_arrow_right.svg';
import MultiGoalSettings from './MultiGoalSettings';
import useStatefulStateWithInitialData from '@components/common/hook/UseStatefulStateWithInitialData';
import WithLoader from '@components/common/WithLoader';
import { convertFormatToDate } from '@utils/DateUtil';
import AdviceTooltip from '@components/common/AdviceTooltip';
import { objectCompare } from '@utils/CompareUtil';
import { CONSTRAINT } from '@models/common/ContraintsByCurrency';
import { numberWithCommas } from '@utils/FormatUtil';
import ModalTitle from '@components/common/optimization/ModalTitle'
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_DVA.svg';
import { MAX_DATE_VALUE } from '@models/common/CommonConstants';
import { OptimizationIcon, InnerHtml } from '@components/common'
import { MediaType } from '@models/common';
import { useActionType, useAuthority, useToast, useDialog } from "@hooks/common";
import { ActionType } from '@models/common/CommonConstants';

export interface Props {
  onClose: (saveYn: string) => void;
  optimizationId?: number;
  type: ActionType;
  open: boolean;
}

const DvOptimizationDetailModal: React.FC<Props> = (props: Props): ReactElement => {
  const { isCreateType, isEditType, isReadType } = useActionType(props.type)
  const { advertiser } = useAuthority()
  const { openToast } = useToast()
  const { openDialog } = useDialog()
  const [optimizationInfo, setOptimizationInfo] = useState<DvOptimizationDetail>();
  const initOptimizationInfo = async () => {
    const startDate = add(new Date(), { days: 1 });
    const endDate = MAX_DATE_VALUE;
    let optimizationInfo: DvOptimizationDetail = {
      advertiserId: advertiser.advertiserId,
      campaigns: [],
      optimizationName: '',
      allocationStartDate: format(startDate, DateFnsFormat.DATE),
      allocationEndDate: format(endDate, DateFnsFormat.DATE),
      dailyBudget: 0,
      budgetChangeRate: budgetChangeRate,
      contributionType: contributionType,
      optimizationGoal: optimizationGoal,
      excludeAdgroups: [],
      kpis: [],
    };

    switch (props.type) {
      case ActionType.READ:
      case ActionType.MODIFY: {
        optimizationInfo = await getDvOptimizationDetail(props.optimizationId!);

        optimizationInfo.allocationStartDate = format(
          convertFormatToDate(optimizationInfo.allocationStartDate, DateFnsFormat.DISP_DATE),
          DateFnsFormat.DATE
        );
        optimizationInfo.allocationEndDate = format(
          convertFormatToDate(optimizationInfo.allocationEndDate, DateFnsFormat.DISP_DATE),
          DateFnsFormat.DATE
        );

        if (optimizationInfo.budgetChangeRate === 5) {
          setBudgetChangeRateType(BudgetChangeRateType.FIVE_OR_LESS);
        } else if (optimizationInfo.budgetChangeRate === 10) {
          setBudgetChangeRateType(BudgetChangeRateType.TEN_OR_LESS);
        } else if (optimizationInfo.budgetChangeRate === 20) {
          setBudgetChangeRateType(BudgetChangeRateType.TWENTY_OR_LESS);
        } else {
          setBudgetChangeRateType(BudgetChangeRateType.DIRECT);
          setBudgetChangeRate(optimizationInfo.budgetChangeRate);
        }

        if (optimizationInfo.contributionType?.length) {
          setEnableContributeType(true);
          setContributionType(optimizationInfo.contributionType as ContributionTypeType);
        } else {
          delete optimizationInfo.contributionType;
          setContributionType(ContributionTypeType.NONE);
          setEnableContributeType(false);
        }

        if (optimizationInfo.optimizationGoal?.length) {
          setOptimizationGoal(optimizationInfo.optimizationGoal as DvOptimizationGoalType);
        }

        if (optimizationInfo.optimizationGoal !== DvOptimizationGoalType.KPIS) {
          optimizationInfo.kpis = [];
          setKpis([]);
        } else {
          optimizationInfo.kpis = optimizationInfo.kpis ?? [];
          setKpis(optimizationInfo.kpis ?? []);
        }

        if (optimizationInfo.excludeAdgroups?.length) {
          setExcludeAdgroups(optimizationInfo.excludeAdgroups);
        }
        setSavedOptimizationInfo(cloneDeep(optimizationInfo));
        break;
      }
    }

    setOptimizationInfo(optimizationInfo);
    setDailyBudget(optimizationInfo.dailyBudget);
  };
  const [savedOptimizationInfo, setSavedOptimizationInfo] = useState<DvOptimizationDetail>();
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [savedCampaignIds, setSavedCampaignIds] = useState<string[]>([]);

  const [enableContributeType, setEnableContributeType] = useState<boolean>(true);

  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [currentAdgroups, setCurrentAdgroups] = useState<DvOptimizationAdGroup[]>([]);
  const [leftAdgroups, setLeftAdgroups, leftAdgroupsStatus, setLeftAdgroupsStatus] = useStatefulStateWithInitialData<
    DvOptimizationAdGroup[]
  >([]);
  const [excludeAdgroups, setExcludeAdgroups] = useState<DvOptimizationAdGroup[]>([]);

  const minDate = add(new Date(), { days: 1 });
  const maxDate = add(new Date(), { years: 1 });
  const todayDate = format(new Date(), DateFnsFormat.DATE)

  const [budgetChangeRateType, setBudgetChangeRateType] = useState<BudgetChangeRateType>(
    BudgetChangeRateType.FIVE_OR_LESS
  );
  const [budgetChangeRate, setBudgetChangeRate] = useState<number>(5);
  const [contributionType, setContributionType] = useState<ContributionTypeType>(ContributionTypeType.TRANSACTION);
  const [optimizationGoal, setOptimizationGoal] = useState<DvOptimizationGoalType>(DvOptimizationGoalType.VIEW);
  const [_kpis, setKpis] = useState<DvKpiType[]>([]);
  const [_optimizationCosts, setOptimizationCosts] = useState<DvOptimizationCost[]>([]);
  const [dailyBudget, setDailyBudget] = useState(0);
  const [_sliderMaxValue, setSliderMaxValue] = useState(0);
  const [_sliderStepValue, setSliderStepValue] = useState(1);
  const {
    min: BUDGET_MIN,
    max: BUDGET_MAX,
    unit: BUDGET_UNIT,
  } = CONSTRAINT[advertiser.advertiserCurrencyCode].DVA_BUDGET;

  const [startIndex, setStartIndex] = useState<number>(); // multi select start index

  const setDailyBudgetDebounce = useCallback(debounce((value) => {
    handleChangeOptimizationInfo('dailyBudget', value);
  }, 100), []);
  useEffect(() => {
    setDailyBudgetDebounce(dailyBudget);
  }, [dailyBudget]);

  const getOptimizationCosts = async(optInfo: DvOptimizationDetail, isLoading=false) => {
    const optimizationCostsRequest: DvOptimizationCostsRequest = {
      advertiserId: advertiser.advertiserId || 0,
      campaigns: optInfo.campaigns.map(({ mediaType, campaignId }) => ({ mediaType, campaignId })),
    };
    const result = await getDvOptimizationCosts(optimizationCostsRequest, isLoading);
    const averageCost = result.find(item => item.days === 7)?.cost || 0;
    const step = Number((2 * averageCost / 100).toLocaleString('fullwide', {maximumSignificantDigits: 1, useGrouping: false}));
    const applyUnit = (val: number) => Math.round(val / BUDGET_UNIT) * BUDGET_UNIT;

    setOptimizationCosts(result);
    setSliderMaxValue(applyUnit(2 * averageCost));
    setSliderStepValue(applyUnit(step))
  }

  const validationSchema = object()
    .shape({
      allocationStartDate: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.DvOptimizationDetailModal.allocationPeriod'),
        })
      ),
      allocationEndDate: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.DvOptimizationDetailModal.allocationPeriod'),
        })
      ),
      optimizationName: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.DvOptimizationDetailModal.optimizationName'),
        })
      ),
      campaigns: array().test({
        message: t('common.message.validation.required', {
          param: t('optimization.label.DvOptimizationDetailModal.targetCampaign'),
        }),
        test: (campaigns) => campaigns !== undefined && campaigns.length > 0,
      }),
      dailyBudget: number()
        .typeError(
          t('common.message.validation.required', {
            param: t('optimization.label.DvOptimizationDetailModal.dailyBudget'),
          })
        )
        .min(
          BUDGET_MIN,
          t('optimization.message.DvOptimizationDetailModal.validation.budgetMin', {
            min: numberWithCommas(BUDGET_MIN),
          })
        )
        .max(
          BUDGET_MAX,
          t('optimization.message.DvOptimizationDetailModal.validation.budgetMax', {
            max: numberWithCommas(BUDGET_MAX),
          })
        )
        .test(
          'dailyBudgetUnit',
          t('optimization.message.DvOptimizationDetailModal.validation.budgetUnit', { unit: BUDGET_UNIT }),
          (value) => !!value && isNumber(value) && (+value / BUDGET_UNIT) % 1 === 0
        ),
      budgetChangeRate: number().required(),
      contributionType: string(),
      optimizationGoal: string().required(
        t('common.message.validation.required', {
          param: t('optimization.label.DvOptimizationDetailModal.optimizationGoal'),
        })
      ),
      kpis: array(),
    })
    .test(
      'allocationStartDate',
      t('optimization.message.DvOptimizationDetailModal.validation.allocationPeriod'),
      (value) => {
        if (!value.allocationStartDate || !value.allocationEndDate) return true;
        return value.allocationStartDate <= value.allocationEndDate;
      }
    )
    .test('kpis', t('optimization.message.DvOptimizationDetailModal.validation.kpis'), (value) => {
      return !(!value.kpis?.length && value.optimizationGoal === DvOptimizationGoalType.KPIS.valueOf());
    });

  const handleSave = () => {
    if (isEditType) {
      const currentOptimizationInfo = {
        ...optimizationInfo,
        excludeAdgroups: excludeAdgroups,
      };
      const currentCampaignIds = currentOptimizationInfo.campaigns?.map(campaign => campaign.campaignId);
      delete currentOptimizationInfo.campaigns;

      if (objectCompare(currentOptimizationInfo, savedOptimizationInfo, true) && objectCompare(currentCampaignIds, savedCampaignIds, true)) {
        openToast(t('common.message.notChanged'))
        props.onClose('N');
        return;
      }
    }

    validationSchema
      .validate(optimizationInfo)
      .then((value) => {
        openDialog({
          message: t('common.message.saveConfirm'),
          cancelLabel: t('common.label.button.cancel'),
          actionLabel: t('common.label.button.confirm'),
          onAction: () => {
            saveOptimization();
          }
        })
      })
      .catch((err) => {
        openToast(err.message)
      });
  };

  const saveOptimization = async () => {
    if (optimizationInfo) {
      const saveRequest: DvOptimizationSaveRequest = {
        advertiserId: optimizationInfo.advertiserId,
        optimizationName: optimizationInfo.optimizationName,
        allocationStartDate: optimizationInfo.allocationStartDate,
        allocationEndDate: optimizationInfo.allocationEndDate,
        dailyBudget: optimizationInfo.dailyBudget,
        budgetChangeRate: optimizationInfo.budgetChangeRate,
        optimizationGoal: optimizationInfo.optimizationGoal,
        excludeAdgroups: excludeAdgroups,
        campaigns: optimizationInfo.campaigns,
      };

      if (enableContributeType) {
        saveRequest.contributionType = optimizationInfo.contributionType;
      }

      if (optimizationInfo.optimizationGoal === DvOptimizationGoalType.KPIS) {
        saveRequest.kpis = optimizationInfo.kpis;
      }

      let response;
      if (isEditType) {
        if (!optimizationInfo?.optimizationId) return;
        response = await updateDvOptimization(optimizationInfo.optimizationId, saveRequest);
      } else {
        response = await createDvOptimization(saveRequest);
      }
      switch (response.statusCode) {
        case StatusCode.SUCCESS:
          openToast(t('common.message.saveSuccess'))
          props.onClose('Y');
          break;
        case StatusCode.DUPLICATE_NAME:
          openToast(t('optimization.message.common.duplicateName'))
          break;
        case StatusCode.DUPLICATE_ADGROUP:
          openToast(t('optimization.message.DvOptimizationDetailModal.duplicateAdgroup'))
          break;
        default:
          openToast(t('common.message.systemError'))
          break;
      }
    }
  };

  const handleChangeOptimizationInfo = (key: string, value: any, init?: boolean) => {
    if (
      props.type !== ActionType.READ &&
      (excludeAdgroups.length > 0 && leftAdgroups.length > 0) &&
      !init &&
      key === 'campaigns'
    ) {
      openToast(t('optimization.message.DvOptimizationDetailModal.changeAdgroups'))
      const campaignIds = value.map((el: any) => el.campaignId);
      setSearchKeyword('');
      setExcludeAdgroups(excludeAdgroups.filter(el => campaignIds.includes(el.campaignId)));
      setLeftAdgroups([]);
      setLeftAdgroupsStatus('INITIAL');
    }

    setOptimizationInfo(
      (prev) =>
        ({
          ...prev,
          [key]: value,
        } as DvOptimizationDetail)
    );
  };

  const handleOnKeyPress = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      if (searchKeyword.length < 2) {
        openToast(t('optimization.message.DvOptimizationDetailModal.validation.keywordLength'))
      } else {
        setCurrentAdgroups([]);
        handleSearchKeyword();
      }
    }
  };

  const handleSearchKeyword = () => {
    if (optimizationInfo?.campaigns.length == 0) {
      openToast(t('optimization.message.DvOptimizationDetailModal.validation.emptyCampaignIds'))
      return;
    }
    const request: DvAdgroupSearchRequest = {
      advertiserId: advertiser.advertiserId,
      searchKeyword: searchKeyword,
      campaigns: optimizationInfo?.campaigns,
    };

    setLeftAdgroupsStatus('LOADING');
    getDvCampaignAdgroups(request)
      .then((response) => {
        if (response?.length === 0) {
          openToast(t('optimization.message.DvOptimizationDetailModal.searchingKeywordIsEmpty'))
        }
        setLeftAdgroups(response);
      })
      .finally(() => {
        setLeftAdgroupsStatus('LOADED');
      });
  };

  const handleMoveLeft = () => {
    if (currentAdgroups.length > 0) {
      setExcludeAdgroups(excludeAdgroups.filter((exclude) => !currentAdgroups.includes(exclude)));
      setLeftAdgroups(leftAdgroups.concat(currentAdgroups));
      setCurrentAdgroups([]);
    }
  };

  const handleMoveRight = () => {
    const isInvalidType = currentAdgroups.find(group => group.mediaType === MediaType.GOOGLE)
    if (isInvalidType) {
      openToast(t('optimization.message.DvOptimizationDetailModal.validation.selectGoogle'))
      return;
    }
    if (currentAdgroups.length > 0) {
      setLeftAdgroups(leftAdgroups.filter((obj) => !currentAdgroups.includes(obj)));
      setExcludeAdgroups(excludeAdgroups.concat(currentAdgroups));
      setCurrentAdgroups([]);
    }
  };

  const handleToggle = (e: React.MouseEvent<HTMLElement>, group: DvOptimizationAdGroup, groups:DvOptimizationAdGroup[]) => {
    setCurrentAdgroups([group]);

    if(e.shiftKey) {
      const last = groups.findIndex(k => k.adgroupId === group.adgroupId);
      const multiRange = groups.slice(startIndex, last+1);
      setCurrentAdgroups(multiRange);
    } else {
      setStartIndex(groups.findIndex(k => k.adgroupId === group.adgroupId))
      setCurrentAdgroups([group]);
    }
  };

  const handleDoubleClick = (selected: DvOptimizationAdGroup) => {
    setCurrentAdgroups([selected])
    if (find(leftAdgroups, { adgroupId: selected.adgroupId })) {
      handleMoveRight();
    }
    if (find(excludeAdgroups, {adgroupId: selected.adgroupId })) {
      handleMoveLeft();
    }
  };

  const convertDate = (dateStr: string): Date => {
    if (dateStr === undefined) {
      return new Date();
    }
    return convertFormatToDate(dateStr, DateFnsFormat.DATE);
  };

  useEffect(() => {
    initOptimizationInfo();
  }, []); // eslint-disable-line

  const getCampaignsComponent = (campaigns: DvOptimizationAdGroup[], disabled: boolean) => {
    return (
      <>
        {campaigns.map((campaign: DvOptimizationAdGroup) => {
          return (
            <ListItem
              key={campaign.adgroupId}
              role="listitem"
              button
              onClick={(e) => handleToggle(e, campaign, campaigns)}
              onDoubleClick={() => handleDoubleClick(campaign)}
              selected={currentAdgroups.includes(campaign)}
              disabled={disabled}
            >
              <ListItemText
                primary={
                  <>
                    [{campaign.mediaType}] {campaign.adgroupName}
                  </>
                }
                secondary={`Campaign ID: ${campaign.campaignId}`}
              />
            </ListItem>
          );
        })}
      </>
    );
  };

  const optimizationGoalCodes: any[] = [];
  getOptimizationGoalCodes().map((code, index) => {
    optimizationGoalCodes.push({
      value: code.optimizationGoal,
      label: code.optimizationGoalName,
    });
  });

  const dvOptimizationGoalCodes: any[] = [];
  getDvOptimizationGoalCodes().map((code, index) => {
    dvOptimizationGoalCodes.push({
      value: code.optimizationGoal,
      label: code.optimizationGoalName,
    });
  });

  const budgetChangeRateCodes: any[] = [];
  getBudgetChangeRateCodes().map((code, index) => {
    budgetChangeRateCodes.push({
      value: code.budgetChangeRate,
      label: code.budgetChangeRateName,
    });
  });

  const contributionTypeCodes: any[] = [];
  getContributionTypeCodes().map((code, index) => {
    contributionTypeCodes.push({
      value: code.contributionType,
      label: code.contributionTypeName,
    });
  });

  const kpiTypes: any[] = [];
  getDvKpiCodes().map((code, index) => {
    kpiTypes.push({
      value: code.kpiType,
      label: code.kpiLabel,
    });
  });

  const handleBudgetChangeRate = (event: ChangeEvent<HTMLInputElement>) => {
    setBudgetChangeRateType((event.target as HTMLInputElement).value as BudgetChangeRateType);
    const rangeType = (event.target as HTMLInputElement).value as BudgetChangeRateType;

    if (rangeType === BudgetChangeRateType.FIVE_OR_LESS) {
      handleChangeOptimizationInfo('budgetChangeRate', 5);
      setBudgetChangeRate(0);
    } else if (rangeType === BudgetChangeRateType.TEN_OR_LESS) {
      handleChangeOptimizationInfo('budgetChangeRate', 10);
      setBudgetChangeRate(0);
    } else if (rangeType === BudgetChangeRateType.TWENTY_OR_LESS) {
      handleChangeOptimizationInfo('budgetChangeRate', 20);
      setBudgetChangeRate(0);
    } else if (rangeType === BudgetChangeRateType.DIRECT) {
      handleChangeOptimizationInfo('budgetChangeRate', 0);
    }
  };

  const handleDirectBudgetChangeRate = (event: ChangeEvent<HTMLInputElement>) => {
    const rangeNum = Number((event.target as HTMLInputElement).value);

    setBudgetChangeRate(rangeNum);
    handleChangeOptimizationInfo('budgetChangeRate', rangeNum);
  };

  const handleContributionType = (event: ChangeEvent<HTMLInputElement>) => {
    const contributionType = (event.target as HTMLInputElement).value as ContributionTypeType;
    setContributionType(contributionType);
    handleChangeOptimizationInfo('contributionType', contributionType);
  };

  const handleOptimizationGoal = (event: React.MouseEvent<HTMLElement, MouseEvent>, value: DvOptimizationGoalType) => {
    if (value) {
      setOptimizationGoal(value);

      handleChangeOptimizationInfo('optimizationGoal', value);

      if (value !== DvOptimizationGoalType.KPIS) {
        setKpis([]);
        handleChangeOptimizationInfo('kpis', []);
      }
    }
  };

  const handleContributeSwitchChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setContributionType(ContributionTypeType.TRANSACTION);
    } else {
      setContributionType(ContributionTypeType.NONE);
    }
    setEnableContributeType(event.target.checked);
  };

  const renderOptimizationGoal = (goalType: DvOptimizationGoalType) => {
    const disabled =
      isReadType ||
      (
        isEditType &&
        (goalType != DvOptimizationGoalType.KPIS || optimizationInfo?.optimizationGoal !== DvOptimizationGoalType.KPIS) &&
        (!!optimizationInfo && !!optimizationInfo.allocationStartDate && optimizationInfo.allocationStartDate <= todayDate)
      );
    return (
      <ToggleButton
        className="optimization-goal"
        key={`optimization-goal-key-${goalType}`}
        value={goalType}
        disabled={disabled}
        disableRipple
      >
        <div className="upper">
          <div className="optimization-icon">
            <OptimizationIcon goalType={goalType} size={50} />
          </div>
          <div>
            <div className="optimization-name-kor">
              {t(`optimization.label.DvOptimizationDetailModal.labelKor.${goalType}`)}
            </div>
            <div className="optimization-name-eng">
              {t(`optimization.label.DvOptimizationDetailModal.labelEng.${goalType}`)}
            </div>
          </div>
        </div>
        {renderOptimizationDescription(goalType, props.type)}
      </ToggleButton>
    );
  };

  const renderOptimizationDescription = (goalType: DvOptimizationGoalType, modalType: ActionType) => {
    if (
      goalType === DvOptimizationGoalType.KPIS &&
      optimizationInfo?.optimizationGoal === DvOptimizationGoalType.KPIS
    ) {
      return (
        <div className="optimization-description-container optimization-kpis-settings">
          <MultiGoalSettings
            kpis={optimizationInfo.kpis}
            setKpis={(kpis: DvKpiType[]) => {
              handleChangeOptimizationInfo('kpis', kpis);
            }}
            modalType={modalType}
            notYetStarted={todayDate < optimizationInfo.allocationStartDate}
          />
        </div>
      );
    }

    return (
      <div className="optimization-description-container">
        <InnerHtml
          className="optimization-description"
          innerHTML={t(`optimization.label.DvOptimizationDetailModal.description.${goalType}`)}
        />
      </div>
    );
  }

  return (
    <div>
      <Dialog id="DvOptimizationDetailModal" open={props.open} fullScreen scroll="body" onClose={() => props.onClose('N')}>
        <DialogTitle>
          <IconButton className="modal-close" aria-label="close" onClick={() => props.onClose('N')}>
            <img alt="close-image" src={CloseIcon} />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {optimizationInfo && (
            <Grid container>
              <Grid item className="LeftGrid">
                <Paper>
                  <Box id="title">
                    <ModalTitle
                      type={props.type}
                      title="Display & Video Ad"
                      icon={<TitleLabelIcon width={24}/>}
                    />
                  </Box>
                  <Box id="BidPeriod">
                    <FormLabel>
                      {t('optimization.label.DvOptimizationDetailModal.labelEng.allocationPeriod')}
                      <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.allocationPeriod')}</span>
                    </FormLabel>
                    <div>
                      <DatePeriodPicker
                        id="dva-optimizatoin-modal-period"
                        disableToolbar={true}
                        startDate={optimizationInfo.allocationStartDate}
                        endDate={optimizationInfo.allocationEndDate}
                        autoOk
                        onClickStartDate={(date) => {
                          date && handleChangeOptimizationInfo('allocationStartDate', format(date, DateFnsFormat.DATE));
                        }}
                        onClickEndDate={(date) => {
                          date && handleChangeOptimizationInfo('allocationEndDate', format(date, DateFnsFormat.DATE));
                        }}
                        minStartDate={
                          (
                            isCreateType || (isEditType && todayDate < optimizationInfo.allocationStartDate)
                          )
                            ? minDate
                            : undefined
                        }
                        maxStartDate={maxDate}
                        minEndDate={
                          isCreateType
                            ? minDate
                            : (
                                isReadType || (isEditType && optimizationInfo.allocationEndDate < todayDate)
                              )
                                ? undefined
                                : max([convertDate(optimizationInfo.allocationStartDate), convertDate(todayDate)])
                        }
                        disabledStartDate={
                          (isEditType && optimizationInfo.allocationStartDate <= todayDate) || isReadType
                        }
                        disabledEndDate={
                          (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                        }
                        allowUnsetEndDate={true}
                      />
                    </div>
                  </Box>
                  <Box>
                    <DvSelectAdGroupList
                      startDate={optimizationInfo.allocationStartDate}
                      endDate={optimizationInfo.allocationEndDate}
                      optimizationId={optimizationInfo?.optimizationId ?? undefined}
                      selectedGroups={campaigns}
                      setSelectedGroups={(campaigns: Campaign[], init?: boolean) => {
                        setCampaigns(campaigns);
                        handleChangeOptimizationInfo('campaigns', campaigns, init);
                      }}
                      setSavedCampaignIds={setSavedCampaignIds}
                      type={props.type}
                    />
                  </Box>
                  <Box id="select-campaigns-bottom">
                    <Box id="select-campaigns-info">
                      <div>
                        <Icon className="select-mark-blue" />
                        <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.possibleBidding')} />
                      </div>
                      <div>
                        <Icon className="select-mark-red" />
                        <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.conditionalBidding')} />
                      </div>
                      <div>
                        <Icon className="select-mark-gray" />
                        <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.impossibleBidding')} />
                      </div>
                      <div>
                        <Icon className="select-mark-new" />
                        <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.newBidding')} />
                      </div>
                    </Box>
                    <Box
                      id="select-campaigns-count"
                      className={`${(
                        (isEditType && optimizationInfo.allocationStartDate <= todayDate) || isReadType
                      ) ? 'disabled' : ''}`}
                    >
                      {optimizationInfo && (
                        <TextField
                          value={optimizationInfo.campaigns?.length}
                          InputProps={{
                            inputComponent: IntegerNumberFormat,
                            startAdornment: <CountCheckIcon />,
                          }}
                          disabled
                        />
                      )}
                      <div className="unit">{t('optimization.label.DvOptimizationDetailModal.unit')}</div>
                    </Box>
                  </Box>
                </Paper>
              </Grid>
              <Grid item className="RightGrid">
                <Paper>
                  <Box id="header">
                    <FormLabel>
                      {t('optimization.label.DvOptimizationDetailModal.labelEng.setting')}
                      <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.setting')}</span>
                    </FormLabel>
                  </Box>
                  <div id="setting-group">
                    <Box id="optimization-name">
                      <FormLabel>
                          <AdviceTooltip
                            id="dv-optimization-advice-tooltip-optimization-name"
                            title={
                              <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.optimizationName')} />
                            }
                            placement="right-start"
                            arrow
                          >
                            <span className="icon">
                              <AdviceMarkIcon />
                            </span>
                          </AdviceTooltip>
                        {t('optimization.label.DvOptimizationDetailModal.labelEng.optimizationName')}
                        <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.optimizationName')}</span>
                      </FormLabel>
                      <TextField
                        data-testid="optimizationName"
                        value={optimizationInfo.optimizationName}
                        onChange={(event) => {
                          handleChangeOptimizationInfo('optimizationName', event.target.value);
                        }}
                        inputProps={{
                          maxLength: 30,
                        }}
                        disabled={isReadType}
                      />
                    </Box>
                    <Box id="budget">
                      <FormLabel>
                        <AdviceTooltip
                          id="dv-optimization-advice-tooltip-budget"
                          title={
                            <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.dailyBudget', {
                              min: numberWithCommas(BUDGET_MIN),
                              max: numberWithCommas(BUDGET_MAX),
                              unit: numberWithCommas(BUDGET_UNIT),
                            })} />
                          }
                          placement="right-start"
                          arrow
                        >
                          <span className="icon">
                            <AdviceMarkIcon />
                          </span>
                        </AdviceTooltip>
                        {t('optimization.label.DvOptimizationDetailModal.labelEng.dailyBudget')}
                        <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.dailyBudget')}</span>
                      </FormLabel>
                      <TextField
                        data-testid="dailyBudget"
                        value={dailyBudget}
                        onChange={(event) => {
                          setDailyBudget(Number(event.target.value));
                        }}
                        InputProps={{
                          inputComponent: IntegerNumberFormat,
                          endAdornment: (
                            <InputAdornment position="end">
                              {t('optimization.label.DvOptimizationDetailModal.vat')}
                            </InputAdornment>
                          ),
                        }}
                        disabled={
                          (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                        }
                      />
                    </Box>
                    <Box id="budget-variance">
                      <FormLabel>
                        <AdviceTooltip
                          id="dv-optimization-advice-tooltip-budget-variance"
                          title={
                            <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.budgetVariance')} />
                          }
                          placement="right-start"
                          arrow
                        >
                          <span className="icon">
                            <AdviceMarkIcon />
                          </span>
                        </AdviceTooltip>
                        {t('optimization.label.DvOptimizationDetailModal.labelEng.budgetVariance')}
                        <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.budgetVariance')}</span>
                      </FormLabel>
                      <span id="budgetChangeRateType" className="customRadioGroup">
                        <CustomRadioGroup<BudgetChangeRateType>
                          items={budgetChangeRateCodes}
                          onChange={handleBudgetChangeRate}
                          defaultValue={budgetChangeRateType}
                          disabled={
                            (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                          }
                          data-testid="budgetChangeRateType"
                          disableRipple
                        />
                      </span>
                      <TextField
                        className="budget-variance-input"
                        data-testid="budgetChangeRate"
                        value={budgetChangeRateType === BudgetChangeRateType.DIRECT ? budgetChangeRate : 0}
                        onChange={handleDirectBudgetChangeRate}
                        inputProps={{
                          maxLength: 30,
                          style: {
                            flex: '1 1 auto',
                          },
                        }}
                        InputProps={{
                          inputComponent: IntegerNumberFormat,
                          endAdornment: (
                            <InputAdornment position="end">{t('common.label.belowPercent')}</InputAdornment>
                          ),
                        }}
                        disabled={
                          (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType ||
                          budgetChangeRateType !== BudgetChangeRateType.DIRECT
                        }
                      />
                    </Box>
                    <Box id="contribution">
                      <FormLabel>
                        <AdviceTooltip
                          id="dv-optimization-advice-tooltip-contribution-type"
                          title={
                            <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.contributionType')} />
                          }
                          placement="right-start"
                          arrow
                        >
                          <span className="icon">
                            <AdviceMarkIcon />
                          </span>
                        </AdviceTooltip>
                        {t('optimization.label.DvOptimizationDetailModal.labelEng.contributionType')}
                        <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.contributionType')}</span>
                      </FormLabel>
                      <Box className="contribution-settings">
                        <Switch
                          data-testid="contributionYnSwitch"
                          className="contributionYnSwitch"
                          edge="end"
                          color="primary"
                          disabled={
                            (isEditType && optimizationInfo.allocationStartDate <= todayDate) || isReadType
                          }
                          onChange={handleContributeSwitchChange}
                          checked={enableContributeType}
                        />
                        <span id="contributionType" className="customRadioGroup">
                          <CustomRadioGroup<ContributionTypeType>
                            items={contributionTypeCodes}
                            onChange={handleContributionType}
                            defaultValue={contributionType ?? ''}
                            disabled={
                              (isEditType && optimizationInfo.allocationStartDate <= todayDate) || isReadType ||
                              !enableContributeType
                            }
                            disableRipple
                          />
                        </span>
                      </Box>
                    </Box>
                  </div>
                  <Box id="optimization-goal">
                    <FormLabel>
                      <AdviceTooltip
                        id="dv-optimization-advice-tooltip-optimization-goal"
                        title={
                          <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.optimizationGoal')} />
                        }
                        placement="right-start"
                        arrow
                      >
                        <span className="icon">
                          <AdviceMarkIcon />
                        </span>
                      </AdviceTooltip>
                      {t('optimization.label.DvOptimizationDetailModal.labelEng.optimizationGoal')}
                      <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.optimizationGoal')}</span>
                    </FormLabel>
                  </Box>
                  <Box id="optimization-goals">
                    <ToggleButtonGroup
                      data-testid="optimizationGoal"
                      value={optimizationInfo.optimizationGoal}
                      exclusive={true}
                      onChange={handleOptimizationGoal}
                    >
                      {dvOptimizationGoalCodes.map((goal) =>
                        renderOptimizationGoal(goal.value as DvOptimizationGoalType)
                      )}
                    </ToggleButtonGroup>
                  </Box>
                  <Box id="negative-adgroups">
                    <FormLabel>
                      <AdviceTooltip
                        id="dv-optimization-advice-tooltip-negative-adgroups"
                        title={
                          <InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.negativeAdgroups')} />
                        }
                        placement="right-start"
                        arrow
                      >
                        <span className="icon">
                          <AdviceMarkIcon />
                        </span>
                      </AdviceTooltip>
                      {t('optimization.label.DvOptimizationDetailModal.labelEng.negativeAdgroups')}
                      <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.negativeAdgroups')}</span>
                    </FormLabel>
                  </Box>
                  <Box id="bid-exception-adgroups">
                    <Grid container justifyContent="space-between" alignItems="center">
                      <Grid item id="LeftShuttle">
                        <Grid container direction="column" alignItems="center">
                          <Grid item id="search-keyword">
                            <TextField
                              data-testid="searchKeyword"
                              value={searchKeyword}
                              onChange={(event) => setSearchKeyword(event.target.value)}
                              placeholder={t('optimization.label.DvOptimizationDetailModal.adGroupsId')}
                              InputProps={{
                                endAdornment: (
                                  <SearchIcon
                                    className="search-icon"
                                    onClick={() => {
                                      if (searchKeyword.length < 2) {
                                        openToast(t(
                                          'optimization.message.DvOptimizationDetailModal.validation.keywordLength'
                                        ))
                                      } else {
                                        setCurrentAdgroups([]);
                                        handleSearchKeyword();
                                      }
                                    }}
                                  />
                                ),
                              }}
                              disabled={
                                (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                              }
                              onKeyPress={handleOnKeyPress}
                              inputProps={{
                                maxLength: SearchingTextLimitLength,
                              }}
                            />
                          </Grid>
                          <Grid item id="searching-adgroups">
                            <WithLoader status={leftAdgroupsStatus}>
                              <Paper>
                                <List dense component="div" role="list" data-testid="leftAdgroupList">
                                  {getCampaignsComponent(leftAdgroups, (
                                    isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                                  )}
                                  <ListItem className="empty" />
                                </List>
                              </Paper>
                            </WithLoader>
                          </Grid>
                        </Grid>
                      </Grid>
                      <Grid item id="ShuttleButton">
                        <Grid container direction="column" alignItems="center">
                          <IconButton
                            id="move-right"
                            data-testid="moveRightBtn"
                            size="small"
                            onClick={handleMoveRight}
                            disabled={
                              leftAdgroups.length === 0 ||
                              (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                            }
                            aria-label="move selected right"
                          >
                            <img alt="arrow-right-image" src={RightArrowIcon} />
                          </IconButton>
                          <IconButton
                            id="move-left"
                            data-testid="moveLeftBtn"
                            size="small"
                            onClick={handleMoveLeft}
                            disabled={
                              excludeAdgroups.length === 0 ||
                              (isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                            }
                            aria-label="move selected left"
                          >
                            <img alt="arrow-left-image" src={LeftArrowIcon} />
                          </IconButton>
                        </Grid>
                      </Grid>
                      <Grid item id="RightShuttle">
                        <Paper>
                          <List dense component="div" role="list" data-testid="excludeAdgroupList">
                            {getCampaignsComponent(excludeAdgroups, (
                              isEditType && optimizationInfo.allocationEndDate < todayDate) || isReadType
                            )}
                            <ListItem className="empty" />
                          </List>
                        </Paper>
                      </Grid>
                    </Grid>
                  </Box>
                  <Box id="save">
                    {props.type !== ActionType.READ && (
                      <Button
                        data-testid="saveButton"
                        variant="contained"
                        onClick={handleSave}
                        className="DialogSaveButton"
                      >
                        {t('common.label.button.save')}
                      </Button>
                    )}
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
export default DvOptimizationDetailModal;
