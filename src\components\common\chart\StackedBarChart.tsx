import React, { useMemo } from 'react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import { Bar } from 'react-chartjs-2';
import ChartCustomTooltip, { TooltipOption } from '@components/common/chart/ChartCustomTooltip';
import { BarChartData } from '@models/common/ChartData';
import { TooltipInfo } from '@models/common/TooltipInfo';

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface Props {
  chartData: BarChartData;
  options: any;
  plugins?: any;
  tooltipInfo: TooltipInfo;
  tooltipOption?: TooltipOption;
}

const StackedBarChart = ({ chartData, options, plugins, tooltipInfo, tooltipOption }: Props) => {
  const toolTipColor = useMemo(() => {
    const targetChartData = chartData.datasets.find((item) => item.label === tooltipInfo.data[0].label);
    return targetChartData?.backgroundColor || '';
  }, [chartData, tooltipInfo]);

  return (
    <>
      <Bar options={options} data={chartData} plugins={plugins} />
      {tooltipInfo.visible && (
        <ChartCustomTooltip
          left={tooltipInfo.left}
          top={tooltipInfo.top}
          label={tooltipInfo.data[0].label}
          value={tooltipInfo.data[0].value}
          title={tooltipInfo.data[0].xTitle}
          indexColor={toolTipColor}
          option={tooltipOption}
        />
      )}
    </>
  );
};

export default StackedBarChart;
