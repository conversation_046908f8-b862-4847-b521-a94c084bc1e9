import { getCampaigns, getMediaAccount, getChannels, getProducts, createCampaign } from './index'
import { callApi, cleanQueryParams, Method } from '@utils/ApiUtil'
import { Service } from '@models/common'
import { CampaignListParams, CampaignParams, UseDailyBudgetTypeEnum, DeviceTypeEnum } from '@models/createCampaign/CreateCampaign'

// Mock the ApiUtil module
jest.mock('@utils/ApiUtil')

const mockCallApi = callApi as jest.MockedFunction<typeof callApi>
const mockCleanQueryParams = cleanQueryParams as jest.MockedFunction<typeof cleanQueryParams>

describe('Creation Campaign API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock cleanQueryParams to return the input object with filtered values
    mockCleanQueryParams.mockImplementation((params: Record<string, any>) => {
      const cleaned: Record<string, any> = {}
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '' && value !== 'ALL') {
          cleaned[key] = value
        }
      })
      return cleaned
    })
  })

  describe('getCampaigns', () => {
    it('should fetch campaigns successfully with all parameters', async () => {
      // Arrange
      const advertiserId = 123
      const params: CampaignListParams = {
        campaignName: 'Test Campaign',
        pageIndex: 1,
        pageSize: 10
      }
      
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          totalCount: 5,
          configurations: [
            {
              campaignId: 1,
              campaignName: 'Test Campaign',
              mediaType: 'NAVER',
              customerName: 'Test Customer',
              businessChannelName: 'Test Channel',
              useDailyBudgetYn: 'Y',
              dailyBudget: 50000,
              status: 'ACTIVE',
              device: 'PC'
            }
          ]
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getCampaigns(advertiserId, params)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/${advertiserId}`,
        method: Method.GET,
        params: {
          queryParams: {
            campaignName: 'Test Campaign',
            pageIndex: 1,
            pageSize: 10
          }
        }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch campaigns with empty campaign name', async () => {
      // Arrange
      const advertiserId = 123
      const params: CampaignListParams = {
        campaignName: '',
        pageIndex: 1,
        pageSize: 10
      }
      
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          totalCount: 0,
          configurations: []
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getCampaigns(advertiserId, params)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/${advertiserId}`,
        method: Method.GET,
        params: {
          queryParams: {
            pageIndex: 1,
            pageSize: 10
          }
        }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch campaigns with undefined campaign name', async () => {
      // Arrange
      const advertiserId = 123
      const params: CampaignListParams = {
        pageIndex: 1,
        pageSize: 10
      }
      
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          totalCount: 3,
          configurations: []
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getCampaigns(advertiserId, params)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/${advertiserId}`,
        method: Method.GET,
        params: {
          queryParams: {
            pageIndex: 1,
            pageSize: 10
          }
        }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should handle API error response', async () => {
      // Arrange
      const advertiserId = 123
      const params: CampaignListParams = {
        campaignName: 'Test Campaign',
        pageIndex: 1,
        pageSize: 10
      }
      
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'ERROR',
        data: null
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getCampaigns(advertiserId, params)

      // Assert
      expect(result).toBeNull()
    })
  })

  describe('getMediaAccount', () => {
    it('should fetch media accounts successfully with default loading state', async () => {
      // Arrange
      const advertiserId = 123
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: [
          {
            customerId: '12345',
            customerName: 'Test Customer',
            sellerAccountId: 'seller123',
            sellerAccountName: 'Test Seller'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getMediaAccount(advertiserId)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/paired-accounts/${advertiserId}`,
        method: Method.GET,
        config: { isLoading: false }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch media accounts with custom loading state', async () => {
      // Arrange
      const advertiserId = 123
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: [
          {
            customerId: '12345',
            customerName: 'Test Customer',
            sellerAccountId: 'seller123',
            sellerAccountName: 'Test Seller'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getMediaAccount(advertiserId, true)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/paired-accounts/${advertiserId}`,
        method: Method.GET,
        config: { isLoading: true }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should return empty array when API call fails', async () => {
      // Arrange
      const advertiserId = 123
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'ERROR',
        data: null
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getMediaAccount(advertiserId)

      // Assert
      expect(result).toEqual([])
    })

    it('should return empty array when successOrNot is not Y', async () => {
      // Arrange
      const advertiserId = 123
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'SUCCESS',
        data: [
          {
            customerId: '12345',
            customerName: 'Test Customer',
            sellerAccountId: 'seller123',
            sellerAccountName: 'Test Seller'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getMediaAccount(advertiserId)

      // Assert
      expect(result).toEqual([])
    })

    it('should handle empty data response', async () => {
      // Arrange
      const advertiserId = 123
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: []
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getMediaAccount(advertiserId)

      // Assert
      expect(result).toEqual([])
    })
  })

  describe('getChannels', () => {
    it('should fetch channels successfully with default loading state', async () => {
      // Arrange
      const customerId = 'customer123'
      const sellerId = 'seller123'
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: [
          {
            businessChannelId: 'channel123',
            businessChannelName: 'Test Channel',
            status: 'ELIGIBLE'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getChannels(customerId, sellerId)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/business-channels/${customerId}/${sellerId}`,
        method: Method.GET,
        config: { isLoading: false }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch channels with custom loading state', async () => {
      // Arrange
      const customerId = 'customer123'
      const sellerId = 'seller123'
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: [
          {
            businessChannelId: 'channel123',
            businessChannelName: 'Test Channel',
            status: 'ELIGIBLE'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getChannels(customerId, sellerId, true)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/business-channels/${customerId}/${sellerId}`, 
        method: Method.GET,
        config: { isLoading: true }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should return empty array when API call fails', async () => {
      // Arrange
      const customerId = 'customer123'
      const sellerId = 'seller123'
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'ERROR',
        data: null
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getChannels(customerId, sellerId)

      // Assert
      expect(result).toEqual([])
    })

    it('should return empty array when successOrNot is not Y', async () => {
      // Arrange
      const customerId = 'customer123'
      const sellerId = 'seller123'
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'SUCCESS',
        data: [
          {
            businessChannelId: 'channel123',
            businessChannelName: 'Test Channel',
            status: 'ELIGIBLE'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getChannels(customerId, sellerId)

      // Assert
      expect(result).toEqual([])
    })

    it('should handle empty data response', async () => {
      // Arrange
      const customerId = 'customer123'
      const sellerId = 'seller123'
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: []
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getChannels(customerId, sellerId)

      // Assert
      expect(result).toEqual([])
    })
  })

  describe('getProducts', () => {
    it('should fetch products successfully with default loading state', async () => {
      // Arrange
      const customerID = 'customer123'
      const sellerId = 'seller123'
      const channelId = 'channel123'
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: [
          {
            productId: 'product123',
            productName: 'Test Product',
            categoryName: 'Test Category',
            price: 10000,
            stock: 100,
            adRevenue: '5000',
            totalRevenue: '15000',
            status: 'SALE',
            adStatus: 'ON'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getProducts(customerID, sellerId, channelId)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/products/${customerID}/${sellerId}/${channelId}`,
        method: Method.GET,
        config: { isLoading: false }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should fetch products with custom loading state', async () => {
      // Arrange
      const customerID = 'customer123'
      const sellerId = 'seller123'
      const channelId = 'channel123'
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: [
          {
            productId: 'product123',
            productName: 'Test Product',
            categoryName: 'Test Category',
            price: 10000,
            stock: 100,
            adRevenue: '5000',
            totalRevenue: '15000',
            status: 'SALE',
            adStatus: 'ON'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getProducts(customerID, sellerId, channelId, true)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: `/v1/campaign/sa/shopping/products/${customerID}/${sellerId}/${channelId}`,
        method: Method.GET,
        config: { isLoading: true }
      })
      expect(result).toEqual(mockResponse.data)
    })

    it('should return empty array when API call fails', async () => {
      // Arrange
      const customerID = 'customer123'
      const sellerId = 'seller123'
      const channelId = 'channel123'
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'ERROR',
        data: null
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getProducts(customerID, sellerId, channelId)

      // Assert
      expect(result).toEqual([])
    })

    it('should return empty array when successOrNot is not Y', async () => {
      // Arrange
      const customerID = 'customer123'
      const sellerId = 'seller123'
      const channelId = 'channel123'
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'SUCCESS',
        data: [
          {
            productId: 'product123',
            productName: 'Test Product',
            categoryName: 'Test Category',
            price: 10000,
            stock: 100,
            adRevenue: '5000',
            totalRevenue: '15000',
            status: 'SALE',
            adStatus: 'ON'
          }
        ]
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getProducts(customerID, sellerId, channelId)

      // Assert
      expect(result).toEqual([])
    })

    it('should handle empty data response', async () => {
      // Arrange
      const customerID = 'customer123'
      const sellerId = 'seller123'
      const channelId = 'channel123'
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: []
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await getProducts(customerID, sellerId, channelId)

      // Assert
      expect(result).toEqual([])
    })
  })

  describe('createCampaign', () => {
    it('should create campaign successfully with default loading state', async () => {
      // Arrange
      const campaignParams: CampaignParams = {
        campaignName: 'Test Campaign',
        customerId: 'customer123',
        advertiserId: 'advertiser123',
        bizChannelId: 'channel123',
        useDailyBudget: UseDailyBudgetTypeEnum.LIMITED,
        dailyBudget: 50000,
        device: DeviceTypeEnum.PC,
        products: ['product1', 'product2']
      }
      
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          campaignId: 123,
          message: 'Campaign created successfully'
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await createCampaign(campaignParams)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: '/v1/campaign/sa/shopping',
        method: Method.POST,
        params: {
          bodyParams: campaignParams
        },
        config: { isLoading: false }
      })
      expect(result).toEqual(mockResponse)
    })

    it('should create campaign with custom loading state', async () => {
      // Arrange
      const campaignParams: CampaignParams = {
        campaignName: 'Test Campaign',
        customerId: 'customer123',
        advertiserId: 'advertiser123',
        bizChannelId: 'channel123',
        useDailyBudget: UseDailyBudgetTypeEnum.UNLIMITED,
        dailyBudget: 0,
        device: DeviceTypeEnum.ALL,
        products: ['product1']
      }
      
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          campaignId: 123,
          message: 'Campaign created successfully'
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await createCampaign(campaignParams, true)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: '/v1/campaign/sa/shopping',
        method: Method.POST,
        params: {
          bodyParams: campaignParams
        },
        config: { isLoading: true }
      })
      expect(result).toEqual(mockResponse)
    })

    it('should handle campaign creation failure', async () => {
      // Arrange
      const campaignParams: CampaignParams = {
        campaignName: 'Test Campaign',
        customerId: 'customer123',
        advertiserId: 'advertiser123',
        bizChannelId: 'channel123',
        useDailyBudget: UseDailyBudgetTypeEnum.LIMITED,
        dailyBudget: 50000,
        device: DeviceTypeEnum.MOBILE,
        products: ['product1', 'product2']
      }
      
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'ERROR',
        data: {
          message: 'Campaign creation failed'
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await createCampaign(campaignParams)

      // Assert
      expect(result).toEqual(mockResponse)
    })

    it('should handle campaign with empty products array', async () => {
      // Arrange
      const campaignParams: CampaignParams = {
        campaignName: 'Test Campaign',
        customerId: 'customer123',
        advertiserId: 'advertiser123',
        bizChannelId: 'channel123',
        useDailyBudget: UseDailyBudgetTypeEnum.LIMITED,
        dailyBudget: 50000,
        device: DeviceTypeEnum.PC,
        products: []
      }
      
      const mockResponse = {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          campaignId: 123,
          message: 'Campaign created successfully'
        }
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await createCampaign(campaignParams)

      // Assert
      expect(mockCallApi).toHaveBeenCalledWith({
        service: Service.MOP_BE,
        url: '/v1/campaign/sa/shopping',
        method: Method.POST,
        params: {
          bodyParams: campaignParams
        },
        config: { isLoading: false }
      })
      expect(result).toEqual(mockResponse)
    })

    it('should handle API error response', async () => {
      // Arrange
      const campaignParams: CampaignParams = {
        campaignName: 'Test Campaign',
        customerId: 'customer123',
        advertiserId: 'advertiser123',
        bizChannelId: 'channel123',
        useDailyBudget: UseDailyBudgetTypeEnum.LIMITED,
        dailyBudget: 50000,
        device: DeviceTypeEnum.PC,
        products: ['product1', 'product2']
      }
      
      const mockResponse = {
        successOrNot: 'N',
        statusCode: 'INTERNAL_SERVER_ERROR',
        data: null
      }

      mockCallApi.mockResolvedValue(mockResponse)

      // Act
      const result = await createCampaign(campaignParams)

      // Assert
      expect(result).toEqual(mockResponse)
    })
  })
}) 