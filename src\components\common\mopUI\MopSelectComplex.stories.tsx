// src/components/common/mopUI/MopSelectComplex.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { useState, useMemo, useRef, useEffect } from 'react'
import MopSelect from './MopSelect'
import MopSelectOptions from './MopSelectOptions'
import MopSelectOption from './MopSelectOption'
import MuiInput from './MuiInput'
import MopSelectVirtual from './MopSelectVirtual'

const meta: Meta<typeof MopSelect> = {
  title: 'Components/Common/MopUI/MopSelect/Complex',
  component: MopSelect,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `Advanced examples showcasing complex layouts and custom renderers for MopSelect.`
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    id: {
      description: 'Unique identifier for the select component, used for testing and accessibility',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    value: {
      description: 'Current selected value(s). Use string for single selection, string[] for multiple selection',
      control: { type: 'text' },
      table: {
        type: { summary: 'string | string[]' },
        defaultValue: { summary: 'undefined' }
      }
    },
    onChange: {
      description: 'Callback function triggered when selection changes. Receives the new value(s) as parameter',
      action: 'changed',
      table: {
        type: { summary: '(value: string | string[]) => void' },
        defaultValue: { summary: 'undefined' }
      }
    },
    options: {
      description: 'Array of options for legacy mode. Not recommended for complex layouts - use children approach instead',
      control: { type: 'object' },
      table: {
        type: { summary: 'string[] | SelectOption[]' },
        defaultValue: { summary: 'undefined' }
      }
    },
    placeholder: {
      description: 'Text displayed when no option is selected',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '"선택하세요"' }
      }
    },
    disabled: {
      description: 'Whether the select component is disabled and non-interactive',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    multiple: {
      description: 'Enable multiple selection mode. Works with complex layouts and custom renderers',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    children: {
      description: 'Child components (MopSelectOptions containing MopSelectOption with render) for complex layouts',
      control: false,
      table: {
        type: { summary: 'React.ReactNode' },
        defaultValue: { summary: 'undefined' }
      }
    },
    className: {
      description: 'Additional CSS classes to apply to the select container. Useful for responsive layouts',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

// Shared helpers for stories
const makeProducts = (count: number) => {
  const emoji = ['👟','🎧','⌨️','🖱️','🧰','🖥️','📸','🎮']
  return Array.from({ length: count }).map((_, i) => ({
    value: `PROD${(i + 1).toString().padStart(String(count).length, '0')}`,
    code: `PROD${(i + 1).toString().padStart(String(count).length, '0')}`,
    label: `프리미엄 상품 ${(i + 1).toString().padStart(Math.max(2, String(count).length), '0')}`,
    icon: emoji[i % emoji.length]
  }))
}

const summarizeSelection = ({ values, getLabel }: { values: string[] | string; getLabel: (v: string) => string }) => {
  const arr = Array.isArray(values) ? values : values ? [values] : []
  if (arr.length === 0) return '선택하세요'
  const MAX = 3
  const labels = arr.map(v => getLabel(v))
  return labels.length <= MAX ? labels.join(', ') : `${labels.slice(0, MAX).join(', ')} 외 ${labels.length - MAX}개`
}

const filterByKeyword = <T extends { label: string; code: string }>(items: T[], kw: string) => {
  const k = kw.trim().toLowerCase()
  if (!k) return items
  return items.filter(p => p.label.toLowerCase().includes(k) || p.code.toLowerCase().includes(k))
}

// User card layout with avatars and status
export const Example1: Story = {
  parameters: {
    docs: {
      description: {
        story: `
User selection with rich profile cards including avatars, roles, contact information, and online status.

**Features Demonstrated:**
- Custom avatar generation with initials
- Status indicators (online/offline)
- Role and contact information display
- Responsive layout with proper truncation
- Visual selection feedback
        `
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-96">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          placeholder="Select a user"
        >
          <MopSelectOptions>
            <MopSelectOption 
              value="user1"
              render={({ active, selected }) => (
                <li className={`relative cursor-pointer select-none px-4 py-3 ${
                  active ? 'bg-blue-50 rounded' : ''
                } ${selected ? 'bg-blue-100 rounded font-medium' : ''}`}>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      김
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">Kim Chulsu</div>
                      <div className="text-xs text-gray-500 truncate">Frontend Developer</div>
                      <div className="text-xs text-blue-600 truncate"><EMAIL></div>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Online
                      </span>
                      {selected && (
                        <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                </li>
              )}
            >
              Kim Chulsu
            </MopSelectOption>
            
            <MopSelectOption 
              value="user2"
              render={({ active, selected }) => (
                <li className={`relative cursor-pointer select-none px-4 py-3 ${
                  active ? 'bg-blue-50 rounded' : ''
                } ${selected ? 'bg-blue-100 rounded font-medium' : ''}`}>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-purple-400 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                      박
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">Park Younghee</div>
                      <div className="text-xs text-gray-500 truncate">Backend Developer</div>
                      <div className="text-xs text-blue-600 truncate"><EMAIL></div>
                    </div>
                    <div className="flex flex-col items-end space-y-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        Offline
                      </span>
                      {selected && (
                        <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                  </div>
                </li>
              )}
            >
              Park Younghee
            </MopSelectOption>
          </MopSelectOptions>
        </MopSelect>
      </div>
    )
  }
}

// Product cards with pricing and ratings
export const Example2: Story = {
  parameters: {
    docs: {
      description: {
        story: `
Product selection with rich product cards including pricing, descriptions, ratings, and promotional badges.

**Features Demonstrated:**
- Product imagery with emoji icons
- Pricing information display
- Star ratings with numeric scores
- Promotional badges (Popular, Recommended)
- Multi-line descriptions with proper truncation
- Border highlights for selected items
        `
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          placeholder="Select a product"
        >
          <MopSelectOptions maxHeight="max-h-80">
            <MopSelectOption 
              value="product1"
              render={({ active, selected }) => (
                <li className={`relative cursor-pointer select-none p-4 ${
                  active ? 'bg-gray-50' : ''
                } ${selected ? 'bg-blue-50 border-l-4 border-blue-500' : ''}`}>
                  <div className="flex items-start space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-red-600 rounded-lg flex items-center justify-center text-white font-bold">
                      📱
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-semibold text-gray-900 truncate">Mobile App</h4>
                        <span className="text-xs font-medium text-green-600">$29.00</span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                        User-friendly mobile app solution optimized for mobile environments.
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          Popular
                        </span>
                        <div className="flex items-center">
                          <span className="text-yellow-400">★★★★★</span>
                          <span className="text-xs text-gray-500 ml-1">(4.8)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              )}
            >
              Mobile App
            </MopSelectOption>

            <MopSelectOption 
              value="product2"
              render={({ active, selected }) => (
                <li className={`relative cursor-pointer select-none p-4 ${
                  active ? 'bg-gray-50' : ''
                } ${selected ? 'bg-blue-50 border-l-4 border-blue-500' : ''}`}>
                  <div className="flex items-start space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center text-white font-bold">
                      💻
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-semibold text-gray-900 truncate">Web Platform</h4>
                        <span className="text-xs font-medium text-green-600">$49.00</span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                        Powerful web platform with cross-browser compatibility support.
                      </p>
                      <div className="flex items-center mt-2 space-x-2">
                        <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-purple-100 text-purple-800">
                          Recommended
                        </span>
                        <div className="flex items-center">
                          <span className="text-yellow-400">★★★★☆</span>
                          <span className="text-xs text-gray-500 ml-1">(4.5)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              )}
            >
              Web Platform
            </MopSelectOption>
          </MopSelectOptions>
        </MopSelect>
      </div>
    )
  }
}

// Complex: Checkbox + Search + Group header + Sticky tools
export const CheckboxSearchGrouped: Story = {
  parameters: {
    docs: {
      description: {
        story: `
멀티 선택을 위한 체크박스와 검색박스(MuiInput), 상단 고정 툴바(모두 선택/모두 해제/결과 수), 그룹 헤더(NAVER 배지)를 포함한 복합 예제입니다.
- 검색은 라벨/코드에 대해 부분일치 필터
- "현재 목록 모두 선택"은 필터된 결과에만 적용
- 선택 요약은 최대 3개 라벨 표시 후 "외 N개"
        `
      }
    }
  },
  render: () => {
    const products = makeProducts(48)

    const [selected, setSelected] = useState<string[]>([])
    const [kw, setKw] = useState('')
    const toolsRef = useRef<HTMLDivElement | null>(null)
    const [toolsH, setToolsH] = useState(0)
    const [scrollTop, setScrollTop] = useState(0)
    const [itemH, setItemH] = useState(56)

    useEffect(() => {
      if (toolsRef.current) {
        setToolsH(toolsRef.current.getBoundingClientRect().height)
      }
    }, [])

    const filtered = useMemo(() => filterByKeyword(products, kw), [kw, products])

    const visibleValues = filtered.map(p => p.value)
    const allSelectedVisible = visibleValues.length > 0 && visibleValues.every(v => selected.includes(v))
    const someSelectedVisible = visibleValues.some(v => selected.includes(v)) && !allSelectedVisible

    const toggleAllVisible = () => {
      if (allSelectedVisible) {
        setSelected(prev => prev.filter(v => !visibleValues.includes(v)))
      } else {
        setSelected(prev => Array.from(new Set([...prev, ...visibleValues])))
      }
    }

    const clearAll = () => setSelected([])

    const rows = useMemo(() => filtered.map((_, idx) => ({ key: filtered[idx].value, index: idx })), [filtered])

    const headerTools = (
      <div ref={toolsRef} className="sticky top-0 z-10 bg-white border-b border-[#efefef]">
        <div className="px-4 pt-3 pb-2">
          <MuiInput
            placeholder="상품 검색하기 (이름/코드)"
            value={kw}
            onChange={(e) => setKw(e.target.value)}
          />
        </div>
        <div className="px-4 py-2 flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              readOnly
              checked={allSelectedVisible}
              ref={(el) => { if (el) el.indeterminate = someSelectedVisible }}
              onClick={toggleAllVisible}
            />
            <button className="text-[#333]" onClick={toggleAllVisible}>현재 목록 모두 선택</button>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-[#707070]">결과 {filtered.length}개</span>
            <button className="text-blue-600" onClick={clearAll}>모두 해제</button>
          </div>
        </div>
        {(() => {
          if (filtered.length === 0) return null
          const contentScroll = Math.max(0, scrollTop - toolsH)
          const firstIndex = Math.min(filtered.length - 1, Math.floor(contentScroll / Math.max(1, itemH)))
          const start = Math.floor(firstIndex / 10) * 10 + 1
          const end = Math.min(start + 9, filtered.length)
          const label = `${start}~${end}`
          return (
          <div className="px-4 py-3 border-t border-[#e7e7e7] bg-[#f9fbff] text-[#334155] font-semibold tracking-wide">
            {label}
          </div>
          )
        })()}
      </div>
    )

    return (
      <div className="w-[520px]">
        <MopSelect
          multiple
          value={selected}
          onChange={(val) => setSelected(val as string[])}
          placeholder="상품 검색 후 선택하세요"
          renderSelected={summarizeSelection}
        >
          <MopSelectVirtual
            rowCount={rows.length}
            estimateSize={itemH}
            overscan={12}
            getRowKey={(i) => rows[i].key}
            maxHeight="max-h-[420px]"
            header={headerTools}
            viewportHeight={420}
            onScroll={(st) => setScrollTop(st)}
            onMeasureItemHeight={(h) => {
              if (h > 0 && Math.abs(h - itemH) > 1) setItemH(Math.round(h))
            }}
            renderRow={(i) => {
              const row = rows[i]
              const item = filtered[row.index]
              return (
                <MopSelectOption key={item.value} value={item.value} render={({ active, selected: isChecked }) => (
                  <li
                    className={`list-none relative cursor-pointer select-none px-4 py-3 transition-colors ${active ? 'bg-[#f9f9fb]' : ''} ${isChecked ? 'bg-[#f9f9fb]' : ''}`}
                  >
                    <div className="grid grid-cols-[20px,56px,1fr] items-center gap-3">
                      <input type="checkbox" readOnly checked={isChecked} />
                      <div className="w-14 h-14 rounded bg-[#f3f4f6] flex items-center justify-center text-xl">{item.icon}</div>
                      <div className="min-w-0">
                        <div className="text-xs text-[#707070]">{item.code}</div>
                        <div className="text-sm text-[#333] truncate">{item.label}</div>
                      </div>
                    </div>
                  </li>
                )}>
                  {item.label}
                </MopSelectOption>
              )
            }}
          />
        </MopSelect>

        <div className="mt-3 text-sm text-gray-600 flex items-center gap-3">
          <span>선택됨: {selected.length}개</span>
          {selected.length > 0 && (
            <button className="text-blue-600" onClick={() => setSelected([])}>전체 해제</button>
          )}
        </div>
      </div>
    )
  }
}

// Virtualized version of CheckboxSearchGrouped using @tanstack/react-virtual
export const CheckboxSearchGroupedVirtual: Story = {
  parameters: {
    docs: {
      description: {
        story: `
대용량(최대 1만개) 목록에 대응하기 위해 @tanstack/react-virtual 기반으로 가상 스크롤을 적용한 예제입니다.
- 검색은 라벨/코드에 대해 부분일치 필터
- "현재 목록 모두 선택"은 필터된 결과에만 적용
- 10개 단위로 그룹 헤더(예: 1~10, 11~20, ...)
- 선택 요약은 최대 3개 라벨 표시 후 "외 N개"
        `
      }
    }
  },
  render: () => {
    const products = makeProducts(1000)

    const [selected, setSelected] = useState<string[]>([])
    const [kw, setKw] = useState('')
    const toolsRef = useRef<HTMLDivElement | null>(null)
    const [toolsH, setToolsH] = useState(0)
    const [scrollTop, setScrollTop] = useState(0)
    const [itemH, setItemH] = useState(56)

    useEffect(() => {
      if (toolsRef.current) {
        setToolsH(toolsRef.current.getBoundingClientRect().height)
      }
    }, [])

    const filtered = useMemo(() => filterByKeyword(products, kw), [kw, products])

    const visibleValues = filtered.map(p => p.value)
    const allSelectedVisible = visibleValues.length > 0 && visibleValues.every(v => selected.includes(v))
    const someSelectedVisible = visibleValues.some(v => selected.includes(v)) && !allSelectedVisible

    const toggleAllVisible = () => {
      if (allSelectedVisible) {
        setSelected(prev => prev.filter(v => !visibleValues.includes(v)))
      } else {
        setSelected(prev => Array.from(new Set([...prev, ...visibleValues])))
      }
    }

    const clearAll = () => setSelected([])

    type Row = { key: string; index: number }

    const rows: Row[] = useMemo(() => filtered.map((_, idx) => ({ key: filtered[idx].value, index: idx })), [filtered])

    const headerTools = (
      <div ref={toolsRef} className="sticky top-0 z-10 bg-white border-b border-[#efefef]">
        <div className="px-4 pt-3 pb-2">
          <MuiInput
            placeholder="상품 검색하기 (이름/코드)"
            value={kw}
            onChange={(e) => setKw(e.target.value)}
          />
        </div>
        <div className="px-4 py-2 flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              readOnly
              checked={allSelectedVisible}
              ref={(el) => { if (el) el.indeterminate = someSelectedVisible }}
              onClick={toggleAllVisible}
            />
            <button className="text-[#333]" onClick={toggleAllVisible}>현재 목록 모두 선택</button>
          </div>
          <div className="flex items-center gap-3">
            <span className="text-[#707070]">결과 {filtered.length}개</span>
            <button className="text-blue-600" onClick={clearAll}>모두 해제</button>
          </div>
        </div>
        {(() => {
          if (filtered.length === 0) return null
          const contentScroll = Math.max(0, scrollTop - toolsH)
          const firstIndex = Math.min(filtered.length - 1, Math.floor(contentScroll / Math.max(1, itemH)))
          const start = Math.floor(firstIndex / 10) * 10 + 1
          const end = Math.min(start + 9, filtered.length)
          const label = `${start}~${end}`
          return (
          <div className="px-4 py-3 border-t border-[#e7e7e7] bg-[#f9fbff] text-[#334155] font-semibold tracking-wide">
            {label}
          </div>
          )
        })()}
      </div>
    )

    return (
      <div className="w-[520px]">
        <MopSelect
          multiple
          value={selected}
          onChange={(val) => setSelected(val as string[])}
          placeholder="상품 검색 후 선택하세요"
          renderSelected={summarizeSelection}
        >
          <MopSelectVirtual
            rowCount={rows.length}
            estimateSize={itemH}
            overscan={12}
            getRowKey={(i) => rows[i].key}
            maxHeight="max-h-[420px]"
            header={headerTools}
            viewportHeight={420}
            onScroll={(st) => setScrollTop(st)}
            onMeasureItemHeight={(h) => {
              if (h > 0 && Math.abs(h - itemH) > 1) setItemH(Math.round(h))
            }}
            renderRow={(i) => {
              const row = rows[i]
              const item = filtered[row.index]
              return (
                <MopSelectOption key={item.value} value={item.value} render={({ active, selected: isChecked }) => (
                  <li
                    className={`list-none relative cursor-pointer select-none px-4 py-3 transition-colors ${active ? 'bg-[#f9f9fb]' : ''} ${isChecked ? 'bg-[#f9f9fb]' : ''}`}
                  >
                    <div className="grid grid-cols-[20px,56px,1fr] items-center gap-3">
                      <input type="checkbox" readOnly checked={isChecked} />
                      <div className="w-14 h-14 rounded bg-[#f3f4f6] flex items-center justify-center text-xl">{item.icon}</div>
                      <div className="min-w-0">
                        <div className="text-xs text-[#707070]">{item.code}</div>
                        <div className="text-sm text-[#333] truncate">{item.label}</div>
                      </div>
                    </div>
                  </li>
                )}>
                  {item.label}
                </MopSelectOption>
              )
            }}
          />
        </MopSelect>

        <div className="mt-3 text-sm text-gray-600 flex items-center gap-3">
          <span>선택됨: {selected.length}개</span>
          {selected.length > 0 && (
            <button className="text-blue-600" onClick={() => setSelected([])}>전체 해제</button>
          )}
        </div>
      </div>
    )
  }
}
