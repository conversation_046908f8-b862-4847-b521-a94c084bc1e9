#AdEfficiencyResultTable {
  table {
    color: var(--point_color);
    font-size: 12px;
  }

  thead {
    border: none;

    th {
      padding: 0;
      height: 48px;
      background-color: var(--bg-gray-light);
      border-color: var(--gray-light);

      .MuiTableSortLabel-root {
        .MuiTableSortLabel-icon {
          display: none;
        }

        &:hover {
          color: var(--color-blue-darker) !important;
        }

        &.MuiTableSortLabel-active {
          color: var(--color-blue-darker);

          .MuiTableSortLabel-icon {
            position: relative;
            display: inline-block;
            width: 12px;
            height: 10px;
            opacity: 1;
            text-indent: -9999px;

            &::after {
              content: '';
              display: inline-block;
              width: 0;
              height: 0;
              position: absolute;
              top: 0;
              right: 0;
              border-style: solid;
              border-width: 10px 6px 0px 6px;
              border-color: var(--color-blue-darker) transparent transparent transparent;
            }
          }
        }
      }
    }
  }

  tbody {
    td {
      font-weight: 300;
      padding: 5px 3px;
      text-align: right;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid var(--gray-light);

      .creative-cell {
        padding-left: 15px;
        width: 260px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .MuiSwitch-edgeEnd,
      .mop-undefined-chip {
        margin: 0 auto;
      }
      .MuiSwitch-root {
        width: 32px;
        height: auto;
        padding: 0;
        margin: 0 auto;
        align-items: center;
        display: flex;

        .MuiSwitch-switchBase {
          left: 2px;
          padding: 0;

          .MuiSwitch-thumb {
            position: relative;
            top: 1px;
            width: 12px;
            height: 12px;
            padding: 0;
            background-color: #ffffff;
            opacity: 1;
            box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
          }

          &.Mui-checked {
            transform: translateX(18px);
          }
        }

        .MuiSwitch-track {
          width: 30px;
          height: 15px;
          padding: 0;
          border-radius: 10px;
          background-color: #707395;
          opacity: 1;
          border: 1px solid #707395;
        }

        .Mui-checked {
          left: -1px;
          & + .MuiSwitch-track {
            background-color: var(--status-active);
            opacity: 1;
            border: 1px solid var(--status-active);
          }
        }

        .Mui-disabled {
          .MuiSwitch-thumb {
            background-color: var(--color-disabled);
            opacity: 1;
          }

          & + .MuiSwitch-track {
            background-color: #fff;
            opacity: 1;
            border: 1px solid #959595;
          }
        }
      }

      .color-on {
        font-weight: 400;
        color: var(--status-active);
      }
    }

    td:last-child {
      border-right: none;
    }
  }
}
