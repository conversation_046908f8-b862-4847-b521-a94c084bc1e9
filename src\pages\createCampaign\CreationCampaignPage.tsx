import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRecoilValue } from 'recoil'
import { useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import CampaignBasicInformation from '@components/createCampaign/CampaignBasicInformation'
import CampaignCompleteModal from '@components/createCampaign/CompletedCamPaignModal'
import MediaAccountChannelSection from '@components/createCampaign/MediaAccountChannelSection'
import ProductListFilterControl from '@components/createCampaign/ProductListFilterControl'
import { useToast } from '@hooks/common'
import {
  BusinessChannel,
  BusinessChannelStatus,
  CampaignBasicInfo,
  CampaignFormData,
  CampaignParams,
  DeviceTypeEnum,
  FormErrors,
  MAX_BUDGET,
  MAX_PRODUCTS,
  MIN_BUDGET,
  NaverAdsNaverCommerceAccount,
  ProductData,
  UseDailyBudgetTypeEnum
} from '@models/createCampaign/CreateCampaign'
import { advertiserState } from '@store/Advertiser'
import './CreationCampaignPage.scss'
import { StatusCode } from '@models/common'
import { createCampaign } from '@api/creationCampaign'
import { validateCampaignName } from '@utils/ValidateUtil'

const initialMediaAccount: NaverAdsNaverCommerceAccount = {
  customerId: '',
  customerName: '',
  sellerAccountId: '',
  sellerAccountName: ''
}

const initialChannel: BusinessChannel = {
  businessChannelId: '',
  businessChannelName: '',
  status: BusinessChannelStatus.ELIGIBLE
}

const initialBasicInfo: CampaignBasicInfo = {
  campaignName: '',
  useDailyBudget: UseDailyBudgetTypeEnum.UNLIMITED,
  deviceType: DeviceTypeEnum.ALL,
  dailyBudget: 0
}

const initialForm: CampaignFormData = {
  basicInfo: initialBasicInfo,
  mediaAccount: initialMediaAccount,
  businessChannel: initialChannel,
  selectedProducts: []
}

const initialFormErrors: FormErrors = {
  campaignName: '',
  dailyBudget: ''
}
const CreationCampaignPage = () => {
  const { t } = useTranslation()
  const advertiser = useRecoilValue(advertiserState)
  const [openConfirmModal, setOpenConfirmModal] = useState(false)
  const [completedCampaign, setCompletedCampaign] = useState<CampaignFormData>(initialForm)
  const [mediaSelected, setMediaSelected] = useState<NaverAdsNaverCommerceAccount>(initialMediaAccount)
  const [channelSelected, setChanelSelected] = useState<BusinessChannel>(initialChannel)
  const [basicInfo, setBasicInfo] = useState<CampaignBasicInfo>(initialBasicInfo)
  const selectedProductsRef = useRef<ProductData[]>([])
  const isCreateButtonEnabled = mediaSelected.customerId !== '' && channelSelected.businessChannelId !== ''
  const { openToast } = useToast()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const onSetSelectedProducts = useCallback((products: ProductData[]) => {
    selectedProductsRef.current = products
  }, [])

  const [formErrors, setFormErrors] = useState<FormErrors>(initialFormErrors)
  const isFirstRender = useRef(true);

  const validateCampaignForm = (): FormErrors => {
    const newErrors: FormErrors = {
      campaignName: '',
      dailyBudget: ''
    };

    if (!basicInfo.campaignName?.trim()) {
      newErrors.campaignName = 'createCampaign.validation.enterCampaignName'
    }

    if (!validateCampaignName(basicInfo.campaignName)) {
      newErrors.campaignName = 'createCampaign.validation.validateName'
    }

    if (basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.LIMITED) {
      if (!basicInfo.dailyBudget || basicInfo.dailyBudget < MIN_BUDGET || basicInfo.dailyBudget % 10 !== 0 || basicInfo.dailyBudget > MAX_BUDGET) {
        newErrors.dailyBudget = 'createCampaign.validation.validateBudget'
      }
    }
    return newErrors
  }

  const handleCreateButtonClick = () => {
    const validationErrors = validateCampaignForm()
    const hasErrors = Object.values(validationErrors).some((msg) => msg !== "");

    if (hasErrors) {
      setFormErrors(validationErrors);
      return;
    }
    setFormErrors({
      campaignName: '',
      dailyBudget: ''
    })

    if (!mediaSelected?.customerId) {
      openToast(t('createCampaign.validation.selectMediaAccount'))
      return
    }
    if (!channelSelected?.businessChannelId) {
      openToast(t('createCampaign.validation.selectChannel'))
      return
    }

    if (selectedProductsRef.current.length === 0) {
      openToast(t('createCampaign.validation.selectProducts'))
      return
    }

    if (selectedProductsRef.current.length > MAX_PRODUCTS) {
      openToast(t('createCampaign.createModal.toast.maxProducts'))
      return
    }

    setCompletedCampaign({
      basicInfo: basicInfo,
      mediaAccount: mediaSelected,
      businessChannel: channelSelected,
      selectedProducts: selectedProductsRef.current
    })
    setOpenConfirmModal(true)
  }

  const handleConfirmButtonClick = async () => {
    const productIds = completedCampaign.selectedProducts.map((p) => p.productId)

    const campaignParams: CampaignParams = {
      advertiserId: advertiser.advertiserId.toString(),
      bizChannelId: completedCampaign.businessChannel.businessChannelId,
      customerId: completedCampaign.mediaAccount.customerId,
      campaignName: completedCampaign.basicInfo.campaignName,
      useDailyBudget: completedCampaign.basicInfo.useDailyBudget,
      dailyBudget:
        completedCampaign.basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.UNLIMITED
          ? 0 : completedCampaign.basicInfo.dailyBudget || 0,
      device: completedCampaign.basicInfo.deviceType,
      products: productIds
    }

      try {
        const response = await createCampaign(campaignParams)
        if (response.statusCode === StatusCode.SUCCESS) {
          openToast(t('createCampaign.createModal.toast.completed'))
          queryClient.invalidateQueries({ queryKey: ['campaignList'] })
        } else {
          openToast(t('common.message.systemError'))
        }
        setOpenConfirmModal(false)
        navigate('/campaign')
      } catch (error) {
        openToast(t('common.message.systemError'))
      }
  }

  return (
    <>
      <div className="CreationCampaignPage" id="createFormPage">
        <div className="left-site-create-new-campaign">
          <div className="flex items-center justify-between mb-5">
            <div className="text-[28px] font-bold text-[#171717] leading-normal font-pretendard">
              {t('createCampaign.createModal.title')}
            </div>
          </div>
          <MediaAccountChannelSection
            mediaSelected={mediaSelected}
            setMediaSelected={setMediaSelected}
            channelSelected={channelSelected}
            setChannelSelected={setChanelSelected}
          />
          <div className="flex-grow flex flex-col">
            <ProductListFilterControl
              mediaSelected={mediaSelected}
              chanelSelected={channelSelected}
              onSetSelectedProducts={onSetSelectedProducts}
            />
          </div>
        </div>

        <div className="right-site-create-new-campaign">
          <CampaignBasicInformation
            handleCreateButtonClick={handleCreateButtonClick}
            formErrors={formErrors}
            setFormErrors={setFormErrors}
            basicInfo={basicInfo}
            setBasicInfo={setBasicInfo}
            disableButton={!isCreateButtonEnabled}
          />
        </div>
      </div>

      <CampaignCompleteModal
        open={openConfirmModal}
        handleModalSubmit={handleConfirmButtonClick}
        onClose={() => {
          setOpenConfirmModal(false)
        }}
        campaignDetail={completedCampaign}
      />
    </>
  )
}

export default CreationCampaignPage
