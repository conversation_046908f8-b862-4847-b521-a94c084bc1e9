.chart-legend-more {
  position: relative;
  width: 100%;
  height: 1px;
  border-top: 1px solid var(--point_color);

  .expand-more {
    position: absolute;
    top: -9px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    .MuiButtonBase-root {
      padding: 0px;
      width: 51px;
      min-width: 51px;
      height: 15px;
      font-size: 7px;
      font-weight: 350;
      color: var(--color-white);
      line-height: 14px;
      background-color: var(--point_color);
      border-radius: 25px;
    }
  }

  .legend-list {
    overflow-x: hidden;
    overflow-y: scroll;
    padding: 12px 0 6px;
    max-height: 242px;
    background-color: #fff;
    border: 1px solid #888ba6;
    border-top: 0;
    box-shadow: 10px 10px 10.79px 2.21px rgba(0, 0, 0, 0.14);

    .chart-legend-row {
      padding-right: 11px !important;
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
      width: 8px;
      border-radius: 8px;
      border: 4px solid transparent;
    }
    &::-webkit-scrollbar-track {
      background-color: transparent;
      width: 8px;
    }
  }
}
