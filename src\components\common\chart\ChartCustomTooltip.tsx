
import React from 'react';

import './ChartCustomTooltip.scss';
import { UnitType } from '@models/common/ChartData';

export interface TooltipOption {
  showLabel?: boolean;
  showValue?: boolean;
  showTitle?: boolean;
  showRatio?: boolean;
  showIndexColor?: boolean;
  valueUnit?: UnitType;
  customizeLabel?: (label: string) => string;
  customizeValue?: (value: string) => string;
  customizeTitle?: (title: string) => string;
}
interface Props {
  left: string;
  top: string;
  label: string;
  ratio?: number|string;
  value: string;
  title?: string;
  indexColor?: string;
  option?: TooltipOption;
  tooltipId?: string;
}
const ChartCustomTooltip: React.FC<Props> = ({
  left,
  top,
  label,
  ratio,
  value,
  title,
  indexColor,
  tooltipId,
  option = { showLabel: true, showValue: true, showTitle: true, showRatio: false, showIndexColor: true, unitType: 'PERCENT' },
}) => {
  const {
    showLabel = true,
    showValue = true,
    showTitle = true,
    showRatio = false,
    showIndexColor = true,
    valueUnit = 'PERCENT',
    customizeLabel,
    customizeValue,
    customizeTitle,
  } = option;

  return (
    <span id={tooltipId ?? "ChartCustomTooltip"} style={{ left: left, top: top }}>
      {showIndexColor && indexColor && (
        <span id="ChartCustomTooltipColorChip" style={{ backgroundColor: indexColor }}></span>
      )}
      {showTitle && title && (
        <span id="ChartCustomTooltipXTitle">{customizeTitle ? customizeTitle(title) : title}</span>
      )}
      {showLabel && label && (
        <span id="ChartCustomTooltipLabel" className={showValue ? 'right-padded' : ''}>
          {customizeLabel ? customizeLabel(label) : label}
        </span>
      )}
      {showRatio && ratio && (
        <span id="ChartCustomTooltipLabel" className={showValue ? 'right-padded' : ''}>
          {`${ratio}%`}
        </span>
      )}
      {showValue && valueUnit === 'PERCENT' && (
        <span id="ChartCustomTooltipValue">{customizeValue ? customizeValue(value) : value}%</span>
      )}
      {showValue && valueUnit === 'NONE' && (
        <span id="ChartCustomTooltipValue">{customizeValue ? customizeValue(value) : value}</span>
      )}
    </span>
  );
};

export default ChartCustomTooltip;
