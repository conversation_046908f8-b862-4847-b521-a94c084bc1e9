// src/components/common/buttons/MopButton.scss
.mop-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.2s ease-in-out;
  outline: none;
  position: relative;
  
  // CSS 변수 기본값
  font-size: var(--button-font-size, 12px);
  padding: var(--button-padding, 8px 16px);
  border-radius: var(--button-radius, 9999px);
  width: var(--button-width, auto);
  height: var(--button-height, auto);
  background-color: var(--button-bg-color, #ffffff);
  color: var(--button-text-color, #5472FF);
  border: 1px solid var(--button-border-color, transparent);

  // Contained 스타일 (기본)
  &.contained {
    background-color: var(--button-bg-color, #5472FF);
    color: var(--button-text-color, #ffffff);
    border: 1px solid var(--button-border-color, transparent);

    &:hover:not(.disabled) {
      background-color: var(--button-bg-color, #5472FF);
      transform: translateY(-1px);
    }

    &:active:not(.disabled) {
      transform: translateY(0);
    }
  }

  // Outlined 스타일
  &.outlined {
    background-color: transparent;
    color: var(--button-text-color, #5472FF);
    border: 1px solid var(--button-border-color, #5472FF);

    &:hover:not(.disabled) {
      background-color: var(--button-bg-color, #ffffff);
      color: var(--button-text-color, #5472FF);
    }
  }

  // 비활성화 상태
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  // 아이콘 스타일
  .button-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .mop-icon-box {
    flex-shrink: 0;
    padding: 0px;
  }

  // 라벨 스타일
  .button-label {
    white-space: nowrap;
    font-weight: inherit;
  }

  // 포커스 상태
  &:focus-visible {
    outline: 2px solid #5472FF;
    outline-offset: 2px;
  }
} 