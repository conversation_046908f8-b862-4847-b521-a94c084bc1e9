import { ReactComponent as DeleteIcon } from '@components/assets/images/icon_delete.svg';
import { ReactComponent as EditIcon } from '@components/assets/images/icon_edit.svg';
import './ColumnSettingButtons.scss'

interface Props {
  handleEdit: () => void
  handleDelete: () => void
  editGtmId?: string
  deleteGtmId?: string
}
const ColumnSettingButtons = ({ handleEdit, handleDelete, editGtmId, deleteGtmId }: Props) => {
  return (
    <div className="column-setting-buttons">
      <span className="icon-button">
        <EditIcon onClick={handleEdit} data-gtm-id={editGtmId} />
      </span>
      <span className="icon-button">
        <DeleteIcon onClick={handleDelete} data-gtm-id={deleteGtmId} />
      </span>
    </div>
  )
}

export default ColumnSettingButtons