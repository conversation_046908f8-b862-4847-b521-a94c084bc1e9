// src/components/common/mopUI/MopSelectOption.tsx
import React, { Fragment } from 'react'
import { Listbox } from '@headlessui/react'
import { cn } from '@utils/index'

interface MopSelectOptionProps {
  value: string
  children: React.ReactNode
  disabled?: boolean
  className?: string
  icon?: React.ReactNode
  render?: (props: {
    active: boolean
    selected: boolean
    disabled: boolean
    children: React.ReactNode
    icon?: React.ReactNode
  }) => React.ReactNode
}

const MopSelectOption = React.forwardRef<HTMLLIElement, MopSelectOptionProps>(
  ({ value, children, disabled = false, className, icon, render }, ref) => {
    return (
      <Listbox.Option value={value} disabled={disabled} as={Fragment}>
        {({ active, selected }) => {
          // 커스텀 렌더러가 있으면 사용
          if (render) {
            return render({
              active,
              selected,
              disabled,
              children,
              icon
            })
          }

          // 기본 렌더링
          return (
            <li
              ref={ref}
              className={cn(
                'relative cursor-pointer select-none px-5 py-2',
                'hover:bg-[#f9f9fb] hover:rounded transition-colors',
                {
                  'bg-[#f9f9fb] rounded': active,
                  'bg-[#f9f9fb] rounded font-bold': selected,
                  'opacity-50 cursor-not-allowed': disabled
                },
                className
              )}
            >
              <div className="flex items-center gap-2">
                {icon && <span className="w-4 flex-shrink-0">{icon}</span>}
                <div className="flex-1 min-w-0">
                  <span className="block truncate text-sm text-[#333]">
                    {children}
                  </span>
                </div>
                {selected && (
                  <span className="text-[#333] flex-shrink-0">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </span>
                )}
              </div>
            </li>
          )
        }}
      </Listbox.Option>
    )
  }
)

MopSelectOption.displayName = 'MopSelectOption'

export default MopSelectOption
