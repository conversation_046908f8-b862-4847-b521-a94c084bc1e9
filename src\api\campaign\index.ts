/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common'


export const requestCollectCampaign = async (mediaPath: string, accountId: string, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/campaigns/${mediaPath}/${accountId}`,
    method: Method.POST,
    config: { isLoading }
  });
  return response
};