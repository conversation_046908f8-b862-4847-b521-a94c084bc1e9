/* istanbul ignore file */

import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import { AxiosResponse } from 'axios';
import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import {
  GetReportDetailResponse,
  GetReportQueryParam,
  GetReportTableQueryParam,
  GetReportTableResponse,
  GetReportSummaryResponse,
} from '@models/report/SearchReport';
import { DVaMedia, DVaOptimization, DVaConfigure, UpdateDVaConfigurationsRequest } from '@models/report/Settings';
import { Report } from '@models/common/Report';

/* istanbul ignore next */
export const getDVaAdgroups = async (advertiserId: number, isLoading = true): Promise<DVaMedia[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/report/dva/adgroups',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data.media : []) as DVaMedia[];
};

/* istanbul ignore next */
export const getDVaOptimizations = async (advertiserId: number, isLoading = true): Promise<DVaOptimization[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/report/dva/optimizations',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data.optimizations : []) as DVaOptimization[];
};

/* istanbul ignore next */
export const getDVaConfiguration = async (reportId: string, isLoading = true): Promise<DVaConfigure> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/dva/${reportId}/configuration`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as DVaConfigure;
};

/* istanbul ignore next */
export const updateDVaConfiguration = async (
  reportId: string,
  param: UpdateDVaConfigurationsRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/report/dva/${reportId}/configuration`,
    method: Method.PATCH,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

/* istanbul ignore next */
export const downloadDVaRawData = async (reportId: string, param: GetReportTableQueryParam, isLoading = true) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/report/dva/${reportId}/raw-data`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
    params: {
      queryParams: {
        ...param,
      },
    },
  });

  if (response.data) {
    openDownloadLink(response);

    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};

/* istanbul ignore next */
export const getDVaReportDetail = async (
  reportId: string,
  param: GetReportQueryParam,
  isLoading = true
): Promise<GetReportDetailResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/dva/${reportId}/detail`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetReportDetailResponse;
};

/* istanbul ignore next */
export const getDVaReportSummary = async (
  reportId: string,
  param: GetReportQueryParam,
  isLoading = true
): Promise<GetReportSummaryResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/dva/${reportId}/summary`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetReportSummaryResponse;
};

/* istanbul ignore next */
export const getDVaReports = async (advertiserId: number, isLoading = true): Promise<Report[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/report/dva',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as Report[];
};

export const getDVaReportTable = async (
  reportId: string,
  param: GetReportTableQueryParam,
  isLoading = true
): Promise<GetReportTableResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/dva/${reportId}/table`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetReportTableResponse;
};
