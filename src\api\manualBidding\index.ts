/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import {
  GetTargetBidResponse,
  GetManualBidListQeury,
  CreateManualBidParams,
  ManualBidSettingItem,
  ManualBidSetting,
  UpdateBidItemYnParams,
  GetKeywordStatsQuery,
  ManualBidKeywordData
} from '@models/manualBidding'

export const getManualBidList = async (queryParams: GetManualBidListQeury, isLoading = true) => {
  return await callApi<GetTargetBidResponse>({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/shopping/ad',
    method: Method.GET,
    params: {
      queryParams: { ...queryParams }
    },
    config: { isLoading }
  })
}

export const createManualBidItem = async (bodyParams: CreateManualBidParams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/shopping/ad',
    method: Method.POST,
    params: { bodyParams },
    config: { isLoading }
  })
}

export const updateManualBidItem = async (monitoringId: number, bodyParams: ManualBidSetting, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/ad/${monitoringId}`,
    method: Method.PUT,
    params: { bodyParams },
    config: { isLoading }
  })
}

export const deleteManualBidItem = async (monitoringId: number, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/ad/${monitoringId}`,
    method: Method.DELETE,
    config: { isLoading }
  })
}

export const getManualBidDetailById = async (monitoringId: number, isLoading = true) => {
  return callApi<ManualBidSettingItem>({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/ad/${monitoringId}`,
    method: Method.GET,
    config: { isLoading }
  })
}

export const getManualBidKeywordStats = async (queryParams: GetKeywordStatsQuery, isLoading = true) => {
  return callApi<ManualBidKeywordData[]>({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/shopping/ad/keywordStats',
    method: Method.GET,
    params: { queryParams: { ...queryParams } },
    config: { isLoading }
  })
}

export const updateManualBidMonitoringYn = async (
  monitoringId: number,
  bodyParams: UpdateBidItemYnParams,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/ad/${monitoringId}`,
    method: Method.PATCH,
    params: { bodyParams },
    config: { isLoading }
  })
}
