import { Popover } from '@headlessui/react'
import { ReactComponent as CalendarIcon } from '@components/assets/images/icon_calendar_sm.svg'
import { ReactComponent as CaretDown } from '@components/assets/images/icon_caret_down.svg'
import RangeCalendar from '@components/common/RangeCalendar'
import { DateFnsFormat } from '@models/common/CommonConstants'
import { format } from 'date-fns'

interface Props {
  startDate: Date
  endDate: Date
  setStartDate: (value: Date) => void
  setEndDate: (value: Date) => void
  disabled?: boolean
}

const DateRangeButton = ({ startDate, endDate, setStartDate, setEndDate, disabled = false }: Props) => {
  return (
    <Popover>
      <Popover.Button
        id="date-range-button"
        disabled={disabled}
        className="flex items-center px-[10px] py-1.5 gap-2 disabled:opacity-50
          border border-blue-active focus:border-blue-active rounded-[3px] text-xs text-blue-active font-medium
        "
      >
        <CalendarIcon />
        <span>{format(startDate, DateFnsFormat.ISO_DATE)} ~ {format(endDate, DateFnsFormat.ISO_DATE)}</span>
        { disabled ? null : <CaretDown /> }
      </Popover.Button>
      <Popover.Panel className="absolute z-10 shadow-md">
        {({ close }) => (
          <RangeCalendar
            startDate={startDate}
            setStartDate={(date) => setStartDate(date)}
            endDate={endDate}
            setEndDate={(date) => setEndDate(date)}
            minDate={new Date('2023-09-01')}
            onClose={() => close()}
          />
        )}
      </Popover.Panel>
    </Popover>
  )
}

export default DateRangeButton