import Plotly from 'plotly.js-basic-dist-min';
import { YNFlag } from '@models/common/YNFlag'
import { t } from 'i18next';
import {
  AdEfficiencyAnalysisResultDetails,
  ChartIndicator
} from '@models/adEfficiency/AdEfficiency';

const desired_maximum_marker_size = 100;

export type AxisOption = {
  label: string
  value: any
}

export const indicatorOptions = () => {
  return Object.keys(ChartIndicator).map((indi) => ({
    label: indi,
    value: ChartIndicator[indi as keyof typeof ChartIndicator]
  }))
}

export const standardOptions = () => {
  return Object.keys(YNFlag).map((flag) => ({
    label: t(`optimization.label.adEfficiency.settingModal.standard.${flag}`),
    value: YNFlag[flag as keyof typeof YNFlag]
  }))
}

export const filterOptions = (selected: string[], options: AxisOption[]) => {
  return options.filter(opt => !selected.includes(opt.value))
}

interface LayoutParams {
  datarevision: number
  uirevision?: string|number
}


export interface ChartDataType {
  x: number[]
  y: number[]
  size: number[]
  label: string
  customdata: any[]
}

export interface TraceParams extends ChartDataType {
  uid: string;
  sizeref: number;
}

type ResultData = {
  [key in ChartIndicator]: number[]
} & { customdata: AdEfficiencyAnalysisResultDetails[] }

export const convertChartData = (result: AdEfficiencyAnalysisResultDetails[]) => {
  return result.reduce((allDetails, currentDetail) => {
    Object.values(ChartIndicator).forEach((key) => {
      if (allDetails[key]) {
        allDetails[key].push(Number(currentDetail[key]))
      } else {
        allDetails[key] = [Number(currentDetail[key])];
      }
    })
    if (allDetails.customdata) {
      allDetails.customdata.push(currentDetail)
    } else {
      allDetails.customdata = [currentDetail]
    }
    return allDetails
  }, {} as ResultData)
}

export type groupAnalysisKey = 'mediaType' | 'campaignId' | 'adgroupId' | 'onoff'
export const groupAnalysisByKeys = (details: AdEfficiencyAnalysisResultDetails[], key: groupAnalysisKey) => {
  return details.reduce((allDetails, currentDetail) => {
    if (allDetails[currentDetail[key]]) {
      allDetails[currentDetail[key]].push(currentDetail)
    } else {
      allDetails[currentDetail[key]] = [currentDetail]
    }
    return allDetails
  }, {} as {[key in string]: AdEfficiencyAnalysisResultDetails[]})
}

export const generateChartData = (rawData: ChartDataType[]): Plotly.Data[] => {
  const size = rawData.flatMap(item => item.size)
  const sizeref = 2.0 * Math.max(...size) / (desired_maximum_marker_size**2) || 1
  return rawData.map((data, index) => {
    return generateTrace({ ...data, uid: `trace-${index}`, sizeref: sizeref })
  })
}

export const generateTrace = ({
  x, y, label, size, uid, customdata, sizeref,
}: TraceParams) => {
  return {
    x, y,
    customdata,
    mode: 'markers',
    name: label,
    marker: {
      size: size[0] < sizeref ? [sizeref*25] : size,
      sizeref,
      sizemode: 'area',
      originalsize: size,
    },
    uid,
    hoverinfo: 'none'
  }
}

export const generateLayout = ({ datarevision, uirevision = 'true' }: LayoutParams): Partial<Plotly.Layout> => {
  return {
    showlegend: false,
    autosize: true,
    datarevision,
    uirevision,
    margin: {
      l: 50,
      r: 24,
      b: 48,
      t: 24,
      pad: 0
    },
    xaxis: {
      showgrid: false,
      ticks: "outside",
      showline: false,
      zeroline: true,
      linecolor: '#040A45',
      linewidth: 1,
      tickmode: 'array',
      tickcolor: 'transparent',
      tickfont: {
        color: '#040A45',
        size: 12
      },
      autorange: true
    } as any,
    yaxis: {
      showline: false,
      zeroline: true,
      showbackground: true,
      griddash: 'dash',
      linewidth: 1,
      linecolor: 'transparent',
      backgroundcolor: '#F2F3F6',
      tickfont: {
        color: '#040A45',
        size: 12
      },
      autorange: true
    } as any
  }
}