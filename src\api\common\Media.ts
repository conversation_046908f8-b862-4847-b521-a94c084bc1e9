/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import { MediaResponse } from '@models/common/Media';

export const getMediaByAdvertiserId = async (advertiserId: number): Promise<MediaResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/media/${advertiserId}`,
    method: Method.GET,
  });
  return (response.successOrNot === 'Y' ? response.data : null) as MediaResponse;
};
