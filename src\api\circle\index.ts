/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { YNFlag, Service } from '@models/common'
import * as CircleModel from '@models/circle';

export const getAuthorities = async (advertiserId: number, isLoading = true) => {
  const response: CommonResponse<CircleModel.AuthorityListItem[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/authorities?advertiserId=${advertiserId}`,
    method: Method.GET,
    config: { isLoading }
  });
  return response.successOrNot === 'Y' ? response.data : undefined
};

export const editAuthority = async (advertiserId: number, params: CircleModel.EditAuthorityQueryPrams) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `v1/circle/authorities?advertiserId=${advertiserId}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        ...params
      },
    },
  });

  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const deleteAuthority = async (params: CircleModel.DeleteAuthorityQueryPrams) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `v1/circle/authorities`,
    method: Method.DELETE,
    params: {
      queryParams: {
        ...params
      },
    },
  });

  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const createInvitation = async (params: CircleModel.CreateInvitationPrams) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/send`,
    method: Method.POST,
    params: {
      bodyParams: params,
    }
  });

  return response;
};

export const getInvitationItems = async (advertiserId: number, isLoading = true) => {
  const response: CommonResponse<CircleModel.InvitationItem[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/send?advertiserId=${advertiserId}`,
    method: Method.GET,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const deleteInvitationItems = async (invitationId: number, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/send/${invitationId}`,
    method: Method.DELETE,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getReceiveInvitationNum = async (isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/receive/total`,
    method: Method.GET,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const invitationCheckLater = () => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/receive/check-later`,
    method: Method.PUT
  });
}

export const getReceiveInvitation = async (isLoading = true) => {
  const response: CommonResponse<CircleModel.ReceiveInvitationItem[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/receive`,
    method: Method.GET,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const replyInvitation = (bodyParams: CircleModel.ReplyInvitationPrarams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/invitation/receive`,
    method: Method.PUT,
    params: { bodyParams },
    config: { isLoading }
  });
}

export const createCircle = async (params: CircleModel.CreateCirclePrarams, isLoading = true): Promise<CommonResponse<CircleModel.CreateCircleResponse>> => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/circle',
    method: Method.POST,
    params: {
      bodyParams: {
        ...params,
        useYn: YNFlag.Y
      },
    },
    config: { isLoading }
  });
};

export const editCircle = async (advertiserId: number, params: CircleModel.EditCirclePrarams, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/${advertiserId}`,
    method: Method.PATCH,
    params: {
      bodyParams: params,
    },
    config: { isLoading }
  })
}

export const getCircleList = async (isLoading = true) => {
  const response: CommonResponse<CircleModel.Circle[]> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/circle/list',
    method: Method.GET,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined
};

export const getExistingUnitList = async (advertiserId: number, isLoading = true) => {
  const response: CommonResponse<CircleModel.ExistingUnit[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}`,
    method: Method.GET,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined
};

export const createCircleUnit = async (advertiserId: string, bodyParams: CircleModel.CreateCircleUnit[], isLoading = true) => {
  const response: CommonResponse<CircleModel.ExistingUnit[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}`,
    method: Method.POST,
    params: { bodyParams },
    config: { isLoading }
  });

  return response
}

export const editCircleUnit = (advertiserId: string, unitId: number, bodyParams: CircleModel.EditCircleUnit, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}/units/${unitId}`,
    method: Method.PUT,
    params: { bodyParams: bodyParams },
    config: { isLoading }
  })
}

export const deleteCircleUnit = (advertiserId: string, unitId: number, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}/units/${unitId}`,
    method: Method.DELETE,
    config: { isLoading }
  })
}


export const getUnitAccountList = async (advertiserId: string, isLoading = true) => {
  const response: CommonResponse<CircleModel.UnitAccount[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}/accounts`,
    method: Method.GET,
    config: { isLoading }
  });

  return response.successOrNot === 'Y' ? response.data : undefined
};

export const getUnitCampaignList = async ({ accountId, advertiserId, mediaType, platformType }: CircleModel.UnitCampaignParams, isLoading = true) => {
  const response: CommonResponse<CircleModel.UnitCampaign[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}/accounts/${accountId}/campaigns`,
    method: Method.GET,
    params: {
      queryParams: {
        mediaType,
        platformType: platformType as string
      }
    },
    config: { isLoading }
  });

  return response.data ?? []
};

export const getConversionMetrics = async (advertiserId: string, queryParams: CircleModel.ConversionMetricQuery, isLoading = true) => {
  const response: CommonResponse<CircleModel.ConversionMetric[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}/conversion-metrics`,
    method: Method.GET,
    // @ts-ignore : FIXME
    params: { queryParams },
    config: { isLoading }
  });

  return response.data ?? [];
};

export const getConversionTools = async (advertiserId: string, isLoading = true) => {
  const response: CommonResponse<CircleModel.ConversionTool[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/unit/${advertiserId}/conversion-tools`,
    method: Method.GET,
    config: { isLoading }
  });

  return response.data ?? [];
};

export const updateCollectCampaign = async (bodyParams: CircleModel.UpdateCollectCampaignParams, isLoading = true) => {
  const response: CommonResponse<CircleModel.UnitCampaign[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/circle/collect/campaign`,
    method: Method.POST,
    params: { bodyParams },
    config: { isLoading }
  });

  return response
};


export default {
  createCircle,
  editCircle,
  getCircleList,
  getExistingUnitList,
  getReceiveInvitationNum,
  getReceiveInvitation,
  replyInvitation,
  createCircleUnit,
  editCircleUnit,
  deleteCircleUnit,
  getUnitAccountList,
  getUnitCampaignList,
  getAuthorities,
  deleteAuthority,
  createInvitation,
  getConversionMetrics,
  getConversionTools,
  updateCollectCampaign
}