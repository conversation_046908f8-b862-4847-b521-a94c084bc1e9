import {generateNaverCommerceBodyParams } from './naverCommerceReport';

describe('generateNaverCommerceBodyParams()', () => {
  test('✅ productIds is not empty → set only channelProductIds', () => {
    const result = generateNaverCommerceBodyParams(['p1', 'p2'], ['o1'])
    expect(result).toEqual({ channelProductIds: ['p1', 'p2'] })
  })

  test('✅ productIds is empty, optimizationIds is not empty → set only optimizationIds', () => {
    const result = generateNaverCommerceBodyParams([], ['o1', 'o2'])
    expect(result).toEqual({ optimizationIds: ['o1', 'o2'] })
  })

  test('✅ both arrays are empty → set optimizationIds and channelProductIds as empty arrays', () => {
    const result = generateNaverCommerceBodyParams([], [])
    expect(result).toEqual({
      optimizationIds: [],
      channelProductIds: []
    })
  })

  test('🔍 productIds is not empty and optimizationIds is empty → set only channelProductIds', () => {
    const result = generateNaverCommerceBodyParams(['p1'], [])
    expect(result).toEqual({ channelProductIds: ['p1'] })
  })
})