// src/components/common/BaseChip.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import BaseChip, { Badge, LiteBadge, ProBadge, NewBadge, BetaBadge } from './BaseChip';
import { MOPIcon } from '@models/common';

// BaseChip Meta
const meta: StorybookMeta<typeof BaseChip> = {
  title: 'Components/Common/BaseChip',
  component: BaseChip,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: { 
      control: 'text', 
      description: 'Text content displayed inside the chip' 
    },
    size: {
      control: 'select',
      options: ['mini', 'sm', 'md', 'lg'],
      description: 'Font size and overall scale of the chip',
    },
    bgColor: {
      control: 'color',
      description: 'Background color of the chip',
    },
    borderColor: {
      control: 'color',
      description: 'Border color of the chip',
    },
    textColor: {
      control: 'color',
      description: 'Text color of the chip content',
    },
    rounded: {
      control: 'select',
      options: ['sm', 'md', 'full'],
      description: 'Border radius style - sm (4px), md (8px), full (pill shape)',
    },
    width: { 
      control: 'text', 
      description: 'Custom width with units (e.g., "100px", "10rem")' 
    },
    height: { 
      control: 'text', 
      description: 'Custom height with units (e.g., "32px", "2rem")' 
    },
    customClass: {
      control: 'text',
      description: 'Additional CSS classes to apply',
    },
    leftIcon: {
      control: 'select',
      options: [undefined, ...Object.values(MOPIcon)],
      description: 'Icon to display on the left side of the chip',
    },
    rightIcon: {
      control: 'select',
      options: [undefined, ...Object.values(MOPIcon)],
      description: 'Icon to display on the right side of the chip',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// BaseChip Stories
export const Default: Story = {
  args: {
    label: 'BaseChip',
    size: 'md',
    rightIcon: MOPIcon.CHECK_CIRCLE,
  },
  parameters: {
    docs: {
      description: {
        story: 'The default BaseChip component with standard styling, medium size, and a check circle icon.',
      },
    },
  },
};

export const WithCustomColors: Story = {
  args: {
    label: 'Custom Chip',
    size: 'md',
    bgColor: '#4CAF50',
    textColor: '#FFFFFF',
    borderColor: '#2E7D32',
  },
  parameters: {
    docs: {
      description: {
        story: 'BaseChip with custom background, text, and border colors applied.',
      },
    },
  },
};

export const SizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Mini" size="mini" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Mini (8px)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Small" size="sm" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Small (10px)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Medium" size="md" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Medium (12px)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Large" size="lg" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Large (14px)</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates all available size variants of BaseChip from mini to large.',
      },
    },
  },
};

export const RoundedVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Small Rounded" rounded="sm" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Small (4px)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Medium Rounded" rounded="md" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Medium (8px)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <BaseChip label="Full Rounded" rounded="full" />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Full (pill)</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows different border radius options for BaseChip components.',
      },
    },
  },
};

export const WithCustomDimensions: Story = {
  args: {
    label: 'Custom Size',
    width: '120px',
    height: '40px',
    bgColor: '#FF9800',
    textColor: '#FFFFFF',
  },
  parameters: {
    docs: {
      description: {
        story: 'BaseChip with custom width and height dimensions specified.',
      },
    },
  },
};

export const WithLeftIcon: Story = {
  args: {
    label: 'Left Icon',
    size: 'md',
    leftIcon: MOPIcon.CHECK_CIRCLE,
  },
  parameters: {
    docs: {
      description: {
        story: 'BaseChip with a check circle icon positioned on the left side.',
      },
    },
  },
};

export const WithRightIcon: Story = {
  args: {
    label: 'Right Icon',
    size: 'md',
    rightIcon: MOPIcon.CHECK_CIRCLE,
  },
  parameters: {
    docs: {
      description: {
        story: 'BaseChip with a check circle icon positioned on the right side.',
      },
    },
  },
};

export const WithBothIcons: Story = {
  args: {
    label: 'Both Icons',
    size: 'md',
    leftIcon: MOPIcon.STAR,
    rightIcon: MOPIcon.CHECK_CIRCLE,
  },
  parameters: {
    docs: {
      description: {
        story: 'BaseChip with icons on both left and right sides.',
      },
    },
  },
};



// LiteBadge Stories
export const LiteBadgeDefault: StoryObj<typeof LiteBadge> = {
  render: (args) => <LiteBadge {...args} />,
  args: {
    size: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Purple badge indicating the Lite subscription plan.',
      },
    },
  },
};

export const LiteBadgeDisabled: StoryObj<typeof LiteBadge> = {
  render: (args) => <LiteBadge {...args} />,
  args: {
    disabled: true,
    size: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Disabled state of the Lite badge with gray coloring.',
      },
    },
  },
};

// ProBadge Stories
export const ProBadgeDefault: StoryObj<typeof ProBadge> = {
  render: (args) => <ProBadge {...args} />,
  args: {
    size: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Red badge indicating the Pro subscription plan.',
      },
    },
  },
};

export const ProBadgeDisabled: StoryObj<typeof ProBadge> = {
  render: (args) => <ProBadge {...args} />,
  args: {
    disabled: true,
    size: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Disabled state of the Pro badge with gray coloring.',
      },
    },
  },
};

// NewBadge Stories
export const NewBadgeDefault: StoryObj<typeof NewBadge> = {
  render: (args) => <NewBadge {...args} />,
  args: {
    size: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Blue badge used to highlight new features or content.',
      },
    },
  },
};

// BetaBadge Stories
export const BetaBadgeDefault: StoryObj<typeof BetaBadge> = {
  render: (args) => <BetaBadge {...args} />,
  args: {
    size: 'md',
  },
  parameters: {
    docs: {
      description: {
        story: 'Black badge used to indicate beta or experimental features.',
      },
    },
  },
};

// Generic Badge Stories
export const GenericBadgeDefault: StoryObj<typeof Badge> = {
  render: (args) => <Badge {...args}>Custom</Badge>,
  args: {
    size: 'md',
    className: 'bg-blue-500 text-white',
  },
  parameters: {
    docs: {
      description: {
        story: 'Generic badge component that can be customized with any content and styling.',
      },
    },
  },
};

export const GenericBadgeOutlined: StoryObj<typeof Badge> = {
  render: (args) => <Badge {...args}>Outlined</Badge>,
  args: {
    size: 'md',
    outlined: true,
    className: 'border-blue-500 text-blue-500',
  },
  parameters: {
    docs: {
      description: {
        story: 'Outlined variant of the generic badge with border styling.',
      },
    },
  },
};

// All Badges Showcase
export const AllBadgesShowcase: Story = {
  render: () => (
    <div>
      <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>Subscription Plan Badges</h3>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '16px',
        padding: '16px',
        marginBottom: '32px',
        border: '1px solid #e0e0e0',
        borderRadius: '8px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <LiteBadge size="md" />
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Lite Badge</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <ProBadge size="md" />
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Pro Badge</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <LiteBadge disabled size="md" />
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Lite Disabled</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <ProBadge disabled size="md" />
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Pro Disabled</div>
        </div>
      </div>

      <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>Feature Status Badges</h3>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '16px',
        padding: '16px',
        marginBottom: '32px',
        border: '1px solid #e0e0e0',
        borderRadius: '8px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <NewBadge size="md" />
          <div style={{ marginTop: '8px', fontSize: '12px' }}>New Badge</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <BetaBadge size="md" />
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Beta Badge</div>
        </div>
      </div>

      <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>Generic Badges</h3>
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: '16px',
        padding: '16px',
        border: '1px solid #e0e0e0',
        borderRadius: '8px'
      }}>
        <div style={{ textAlign: 'center' }}>
          <Badge size="md" className="bg-blue-500 text-white">Custom</Badge>
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Custom Badge</div>
        </div>
        <div style={{ textAlign: 'center' }}>
          <Badge size="md" outlined className="border-blue-500 text-blue-500">Outlined</Badge>
          <div style={{ marginTop: '8px', fontSize: '12px' }}>Outlined Badge</div>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive showcase of all available badge types organized by category.',
      },
    },
  },
};

// Size Variations for all badges
export const BadgeSizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px',
      padding: '16px'
    }}>
      {['mini', 'sm', 'md', 'lg'].map((size) => (
        <div key={size}>
          <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
            Size: {size}
          </h4>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '12px'
          }}>
            <LiteBadge size={size as any} />
            <ProBadge size={size as any} />
            <NewBadge size={size as any} />
            <BetaBadge size={size as any} />
            <Badge size={size as any} className="bg-gray-500 text-white">Custom</Badge>
          </div>
        </div>
      ))}
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates all badge components across different size variations.',
      },
    },
  },
};

// Interactive Badge Examples
export const InteractiveBadgeExamples: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px',
      padding: '16px'
    }}>
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Plan Comparison
        </h4>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px'
        }}>
          <span>Current Plan:</span>
          <LiteBadge size="sm" />
          <span>→ Upgrade to:</span>
          <ProBadge size="sm" />
        </div>
      </div>
      
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Feature Status
        </h4>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px',
          flexWrap: 'wrap'
        }}>
          <span>AI Assistant</span>
          <NewBadge size="mini" />
          <span>•</span>
          <span>Advanced Analytics</span>
          <BetaBadge size="mini" />
          <span>•</span>
          <span>Custom Reports</span>
          <Badge size="mini" className="bg-green-500 text-white">Stable</Badge>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Examples showing how badges can be used in real-world UI scenarios.',
      },
    },
  },
};

// Icon Showcase
export const IconShowcase: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px',
      padding: '16px'
    }}>
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Icon Positions
        </h4>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '16px',
          flexWrap: 'wrap'
        }}>
          <BaseChip label="Left Icon" leftIcon={MOPIcon.CHECK_CIRCLE} />
          <BaseChip label="Right Icon" rightIcon={MOPIcon.CHECK_CIRCLE} />
          <BaseChip label="Both Icons" leftIcon={MOPIcon.STAR} rightIcon={MOPIcon.CHECK_CIRCLE} />
        </div>
      </div>
      
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Different Icon Types
        </h4>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '16px',
          flexWrap: 'wrap'
        }}>
          <BaseChip label="Check Circle" rightIcon={MOPIcon.CHECK_CIRCLE} bgColor="#22C55E" textColor="#FFFFFF" />
          <BaseChip label="Star" rightIcon={MOPIcon.STAR} bgColor="#F59E0B" textColor="#FFFFFF" />
          <BaseChip label="Warning" rightIcon={MOPIcon.WARNING} bgColor="#EF4444" textColor="#FFFFFF" />
          <BaseChip label="Search" leftIcon={MOPIcon.SEARCH} bgColor="#3B82F6" textColor="#FFFFFF" />
        </div>
      </div>

      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Size Variations with Icons
        </h4>
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '16px',
          flexWrap: 'wrap'
        }}>
          <BaseChip label="Mini" size="mini" rightIcon={MOPIcon.CHECK_CIRCLE} />
          <BaseChip label="Small" size="sm" rightIcon={MOPIcon.CHECK_CIRCLE} />
          <BaseChip label="Medium" size="md" rightIcon={MOPIcon.CHECK_CIRCLE} />
          <BaseChip label="Large" size="lg" rightIcon={MOPIcon.CHECK_CIRCLE} />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive showcase of BaseChip with various icon configurations and combinations.',
      },
    },
  },
}; 