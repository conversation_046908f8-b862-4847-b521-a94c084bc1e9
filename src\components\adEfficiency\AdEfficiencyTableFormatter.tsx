import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { FixedLayoutColumn } from '@components/common/table'
import { AdEfficiencyListItem, AdEfficiencyStatus } from '@models/adEfficiency/AdEfficiency';
import CommonTooltip from '@components/common/CommonTooltip';
import TooltipCard from '@components/common/tooltip/TooltipCard';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import { ColumnSettingButtons } from '@components/common/buttons';
import { checkAuthority } from '@utils/AuthorityUtil';
import { convertDateToStr } from '@utils/DateUtil'
import { MIN_DATE_VALUE } from '@models/common/CommonConstants'
import { AuthorityType } from '@models/common/Advertiser';
import { useRecoilValue } from 'recoil';
import { advertiserState } from '@store/Advertiser';
import InnerHtml from '@components/common/InnerHtml';
import { withStyles } from '@material-ui/core/styles';

import { ReactComponent as ResultIcon } from '@components/assets/images/icon_optimization_result.svg';
import { ReactComponent as CopyIcon } from '@components/assets/images/icon_copy.svg';
import { ReactComponent as HistoryIcon } from '@components/assets/images/icon_optimization_history.svg';

import './AdEfficiencyTableFormatter.scss';

type AdEfficiencyTableRow = FixedLayoutColumn<AdEfficiencyListItem>

interface TitleTooltipProps {
  tooltipTitle: any
  label: string
}
const TitleTooltip = ({
  tooltipTitle, label
}:TitleTooltipProps) => {
  return (
    <div className="tooltip-container">
      <CommonTooltip
        title={tooltipTitle}
        placement="bottom-start"
      >
        <AdviceMarkIcon />
      </CommonTooltip>
      <span>{ label }</span>
    </div>
  )
}
const MyTooltip = withStyles({
  tooltip: {
    minWidth: 'unset',
    transform: 'translateY(10px)',
    padding: 0,
    '& .kpi-list-item': {
      display: 'block',
      width: 105,
      padding: '5px 0px',
      margin: 10,
      background: '#F2F3F6',
      borderRadius: 45,
      textAlign: 'center',
    }
  },
})(CommonTooltip)

export default class BudgetOptTableFormatter {
  handleDelete (id: number) {}
  handleCopy (id: number) {}
  getColumnFormat = (): Array<AdEfficiencyTableRow> => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const advertiser = useRecoilValue(advertiserState);
    const hasAuthority = checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)
    const strMIN_DATE_VALUE = convertDateToStr(MIN_DATE_VALUE)
    const routeStep = (url: string) => {
      navigate(url, { state: { advertiserId: advertiser.advertiserId } })
    }

    const columnOptStatus = () => {
      return {
        title: <CommonTooltip
          placement="bottom-start"
          arrow
          title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.status'} type={'defineList'} />}
        >
          <span>
            <AdviceMarkIcon /> {t('optimization.label.list.status')}
          </span>
        </CommonTooltip>,
        field: 'status',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120,
        },
        render: ({status}) => {
          return (
            <span className={`status ${status}`}>
              {t(`adEfficiency.label.adEfficiencyOpt.list.status.${status}`)}
            </span>
          )
        }
      } as AdEfficiencyTableRow
    }

    const columnOptId = () => {

      return {
        title: t(`adEfficiency.label.adEfficiencyList.analysisId`),
        field: 'analysisId',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120,
        },
        render: (rowData) => {
          const canCopy = AdEfficiencyStatus.SETTING !== rowData.status && hasAuthority
          return (
            <div className='cell-body-box'>
              {rowData.analysisId}
              {canCopy && <CopyIcon onClick={() => this.handleCopy(rowData.analysisId)} />}
            </div>
          )
        }
      } as AdEfficiencyTableRow
    }

    const columnOptName = () => {
      return {
        title: t(`adEfficiency.label.adEfficiencyList.analysisName`),
        field: 'analysisName',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '18%'
        },
        render: (rowData) => {
          const handleClick = () => {
            let stepUrl = `./${rowData.analysisId}`
            if (rowData.status === AdEfficiencyStatus.FINISHED) {
              routeStep(`${stepUrl}?step=2`)
              return
            }
            routeStep(stepUrl)
          }
          return (
            <div className='cell-body-box' onClick={handleClick}>
              {rowData.analysisName}
            </div>
          )
        }
      } as AdEfficiencyTableRow
    }

    const columnAnalysisTerm = () => {
      return {
        title: t(`adEfficiency.label.adEfficiencyList.analysisTerm`),
        field: 'analysisTerm',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '18%'
        },
        render: (rowData) => {
          const isInitalSetting = rowData.analysisStartDate === '1900.01.01'
          if(!rowData.analysisStartDate && !rowData.analysisEndDate) return <></>
          return (
            <div className='cell-body-box'>
              { !isInitalSetting && <span>{rowData.analysisStartDate} ~ {rowData.analysisEndDate}</span> }
            </div>
          )
        }
      } as AdEfficiencyTableRow
    }

    const columnAnalysisIndicator = () => {
      return {
        title: t(`adEfficiency.label.adEfficiencyList.analysisGoal`),
        field: 'analysisGoal',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '12%'
        },
        render: (rowData) => {
          return (
            <MyTooltip
              title={
                <div>
                  { rowData.kpiList.map(kpi =>
                    <span className="kpi-list-item">
                      { t(`common.code.kpis.${kpi}`) }
                    </span>) }
                </div>
              }
              arrow
              placement="bottom"
            >
              <div className='cell-body-box' onClick={() => routeStep(`./${rowData.analysisId}`)}>
                { rowData.kpiList.length !== 0 ?
                  <>
                    <span className="kpi-list-item">
                      { t(`common.code.kpis.${rowData.kpiList[0]}`) }
                    </span>
                    <span style={{visibility: rowData.kpiList.length > 1 ? 'visible' : 'hidden'}}>+{ rowData.kpiList.length-1}</span>
                  </> :
                  <></>
                }
              </div>
            </MyTooltip>
          )
        }
      } as AdEfficiencyTableRow
    }

    const columnEngineRunDate = () => {
      return {
        title: t(`adEfficiency.label.adEfficiencyList.engineRunDate`),
        field: 'engineRunDate',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '18%'
        },
        render: (rowData) => {
          if(
            !rowData.engineRunDate
            || rowData.status === AdEfficiencyStatus.SETTING
            || rowData.status === AdEfficiencyStatus.ERROR
            || rowData.status === AdEfficiencyStatus.RUNNING
            || strMIN_DATE_VALUE === rowData.engineRunDate.split(".").join("") ) return <></>
          return (
            <div className='cell-body-box'>
              <span>{rowData.engineRunDate}</span>
                <ResultIcon onClick={() => routeStep(`./${rowData.analysisId}?step=2`)} />
            </div>
          )
        }
      } as AdEfficiencyTableRow
    }

    const getColumnContextMenu = () => {
      return {
        field: 'CONTEXT_MENU',
        sorting: false,
        cellStyle: {
          width: '10%',
        },
        render: (rowData) => {
          if (
            rowData.status === AdEfficiencyStatus.RUNNING ||
            !hasAuthority
          ) return <></>
          return (
            <>
              { <ColumnSettingButtons
                  handleEdit={() => routeStep(`./${rowData.analysisId}?step=1`)}
                  handleDelete={() => this.handleDelete(rowData.analysisId)}
                />
              }
            </>
          )
        },
      } as AdEfficiencyTableRow;
    };

    return [
      columnOptStatus(),
      columnOptId(),
      columnOptName(),
      columnAnalysisTerm(),
      columnAnalysisIndicator(),
      columnEngineRunDate(),
      getColumnContextMenu()
    ];
  };
}
