.campaign-report-chart {
  width: 100%;
  height: 320px;
  position: relative;
  
  // Force canvas size to prevent Chart.js from auto-sizing
  canvas {
    width: 100% !important;
    height: 320px !important;
    max-height: 320px !important;
    max-width: 100% !important;
  }
}

@media screen and (max-width: 1919px) {
 .campaign-report-chart {
  width: 1686px !important;
  
  canvas {
    width: 1686px !important;
    height: 320px !important;
    max-height: 320px !important;
  }
 } 
}
@media screen and (min-width: 1920px) {
  .campaign-report-chart {
   width: 100% !important;
   
   canvas {
    width: 100% !important;
    height: 320px !important;
    max-height: 320px !important;
   }
  } 
}