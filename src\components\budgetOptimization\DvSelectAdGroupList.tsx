import React, { useEffect, useState } from 'react';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg';
import { getCampaignsForOptimizationDva } from '@api/optimization/OptimizationCommon';
import { useTranslation } from 'react-i18next';
import {
  SearchOptimizationAdgroupsOption,
  ConvertCampaignData as DvConvertCampaignData,
} from '@models/budgetOptimization/DvOptimizationAdgroups';
import { useActionType, useAuthority, useToast } from "@hooks/common";
import DvCampaignTreeComponent from './DvCampaignTreeComponent';
import { Campaign } from '@models/common/Campaign';
import { GetCampainsQueryParam } from '@models/optimization/GetAdgroupsQueryParam';
import { Box, FormLabel } from '@material-ui/core';
import useStatefulStateWithInitialData from '@components/common/hook/UseStatefulStateWithInitialData';
import WithLoader from '@components/common/WithLoader';
import AdviceTooltip from '@components/common/AdviceTooltip';
import { DateFnsFormat, SearchingTextLimitLength } from '@models/common/CommonConstants';
import { format } from 'date-fns';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import InnerHtml from '@components/common/InnerHtml';
import { ActionType } from '@models/common/CommonConstants';

interface Props {
  startDate: string;
  endDate: string;
  selectedGroups: Campaign[];
  setSelectedGroups: (campaign: Campaign[], init?: boolean) => void;
  type: ActionType;
  optimizationId?: number;
  setSavedCampaignIds: React.Dispatch<React.SetStateAction<string[]>>
}

const DvSelectAdGroupList: React.FC<Props> = ({
  selectedGroups,
  setSelectedGroups,
  type,
  startDate,
  endDate,
  optimizationId,
  setSavedCampaignIds,
}: Props) => {
  const { t } = useTranslation();
  const { advertiser } = useAuthority();
  const { isCreateType, isEditType, isReadType } = useActionType(type);
  const [filteredNodes, setFilteredNodes, filteredNodesStatus, setFilteredNodesStatus] =
    useStatefulStateWithInitialData<DvConvertCampaignData>({});
  const { openToast } = useToast();
  const [options, setOptions] = useState<SearchOptimizationAdgroupsOption>({
    keywordName: null,
    statusOn: true,
    statusOff: true,
    statusInBidding: true,
    statusNew: true,
  });
  const todayDate = format(new Date(), DateFnsFormat.DATE);

  const sortStatusName = (targets?: Array<any>) => {
    targets?.sort((target1, target2) => {
      if (target1.active !== target2.active) {
        return Number(target2.active) - Number(target1.active);
      }
      return target1.name < target2.name ? -1 : target1.name > target2.name ? 1 : 0;
    });
  };

  const getData = async () => {
    setFilteredNodesStatus('LOADING');

    const param = {
      advertiserId: advertiser.advertiserId,
      startDate: startDate,
      endDate: endDate,
    } as GetCampainsQueryParam;

    if (isReadType || (isEditType && startDate <= todayDate)) {
      param.optimizationId = optimizationId;
    }

    if (startDate === undefined || endDate === undefined) return;

    const response = await getCampaignsForOptimizationDva(param);
    const campaigns = response.campaigns || [];
    const convertData: DvConvertCampaignData = {};
    const newSelectedGroups: Campaign[] = [];

    if (campaigns === undefined) return;

    sortStatusName(campaigns);

    campaigns.forEach((campaign) => {
      convertData[campaign.mediaType] = {
        id: campaign.mediaType,
        name: campaign.mediaType,
        mediaType: campaign.mediaType,
        active: campaign.active,
        inAllocating: campaign.inAllocating,
        dailyBudget: null,
        canSelected: campaign.active,
        isSelected: false,
        maxAdgroupSelectedCount: 0,
        activeAdgroupSelectCount: 0,
        new: campaign.active,
        campaigns: {},
        predicted: campaign.predicted,
        newlyCreated: campaign.newlyCreated,
      };

      if (isReadType || (isEditType && startDate <= todayDate)) {
        const selectedCampaign = {
          accountId: campaign.accountId,
          campaignId: campaign.campaignId,
          mediaType: campaign.mediaType,
        } as Campaign;
        newSelectedGroups.push(selectedCampaign);
      }
    });

    if (isEditType && todayDate < startDate) {
      param.optimizationId = optimizationId;
      const selectedResponse = await getCampaignsForOptimizationDva(param);
      selectedResponse.campaigns.forEach(campaign => {
        newSelectedGroups.push({
          accountId: campaign.accountId,
          campaignId: campaign.campaignId,
          mediaType: campaign.mediaType,
        } as Campaign);
      })
      setSavedCampaignIds(newSelectedGroups.map(campaign => campaign.campaignId));
    }

    for (let key in convertData) {
      convertData[key].dailyBudget = campaigns
        .filter(
          (x) =>
            x.mediaType === key &&
            ((isCreateType && !x?.inAllocating) || (!isCreateType && x?.inAllocating))
        )
        .reduce((sum, value) => {
          return sum + (value.dailyBudget === null ? 0 : value.dailyBudget);
        }, 0);

      convertData[key].active = campaigns.filter((x) => x.mediaType === key && x.active).length > 0;
      convertData[key].canSelected = convertData[key].active;
    }

    campaigns.forEach((campaign) => {
      if (
        isCreateType ||
        (isReadType && campaign?.inAllocating) ||
        (isEditType && todayDate < startDate) ||
        (isEditType && startDate <= todayDate && campaign?.inAllocating)
      ) {
        const canSelect = !campaign.inAllocating;
        const isSelected = newSelectedGroups.findIndex((x) => x.campaignId === campaign.campaignId) !== -1;

        if (isSelected) {
          convertData[campaign.mediaType].activeAdgroupSelectCount += 1;
        }
        if (canSelect || isSelected) {
          convertData[campaign.mediaType].maxAdgroupSelectedCount += 1;
        }

        convertData[campaign.mediaType]['campaigns']![`${campaign.active}-${campaign.campaignName}`] = {
          id: campaign.campaignId,
          accountId: campaign.accountId,
          name: campaign.campaignName,
          mediaType: campaign.mediaType,
          active: campaign.active,
          inAllocating: campaign.inAllocating || false,
          dailyBudget: campaign.dailyBudget,
          canSelected: canSelect,
          isSelected: isSelected,
          maxAdgroupSelectedCount: 0,
          activeAdgroupSelectCount: 0,
          predicted: campaign.predicted,
          newlyCreated: campaign.newlyCreated,
        };
      }

      convertData[campaign.mediaType].isSelected =
        convertData[campaign.mediaType].activeAdgroupSelectCount === convertData[campaign.mediaType].maxAdgroupSelectedCount &&
        convertData[campaign.mediaType].maxAdgroupSelectedCount !== 0;
    });
    setFilteredNodes(convertData);
    setFilteredNodesStatus('LOADED');
    setSelectedGroups(newSelectedGroups, true);
  };

  useEffect(() => {
    if (endDate < startDate) {
      openToast(t('optimization.message.searchOptimization.validation.periodInvalid'))
      setFilteredNodes({});
      setFilteredNodesStatus('INITIAL');
      setSelectedGroups([]);
    } else {
      getData();
    }
  }, [startDate, endDate]); //eslint-disable-line

  return (
    <div id="DvSelectGroupComponent">
      <Box>
        <FormLabel>
          {t('optimization.label.DvOptimizationDetailModal.labelEng.targetCampaign')}
          <span>{t('optimization.label.DvOptimizationDetailModal.labelKor.targetCampaign')}</span>
        </FormLabel>

        <div className="searchArea">
          <OutlinedInput
            id="outlined-adornment-weight"
            onChange={(e) => {
              setOptions((before) => {
                return { ...before, keywordName: e.target.value };
              });
            }}
            endAdornment={<SearchIcon className="search-icon" />}
            aria-describedby="outlined-weight-helper-text"
            labelWidth={0}
            inputProps={{
              maxLength: SearchingTextLimitLength,
            }}
          />
        </div>
      </Box>
      <Box className="tableHeader">
        <div>
          <AdviceTooltip
            id="dv-treeview-tooltip-target-campaigns"
            title={<InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.targetCampaigns')} />}
            placement="right-start"
            arrow
          >
            <span className="icon">
              <AdviceMarkIcon />
            </span>
          </AdviceTooltip>
          {t('optimization.label.DvOptimizationDetailModal.tableHeader.targetCampaigns')}
        </div>
        <div>
          <AdviceTooltip
            id="dv-treeview-tooltip-target-onandoff"
            title={<InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.onAndOff')} />}
            placement="right-start"
            arrow
          >
            <span className="icon">
              <AdviceMarkIcon />
            </span>
          </AdviceTooltip>
          {t('optimization.label.DvOptimizationDetailModal.tableHeader.onAndOff')}
        </div>
        <div>
          <AdviceTooltip
            id="dv-treeview-tooltip-target-daily-budget"
            title={<InnerHtml innerHTML={t('optimization.label.DvOptimizationDetailModal.tooltip.dailyBudgetCampaigns')} />}
            placement="right-start"
            arrow
          >
            <span className="icon">
              <AdviceMarkIcon />
            </span>
          </AdviceTooltip>
          {t('optimization.label.DvOptimizationDetailModal.tableHeader.dailyBudget')}
        </div>
      </Box>
      <WithLoader status={filteredNodesStatus}>
        <DvCampaignTreeComponent
          data={filteredNodes}
          setData={setFilteredNodes}
          selectedGroups={selectedGroups}
          setSelectedGroups={setSelectedGroups}
          options={options}
          type={type}
          notYetStarted={todayDate < startDate}
        />
      </WithLoader>
    </div>
  );
};

export default DvSelectAdGroupList;
