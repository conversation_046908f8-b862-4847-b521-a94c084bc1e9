#AdEfficiencyResultChart {
  display: flex;
  position: relative;
  height: 560px;


  .chart-container {
    position: relative;
    flex:1;
    height: 100%;
    width: 100%;

    .x-axis-label, .y-axis-label {
      position: absolute;
      z-index: 1;
      font-weight: 700;
      font-size: 12px;
      color: var(--point_color);
    }
    .x-axis-label {
      bottom: 0;
      right: 12px;
      text-align: right;
    }
  }

  #adEfficiencyChart {
    width: 100%;
    height: 560px;
  }
}

@media screen and (max-width: 1919px) {
  #adEfficiencyChart {
   width: 1316px !important;
  } 
 }
 @media screen and (min-width: 1920px) {
   #adEfficiencyChart {
    width: 100%;
   } 
}