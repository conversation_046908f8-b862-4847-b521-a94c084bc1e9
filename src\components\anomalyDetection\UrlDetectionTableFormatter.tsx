import { useTranslation } from 'react-i18next';
import { UrlAnomalyDetectionColumn } from '@models/anomalyDetection';
import { MediaIcon } from '@components/common';

import './UrlDetectionTableFormatter.scss';

export default class UrlDetectionTableFormatter {
  getColumnFormat = (
    orderBy?: string | undefined,
    sorting?: string | undefined
  ): Array<UrlAnomalyDetectionColumn> => {
    const { t } = useTranslation();

    const columnOptId = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.optId'),
        field: 'optimizationId',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: 90,
          minWidth: 90,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              {rowData.optimizationId}
            </div>
          )
        }
      } as UrlAnomalyDetectionColumn
    }

    const columnMedia = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.media'),
        field: 'mediaType',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: 60,
        },
        render: (rowData) => {
          return (
            <div className="cell-body-box media" data-title={`${t('anomalyDetection.tooltip.accountId')}: ${rowData.accountId}`}>
              <MediaIcon mediaType={rowData.mediaType} />
            </div>
          )
        }
      } as UrlAnomalyDetectionColumn
    }

    const columnAnomalyType = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.type'),
        field: 'anomalyType',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: 150,
        },
        render: (rowData) => {
          const chipType = rowData.anomalyType === 'Keyword' ? 'keyword' : 'ad'
          return (
            <div className='cell-body-box'>
              <p className={`chip ${chipType}`}>
                { t(`anomalyDetection.label.anomalyType.${rowData.anomalyType}`) }
              </p>
            </div>
          )
        }
      } as UrlAnomalyDetectionColumn
    }

    const columnName = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.name'),
        field: 'name',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: 320,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box name'>
              <span className='title'>{rowData.name}</span>
              <span className='id'>{rowData.id}</span>
            </div>
          )
        }
      } as UrlAnomalyDetectionColumn
    }

    const columnDetectionCode = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.detection'),
        field: 'statusCode',
        sorting: false,
        align: 'center',
        cellStyle: {
          width: 160,
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box'>
              {t(`anomalyDetection.columnHeader.url.detectionError.${rowData.statusCode}`)}
              {/* {rowData.statusCode} */}
            </div>
          )
        }
      } as UrlAnomalyDetectionColumn
    }

    const columnLandingURL = () => {
      return {
        title: t('anomalyDetection.columnHeader.url.landingUrl'),
        field: 'url',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: 'auto',
        },
        render: (rowData) => {
          return (
            <div className='cell-body-box url'>
              {decodeURIComponent(rowData.url)}
            </div>
          )
        }
      } as UrlAnomalyDetectionColumn
    }

    const columns: UrlAnomalyDetectionColumn[] = [
      columnOptId(),
      columnMedia(),
      columnAnomalyType(),
      columnName(),
      columnDetectionCode(),
      columnLandingURL(),
    ];

    return columns;
  };
}
