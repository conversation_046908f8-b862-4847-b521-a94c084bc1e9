#AdEfficiencyResult {
  padding: 0 44px 40px;
  .budget-result-running {
    height: 565px;
    background-color: var(--bg-gray-light);
    border-top: 2px solid var(--point_color);
    color: var(--point_color);
    display: flex;
    flex-flow: column;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    gap: 34px;
    p {
      text-align: center;
    }
  }

  .analysis-setting {
    width: 100%;
    background-color: var(--bg-gray-light);
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.11);
    border-radius: 3px;
    display: grid;
    grid-template-columns: 300px 1fr;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    margin: 16px 0;

    &__label {
      margin: 16px 0;
      .title {
        font-size: 16px;
        font-weight: 700;
        color: var(--point_color);
      }
      .sub-title {
        padding-left: 8px;
        font-size: 12px;
        color: var(--point_color);
      }
    }

    &__option {
      display: flex;
      align-items: center;
      justify-content: space-between;

      #adEfficiency-result-date {
        width: 500px;
        justify-content: space-between;
      }

      .date-chip-box {
        display: flex;
        gap: 16px;

        .date-chip {
          border-radius: 9999px;
          padding: 4px 16px;
          font-size: 12px;
          font-weight: 700;
          border: 1px solid #cccccc;
          color: #909090;
          background-color: transparent;
          &.isSelected {
            border: 1px solid var(--color-active-blue);
            color: var(--color-active-blue);
            background-color: #fff;
          }
        }
      }

      #budget-opt-config-multi-goal {
        width: 100%;
      }
    }

    &__submit {
      grid-column: 1 / span 2;
      text-align: center;
      .submit-button {
        border-radius: 9999px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 700;
        border: none;
        color: #fff;
        background-color: var(--color-active-blue);
        margin-bottom: 12px;
        cursor: pointer;
      }
    }
  }

  .analysis-result {
    width: 100%;
    background-color: var(--bg-gray-light);
    padding: 8px 16px;
    border-radius: 3px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.11);

    &__label {
      margin: 16px 0;
      .title {
        font-size: 16px;
        font-weight: 700;
        color: var(--point_color);
      }
      .sub-title {
        padding-left: 8px;
        font-size: 12px;
        color: var(--point_color);
      }
      .date {
        top: 8px;
        right: 10px;
        margin-left: 20px;
        padding: 2px 10px;
        height: 18px;
        line-height: 18px;
        border-radius: 9px;
        background-color: var(--point_color);
        color: #fff;
        font-size: 12px;
        font-weight: 400;
      }
    }

    &__tabs {
      .MuiTabs-root {
        min-height: 30px;
      }
      .MuiTabs-flexContainer {
        .MuiButtonBase-root {
          min-height: 30px;
          text-transform: none;
          font-size: 12px;
          opacity: 0.6;
          background-color: white;
          box-shadow: 2px 1px 4px 0px rgba(0, 0, 0, 0.25);
        }
        .Mui-selected {
          opacity: 1;
          color: var(--color-active-blue);
          font-weight: 700;
        }
      }
      .MuiTabs-indicator {
        top: 0px;
        background-color: var(--color-active-blue);
      }
    }

    &__container {
      background-color: white;
      padding: 16px;
    }
  }
}
