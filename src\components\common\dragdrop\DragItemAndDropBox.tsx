import React, { useRef } from "react";
import { useDrag, useDrop } from "react-dnd";
import type { Identifier } from "dnd-core";

interface DragItemAndDropBoxProps {
    type?: string;
    index: number;
    id: number;
    children: React.ReactElement;
    className: string;
    onMoveItem: (dragIndex: number, hoverIndex: number) => void;
    disabled: boolean
}

interface DragItem {
    index: number;
    id: string;
    type: string;
}

const DragItemAndDropBox = (props: DragItemAndDropBoxProps) => {
    const ref = useRef<HTMLDivElement>(null);
    const { type='ITEM', index, id, className, disabled = false, children, onMoveItem } = props;

    const [{ handlerId }, drop] = useDrop<
        DragItem,
        void,
        { handlerId: Identifier | null }
    >({
        accept: type,
        collect(monitor) {
        return {
            handlerId: monitor.getHandlerId(),
        };
        },
        hover(item: DragItem, monitor) {
        if (!ref.current) {
            return;
        }
        const dragIndex = item.index;
        const hoverIndex = index;

        if (dragIndex === hoverIndex) {
            return;
        }

        onMoveItem(dragIndex, hoverIndex);

        item.index = hoverIndex;
        },
    });

    const [{ isDragging }, drag] = useDrag({
        type,
        item: () => {
            return { id, index };
        },
        collect: (monitor: any) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    const opacity = isDragging ? 0 : 1;
    drag(drop(ref));

    return (
        <div
            ref={disabled ? null : ref}
            style={{ opacity }}
            data-handler-id={handlerId}
            className={className}
        >
            {children}
        </div>
    )
}

export default DragItemAndDropBox;