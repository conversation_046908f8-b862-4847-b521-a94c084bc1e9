#UtmAnomalyDetectionTable {
  & > * {
    box-shadow: none;
    transition: none;
  }

  .MuiTableCell-root.MuiTableCell-head {
    background-color: var(--bg-table-main);
    color: var(--point_color);
    font-size: 16px;
    padding: 0;
    border-bottom: none;

    .utm-rule {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      &.head {
        flex-direction: column;
      }

      &.title {
        padding: 0 16px;

        p {
          font-weight: 300;
          border-bottom: 1px solid var(--border-table-main);
        }
      }

      p {
        flex: 1;
        margin: 0;
        padding: 8px 0;
        text-align: center;
        text-transform: uppercase;
      }
    }
  }

  .MuiTableRow-root {
    min-height: 70px;

    .MuiTableCell-root.MuiTableCell-body {
      padding: 0;
      height: 100%;
      min-height: 70px;
    }
  }

  .cell-body-box {
    width: 100%;
    color: var(--point_color);
    min-height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;

    &.name {
      padding: 4px;
      display: inline-flex;
      flex-direction: column;
      align-items: flex-start;

      .title {
        font-weight: 700;
        width: 199px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &.rules {
      div {
        flex: 1;
      }
      .utm-rules-icon {
        display: inline-block;
        margin: 0 4px;
      }
    }
    &.urls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px;

      ul {
        padding-inline-start: 16px;
        width: calc(100% - 72px);
        margin: 4px 0;
        li {
          width: 100%;
          margin: 4px 0;
          text-align: left;
          span {
            width: 100%;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            vertical-align: bottom;
            font-weight: 300;
            font-size: 16px;
          }
        }
      }

      .utm-more {
        cursor: pointer;
        background-color: unset;
        padding: 4px 8px;
        margin: 0 10px;
        border: 1px solid var(--border-primary);
        border-radius: 9999px;
        font-size: 10px;
        font-weight: 700;
      }
    }

    .chip {
      border-radius: 6px;
      padding: 4px 16px;
      margin: 0 auto;
      width: fit-content;

      &.keyword {
        background-color: #e2f6ec;
        color: #1da152;
      }

      &.ad {
        background-color: #e8f2fe;
        color: var(--color-active-blue);
      }
    }
  }
}
