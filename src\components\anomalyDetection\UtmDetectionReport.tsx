import React, { ReactElement, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useRecoilValue, useSetRecoilState } from 'recoil'

import { utmsAnomalySummaries, selectedUtmAnomalySummary, openAbnormalLandingUrlModal } from '@store/AnomalyDetection';

import UtmDetectionAccordion from './UtmDetectionAccordion';
import EmptyDetection from './EmptyDetection';
import UtmAbnormalLandingUrlModal from './UtmAbnormalLandingUrlModal';
import { ReactComponent as StarBlink } from '@components/assets/images/icon_star_blink.svg';
import { ReactComponent as EmptyNotice } from '@components/assets/images/icon_notice_outline.svg';
import { UtmSummary } from '@models/anomalyDetection'

interface Props {
  advertiserId: number
  isAllowGA: boolean;
}

const UtmDetectionReport: React.FC<Props> = ({ advertiserId, isAllowGA }: Props): ReactElement => {
  const { t } = useTranslation()
  const utmSummaries = useRecoilValue(utmsAnomalySummaries);
  const setCurrentUtmSummary = useSetRecoilState(selectedUtmAnomalySummary);
  const isOpenLandingUrlModal = useRecoilValue(openAbnormalLandingUrlModal)
  const [isExpanded, setIsExpanded] = useState<string>('');
  const isEmpty = useMemo(() => utmSummaries.length === 0, [utmSummaries])

  const toggleExpanded = (summary: UtmSummary) => {
    if(isExpanded === summary.campaignId) {
      setIsExpanded('');
      return;
    }
    setIsExpanded(summary.campaignId);
    setCurrentUtmSummary(summary)
  }
  return (
    <div className={`detection-utm-report ${isEmpty ? 'detection-utm-report--background' : ''}`}>
      { isEmpty
        ? isAllowGA ? <EmptyDetection icon={<EmptyNotice />} content={t('anomalyDetection.empty.utm')}/>
          : <EmptyDetection icon={<StarBlink />} content={t('anomalyDetection.empty.analytics')}/>
        : utmSummaries.map((summary) => (
          <UtmDetectionAccordion
            key={summary.campaignId}
            expanded={isExpanded === summary.campaignId}
            onChange={() => toggleExpanded(summary)}
            summary={summary}
          />
        ))
      }
      {isOpenLandingUrlModal &&
        <UtmAbnormalLandingUrlModal
          open={isOpenLandingUrlModal}
          advertiserId={advertiserId}
        />
      }
    </div>
  )
}

export default UtmDetectionReport;