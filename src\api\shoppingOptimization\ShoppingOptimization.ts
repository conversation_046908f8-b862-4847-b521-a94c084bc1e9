/* istanbul ignore file */

import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import { AxiosResponse } from 'axios';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  ShoppingOptimizationDetail,
  ShoppingOptimizationInfo,
  ShoppingOptimizationList,
  ShoppingOptimizationsRequest,
  ShoppingOptimizationSaveRequest,
  ShoppingOptimizationSaveRequestWithoutAdgroup,
  ShoppingOptimizationCostsRequest,
  ShoppingOptimizationCost,
} from '@models/shoppingOptimization/ShoppingOptimization';
import {
  GetShoppingAdgroupsQueryParam,
  GetCampainSearchQueryParam,
  GetAdgroupSearchQueryParam,
  GetKeywordEfficiencyQueryParam,
  GetKeywordEfficiencyDetailQueryParam,
  GetKeywordEfficiencyDownloadlQueryParam,
  GetRestrictKeywordQueryParam,
  UpdateRestrictKeywordBodyParam,
} from '@models/optimization/GetAdgroupsQueryParam';
import {
  GetShoppingAdgroupsResponse,
  GetCampainSearchValueInfo,
  GetAdgroupSearchValueInfo,
  GetKeywordEfficiencyResponse,
  KeywordEfficiencyDetail,
} from '@models/optimization/GetAdgroupsResponse';
import { pageSizeOptions } from '@models/common/CommonConstants';

export const getShoppingOptimizations = async (
  param: ShoppingOptimizationsRequest,
  isLoading = true
): Promise<ShoppingOptimizationList> => {
  const ret: ShoppingOptimizationList = {
    totalCount: 0,
    pageSize: pageSizeOptions[0],
    pageIndex: 1,
    optimizations: [],
    currentAdgroupsCount: 0,
    maxAdgroupsCount: 50,
    currentItemsCount: 0,
    maxItemsCount: -1,
  };

  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/shopping',
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  if (response.successOrNot === 'Y' && (response.data.totalCount as number) > 0) {
    ret.totalCount = response.data.totalCount as number;
    ret.optimizations = response.data.optimizations as ShoppingOptimizationInfo[];
    ret.pageSize = param.pageSize;
    ret.pageIndex = param.pageIndex;
    if (response.data.currentAdgroupsCount !== undefined) {
      ret.currentAdgroupsCount = response.data.currentAdgroupsCount as number;
    }
    if (response.data.maxAdgroupsCount !== undefined) {
      ret.maxAdgroupsCount = response.data.maxAdgroupsCount as number;
    }
    if (response.data.currentItemsCount !== undefined) {
      ret.currentItemsCount = response.data.currentItemsCount as number;
    }
    if (response.data.maxItemsCount !== undefined) {
      ret.maxItemsCount = response.data.maxItemsCount as number;
    }
  }

  return ret;
};

export const updateShoppingOptimizationBidYn = async (optimizationId: number, bidYn: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/${optimizationId}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        bidYn: bidYn,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const deleteShoppingOptimization = async (optimizationId: number, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/${optimizationId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });
};

export const createShoppingOptimization = async (param: ShoppingOptimizationSaveRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/shopping',
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateShoppingOptimization = async (
  optimizationId: number,
  param: ShoppingOptimizationSaveRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/${optimizationId}`,
    method: Method.PUT,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateShoppingOptimizationWithoutAdgroup = async (
  optimizationId: number,
  param: ShoppingOptimizationSaveRequestWithoutAdgroup,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/${optimizationId}/config`,
    method: Method.PATCH,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const getShoppingOptimizationDetail = async (
  optimizationId: number,
  isLoading = true
): Promise<ShoppingOptimizationDetail> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/${optimizationId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as ShoppingOptimizationDetail;
};

export const getShoppingOptimizationCosts = async (param: ShoppingOptimizationCostsRequest, isLoading = false): Promise<ShoppingOptimizationCost[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/shopping/costs',
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as ShoppingOptimizationCost[];
};

export const getShoppingAdgroups = async (param: GetShoppingAdgroupsQueryParam, isLoading = true): Promise<GetShoppingAdgroupsResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/adgroups`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as GetShoppingAdgroupsResponse;
};

export const getShoppingCampaignSearch = async (optimizationId: number, param: GetCampainSearchQueryParam, isLoading = true): Promise<GetCampainSearchValueInfo[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/target/campaigns/${optimizationId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as GetCampainSearchValueInfo[];
};

export const getShoppingAdgroupSearch = async (optimizationId: number, param: GetAdgroupSearchQueryParam, isLoading = true): Promise<GetAdgroupSearchValueInfo[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/target/adgroups/${optimizationId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as GetAdgroupSearchValueInfo[];
};

export const getShoppingKeywordEfficiency = async (optimizationId: number, param: GetKeywordEfficiencyQueryParam, isLoading = true): Promise<GetKeywordEfficiencyResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/keyword/efficiency/${optimizationId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as GetKeywordEfficiencyResponse;
};

export const getShoppingKeywordEfficiencyDetail = async (optimizationId: number, param: GetKeywordEfficiencyDetailQueryParam, isLoading = true): Promise<KeywordEfficiencyDetail[]|null> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/keyword/efficiency/${optimizationId}/detail`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as KeywordEfficiencyDetail[]|null;
};

// TODO
export const getShoppingKeywordEfficiencyDownload = async (optimizationId: number, param: GetKeywordEfficiencyDownloadlQueryParam, isLoading = true) => {
  const response: CommonResponse| AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/keyword/efficiency/${optimizationId}/raw-data`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  if (response.data) {
    openDownloadLink(response);
    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};

export const getShoppingRestrictKeyword = async (adId: string, param: GetRestrictKeywordQueryParam, isLoading = true): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/restrictKeyword/${adId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response;
};

export const updateShoppingRestrictKeyword = async (adId: string, param: UpdateRestrictKeywordBodyParam, isLoading = true): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/shopping/restrictKeyword/${adId}`,
    method: Method.PUT,
    params: {
      bodyParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response;
};

export const getShoppingOptimizationNewAdgroup = async (param: {advertiserId: number}, isLoading = true): Promise<string> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/shopping/new-adgroup',
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as string;
};
