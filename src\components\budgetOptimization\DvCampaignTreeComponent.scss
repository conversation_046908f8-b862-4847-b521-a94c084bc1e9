#DvSelectGroupComponent {
  .searchArea {
    .MuiInputBase-root {
      width: 340px;
      height: 35px;
      padding-right: 12px;
      font-size: 14px;
      font-weight: 300;
      color: var(--point_color);
      border: 1px solid #bbbdcd;
      border-radius: 22.5px;
      background-color: #fff;

      input {
        height: 23px;
        padding: 1px 0px 1px 15px;
        width: 100%;
      }

      fieldset {
        border: none;
      }

      &.MuiInput-underline {
        &::before,
        &::after {
          display: none;
        }
      }

      .MuiInputBase-input {
        font-size: 14px;
        font-weight: 300;
        color: var(--point_color);
        box-sizing: border-box;

        &:focus {
          background: none;
        }
      }

      &.MuiInputBase-adornedEnd .search-icon {
        width: 22px;
        height: 22px;
      }
    }
  }

  .tableHeader {
    display: flex;
    height: 45px;
    margin-top: 20px;
    font-family: 'Noto Sans Korean', sans-serif;
    font-size: 14px;
    font-weight: 500;
    border-top: 1px solid var(--point_color);
    color: var(--point_color);

    > div {
      display: flex;
      align-items: center;
      justify-content: center;
      &:nth-child(1) {
        flex: 6;
        padding-right: 20px;
        margin-left: -20px;
      }
      &:nth-child(2) {
        flex: 1.5;
      }
      &:nth-child(3) {
        flex: 2.5;
      }
      .icon {
        line-height: 1;
        padding-right: 4px;
      }
    }
  }

  .treeViewRoot {
    height: 491px;
    overflow-y: auto;
    border-top: 1px solid var(--point_color);
    border-bottom: 1px solid var(--point_color);
    background-color: #fff;

    .label-selected {
      background-color: #eee;
    }
    &.readOnly {
      opacity: 0.4;
      .label-selected {
        background-color: inherit;
      }
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
  }

  .labelBasic {
    color: var(--color-blue-darker);
  }

  .MuiTreeItem-content {
    width: auto;
    height: 48px;
    padding: 0 25px;
  }

  .labelNoSelectGrey {
    color: #909090;
  }

  .labelNotPredicted {
    color: #b51b32;
  }

  width: 100%;
  padding: 0;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label {
      font-family: 'NotoSans';
      font-weight: 500;
      font-size: 11px;
      color: #565656;
    }
  }

  .searchOptions {
    display: flex;
    margin-top: 10px;
    justify-content: space-between;
    border-bottom: 1px solid #dbdbdb;
    padding-bottom: 10px;

    button {
      width: 92px;
      height: 26px;
      padding: 0;
      border-radius: 13px;
      background-color: #ffffff;
      border: solid 1px #dbdbdb;
      box-shadow: none;
      font-family: 'NotoSans';
      font-weight: 500;
      font-size: 12px;

      &.active {
        border: none;
        background-color: #dee3e7;
      }
    }

    button:nth-child(-n + 2) {
      color: #808080;

      &.active {
        color: #2b2b2b;
      }
    }

    button:nth-child(n + 3) {
      color: #dd3539;

      &.active {
        color: #dd3539;
      }
    }
  }

  ul.MuiTreeView-root {
    overflow-y: auto;

    .MuiTreeItem-root {
      &:not(:first-child) {
        border-top: 1px solid #b3b8c5;
      }

      &.campaign-item {
        border-top: 1px solid #b3b8c5;
      }
      &.media-item:last-child {
        border-bottom: 1px solid #b3b8c5;
      }
    }

    .MuiTreeItem-iconContainer {
      width: 30px;

      svg {
        width: 30px;
        height: 30px;
        color: var(--point_color);
        stroke: #ffffff;

        &.MuiSvgIcon-colorDisabled {
          border: 1px solid #b3b8c5;
          border-radius: 50%;
        }
      }
    }

    .MuiTreeItem-label {
      height: 100%;
      .labelComponent {
        display: flex;
        height: 100%;
        align-items: center;
        font-weight: 400px;
        font-size: 11px;
      }
    }

    li.media-item {
      span {
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .labelName {
        width: 302px;
        margin-left: 20px;
        display: flex;
        align-items: center;
        .new {
          display: inline-block;
          position: relative;
          height: 16px;
          width: 36px;
          margin-left: 10px;
          &::after {
            content: 'NEW';
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            height: 16px;
            width: 36px;
            font-size: 8px;
            font-weight: 700;
            color: white;
            background-color: var(--color-active-blue);
            border-radius: 8px;
            padding-bottom: 1px;
          }
        }
      }

      .labelStatus {
        width: 80px;
        font-weight: 700;
        text-align: center;
      }

      .labelBudget {
        flex: 1;
        text-align: end;
      }

      .MuiTreeItem-group {
        margin: 0px;

        .MuiTreeItem-label {
          padding-left: 21px;
        }
      }
    }

    li.campaign-item {
      .labelName {
        width: 282px;
        margin-left: 23px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  ul.MuiCollapse-root,
  ul.MuiTreeView-root {
    .MuiTreeItem-label {
      background-color: transparent;

      &:focus {
        background: none;
      }
    }
  }

  .hidden {
    display: none;
  }
}

#dv-treeview-tooltip-target-campaigns,
#dv-treeview-tooltip-target-onandoff,
#dv-treeview-tooltip-target-daily-budget {
  border: 1px solid var(--point_color);
  background-color: #fff;
  .MuiTooltip-tooltip {
    padding: 0 0 12px 0;
    margin: 0;
    background-color: transparent;
    .border-bottom {
      border-bottom: 1px solid #9196a4;
    }
    .indent1 {
      padding: 0 12px 0 14px;
      text-indent: -14px;
    }
    .indent2 {
      padding: 0 12px 0 30px;
      text-indent: -14px;
    }
    .indent3 {
      padding: 0 12px 0 38px;
      text-indent: -38px;
    }
    > div {
      color: var(--point_color);
      font-size: 12px;
      font-weight: normal;
      > h1,
      > h2,
      > h3,
      > h4,
      > h5,
      > h6 {
        font-size: 12px;
        font-weight: bold;
        margin: 0;
        padding: 12px 0px;
        border-bottom: 1px solid #9196a4;
        font-weight: 700;
        text-align: center;
      }
      > div {
        padding: 8px 12px 6px;
        line-height: 1.5;
        ul {
          margin: 0;
          padding-inline-start: 2em;
        }
      }
    }
    > div > ul {
      color: var(--point_color);
      font-size: 12px;
      font-weight: 500;
    }
    .MuiTooltip-arrow::before {
      border-color: var(--point_color);
    }
  }
  .select-mark-blue,
  .select-mark-gray,
  .select-mark-red {
    display: inline-block;
    width: 22px;
    height: 4px;
    margin: 0 8px;
  }
  .select-mark-blue {
    background-color: var(--color-blue-darker);
  }
  .select-mark-gray {
    background-color: #909090;
  }
  .select-mark-red {
    background-color: #b51b32;
  }
  .select-mark-new {
    display: inline-block;
    position: relative;
    height: 16px;
    width: 36px;
    margin-right: 4px;
    padding-top: 3px;
    text-indent: 0;
    &::after {
      content: 'NEW';
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      height: 16px;
      width: 36px;
      font-size: 8px;
      font-weight: 700;
      color: white;
      background-color: var(--color-active-blue);
      border-radius: 8px;
      line-height: 1;
    }
  }
}
