/* istanbul ignore file */

import { callApi, downloadByteArray, Method, openDownloadLink } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  CreateBudgetOptParams,
  BudgetOptimizationListItem,
  BudgetOptimizationInfo,
  UpdateBudgetOptParams,
  BudgetOptimizationCampaignsResult,
  BudgetOptimizationTargetCampaign,
  BudgetOptimizationResult,
  UpdateBudgetOptName
} from '@models/budgetOpt/BudgetOpt';

export const getBudgetOptimizations = async (advertiserId: number, isLoading = true): Promise<BudgetOptimizationListItem[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/budget',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationListItem[];
};

export const createBudgetOptimization = async (budgetParams: CreateBudgetOptParams, isLoading = true): Promise<BudgetOptimizationInfo|null> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget`,
    method: Method.POST,
    params: {
      bodyParams: budgetParams,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationInfo;
};

export const getBudgetOptimization = async (optimizationId: number, isLoading = true): Promise<BudgetOptimizationInfo> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationInfo;
};

export const updateBudgetOptimization = async (optimizationId: number, budgetParams: UpdateBudgetOptParams|UpdateBudgetOptName, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        ...budgetParams,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationListItem[];
};

export const deleteBudgetOptimization = async (optimizationId: number, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationListItem[];
};

export const copyBudgetOptimization = async (optimizationId: number, isLoading = true) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}`,
    method: Method.POST,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationListItem;
};

export const getBudgetOptimizationCampaigns = async (optimizationId: number, isLoading = true): Promise<BudgetOptimizationCampaignsResult> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}/campaigns`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationCampaignsResult;
}

export const updateBudgetOptimizationTargetCampaigns = async (optimizationId: number, targets: BudgetOptimizationTargetCampaign[], isLoading = true): Promise<any> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}/targets`,
    method: Method.POST,
    params: {
      bodyParams: {
        optimizationId: optimizationId,
        targets: targets,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as any;
}

export const getBudgetOptimizationResult = async (optimizationId: number, isLoading = true): Promise<BudgetOptimizationResult[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/budget/${optimizationId}/result`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as BudgetOptimizationResult[];
}