import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@material-ui/core/';

import { UtmSummary } from '@models/anomalyDetection';
import UtmDetectionTable from './UtmDetectionTable';
import { MediaIcon, MopIcon } from '@components/common';
import { MOPIcon } from '@models/common';
import { numberWithCommas } from '@utils/FormatUtil'
import './UtmDetectionAccordion.scss'

interface Props {
  expanded: boolean;
  onChange: () => void
  summary: UtmSummary;
}

const UtmDetectionAccordion = ({
  expanded,
  onChange,
  summary
}: Props) => {
  const { t } = useTranslation();
  return (
    <Accordion
      expanded={expanded}
      onChange={onChange}
      className="utm-accordion"
      style={{'--accordion-border': `${expanded ? '#3B5EC9': '#D7D8E2'}`} as React.CSSProperties}
      TransitionProps={{
        timeout: 0,
      }}
    >
      <AccordionSummary className="utm-accordion-summary">
        <div className='side-left'>
          <p className='summary platform bold'>{summary.platformType}</p>
          <div className='summary media' data-title={`${t('anomalyDetection.tooltip.accountId')}: ${summary.accountId}`}><MediaIcon mediaType={summary.mediaType} /></div>
          <p className='summary label'>Campaigns</p>
          <p className='summary name bold'>{summary.campaignName}</p>
        </div>
        <div className='side-right'>
          <MediaIcon mediaType={summary.analyticsType}/>
          <p className='summary count bold'>{numberWithCommas(summary.count)}</p>
          <p className='summary cases bold'>
            {t('anomalyDetection.label.accordion.cases')}
          </p>
          <div className='summary arrow-down'>
            <MopIcon name={MOPIcon.ARROW_DOWN}/>
          </div>
        </div>
      </AccordionSummary>
      <AccordionDetails className="utm-accordion-details">
        <UtmDetectionTable isExpanded={expanded} summary={summary} />
      </AccordionDetails>
    </Accordion>
  )
}

function areEqual(prev: Props, next: Props) {
  if(prev.expanded === next.expanded) {
    return true
  } else {
    return false
  }
}

export default React.memo(UtmDetectionAccordion, areEqual);