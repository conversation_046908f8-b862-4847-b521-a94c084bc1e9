import { Button } from '@material-ui/core';
import { ChartLegendData } from '@models/common/ChartData';
import React, { useState } from 'react';

import { useTranslation } from 'react-i18next';
import './ChartLegendMore.scss';
import ChartLegendRow from './ChartLegendRow';
import { UnitType } from '@models/common/ChartData';

interface Props {
  legends: ChartLegendData[];
  useTooltip?: boolean;
  unitType: UnitType;
}

const ChartLegendMore: React.FC<Props> = ({ legends, useTooltip, unitType }) => {
  const { t } = useTranslation();
  const [expanded, setExpaded] = useState<boolean>(false);

  return (
    <div className="chart-legend-more">
      <div className="expand-more">
        <Button disableRipple onClick={() => setExpaded(!expanded)}>
          {t('analysis.label.ConversionTrendChart.more')}
        </Button>
      </div>
      <div className="legend-list" style={{ visibility: expanded ? undefined : 'hidden' }}>
        {legends.map((legend, idx) => (
          <ChartLegendRow
            key={idx}
            className={legend.className}
            backgroundColor={legend.backgroundColor}
            label={legend.label}
            value={legend.value}
            useTooltip={useTooltip}
            unitType={unitType}
          />
        ))}
      </div>
    </div>
  );
};

export default ChartLegendMore;
