import DoughnutsChart from '@components/common/chart/DoughnutsChart';
import useDoughnutsChart from './hook/useDoughnutsChart';
import useCustomTooltip from './hook/useBudgetCustomTooltip';
import outlinedLabel from './hook/outlineLabelPlugin';

const convertData = (data: any) => {
  const result = []
  for(const [key, value] of Object.entries(data)) {
    result.push({
      key, ...value as any
    })
  }
  return result
}
interface Props {
  recommend: any
}
const BudgetChart = ({ recommend }: Props) =>{
  const reports = convertData(recommend)
  const { chartData } = useDoughnutsChart({
    reports,
    targetDataKey: 'budgetRatio'
  })
  const { tooltipInfo, customTooltip } = useCustomTooltip({
    reportData: reports
  });

  const options: any = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 45,
    legend: {
      display: false,
      position: 'right',
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
        external: customTooltip,
      },
      outlinedLabel: {
        id: 'recommendation-chart'
      }
    },
    elements: {
      arc: {
        borderWidth: 0,
      },
    },
  };
  return (
    <DoughnutsChart
      key="BudgetChart"
      chartData={chartData}
      options={options}
      tooltipId="arrow-boxed-tooltip"
      tooltipInfo={tooltipInfo}
      tooltipOption={{
        valueUnit: 'NONE',
        showRatio: true
      }}
      // plugins={[outlinedLabel(reports, 'recommendation')]}
    />
  )
}

export default BudgetChart