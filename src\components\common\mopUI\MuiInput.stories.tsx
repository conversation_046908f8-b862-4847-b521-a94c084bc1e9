// src/components/common/mopUI/MuiInput.stories.tsx
import React, { useState } from 'react'
import { Meta, StoryObj } from '@storybook/react'
import MuiInput from './MuiInput'

const meta: Meta<typeof MuiInput> = {
  title: 'Components/Common/StyledMui/MuiInput',
  component: MuiInput,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    hasFocus: {
      control: 'boolean',
      description: 'Controls border color change on focus state',
      defaultValue: true,
    },
    useNumberFormat: {
      control: 'boolean',
      description: 'Enable react-number-format functionality',
      defaultValue: false,
    },
    type: {
      control: 'select',
      options: ['default', 'raw-data', 'commerce'],
      description: 'Style type (same approach as MopSearch)',
      defaultValue: 'default',
    },
    placeholder: {
      control: 'text',
      description: 'Input field placeholder text',
    },
    disabled: {
      control: 'boolean',
      description: 'Disable input field',
      defaultValue: false,
    },
  },
}

export default meta
type Story = StoryObj<typeof MuiInput>

// Basic Input Story
export const Default: Story = {
  args: {
    placeholder: 'Enter text here',
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </div>
    )
  },
}

// Number Formatting Input Story
export const WithNumberFormat: Story = {
  args: {
    placeholder: 'Enter numbers',
    useNumberFormat: true,
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          numberFormatProps={{
            thousandSeparator: true,
            decimalSeparator: '.',
            allowNegative: false,
          }}
        />
      </div>
    )
  },
}

// Currency Formatting Input Story
export const CurrencyFormat: Story = {
  args: {
    placeholder: 'Enter amount',
    useNumberFormat: true,
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          numberFormatProps={{
            thousandSeparator: true,
            prefix: '₩ ',
            allowNegative: false,
          }}
        />
      </div>
    )
  },
}

// Percentage Formatting Input Story
export const PercentageFormat: Story = {
  args: {
    placeholder: 'Enter percentage',
    useNumberFormat: true,
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          numberFormatProps={{
            suffix: '%',
            allowNegative: false,
            decimalScale: 2,
          }}
        />
      </div>
    )
  },
}

// Disabled State Story
export const Disabled: Story = {
  args: {
    placeholder: 'Disabled input field',
    disabled: true,
    value: 'Disabled value',
  },
  render: (args) => (
    <div style={{ width: '300px' }}>
      <MuiInput {...args} />
    </div>
  ),
}

// No Focus State Story
export const NoFocus: Story = {
  args: {
    placeholder: 'No focus effect',
    hasFocus: false,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </div>
    )
  },
}

// Raw-data Type Story
export const RawDataType: Story = {
  args: {
    placeholder: 'Raw-data style (27px, rounded border)',
    type: 'raw-data',
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </div>
    )
  },
}

// Commerce Type Story
export const CommerceType: Story = {
  args: {
    placeholder: 'Commerce style (42px, right aligned)',
    type: 'commerce',
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </div>
    )
  },
}

// All Types Comparison Story
export const AllTypes: Story = {
  render: () => {
    const [values, setValues] = useState({
      default: '',
      rawData: '',
      commerce: '',
    })
    
    return (
      <div style={{ width: '300px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <div>
          <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
            Default (42px, center aligned, rectangle)
          </h4>
          <MuiInput
            type="default"
            placeholder="Default style"
            value={values.default}
            onChange={(e) => setValues({ ...values, default: e.target.value })}
          />
        </div>
        
        <div>
          <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
            Raw-data (27px, center aligned, rounded)
          </h4>
          <MuiInput
            type="raw-data"
            placeholder="Raw-data style"
            value={values.rawData}
            onChange={(e) => setValues({ ...values, rawData: e.target.value })}
          />
        </div>
        
        <div>
          <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
            Commerce (42px, right aligned, rectangle)
          </h4>
          <MuiInput
            type="commerce"
            placeholder="Commerce style"
            value={values.commerce}
            onChange={(e) => setValues({ ...values, commerce: e.target.value })}
          />
        </div>
      </div>
    )
  },
}

// Various MUI Props Usage Example
export const WithMUIFeatures: Story = {
  args: {
    placeholder: 'MUI features showcase',
    hasFocus: true,
  },
  render: (args) => {
    const [value, setValue] = useState('')
    return (
      <div style={{ width: '300px', display: 'flex', flexDirection: 'column', gap: '16px' }}>
        <MuiInput
          {...args}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          startAdornment={<span>🔍</span>}
          endAdornment={<span>✨</span>}
        />
        <MuiInput
          {...args}
          placeholder="Error state"
          error
        />
        <MuiInput
          {...args}
          placeholder="Default size"
        />
      </div>
    )
  },
}
