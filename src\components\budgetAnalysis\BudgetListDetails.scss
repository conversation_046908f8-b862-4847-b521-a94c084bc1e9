.budget-list-details {
  display: flex;
  height: 345px;
  width: 100%;
  position: relative;
  .toggle-wrapper {
    background: #f2f3f6;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    width: 190px;
    border-right: 1px solid var(--border-spacer);
    .toggle-title {
      width: 100%;
      height: 15%;
      border-bottom: 1px solid var(--point_color);
      text-align: center;
      color: var(--point_color);
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 16px;
    }
    .toggle {
      display: inline-block;
      appearance: none;
      border-radius: 9999px;
      font-family: inherit;
      font-size: 12px;
      font-weight: 500;
      border: none;
      width: 100%;
      padding: 3px;
      cursor: pointer;
      &.impressions {
        color: #fff;
        background-color: #00359c;
      }
      &.clicks {
        color: #fff;
        background-color: #ff5c00;
      }
      &.revenue {
        color: #fff;
        background-color: #774cf2;
      }
      &.conversions {
        color: var(--point_color);
        background-color: #ffd600;
      }
      &.top-impression-share {
        color: var(--point_color);
        background-color: #adda2f;
      }
      &.off {
        color: #fff !important;
        background-color: #ccc !important;
      }
    }
  }

  .chart-panel-wrapper {
    display: flex;
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 190px;
    overflow-x: scroll;
    .panel-item {
      border-right: 1px solid var(--border-spacer);
      position: relative;
      .dday {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 27px;
        background-color: #f2f3f6;
        color: var(--color-active-blue);
        font-size: 18px;
        font-weight: 700;
      }
      .chart {
        padding: 30px 20px 10px 10px;
        margin-right: 10px;
      }
      .date {
        position: absolute;
        top: 40px;
        right: 10px;
        padding: 0 10px;
        height: 16px;
        line-height: 16px;
        border-radius: 8px;
        background-color: #d9d9d9;
        color: var(--point_color);
        font-size: 10px;
        font-weight: 700;
      }
    }
  }
}
