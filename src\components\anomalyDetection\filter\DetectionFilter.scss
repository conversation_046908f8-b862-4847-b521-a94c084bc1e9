.spa-detection-filter-group {
  .list-item-filter {
    flex: 2;
    &--spaType {
      flex: 1;
    }
  }
}

.filter-item-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  &__head {
    font-weight: 700;
    display: inline-block;
    overflow-wrap: break-word;
  }
}

.MuiSelect-root.MuiSelect-select .filter-item-group {
  align-items: center;
  max-width: 33vw;

  &__head {
    width: 100%;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}