#budgetOptTable {
  .tooltip-container,
  .cell-body-box {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    line-height: 1.1;
    span {
      line-height: 1.1;
      svg {
        vertical-align: bottom;
      }
    }
  }

  .MuiTableCell-head {
    line-height: 1.1;
    .tooltip-icon {
      margin: 0 2px -2px 0;
      display: inline;
      vertical-align: baseline;
    }
  }

  .cell-body-box {
    gap: 8px;
  }

  .status {
    font-size: 13px;
    color: var(--point_color);
    &.SETTING,
    &.FINISHED,
    &.ERROR,
    &.RUNNING {
      font-weight: 700;
    }
    &.FINISHED {
      color: var(--status-active);
    }
    &.ERROR {
      color: var(--status-warning);
    }
    &.RUNNING {
      color: var(--status-running);
    }
  }
}
