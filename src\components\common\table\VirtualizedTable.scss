.virtualized-table {
  border: 1px solid #EFEFEF;
  border-radius: 4px;
  overflow: hidden;
  background-color: #ffffff;
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .virtualized-header-row {
    display: flex;
    align-items: center;
    height: 100%;
    overflow: hidden;
    position: relative;
    min-width: 100%;

    .virtualized-header-cell {
      display: flex;
      align-items: center;
      font-weight: bold;
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:last-child {
        border-right: none;
      }
      &.checkbox-header {
        justify-content: center;
        flex-shrink: 0;
      }
    }
  }
  .Mui-checked {
    color: var(--mop20-active-blue) !important;
  }
  .virtualized-data-row {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #EFEFEF;
    transition: background-color 0.2s ease;
    height: 100%;
    min-width: 100%;

    &:hover {
      background-color: #f5f5f5;
    }

    &.disabled-row {
      background-color: #f5f5f5;
      opacity: 0.6;
      cursor: not-allowed;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    .virtualized-data-cell {
      display: flex;
      align-items: center;
      padding: 30px 10px;

      &:last-child {
        border-right: none;
      }

      &.checkbox-cell {
        justify-content: center;
        flex-shrink: 0;
      }

      .cell-content {
        width: 100%;
        min-width: 0;
        
        .cell-text {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: pointer;
        }
      }
    }
  }

  .virtualized-data-list {
    outline: none;
    overflow-x: auto;
    overflow-y: auto;
  }

  .virtualized-header-grid {
    outline: none;
    overflow: hidden !important;
    
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
    
    .ReactVirtualized__Grid {
      overflow: hidden !important;
    }
  }

  .sticky {
    overflow: hidden !important;
    
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #666;
    font-size: 14px;
  }

  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    
    .MuiCircularProgress-root {
      color: var(--mop20-active-blue, #1976d2);
    }
    
    span {
      font-family: 'Pretendard', sans-serif;
      font-weight: 500;
    }
  }

  @media (max-width: 768px) {
    .virtualized-data-cell {
      padding: 0 8px;
      font-size: 12px;
    }

    .virtualized-header-cell {
      padding: 0 8px;
      font-size: 12px;
    }
  }
}

// Custom scrollbar styling
.virtualized-table {
  .virtualized-data-list {
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }
} 