.mop-dialog {
  .MuiPaper-root.MuiDialog-paper {
    border-radius: 0;
    max-width: unset;
    width: fit-content;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  &__header {
    position: relative;
    text-align: center;
    font-size: 24px;
    padding: 16px 0;
    color: var(--point_color);

    .mop-icon-box {
      position: absolute;
      top: 12px;
      right: 12px;
      cursor: pointer;
    }

    &--background {
      background-color: var(--bg-gray-light);
      .mop-icon-box {
        --background-color: white;
      }
    }
  }

  &__content,
  &__footer {
    padding: 0 32px;
  }

  &__content {
    flex: 1;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 1rem;

    .mop-action-button {
      margin: 0;
    }
  }
}
