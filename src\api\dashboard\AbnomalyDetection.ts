/* istanbul ignore file */

import CommonResponse from '@models/common/CommonResponse';
import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import { UrlsAbnormalDetection, UtmsAbnormalDetection, UtmsAnomalyDetectionParams, UtmAbnormalDetectionDetails, UtmsAbnormalDetectionRules } from '@models/anomalyDetection'
import { AxiosResponse } from 'axios';


export const getUrlsAnomalyDetection = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<UrlsAbnormalDetection> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/dashboard/abnormal/urls?advertiserId=${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};



export const downloadUrlAnomalyDetection = async (advertiserId: number, isLoading = true) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/dashboard/abnormal/urls/${advertiserId}/raw-data`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  if (response.data) {
    openDownloadLink(response);
    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};

export const getUtmsAnomalyDetection = async (advertiserId: number, isLoading = false) => {
  const response: CommonResponse<UtmsAbnormalDetection> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/dashboard/abnormal/utms/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const getUtmsAnomalyDetectionDetail = async (params: UtmsAnomalyDetectionParams, isLoading = false) => {
  const {advertiserId, mediaType, campaignId} = params
  const response: CommonResponse<UtmAbnormalDetectionDetails> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/dashboard/abnormal/utms/${advertiserId}/${mediaType}/${campaignId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return response
};


export const getUtmAnomalyDetectionRules = async (params: UtmsAnomalyDetectionParams, isLoading = false) => {
  const {advertiserId, mediaType, campaignId} = params
  const response: CommonResponse<UtmsAbnormalDetectionRules> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/dashboard/utm-rules/${advertiserId}/${mediaType}/${campaignId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};

export const downloadUtmAnomalyDetection = async (advertiserId: number, isLoading = true) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/dashboard/abnormal/utms/${advertiserId}/raw-data`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  if (response.data) {
    openDownloadLink(response);
    return null;
  } else {
    return response as unknown as CommonResponse;
  }
};