import React, { useState, useEffect } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import { useTranslation } from 'react-i18next';
import {
  getBudgetOptimizationCampaigns,
} from '@api/budgetOpt/BudgetOpt';
import {
  BudgetOptimizationInfo,
  BudgetOptimizationCampaign,
  BudgetOptimizationTargetCampaign,
} from '@models/budgetOpt/BudgetOpt';
import TreeView from '@components/common/TreeView';
import { Icon, TextField } from '@material-ui/core';
import { MediaIcon } from '@components/common';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import { SearchingTextLimitLength } from '@models/common/CommonConstants';
import CommonTooltip from '@components/common/CommonTooltip';
import IntegerNumberFormat from '@components/common/IntegerNumberFormat';
import { downloadCSVFile } from '@utils/jsonToCSV';
import { withStyles } from '@material-ui/core/styles';
import InnerHtml from '@components/common/InnerHtml';
import TreeItem from '@material-ui/lab/TreeItem';
import CustomTooltip from '@components/common/CustomTooltip';

import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg';
import { ReactComponent as CountCheckIcon } from '@components/assets/images/icon_count_check.svg';
import { ReactComponent as DownloadIcon } from '@components/assets/images/icon_download.svg';
import AddIcon from '@material-ui/icons/Add';
import RemoveIcon  from '@material-ui/icons/Remove';
import { mediaActiveStatus } from '@utils/common/MediaType';
import './BudgetOptTreeView.scss';

interface Props {
  optimization: BudgetOptimizationInfo;
  setTargetCampaigns: (value: BudgetOptimizationTargetCampaign[]) => void;
  hasAuthority: boolean;
}

export interface TreeItem {
  type: 'mediaType' | 'accountId' | 'campaign';
  id: string;
  name: string;
  children?: this[];
  parent?: this;
  data?: BudgetOptimizationCampaign;
}

const MyTooltip = withStyles({
  tooltip: {
    '& .bar': {
      display: 'inline-block',
      height: 4,
      margin: '0 8px',
      width: 22,
      verticalAlign: 'middle',
      '&.navy': {
        backgroundColor: 'var(--point_color)',
      },
      '&.red': {
        backgroundColor: '#EB414C',
      }
    },
    '& .new': {
      display: 'inline-block',
      position: 'relative',
      height: 16,
      width: 36,
      marginRight: 4,
      verticalAlign: 'text-bottom',
      '&::after': {
        content: '"NEW"',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        height: 16,
        width: 36,
        fontSize: 8,
        fontWeight: 700,
        color: 'white',
        backgroundColor: '#3B5EC9',
        borderRadius: 8,
      },
    },
  },
})(CommonTooltip)

const BudgetOptTreeView: React.FC<Props> = ({ optimization, setTargetCampaigns, hasAuthority }: Props) => {
  const { t } = useTranslation()
  const [selected, setSelected] = useState<string[]>([])
  const [expanded, setExpanded] = useState<string[]>([])
  const [searchText, setSearchText] = useState<string>('')
  const [treeData, setTreeData] = useState<TreeItem[]>([])

  const _flatten = (node: TreeItem): TreeItem[] => [node, ...(node.children?.flatMap(_flatten) || [])]
  const nodes = _flatten({ id: '', name: '', children: treeData } as TreeItem)
  const allParents = nodes.filter(node => node.type !== 'campaign')
  const targetCampaigns = nodes.filter(node => selected.includes(node.id) && node.type === 'campaign')

  const getTreeData = async() => {
    const hash: {[key: string]: TreeItem[]} = {}
    const _treeData: TreeItem[] = []
    const _selected = optimization.targets.map(target => `${target.mediaType}-${target.accountId}-${target.campaignId}`)

    const res = await getBudgetOptimizationCampaigns(optimization.optimizationId)
    const uniqueMediaTypes = [...new Set(res.campaigns.map(campaign => campaign.mediaType))]
    const uniqueAccountIds = [...new Set(res.campaigns.map(campaign => campaign.accountId))]
    res.campaigns.sort((a, b) => {
      if (a.campaignName < b.campaignName) {
        return -1
      } else if (a.campaignName > b.campaignName) {
        return 1
      } else {
        return 0
      }
    })

    res.campaigns.forEach(campaign => {
      const key = `${campaign.mediaType}-${campaign.accountId}`
      const campaignNode: TreeItem = {
        type: 'campaign',
        id: `${campaign.mediaType}-${campaign.accountId}-${campaign.campaignId}`,
        name: campaign.campaignName,
        data: campaign,
      }
      if (!hash[key]) {
        hash[key] = []
      }
      hash[key].push(campaignNode)
    })

    uniqueMediaTypes.forEach(mediaType => {
      const mediaTypeNode: TreeItem = {
        type: 'mediaType',
        id: `${mediaType}`,
        name: `${mediaType}`,
        children: [],
      }
      uniqueAccountIds.forEach(accountId => {
        const children = hash[`${mediaType}-${accountId}`]
        if (children && children.length > 0) {
          const accountName = children[0].data!.accountName
          const accountIdNode: TreeItem = {
            type: 'accountId',
            id: `${mediaType}-${accountId}`,
            name: accountName!,
            children: children,
            parent: mediaTypeNode,
          }
          children.forEach(child => child.parent = accountIdNode)
          if (children.every(child => _selected.includes(child.id))) {
            _selected.push(accountIdNode.id)
          }
          mediaTypeNode.children!.push(accountIdNode)
        }
      })
      if (mediaTypeNode.children!.every(child => _selected.includes(child.id))) {
        _selected.push(mediaTypeNode.id)
      }
      _treeData.push(mediaTypeNode)
    })

    unstable_batchedUpdates(() => {
      setTreeData(_treeData)
      setSelected(_selected)
      const nodes = _flatten({ id: '', name: '', children: _treeData } as TreeItem)
      const allParents = nodes.filter(node => node.type !== 'campaign')
      setExpanded(allParents.map(node => node.id))
    })
  }

  const renderTreeItem = (node: TreeItem, depth: number) => {
    return (
      <TreeItem
        key={node.id}
        nodeId={node.id}
        label={
          <CustomTooltip title={node.name} placement="bottom-start">
            <div className="treeItemContent">
              <div className={`labelName ${(node.type === 'campaign' && node.data?.predicted === false) ? 'notPredicted' : ''}`}>
                { node.type === 'mediaType' &&
                  <div className="columns">
                    <MediaIcon mediaType={node.name} size={30} />
                    <div className="mediaName">{node.name}</div>
                  </div>
                }
                { node.type === 'accountId' &&
                  <div className="rows">
                    <div><b>{node.name}</b></div>
                    <div>{node.id.split('-')[1]}</div>
                  </div>
                }
                { node.type === 'campaign' &&
                  <div className="rows">
                    <div><b>{node.name}</b></div>
                    <div>{node.data?.campaignId}</div>
                  </div>
                }
                { node.data?.newlyCreated && <Icon className="new" /> }
              </div>
              <span className="labelAdType">
                <b className={(node.type === 'campaign' && node.data?.predicted === false) ? 'notPredicted' : ''}>{node.data?.adType ?? ''}</b>
              </span>
              <span className="labelStatus">
                <b className={(node.type === 'campaign' && node.data?.predicted === false) ? 'notPredicted' : ''}>{node.data && mediaActiveStatus(node.data.active, node.data.mediaType)}</b>
              </span>
            </div>
          </CustomTooltip>
        }
        className={`treeItemType-${node.type} ${(node.type === 'campaign' && searchText && !node.name.includes(searchText)) ? 'hidden' : ''}`}
        onLabelClick={(e) => {
          e.preventDefault();
        }}
      >
        {Array.isArray(node.children) ? node.children.map((child) => renderTreeItem(child, depth+1)) : null}
      </TreeItem>
    )
  }

  const onNodeSelect = (nodeId: string) => {
    const node = nodes.find(node => node.id === nodeId)
    if (!node || !hasAuthority) {
      return
    }
    const type = node.type
    if (type === 'mediaType' || type === 'accountId') {
      const flatten = ((type === 'mediaType' ? node.children?.map(n => n.children).flat() : node.children) ?? []).map(n => n!.id)
      const selectedIds = flatten.filter(id => selected.includes(id))
      const targets = [...flatten, nodeId]
      if (type === 'mediaType' && node.children) {
        targets.push.apply(targets, node.children.map(n => n.id))
      }
      if (type === 'accountId') {
        if (flatten.length === selectedIds.length) {
          node.parent && targets.push(node.parent.id) // 제거할 겨우, 부모도 제거
        } else {
          node.parent && isSelected(node.parent, nodeId) && targets.push(node.parent.id) // 추가할 경우에는 부모를 isSelected 체크하고 추가
        }
      }
      if (flatten.length === selectedIds.length) {
        setSelected(selected.filter(id => !targets.includes(id)))
      } else {
        setSelected([...new Set([...selected, ...targets])])
      }
    } else {
      if (selected.includes(nodeId)) {
        const targets = [nodeId]
        if (node.parent) {
          targets.push(node.parent.id)
        }
        if (node.parent?.parent) {
          targets.push(node.parent.parent.id)
        }
        setSelected(selected.filter(id => !targets.includes(id)))
      } else {
        const targets = [nodeId]
        if (node.parent && isSelected(node.parent, nodeId)) {
          targets.push(node.parent.id)
        }
        if (node.parent?.parent && isSelected(node.parent?.parent, nodeId)) {
          targets.push(node.parent.parent.id)
        }
        setSelected([...new Set([...selected, ...targets])])
      }
    }
  }

  const isSelected = (node: TreeItem, clickedId?: string): boolean => {
    if (node.type === 'campaign') {
      return node.id === clickedId ? true : selected.includes(node.id)
    } else if (node.children) {
      return node.id === clickedId ? true : node.children.every(n => isSelected(n, clickedId))
    }
    return false
  }

  const onNodeToggle = (nodeIds: string[]) => {
    setExpanded(nodeIds)
  }

  const toggleAll = () => {
    if (expanded.length === allParents.length) {
      setExpanded([])
    } else {
      setExpanded(allParents.map(node => node.id))
    }
  }

  const downloadCsv = () => {
    downloadCSVFile(targetCampaigns
      .filter((node): node is TreeItem & { data: BudgetOptimizationCampaign } => Boolean(node.data))
      .map(node => ({
        mediaType: node.data.mediaType,
        accountId: node.data.accountId,
        accountName: node.data.accountName,
        campaignId: node.data.campaignId,
        campaignName: node.data.campaignName,
        adType: node.data.adType || '', // adType이 없는 경우 빈 문자열로 처리
        onAndOff: mediaActiveStatus(node.data.active, node.data.mediaType),
      })), `${optimization.optimizationId}-campaigns`)
  }

  useEffect(() => {
    getTreeData()
  }, []) //eslint-disable-line

  useEffect(() => {
    setTargetCampaigns(targetCampaigns
      .filter((node): node is TreeItem & { data: BudgetOptimizationCampaign } => Boolean(node.data))
      .map(node => {
        return {
          accountId: `${node.data.accountId}`,
          campaignId: node.data.campaignId,
          mediaType: node.data.mediaType,
        }
      }))
  }, [selected]) //eslint-disable-line

  return (
    <div id="BudgetOptTreeView">
      <div className="flex-container">
        <div className="tree">
          <div className="treeHeader">
            <button className="toggleWrapper" onClick={toggleAll}>
              { expanded.length === allParents.length && <RemoveIcon /> }
              { expanded.length < allParents.length && <AddIcon color="disabled"/> }
            </button>
            <div className="labelName">
              <MyTooltip
                title={
                  <>
                    <h1>{t('optimization.label.budgetOpt.tooltip.targetCampaign.title')}</h1>
                    <p>
                      <div>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.0')}</div>
                      <div>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.1')}</div>
                      <div>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.2')}</div>
                    </p>
                    <p>
                      <div><span className="bar navy"></span><InnerHtml tagName={'span'} innerHTML={t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.3')} /></div>
                      <div><span className="bar red"></span><InnerHtml tagName={'span'} innerHTML={t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.4')} /></div>
                      <div><span className="new"></span>{t('optimization.label.budgetOpt.tooltip.targetCampaign.contents.5')}</div>
                    </p>
                  </>
                }
                placement="bottom-start"
                className="tooltipContents"
              >
                <span><AdviceMarkIcon />Media / Account / Campaign</span>
              </MyTooltip>
            </div>
            <div className="labelAdType">
              <span>Ad Type</span>
            </div>
            <div className="labelStatus">
              <CommonTooltip
                title={
                  <>
                    <h1>{t('optimization.label.budgetOpt.tooltip.onAndOff.title')}</h1>
                    <p>
                      <div>{t('optimization.label.budgetOpt.tooltip.onAndOff.contents.0')}</div>
                      <div className="indent2">{t('optimization.label.budgetOpt.tooltip.onAndOff.contents.1')}</div>
                      <div className="indent2">{t('optimization.label.budgetOpt.tooltip.onAndOff.contents.2')}</div>
                    </p>
                  </>
                }
                placement="bottom-start"
                className="tooltipContents"
              >
                <span><AdviceMarkIcon />ON / OFF</span>
              </CommonTooltip>
            </div>
            <button className="download" onClick={downloadCsv}>
              <DownloadIcon />
            </button>
          </div>
          <TreeView
            selected={selected}
            expanded={expanded}
            onNodeSelect={onNodeSelect}
            onNodeToggle={onNodeToggle}
          >
            { treeData.map((node) => renderTreeItem(node, 0)) }
          </TreeView>
        </div>
        <div className="sidebar">
          <OutlinedInput
            className="searchInput"
            onChange={(e) => {
              setSearchText(e.target.value)
            }}
            endAdornment={<SearchIcon className="search-icon" />}
            labelWidth={0}
            inputProps={{
              maxLength: SearchingTextLimitLength,
            }}
            placeholder="Campaigns"
          />
          <TextField
            className="counter"
            value={targetCampaigns.length}
            InputProps={{
              inputComponent: IntegerNumberFormat,
              startAdornment: <CountCheckIcon />,
              endAdornment: <span className="endAdornment">Campaigns</span>
            }}
            disabled
          />
          <div className="info">
            <div className="marker">
              <Icon className="select-mark-blue" />
              <div>
                <InnerHtml innerHTML={t('optimization.label.budgetOpt.info.available')} />
              </div>
            </div>
            <div className="marker">
              <Icon className="select-mark-red" />
              <div>
                <InnerHtml innerHTML={t('optimization.label.budgetOpt.info.unavailable')} />
              </div>
            </div>
            <div className="marker">
              <Icon className="select-mark-new" />
              <div>
                <InnerHtml innerHTML={t('optimization.label.budgetOpt.info.linked')} />
              </div>
            </div>
            <div className="indent" style={{margin: "25px 0 10px"}} >
              <InnerHtml innerHTML={t('optimization.label.budgetOpt.info.factor')} />
            </div>
            <div className="indent">
              · {t('optimization.label.budgetOpt.info.active')}
            </div>
            <div className="indent">
              · {t('optimization.label.budgetOpt.info.running')}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BudgetOptTreeView