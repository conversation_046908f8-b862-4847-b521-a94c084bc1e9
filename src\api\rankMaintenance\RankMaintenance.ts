/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import CommonResponse from '@models/common/CommonResponse'
import { Service } from '@models/common/Service'
import {
  RankMaintenanceList,
  RankMaintenanceListRequest,
  RankMaintenanceCreateRequest,
  RankMaintenanceDetail,
  RankMaintenanceKeywordInfo
} from '@models/rankMaintenance/RankMaintenance'
import { RegionTartgetKeyword, SearchKeywordByIdReqeust } from '@models/common/Keyword'

export const getRankMaintenanceList = async (
  param: RankMaintenanceListRequest,
  isLoading = true
): Promise<RankMaintenanceList> => {
  let ret: RankMaintenanceList = {
    maxCount: 0,
    totalCount: 0,
    currentCount: 0,
    pageSize: param.pageSize,
    pageIndex: param.pageIndex,
    keywords: []
  }

  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/keyword',
    method: Method.GET,
    params: {
      queryParams: {
        ...param
      }
    },
    config: {
      isLoading: isLoading
    }
  })

  if (response.successOrNot === 'Y') {
    ret = { ...ret, ...response.data }
    console.log(ret, 'success')
  }

  return ret
}

export const updateRankMaintenanceBidMonitoringYn = async (
  advertiserId: number,
  keywordMonitoringId: number,
  bidYn?: string,
  monitoringYn?: string,
  isLoading = true
) => {
  const bodyParams = {
    advertiserId: advertiserId,
    bidYn: bidYn,
    monitoringYn: monitoringYn
  }
  if (!bidYn) delete bodyParams.bidYn
  if (!monitoringYn) delete bodyParams.monitoringYn
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/keyword/${keywordMonitoringId}`,
    method: Method.PATCH,
    params: {
      bodyParams: bodyParams
    },
    config: {
      isLoading: isLoading
    }
  })
}

export const createRankMaintenance = async (param: RankMaintenanceCreateRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/keyword',
    method: Method.POST,
    params: {
      bodyParams: param
    },
    config: {
      isLoading: isLoading
    }
  })
}

export const getRankMaintenanceDetail = async (
  keywordMonitoringId: number,
  isLoading = true
): Promise<RankMaintenanceDetail> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/keyword/${keywordMonitoringId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : null) as RankMaintenanceDetail
}

export const updateRankMaintenance = async (
  keywordMonitoringId: number,
  param: Partial<RankMaintenanceCreateRequest>,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/keyword/${keywordMonitoringId}`,
    method: Method.PUT,
    params: {
      bodyParams: param
    },
    config: {
      isLoading: isLoading
    }
  })
}

export const deleteRankMaintenance = async (keywordMonitoringId: number, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/keyword/${keywordMonitoringId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading
    }
  })
}

export const getRankMaintenanceKeywordList = async (
  advertiserId: number,
  isLoading = true
): Promise<RankMaintenanceKeywordInfo[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/keywords',
    method: Method.GET,
    params: {
      queryParams: {
        advertiserId: advertiserId
      }
    },
    config: {
      isLoading: isLoading
    }
  })

  return (response.successOrNot === 'Y' ? response.data : null) as RankMaintenanceKeywordInfo[]
}

export const getKeywordById = async (
  param: SearchKeywordByIdReqeust,
  isLoading = true
): Promise<RegionTartgetKeyword> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/keywords/${param.keywordId}`,
    method: Method.GET,
    params: {
      queryParams: {
        mediaType: param.mediaType,
        advertiserId: param.advertiserId
      }
    },
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : null) as RegionTartgetKeyword
}
