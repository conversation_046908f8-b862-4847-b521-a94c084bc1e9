import './UtmDetectionTableFormatter.scss';
import { useTranslation } from 'react-i18next';
import { useSetRecoilState } from 'recoil'
import CustomTooltip from '@components/common/CustomTooltip';
import UtmRuleStatusIcons from './UtmRuleStatusIcons'
import { withStyles } from '@material-ui/core/styles'
import { openAbnormalLandingUrlModal, abnormalLandingUrlDetail } from '@store/AnomalyDetection';
import { UtmAnomalyDetectionColumn, UtmRuleStatus } from '@models/anomalyDetection'
import { AnalyticsType } from '@models/common'
import { getUtmDetectionColumns } from '@utils/settings/utmRule'

type UtmRulesHeadProps = { analyticsType: AnalyticsType }

const UtmRulesHead = ({ analyticsType }: UtmRulesHeadProps) => {
  const { t } = useTranslation()
  return (
    <div className='utm-rule head'>
      <div className='utm-rule title'>
        <p>{t('anomalyDetection.columnHeader.utm.rule.title')}</p>
      </div>
      <div className='utm-rule items'>
        {getUtmDetectionColumns(analyticsType).map((column) =>
          <p key={column}>{t(`anomalyDetection.columnHeader.utm.rule.${analyticsType}.${column}`)}</p>
        )}
      </div>
    </div>
  )
}

const MyTooltip = withStyles({ tooltip: { minWidth: 'unset !important' }})(CustomTooltip)


export default class UtmDetectionTableFormatter {
  analyticsType: AnalyticsType
  constructor(analyticsType: AnalyticsType) {
    this.analyticsType = analyticsType
  }
  getColumnFormat = (orderBy?: string, sorting?: string): Array<UtmAnomalyDetectionColumn> => {
    const { t } = useTranslation();
    const toggleLandingUrlModal = useSetRecoilState(openAbnormalLandingUrlModal);
    const setCurrentLandingUrlDetail = useSetRecoilState(abnormalLandingUrlDetail);

    const openLandingUrlDetailModal = (data: any) => {
      setCurrentLandingUrlDetail(data)
      toggleLandingUrlModal(true)
    }

    const columnAnomalyType = (): UtmAnomalyDetectionColumn => {
      return {
        title: t('anomalyDetection.columnHeader.utm.type'),
        field: 'anomalyType',
        align: 'center',
        sorting: false,
        cellStyle: { width: '7%' },
        render: (rowData) => {
          const chipType = rowData.anomalyType === 'Keyword' ? 'keyword' : 'ad'
          return (
            <div className='cell-body-box'>
              <p className={`chip ${chipType}`}>
                { t(`anomalyDetection.label.anomalyType.${rowData.anomalyType}`) }
              </p>
            </div>
          )
        }
      }
    }

    const columnName = (): UtmAnomalyDetectionColumn => {
      return {
        title: t('anomalyDetection.columnHeader.utm.name'),
        field: 'mediaType',
        align: 'center',
        sorting: false,
        cellStyle: { width: '13%' },
        render: (rowData) => {
          return (
            <MyTooltip
              title={<p>{rowData.name}</p>}
              placement="bottom"
              arrow
            >
              <div className='cell-body-box name'>
                <span className='title'>{rowData.name}</span>
                <span className='id'>{rowData.id}</span>
              </div>
            </MyTooltip>
          )
        }
      }
    }

    const columnUtmRules = (): UtmAnomalyDetectionColumn => {
      return {
        title: <UtmRulesHead analyticsType={this.analyticsType} />,
        field: 'anomalyType',
        align: 'center',
        sorting: false,
        cellStyle: { width: '30%' },
        render: (rowData) => {
          return (
            <div className='cell-body-box rules'>
              {getUtmDetectionColumns(this.analyticsType).map((column) =>
                <div><UtmRuleStatusIcons status={rowData.statusCode[column as keyof UtmRuleStatus]}/></div>
              )}
            </div>
          )
        }
      }
    }

    const columnLandingURLs = (): UtmAnomalyDetectionColumn => {
      return {
        title: t('anomalyDetection.columnHeader.utm.landingUrl'),
        field: 'name',
        align: 'center',
        sorting: false,
        cellStyle: { width: '42%' },
        render: (rowData) => {
          return (
            <div className='cell-body-box urls'>
              <ul>
                {(rowData.landingUrls ? rowData.landingUrls : []).map((url, i) => {
                  return <li key={i}><span>{decodeURIComponent(url)}</span></li>
                })}
              </ul>
              <button className='utm-more' onClick={() => openLandingUrlDetailModal({
                id: rowData.id ?? '',
                name: rowData.name ?? '',
                landingUrls: rowData.landingUrls ?? [],
                mediaType: rowData.mediaType ?? '',
                statusCode: rowData.statusCode
              })}>
                {t('anomalyDetection.label.button.more')}
              </button>
            </div>
          )
        }
      }
    }

    return [
      columnAnomalyType(),
      columnName(),
      columnUtmRules(),
      columnLandingURLs()
    ]
  };
}
