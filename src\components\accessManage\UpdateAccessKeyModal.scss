// FIXME
.update-access-key-modal {
  .MuiDialog-paper {
    width: 760px;
    max-width: unset;
  }
  &__header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #dfe0e9;
    padding: 20px 0;

    .mop-chip {
      color: var(--point_color);
      background-color: var(--bg-gray-light);
      font-size: 10px;
    }

    .mop-icon-box {
      position: absolute;
      top: 12px;
      right: 12px;
    }

    .update-access-key__title {
      font-size: 20px;
      color: var(--color-active-blue);
      font-weight: 700;
    }
  }

  .MuiPaper-rounded {
    border-radius: 0px;
  }
}

#update-access-key-modal {
  .MuiDialog-paperWidthSm {
    max-width: 760px;
    width: 760px;
  }

  .MuiSelect-outlined.MuiSelect-outlined {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    justify-content: center;
    color: var(--gray-dark);
    .advdertiser-item-text {
      color: var(--point_color);
    }
  }

  .access-key-modal-table {
    margin-top: 15px;
    margin-bottom: 15px;
    width: 100%;
    border-top: 2px solid var(--color-active-blue);
    border-bottom: 2px solid var(--color-active-blue);
    border-spacing: 0px;
    &.tbody-scroll {
      display: block;
      table-layout: fixed;
      thead {
        th {
          &:nth-of-type(1) {
            width: 114px;
          }
          &:nth-of-type(2) {
            width: 95px;
          }
          &:nth-of-type(3) {
            width: 162px;
          }
          &:nth-of-type(4) {
            width: 104px;
          }
          &:last-child {
            width: 65px;
          }
        }
      }
      tbody {
        max-height: 224px;
        display: block;
        overflow: auto;
        td {
          > div {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          &:nth-of-type(1) {
            width: 114px;
            > div {
              width: 114px;
            }
          }
          &:nth-of-type(2) {
            width: 95px;
            > div {
              width: 95px;
            }
          }
          &:nth-of-type(3) {
            width: 162px;
            > div {
              width: 162px;
            }
          }
          &:nth-of-type(4) {
            width: 104px;
            > div {
              width: 104px;
            }
          }
          &:last-child {
            width: 50px;
            > div {
              width: 50px;
            }
          }
        }
      }
    }
    &.row-height-large {
      tbody {
        tr {
          height: 48px;
          line-height: 48px;
        }
      }
    }
    .align-left {
      text-align: left;
      .text-icon {
        margin: 0 5px 0 12px;
      }
    }
    thead {
      tr {
        height: 36px;
      }
      th {
        font-size: 12px;
        font-weight: 700;
        color: var(--point_color);
        background-color: var(--bg-gray-light);
      }
    }
    tbody {
      tr {
        height: 36px;
      }
      td {
        text-align: center;
        font-size: 10px;
        font-weight: 400;
        border-bottom: 1px solid var(--gray-light);

        .MuiChip-sizeSmall {
          height: 20px;
        }
        .MuiChip-labelSmall {
          font-size: 10px;
          font-weight: 700;
        }

        .outline-select {
          min-width: 190px;
          height: 36px;
          font-size: 10px;
          border-radius: 0px;
        }

        .MuiTextField-root {
          width: 90%;
          margin-top: 6px;

          input[type='text'] {
            padding: 0;
            width: 465px;
            height: 36px;
            text-align: center;
            font-size: 12px;
          }
        }
        .delete-button {
          cursor: pointer;
        }
        .add-button {
          margin-left: 6px;
          vertical-align: middle;
          cursor: pointer;
        }
        .send-button {
          width: 85px;
          height: 27px;
          background-color: var(--bg-gray-light);
          color: var(--color-active-blue);
          font-weight: bold;
          border-radius: 20px;
        }
      }
    }
  }

  .access-key-modal-close {
    cursor: pointer;
  }

  .modal-update-button,
  .modal-delete-button {
    float: right;
    color: white;
    width: 120px;
    height: 32px;
    border-radius: 20px;
    background-color: var(--point_color);
    margin: 0 auto;
    margin-left: 5px;
    &:disabled {
      opacity: 0.6;
    }
  }

  .access-key-modal-grid {
    margin-top: 15px;
    margin-bottom: 15px;

    .grid-row {
      width: 100%;
      min-height: 40px;
      display: grid;
      grid-template-columns: 25% 75%;
      justify-items: center;
      border-bottom: 1px solid #d7d7d7;
    }

    .grid-row__header {
      border-top: 1px solid var(--point_color);
    }

    .grid-row__label {
      background-color: #f1f6ff;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      word-break: break-all;
      padding: 0px 10px;
    }

    .grid-row__value {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      word-break: break-all;
      padding: 0px 10px;
    }
  }
}
