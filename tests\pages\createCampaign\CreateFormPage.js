/// <reference types="Cypress" />

import BasePage from '@pages/BasePage';

class CreateFormPage extends BasePage {
  // Page elements
  PAGE_CONTAINER = '#createFormPage';
  PAGE_TITLE = '.text-3xl.font-bold';
  BACK_TO_LIST_LINK = 'a[href="/campaign"]';
  
  // Media Account and Channel Section
  MEDIA_ACCOUNT_SECTION = '#MediaAccountChannelSection';
  MEDIA_SELECT = '#select-media';
  CHANNEL_SELECT = '#select-chanel';
  
  // Product List Filter Control
  PRODUCT_FILTER_SECTION = '#ProductListFilterControl';
  PRODUCT_CONDITION_FILTER = '#select-productCondition';
  AD_STATUS_FILTER = '#select-adStatus';
  SEARCH_KEYWORD_INPUT = '#productKeywordInput';
  SEARCH_BUTTON = '[data-testid="searchButton"]';
  
  // Campaign Basic Information
  BASIC_INFO_SECTION = '.CampaignBasicInformation';
  CAMPAIGN_NAME_INPUT = 'input[name="campaignName"]';
  DAILY_BUDGET_UNLIMITED_RADIO = 'input[id="N"]';
  DAILY_BUDGET_LIMITED_RADIO = 'input[id="Y"]';
  DAILY_BUDGET_INPUT = 'input[name="dailyBudget"]';
  DEVICE_TYPE_ALL_RADIO = 'input[id="ALL"]';
  DEVICE_TYPE_PC_RADIO = 'input[id="PC"]';
  DEVICE_TYPE_MOBILE_RADIO = 'input[id="MOBILE"]';
  
  // Buttons
  CREATE_CAMPAIGN_BUTTON = 'button:contains("Create campaign")';
  CANCEL_BUTTON = 'button:contains("Cancel")';
  
  // Modal
  COMPLETION_MODAL = '[data-testid="completed-campaign-modal"]';
  MODAL_TITLE = '.MuiDialogTitle-root';
  MODAL_CONFIRM_BUTTON = '[data-testid="completed-campaign-modal"] button:contains("Confirm")';
  MODAL_CANCEL_BUTTON = '[data-testid="completed-campaign-modal"] button:contains("Cancel")';
  
  // Product Table
  PRODUCT_TABLE = '.product-table';
  PRODUCT_ROW = '.product-row';
  PRODUCT_CHECKBOX = '[data-testid="checkbox-cell-item"] input[type="checkbox"]';
  PRODUCT_CHECKBOX_PARENT = '[data-testid="checkbox-cell-item"]';
  SELECT_ALL_CHECKBOX = '.checkbox-header input[type="checkbox"]';
  
  // Toast messages
  TOAST_MESSAGE = '#CommonToast';

  // Page navigation
  visitCreateCampaignPage() {
    cy.visit('/campaign/create');
    cy.get(this.PAGE_CONTAINER).should('be.visible');
  }

  // Assertions
  assertPageLoaded() {
    cy.get(this.PAGE_CONTAINER).should('be.visible');
    cy.get(this.PAGE_TITLE).should('contain', 'Create a shopping search ad campaign');
    cy.get(this.BACK_TO_LIST_LINK).should('be.visible');
  }

  assertMediaAccountSectionVisible() {
    cy.get(this.MEDIA_ACCOUNT_SECTION).should('be.visible');
    cy.get(this.MEDIA_SELECT).should('be.visible');
    cy.get(this.CHANNEL_SELECT).should('be.visible');
  }

  assertProductFilterSectionVisible() {
    cy.get(this.PRODUCT_FILTER_SECTION).should('be.visible');
    cy.get(this.PRODUCT_CONDITION_FILTER).should('be.visible');
    cy.get(this.AD_STATUS_FILTER).should('be.visible');
    cy.get(this.SEARCH_KEYWORD_INPUT).should('be.visible');
  }

  assertBasicInfoSectionVisible() {
    cy.get(this.BASIC_INFO_SECTION).should('be.visible');
    cy.get(this.CAMPAIGN_NAME_INPUT).should('be.visible');
    cy.get(this.DAILY_BUDGET_UNLIMITED_RADIO).should('be.visible');
    cy.get(this.DAILY_BUDGET_LIMITED_RADIO).should('be.visible');
    cy.get(this.DEVICE_TYPE_ALL_RADIO).should('be.visible');
  }

  assertCreateButtonDisabled() {
    cy.get(this.CREATE_CAMPAIGN_BUTTON).should('be.disabled');
  }

  assertCreateButtonEnabled() {
    cy.get(this.CREATE_CAMPAIGN_BUTTON).should('not.be.disabled');
  }

  assertCompletionModalVisible() {
    cy.get(this.COMPLETION_MODAL).should('be.visible');
  }

  assertCompletionModalHidden() {
    cy.get(this.COMPLETION_MODAL).should('not.exist');
  }

  // Form interactions
  typeCampaignName(campaignName) {
    cy.get(this.CAMPAIGN_NAME_INPUT).clear().type(campaignName);
  }

  selectDailyBudgetUnlimited() {
    cy.get(this.DAILY_BUDGET_UNLIMITED_RADIO).check({ force: true });
    cy.get(this.DAILY_BUDGET_INPUT).should('not.exist');
  }

  selectDailyBudgetLimited() {
    cy.get(this.DAILY_BUDGET_LIMITED_RADIO).check({ force: true });
    // cy.get(this.DAILY_BUDGET_INPUT).should('not.be.disabled');
  }

  setDailyBudget(amount) {
    cy.get(this.DAILY_BUDGET_LIMITED_RADIO).check({ force: true });
    cy.get(this.DAILY_BUDGET_INPUT).type(amount.toString());
  }

  selectDeviceType(deviceType) {
    switch (deviceType) {
      case 'ALL':
        cy.get(this.DEVICE_TYPE_ALL_RADIO).check({ force: true });
        break;
      case 'PC':
        cy.get(this.DEVICE_TYPE_PC_RADIO).check({ force: true });
        break;
      case 'MOBILE':
        cy.get(this.DEVICE_TYPE_MOBILE_RADIO).check({ force: true });
        break;
      default:
        throw new Error(`Invalid device type: ${deviceType}`);
    }
  }

  selectMediaAccount(accountName) {
    cy.get(this.MEDIA_SELECT).click();
    cy.get('li').contains(accountName).click();
  }

  selectChannel(channelName) {
    cy.get(this.CHANNEL_SELECT).click();
    cy.get('li').contains(channelName).click();
  }

  filterProductsByCondition(condition) {
    cy.get(this.PRODUCT_CONDITION_FILTER).click();
    cy.get('li').contains(condition).click();
  }

  filterProductsByAdStatus(status) {
    cy.get(this.AD_STATUS_FILTER).click();
    cy.get('li').contains(status).click();
  }

  searchProducts(keyword) {
    cy.get(this.SEARCH_KEYWORD_INPUT).clear().type(keyword);
  }

  selectProduct(productIndex = 0) {
    cy.get(this.PRODUCT_CHECKBOX).eq(productIndex).click({ force: true });
  }

  selectAllProducts() {
    cy.get(this.SELECT_ALL_CHECKBOX).click({ force: true });
  }

  // Button interactions
  clickCreateCampaign() {
    cy.get(this.CREATE_CAMPAIGN_BUTTON).click();
  }

  clickCancel() {
    cy.get(this.CANCEL_BUTTON).click();
  }

  clickBackToList() {
    cy.get(this.BACK_TO_LIST_LINK).click();
  }

  // Modal interactions
  clickModalConfirm() {
    cy.get(this.MODAL_CONFIRM_BUTTON).click();
  }

  clickModalCancel() {
    cy.get(this.MODAL_CANCEL_BUTTON).click();
  }

  closeModal() {
    cy.get(this.MODAL_CANCEL_BUTTON).click();
  }

  assertCampaignNameRequired() {
    cy.get(this.CAMPAIGN_NAME_INPUT).clear().blur();

  }

  assertDailyBudgetValidation() {
    cy.get(this.DAILY_BUDGET_LIMITED_RADIO).check({ force: true });
    cy.get(this.DAILY_BUDGET_INPUT).type(100).clear()
    // Should show validation error or prevent negative values
    cy.get(this.DAILY_BUDGET_INPUT).should('have.value', '');
  }

  // Complete form workflow
  fillBasicCampaignInfo(campaignName, dailyBudget = null, deviceType = 'ALL') {
    this.typeCampaignName(campaignName);
    
    if (dailyBudget) {
      this.setDailyBudget(dailyBudget);
    } else {
      this.selectDailyBudgetUnlimited();
    }
    
    this.selectDeviceType(deviceType);
  }

  selectIndividualProduct(productIndex = 0) {
    cy.get(this.PRODUCT_CHECKBOX).eq(productIndex + 1).click({ force: true });
  }

  selectProductsForCampaign(productCount = 1) {
    // Use the new method to select individual products
    for (let i = 0; i < productCount; i++) {
      this.selectIndividualProduct(i);
    }
  }

  completeCampaignCreation(campaignName, dailyBudget = null, deviceType = 'ALL', productCount = 5) {
    this.fillBasicCampaignInfo(campaignName, dailyBudget, deviceType);
    this.selectIndividualProduct(productCount);
    this.clickCreateCampaign();
    this.assertCompletionModalVisible();
  }

  // Reset functionality
  assertFormReset() {
    cy.get(this.CAMPAIGN_NAME_INPUT).should('have.value', '');
    cy.get(this.DAILY_BUDGET_UNLIMITED_RADIO).should('be.checked');
    cy.get(this.DAILY_BUDGET_INPUT).should('be.disabled');
    cy.get(this.DEVICE_TYPE_ALL_RADIO).should('be.checked');
  }

  // Navigation
  navigateBackToList() {
    this.clickBackToList();
    cy.url().should('include', '/campaign');
  }

  // Toast messages
  assertToastMessage(message) {
    cy.get(this.TOAST_MESSAGE).should('contain', message);
  }

  // Wait for loading states
  waitForProductsToLoad() {
    cy.get(this.PRODUCT_TABLE).should('be.visible');
    cy.get(this.PRODUCT_ROW).should('have.length.greaterThan', 0);
  }

  waitForMediaAccountsToLoad() {
    cy.get(this.MEDIA_SELECT).should('not.be.disabled');
  }

  waitForChannelsToLoad() {
    cy.get(this.CHANNEL_SELECT).should('not.be.disabled');
  }
}

export default CreateFormPage;
