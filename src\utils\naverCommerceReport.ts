import { NaverCommerceBodyParams } from '@models/report/NaverCommerce'
import { t } from 'i18next'

export enum NaverCommerceIndicator {
  PAGE_VIEW = 'pageView',
  NUM_PURCHASES = 'numPurchases',
  PRODUCT_QUANTITY = 'productQuantity',
  PAY_AMOUNT = 'payAmount',
  REFUND_NUM_PURCHASES = 'refundNumPurchases',
  REFUND_PRODUCT_QUANTITY = 'refundProductQuantity',
  REFUND_PAY_AMOUNT = 'refundPayAmount',
  PRODUCT_COUPON_DISCOUNT_AMOUNT = 'productCouponDiscountAmount',
  ORDER_COUPON_DISCOUNT_AMOUNT = 'orderCouponDiscountAmount',
  COST = 'cost',
  SALES_AMOUNT = 'salesAmount',
  IMPRESSIONS = 'impressions',
  CLICKS = 'clicks',
  CONVERSION = 'conversion',
  DIRECT_CONVERSION = 'directConversion',
  CONVERSION_REVENUE = 'conversionRevenue',
  DIRECT_CONVERSION_REVENUE = 'directConversionRevenue',
  CTR = 'ctr',
  CVR = 'cvr',
  CPM = 'cpm',
  CPA = 'cpa',
  ROAS = 'roas',
  CPC = 'cpc'
}

export const KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE = {
  products: [
    NaverCommerceIndicator.PAGE_VIEW,
    NaverCommerceIndicator.NUM_PURCHASES,
    NaverCommerceIndicator.PRODUCT_QUANTITY,
    NaverCommerceIndicator.PAY_AMOUNT,
    NaverCommerceIndicator.REFUND_NUM_PURCHASES,
    NaverCommerceIndicator.REFUND_PRODUCT_QUANTITY,
    NaverCommerceIndicator.REFUND_PAY_AMOUNT,
    NaverCommerceIndicator.PRODUCT_COUPON_DISCOUNT_AMOUNT,
    NaverCommerceIndicator.ORDER_COUPON_DISCOUNT_AMOUNT
  ],
  ads: [
    NaverCommerceIndicator.COST,
    NaverCommerceIndicator.SALES_AMOUNT,
    NaverCommerceIndicator.IMPRESSIONS,
    NaverCommerceIndicator.CLICKS,
    NaverCommerceIndicator.CONVERSION,
    NaverCommerceIndicator.DIRECT_CONVERSION,
    NaverCommerceIndicator.CONVERSION_REVENUE,
    NaverCommerceIndicator.DIRECT_CONVERSION_REVENUE,
    NaverCommerceIndicator.CTR,
    NaverCommerceIndicator.CVR,
    NaverCommerceIndicator.CPM,
    NaverCommerceIndicator.CPA,
    NaverCommerceIndicator.ROAS,
    NaverCommerceIndicator.CPC
  ]
}

export const SORTING_NAVER_COMMERCE_INDICATORS_MAPPING: Record<string, string> = {
  [NaverCommerceIndicator.PAGE_VIEW]: 'PAGE_VIEW',
  [NaverCommerceIndicator.NUM_PURCHASES]: 'NUM_PURCHASES',
  [NaverCommerceIndicator.PRODUCT_QUANTITY]: 'PRODUCT_QUANTITY',
  [NaverCommerceIndicator.PAY_AMOUNT]: 'PAY_AMOUNT',
  [NaverCommerceIndicator.REFUND_NUM_PURCHASES]: 'REFUND_NUM_PURCHASES',
  [NaverCommerceIndicator.REFUND_PRODUCT_QUANTITY]: 'REFUND_PRODUCT_QUANTITY',
  [NaverCommerceIndicator.REFUND_PAY_AMOUNT]: 'REFUND_PAY_AMOUNT',
  [NaverCommerceIndicator.PRODUCT_COUPON_DISCOUNT_AMOUNT]: 'PRODUCT_COUPON_DISCOUNT_AMOUNT',
  [NaverCommerceIndicator.ORDER_COUPON_DISCOUNT_AMOUNT]: 'ORDER_COUPON_DISCOUNT_AMOUNT',
  [NaverCommerceIndicator.COST]: 'COST',
  [NaverCommerceIndicator.SALES_AMOUNT]: 'SALES_AMOUNT',
  [NaverCommerceIndicator.IMPRESSIONS]: 'IMPRESSIONS',
  [NaverCommerceIndicator.CLICKS]: 'CLICKS',
  [NaverCommerceIndicator.CONVERSION]: 'CONVERSION',
  [NaverCommerceIndicator.DIRECT_CONVERSION]: 'DIRECT_CONVERSION',
  [NaverCommerceIndicator.CONVERSION_REVENUE]: 'CONVERSION_REVENUE',
  [NaverCommerceIndicator.DIRECT_CONVERSION_REVENUE]: 'DIRECT_CONVERSION_REVENUE',
  [NaverCommerceIndicator.CTR]: 'CTR',
  [NaverCommerceIndicator.CVR]: 'CVR',
  [NaverCommerceIndicator.CPM]: 'CPM',
  [NaverCommerceIndicator.CPA]: 'CPA',
  [NaverCommerceIndicator.ROAS]: 'ROAS',
  [NaverCommerceIndicator.CPC]: 'CPC'
}

export type NaverCommerceIndicatorKey = keyof typeof NaverCommerceIndicator
export const naverIndicatorItems = () => {
  return Object.keys(NaverCommerceIndicator).map((indi) => {
    const value = NaverCommerceIndicator[indi as NaverCommerceIndicatorKey]
    return {
      label: t(`naverReport.indicator.lableWithUnit.${value}`),
      value: value
    }
  })
}

export const generateNaverCommerceBodyParams = (productIds: string[], optimizationIds: string[]) => {
  const params: NaverCommerceBodyParams = {}

  if (productIds.length) {
    params.channelProductIds = productIds
  } else {
    params.optimizationIds = optimizationIds.map(Number)
    if (!optimizationIds.length) {
      params.channelProductIds = []
    }
  }

  return params
}
