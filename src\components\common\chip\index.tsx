import tw, { type TwStyle } from 'twin.macro'
import styled from '@emotion/styled'

interface BaseProps {
  isFilled?: boolean
  color?: string
  customStyle?: TwStyle
}

export const BaseChip = styled.div<BaseProps>`
  ${tw`rounded-full flex items-center justify-center font-bold border`};
  ${({ customStyle }) => customStyle};
  ${({ color = '#5E81F4', isFilled }) => isFilled && `border-color: ${color}`};
  ${({ color = '#5E81F4', isFilled }) => isFilled ? 'color: white' : `color: ${color}`};
  ${({ color = '#5E81F4', isFilled }) => isFilled ? `background-color: ${color}` : 'background-color: transparent'};
`;

interface VarianceProps {
  value: number
  customStyle?: TwStyle
}

export const VarianceLayout = styled.p<VarianceProps>`
  ${tw`font-bold rounded-md text-center`};
  ${({ customStyle }) => customStyle };
  ${({ value }) => {
    const isZero = value === 0
    const isPositive = value > 0
    return isZero
      ? tw`text-[#A4A8B5] bg-[#F0F0F0]`
      : isPositive
        ? tw`text-[#EE6069] bg-[#FFF3F4]`
        : tw`text-[#5E81F4] bg-[#F0F5FF]`
  }};
`
export const VarianceChip = ({ customStyle, value }: VarianceProps) => {
  const roundedValue = Math.round(value * 100) / 100
  return (
    <VarianceLayout customStyle={customStyle} value={roundedValue} >
      {`${(roundedValue === 0 ? Math.abs(roundedValue) : roundedValue).toFixed(2)}%`}
    </VarianceLayout>
  )
}