.rounded-dropdown {
  --dropdown-width: 40px;
  --width: 100%;
  --font-size: 14px;
  width: var(--width);
  display: flex;
  align-items: center;
  gap: 8px;

  &__label {
    color: var(--point_color);
    font-weight: 700;
  }
  
  .tooltip-icon {
    margin: 0 2px -2px 0;
    display: inline;
    vertical-align: baseline;
  }

  .MuiInputBase-root.MuiInput-root {
    padding: 8px;
    border: 1px solid #E4E7EE;
    background-color: white;
    border-radius: 9999px;

    flex: 1;
    font-size: var(--font-size);
    font-weight: 300;
    color: var(--point_color);
    box-sizing: border-box;

    .MuiSelect-select.MuiSelect-select {
      padding: 0;

      & .need-select {
        width: 100%;
        text-align: center;
        font-weight: 700;
        font-size: 16px;
      }
    }

    #select-dropdown-icon {
      width: var(--dropdown-width);
      min-width: none;
      height: 100%;
      border: none;
      right: 8px;
    }

    &.MuiInput-underline {
      &:before, &:after, &:hover:not(.Mui-disabled):before {
        border-bottom: none;
      }
    }

    &:focus {
      background: none;
    }
  }
}


.filter-options-popover__rounded-dropdown .MuiPopover-paper {
  margin-top: 1px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.10);
  display: flex;
  flex-direction: column-reverse;
  padding: 8px;
  gap: 8px;

  .MuiList-root.MuiMenu-list {
    padding: 0;
  }

  .need-select {
    display: none;
  }
}
