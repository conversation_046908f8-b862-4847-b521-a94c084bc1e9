/// <reference types='Cypress' />

const mockCampaigns = {
  totalCount: 5,
  configurations: [
    {
      requestId: 1,
      campaignName: '[브랜드A] 베스트 상품 프로모션',
      mediaType: 'NAVER',
      customerName: 'ABC 마케팅',
      businessChannelName: 'ABC 쇼핑몰',
      useDailyBudgetYn: 'Y',
      dailyBudget: 500000,
      device: 'PC',
      campaignCreationStatus: 'SUCCESS',
      adgroupCreationStatus: 'SUCCESS',
      adCreationStatus: 'SUCCESS',
      adsCreationStatus: 'SUCCESS',
      adsReviewStatus: 'APPROVED',
      adgroupCreationSuccessCount: 3,
      adgroupCreationFailCount: 0,
      adsCreationSuccessCount: 5,
      adsCreationFailCount: 0,
      adsApprovedCount: 5,
      adsPendingCount: 0,
      adsUnderReviewCount: 0,
      adsEligibleCount: 0,
      adsDeniedCount: 0
    },
    {
      requestId: 2,
      campaignName: '[브랜드B] 베스트 상품 프로모션',
      mediaType: 'NAVER',
      customerName: 'ABC 마케팅',
      businessChannelName: 'ABC 쇼핑몰',
      useDailyBudgetYn: 'Y',
      dailyBudget: 500000,
      device: 'PC',
      campaignCreationStatus: 'SUCCESS',
      adgroupCreationStatus: 'SUCCESS',
      adCreationStatus: 'PENDING',
      adsCreationStatus: 'PENDING',
      adsReviewStatus: 'PENDING',
      adgroupCreationSuccessCount: 2,
      adgroupCreationFailCount: 0,
      adsCreationSuccessCount: 0,
      adsCreationFailCount: 0,
      adsApprovedCount: 0,
      adsPendingCount: 3,
      adsUnderReviewCount: 0,
      adsEligibleCount: 0,
      adsDeniedCount: 0
    },
    {
      requestId: 3,
      campaignName: '[브랜드C] 베스트 상품 프로모션',
      mediaType: 'NAVER',
      customerName: 'ABC 마케팅',
      businessChannelName: 'ABC 쇼핑몰',
      useDailyBudgetYn: 'Y',
      dailyBudget: 500000,
      device: 'PC',
      campaignCreationStatus: 'SUCCESS',
      adgroupCreationStatus: 'SUCCESS',
      adCreationStatus: 'SUCCESS',
      adsCreationStatus: 'SUCCESS',
      adsReviewStatus: 'UNDER_REVIEW',
      adgroupCreationSuccessCount: 4,
      adgroupCreationFailCount: 0,
      adsCreationSuccessCount: 6,
      adsCreationFailCount: 0,
      adsApprovedCount: 2,
      adsPendingCount: 1,
      adsUnderReviewCount: 3,
      adsEligibleCount: 0,
      adsDeniedCount: 0
    },
    {
      requestId: 4,
      campaignName: '[브랜드D] 베스트 상품 프로모션',
      mediaType: 'NAVER',
      customerName: 'ABC 마케팅',
      businessChannelName: 'ABC 쇼핑몰',
      useDailyBudgetYn: 'Y',
      dailyBudget: 500000,
      device: 'PC',
      campaignCreationStatus: 'SUCCESS',
      adgroupCreationStatus: 'SUCCESS',
      adCreationStatus: 'SUCCESS',
      adsCreationStatus: 'SUCCESS',
      adsReviewStatus: 'DENIED',
      adgroupCreationSuccessCount: 2,
      adgroupCreationFailCount: 1,
      adsCreationSuccessCount: 3,
      adsCreationFailCount: 2,
      adsApprovedCount: 1,
      adsPendingCount: 0,
      adsUnderReviewCount: 0,
      adsEligibleCount: 0,
      adsDeniedCount: 2
    },
    {
      requestId: 5,
      campaignName: '[브랜드E] 베스트 상품 프로모션',
      mediaType: 'NAVER',
      customerName: 'ABC 마케팅',
      businessChannelName: 'ABC 쇼핑몰',
      useDailyBudgetYn: 'Y',
      dailyBudget: 500000,
      device: 'PC',
      campaignCreationStatus: 'FAIL',
      adgroupCreationStatus: 'FAIL',
      adCreationStatus: 'FAIL',
      adsCreationStatus: 'FAIL',
      adsReviewStatus: 'DENIED',
      adgroupCreationSuccessCount: 0,
      adgroupCreationFailCount: 3,
      adsCreationSuccessCount: 0,
      adsCreationFailCount: 5,
      adsApprovedCount: 0,
      adsPendingCount: 0,
      adsUnderReviewCount: 0,
      adsEligibleCount: 0,
      adsDeniedCount: 0
    }
  ]
}
const mediaAccount = [
  {
    customerId: '2232364',
    customerName: '시원스쿨랩',
    sellerAccountId: 'barewalls',
    sellerAccountName: '아트뮤'
  }
]

const channelList = [
  {
    businessChannelId: 'bsn-a001-00-***************',
    businessChannelName: 'http://www.artmu.co.kr',
    status: 'ELIGIBLE'
  },
  {
    businessChannelId: 'bsn-a001-00-***************',
    businessChannelName: 'http://storefarm.naver.com/artmu',
    status: 'ELIGIBLE'
  }
]
const productList = [
  {
    productId: '**********',
    productName: '트윌 USB 3.1 gen1 C타입 OTG케이블',
    categoryName: '디지털/가전>휴대폰액세서리>휴대폰케이블',
    price: 4800,
    stock: 953,
    adRevenue: 0,
    totalRevenue: 0,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '**********',
    productName: '무선 블루투스 이어폰',
    categoryName: '디지털/가전>휴대폰액세서리>이어폰',
    price: 25000,
    stock: 156,
    adRevenue: 1250000,
    totalRevenue: 3900000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882799',
    productName: '스마트폰 보호 케이스',
    categoryName: '디지털/가전>휴대폰액세서리>케이스',
    price: 8900,
    stock: 342,
    adRevenue: 445000,
    totalRevenue: 3043800,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882800',
    productName: '고속 충전기 어댑터',
    categoryName: '디지털/가전>휴대폰액세서리>충전기',
    price: 15000,
    stock: 89,
    adRevenue: 750000,
    totalRevenue: 1335000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882801',
    productName: '스마트 워치 스트랩',
    categoryName: '디지털/가전>웨어러블>스마트워치액세서리',
    price: 12000,
    stock: 234,
    adRevenue: 600000,
    totalRevenue: 2808000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882802',
    productName: '노트북 스탠드 거치대',
    categoryName: '디지털/가전>컴퓨터>노트북액세서리',
    price: 35000,
    stock: 67,
    adRevenue: 1750000,
    totalRevenue: 2345000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882803',
    productName: '무선 마우스',
    categoryName: '디지털/가전>컴퓨터>마우스',
    price: 18000,
    stock: 189,
    adRevenue: 900000,
    totalRevenue: 3402000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882804',
    productName: '블루투스 스피커',
    categoryName: '디지털/가전>오디오>스피커',
    price: 45000,
    stock: 45,
    adRevenue: 2250000,
    totalRevenue: 2025000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882805',
    productName: '게이밍 키보드',
    categoryName: '디지털/가전>컴퓨터>키보드',
    price: 65000,
    stock: 78,
    adRevenue: 3250000,
    totalRevenue: 5070000,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  },
  {
    productId: '3745882806',
    productName: '스마트폰 거치대',
    categoryName: '디지털/가전>휴대폰액세서리>거치대',
    price: 8500,
    stock: 267,
    adRevenue: 425000,
    totalRevenue: 2269500,
    status: 'SALE',
    adStatus: 'ON',
    productImageUrl: 'components/assets/images/product_example.svg'
  }
]
class CreateCampaignMock {
  successWhenGetCampaigns() {
    cy.intercept('GET', '/v1/campaign/sa/shopping/1?pageIndex=1&pageSize=10', {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: mockCampaigns
      }
    }).as('successWhenGetCampaigns')
  }
  successWhenSearchCampaigns(keyword) {
    cy.intercept('GET', `/v1/campaign/sa/shopping/1?pageIndex=1&pageSize=10&campaignName=${keyword}`, {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: mockCampaigns
      }
    }).as('successWhenSearchCampaigns')
  }
  successWhenGetMediaAccount() {
    cy.intercept('GET', `/v1/campaign/sa/shopping/paired-accounts/1`, {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: mediaAccount
      }
    }).as('successWhenGetMediaAccount')
  }
  successWhenGetShoppingMall() {
    cy.intercept('GET', `/v1/campaign/sa/shopping/business-channels/2232364/barewalls`, {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: channelList
      }
    }).as('successWhenGetShoppingMall')
  }
  successWhenCreateCampaign() {
    cy.intercept('POST', '/v1/campaign/sa/shopping', {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: {
          campaignId: 12345,
          message: 'Campaign created successfully'
        }
      }
    }).as('successWhenCreateCampaign')
  }
  successWhenGetProducts() {
    cy.intercept('GET', '/v1/campaign/sa/shopping/products/*/*/*', {
      statusCode: 200,
      body: {
        successOrNot: 'Y',
        statusCode: 'SUCCESS',
        data: productList
      }
    }).as('successWhenGetProducts')
  }
}

export default CreateCampaignMock
