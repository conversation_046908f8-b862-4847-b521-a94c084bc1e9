import * as ApiUtil from '@utils/ApiUtil';
import { getSaReportTable } from './SaReport';

describe('getReportTable', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('328 - 대시보드 리포트 조회 응답이 정상인 경우, 리포트 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        value: {
          salesAmount: '10000',
          impressions: '3',
          clicks: '1',
          transactions: '2',
          transactionRevenue: '300000',
          ctr: '1.02',
          cvr: '1.02',
          cpc: '1.02',
          cpa: '1.02',
          roas: '1.02',
        },
        media: [
          {
            mediaType: 'NAVER',
            value: {
              salesAmount: '10000',
              impressions: '3',
              clicks: '1',
              transactions: '2',
              transactionRevenue: '300000',
              ctr: '1.02',
              cvr: '1.02',
              cpc: '1.02',
              cpa: '1.02',
              roas: '1.02',
            },
          },
        ],
      },
    };

    const requestParam = {
      startDate: '20220522',
      endDate: '20220523',
    };

    const response = responseMock.data;

    mockCallApi.mockResolvedValue(responseMock);
    const reportResponse = await getSaReportTable('reportId', requestParam, true);
    expect(reportResponse).toEqual(response);
  });

  it('328 - 대시보드 리포트 조회 응답이 비정상인 경우, null을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const requestParam = {
      startDate: '20220522',
      endDate: '20220523',
      compareStartDate: '20220520',
      compareEndDate: '20220521',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const reportResponse = await getSaReportTable('reportId', requestParam, true);
    expect(reportResponse).toEqual(null);
  });
});

describe('getReportKeywordReport', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('328 - 대시보드 리포트 디테일 조회 응답이 정상인 경우, 디테일 리포트 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          keywordId: 'id',
          keywordName: '키워드',
          value: {
            salesAmount: '10000',
            impressions: '3',
            clicks: '1',
            transactions: '2',
            transactionRevenue: '300000',
            ctr: '1.02',
            cvr: '1.02',
            cpc: '1.02',
            cpa: '1.02',
            roas: '1.02',
          },
        },
      ],
    };

    const requestParam = {
      startDate: '20220522',
      endDate: '20220523',
    };

    const response = responseMock.data;

    mockCallApi.mockResolvedValue(responseMock);
    // const reportResponse = await getReportKeywordReport('reportId', 'adgroupId', requestParam, true);
    // expect(reportResponse).toEqual(response);
  });

  it('328 - 대시보드 리포트 디테일 조회 응답이 비정상인 경우, 빈 배열을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: [],
    };

    const requestParam = {
      startDate: '20220522',
      endDate: '20220523',
      compareStartDate: '20220520',
      compareEndDate: '20220521',
    };

    mockCallApi.mockResolvedValue(responseMock);
    // const reportResponse = await getReportKeywordReport('reportId', 'adgroupId', requestParam, true);
    // expect(reportResponse).toEqual([]);
  });
});
