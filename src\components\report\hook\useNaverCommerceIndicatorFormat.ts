import { useRecoilValue } from 'recoil';
import { advertiserState } from '@store/Advertiser';
import { AdvertiserCurrencyCode } from '@models/common/Advertiser';
import { NaverCommerceIndicator } from '@utils/naverCommerceReport';
import { numberWithCommas } from '@utils/FormatUtil';

const useNaverCommerceIndicatorFormat = () => {
  const { advertiserCurrencyCode: currency } = useRecoilValue(advertiserState);

  // Check if indicator is a percentage type
  const isPercentage = (indicator: NaverCommerceIndicator) => {
    return [
      NaverCommerceIndicator.CTR,
      NaverCommerceIndicator.CVR,
      NaverCommerceIndicator.ROAS
    ].includes(indicator);
  };

  // Check if indicator is a cost/monetary value
  const isCostValue = (indicator: NaverCommerceIndicator) => {
    return [
      NaverCommerceIndicator.COST,
      NaverCommerceIndicator.SALES_AMOUNT,
      NaverCommerceIndicator.PAY_AMOUNT,
      NaverCommerceIndicator.REFUND_PAY_AMOUNT,
      NaverCommerceIndicator.PRODUCT_COUPON_DISCOUNT_AMOUNT,
      NaverCommerceIndicator.ORDER_COUPON_DISCOUNT_AMOUNT,
      NaverCommerceIndicator.CONVERSION_REVENUE,
      NaverCommerceIndicator.DIRECT_CONVERSION_REVENUE,
      NaverCommerceIndicator.CPM,
      NaverCommerceIndicator.CPC,
      NaverCommerceIndicator.CPA
    ].includes(indicator);
  };

  // Check if indicator is a count/quantity value
  const isCountValue = (indicator: NaverCommerceIndicator) => {
    return [
      NaverCommerceIndicator.PAGE_VIEW,
      NaverCommerceIndicator.NUM_PURCHASES,
      NaverCommerceIndicator.PRODUCT_QUANTITY,
      NaverCommerceIndicator.REFUND_NUM_PURCHASES,
      NaverCommerceIndicator.REFUND_PRODUCT_QUANTITY,
      NaverCommerceIndicator.IMPRESSIONS,
      NaverCommerceIndicator.CLICKS,
      NaverCommerceIndicator.CONVERSION,
      NaverCommerceIndicator.DIRECT_CONVERSION
    ].includes(indicator);
  };

  // Format for KRW currency
  const formatKRW = (value: number, indicator: NaverCommerceIndicator) => {
    if (isPercentage(indicator)) {
      return (value * 100).toFixed(2);
    }
    if (isCountValue(indicator)) {
      return value.toFixed(0);
    }
    return value.toFixed(0);
  };

  // Format for USD currency
  const formatUSD = (value: number, indicator: NaverCommerceIndicator) => {
    if (isPercentage(indicator)) {
      return (value * 100).toFixed(2);
    }
    if (isCostValue(indicator)) {
      return value.toFixed(2);
    }
    if (isCountValue(indicator)) {
      return value.toFixed(0);
    }
    return value.toFixed(2);
  };

  // Main formatting function for display
  const indicatorFormat = (value: string | number, indicator: NaverCommerceIndicator, forDisplay: boolean = true) => {
    if(!value) return '-';
    const nValue = Number(value);
    if (isNaN(nValue)) return value;
    
    if (currency === AdvertiserCurrencyCode.USD) {
      return numberWithCommas(formatUSD(nValue, indicator), false);
    }
    return numberWithCommas(formatKRW(nValue, indicator), false);
  };

  // Format for download/export (returns raw number)
  const indicatorFormatForDownload = (value: string | number, indicator: NaverCommerceIndicator) => {
    const nValue = Number(value);
    if (isNaN(nValue)) {
      return value;
    } else {
      return nValue;
    }
  };

  // Get comparison ratio with proper formatting
  const getCompareRatio = (value: number | string, compareValue?: number | string, compareRatio?: number | string) => {
    if (!compareValue) return '';
    if (compareRatio === '-') return compareRatio;
    if (compareRatio === null || compareRatio === undefined || compareRatio === '') return '0.00%';
    if (value === compareValue) return `${compareRatio}%`;
    if (Number(value) > Number(compareValue)) return `+ ${Number(compareRatio).toFixed(2)}%`;
    return `${Number(compareRatio).toFixed(2)}%`;
  };

  // Format comparison value for display
  const formatCompareValue = (value: string | number, indicator: NaverCommerceIndicator) => {
    return indicatorFormat(value, indicator);
  };

  return {
    indicatorFormat,
    indicatorFormatForDownload,
    getCompareRatio,
    formatCompareValue,
    isPercentage,
    isCostValue,
    isCountValue,
  };
};

export default useNaverCommerceIndicatorFormat;
