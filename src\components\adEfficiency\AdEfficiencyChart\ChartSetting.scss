.chart-setting {
  &-box {
    display: flex;
    flex-direction: column;
    background-color: #f2f3f6;
    padding: 24px;
  }

  &__header {
    margin-bottom: 32px;

    .title {
      font-size: 14px;
      font-weight: 900;
      color: var(--point_color);
    }
    .sub-title {
      padding-left: 8px;
      font-size: 10px;
      color: var(--point_color);
    }
  }

  &__items {
    display: grid;
    grid-template-columns: 100px 160px;
    gap: 8px;
    align-items: center;

    span {
      font-size: 12px;
      font-weight: 700;
      color: var(--point_color);
    }
  }

  &__sizes {
    width: 100%;
    display: grid;
    margin: 32px 0;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    align-items: center;

    .chart-bubble {
      &__box {
        display: grid;
        grid-template-rows: 1fr 24px;
        height: 100%;
        align-items: center;
      }

      &__size {
        --size: 10px;
        border-radius: 100%;
        background-color: #6ec7c2;
        width: var(--size);
        height: var(--size);
        margin: auto;
      }

      &__label {
        font-size: 10px;
        color: var(--point_color);
        text-align: center;
      }
    }
  }

  &__legends {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-thumb {
      border: 2px solid transparent;
      border-radius: 3px;
      background-color: var(--point_color);
    }
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }
  }
}
