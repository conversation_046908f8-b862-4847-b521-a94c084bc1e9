import { useCallback, useEffect, useState } from 'react'
import isEqual from 'lodash/isEqual'

const useDebounce = <T>(value: T, delay: number = 500) => {
  const [debounceValue, setDebounceValue] = useState<T>(value)

  const flush = useCallback(() => {
    setDebounceValue(value)
  }, [value])

  useEffect(() => {
    const handler = setTimeout(() => {
      if (!isEqual(value, debounceValue)) {
        setDebounceValue(value)
      }
    }, delay)
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  return { debounceValue, flush }
}
export default useDebounce
