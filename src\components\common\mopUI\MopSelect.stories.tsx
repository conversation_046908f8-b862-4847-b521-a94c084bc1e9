// src/components/common/mopUI/MopSelect.stories.tsx
import type { Meta, StoryObj } from '@storybook/react'
import { useEffect, useState } from 'react'
import MopSelect from './MopSelect'
import MopSelectOptions from './MopSelectOptions'
import MopSelectOption from './MopSelectOption'

const meta: Meta<typeof MopSelect> = {
  title: 'Components/common/MopUI/MopSelect/Default',
  component: MopSelect,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
A flexible select component that supports both traditional options array and modern children-based composition patterns.

**Key Features:**
- **Dual API Support**: Works with both \`options\` prop (legacy) and \`children\` composition (recommended)
- **Multiple Selection**: Supports single and multiple selection modes
- **Loading State**: Shows loading indicator and prevents interaction when data is being fetched
        `
      }
    }
  },
  tags: ['autodocs'],
  argTypes: {
    id: {
      description: 'Unique identifier for the select component, used for testing and accessibility',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    value: {
      description: 'Current selected value(s). Use string for single selection, string[] for multiple selection',
      control: { type: 'text' },
      table: {
        type: { summary: 'string | string[]' },
        defaultValue: { summary: 'undefined' }
      }
    },
    onChange: {
      description: 'Callback function triggered when selection changes. Receives the new value(s) as parameter',
      action: 'changed',
      table: {
        type: { summary: '(value: string | string[]) => void' },
        defaultValue: { summary: 'undefined' }
      }
    },
    options: {
      description: 'Array of options for legacy mode. Can be string[] or SelectOption[]. Use children approach for modern usage',
      control: { type: 'object' },
      table: {
        type: { summary: 'string[] | SelectOption[]' },
        defaultValue: { summary: 'undefined' }
      }
    },
    placeholder: {
      description: 'Text displayed when no option is selected. Automatically shows "Loading..." when isLoading is true',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '"Please select"' }
      }
    },
    disabled: {
      description: 'Whether the select component is disabled and non-interactive',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    multiple: {
      description: 'Enable multiple selection mode. When true, value should be string[] and multiple options can be selected',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    },
    children: {
      description: 'Child components (MopSelectOptions containing MopSelectOption components) for modern composition pattern',
      control: false,
      table: {
        type: { summary: 'React.ReactNode' },
        defaultValue: { summary: 'undefined' }
      }
    },
    className: {
      description: 'Additional CSS classes to apply to the select container for custom styling',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' }
      }
    },
    isLoading: {
      description: 'Shows loading state with "Loading..." placeholder and disables user interaction',
      control: { type: 'boolean' },
      table: {
        type: { summary: 'boolean' },
        defaultValue: { summary: 'false' }
      }
    }
  }
}

export default meta
type Story = StoryObj<typeof meta>

// Default example using legacy options array
export const Default: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Basic select component using the legacy `options` prop. This approach is maintained for backward compatibility.'
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          options={['Option 1', 'Option 2', 'Option 3']}
          placeholder="Please select an option"
        />
      </div>
    )
  }
}

// Modern children-based approach
export const WithChildren: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Modern children-based composition using `MopSelectOptions` and `MopSelectOption` components. This is the recommended approach for new implementations as it provides more flexibility.'
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-80">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          placeholder="Please select an option"
        >
          <MopSelectOptions>
            <MopSelectOption value="option1">Option 1</MopSelectOption>
            <MopSelectOption value="option2">Option 2</MopSelectOption>
            <MopSelectOption value="option3">Option 3</MopSelectOption>
          </MopSelectOptions>
        </MopSelect>
      </div>
    )
  }
}

// Options with custom icons
export const WithIcons: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Select options can include icons to provide visual context. The `icon` prop adds an icon before the option text.'
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    
    return (
      <div className="w-96">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          placeholder="Choose your plan"
        >
          <MopSelectOptions>
            <MopSelectOption 
              value="basic"
              icon={<span>🏠</span>}
            >
              Basic Plan
            </MopSelectOption>
            <MopSelectOption 
              value="pro"
              icon={<span>⭐</span>}
            >
              Pro Plan
            </MopSelectOption>
            <MopSelectOption 
              value="enterprise"
              icon={<span>🏢</span>}
            >
              Enterprise Plan
            </MopSelectOption>
          </MopSelectOptions>
        </MopSelect>
      </div>
    )
  }
}

// Loading state example
export const LoadingState: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Select component in loading state. Shows "Loading..." placeholder and prevents user interaction until loading is complete.'
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    const [isLoading, setIsLoading] = useState<boolean>(true)
    
    // Simulate loading completion after 3 seconds using useEffect
    useEffect(() => {
      const t = setTimeout(() => setIsLoading(false), 3000)
      return () => clearTimeout(t)
    }, [])
    
    return (
      <div className="w-80 space-y-4">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          options={['Option 1', 'Option 2', 'Option 3']}
          placeholder="Please select an option"
          isLoading={isLoading}
        />
        <p className="text-sm text-gray-600">
          {isLoading ? 'Loading options...' : 'Options loaded!'}
        </p>
      </div>
    )
  }
}

// Interactive loading toggle
export const InteractiveLoading: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive example showing how loading state affects the select component behavior. Toggle loading to see the difference.'
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    const [isLoading, setIsLoading] = useState<boolean>(false)
    
    return (
      <div className="w-80 space-y-4">
        <MopSelect
          value={value}
          onChange={(val) => setValue(val as string)}
          placeholder="Custom placeholder"
          isLoading={isLoading}
        >
          <MopSelectOptions>
            <MopSelectOption value="option1">Option 1</MopSelectOption>
            <MopSelectOption value="option2">Option 2</MopSelectOption>
            <MopSelectOption value="option3">Option 3</MopSelectOption>
          </MopSelectOptions>
        </MopSelect>
        
        <button
          onClick={() => setIsLoading(!isLoading)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          {isLoading ? 'Stop Loading' : 'Start Loading'}
        </button>
        
        <p className="text-sm text-gray-600">
          Current state: {isLoading ? 'Loading' : 'Ready'}
        </p>
      </div>
    )
  }
}

// Placeholder examples
export const PlaceholderExamples: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Different placeholder examples showing default behavior, custom placeholder, and loading state placeholder.'
      }
    }
  },
  render: () => {
    const [value1, setValue1] = useState<string>('')
    const [value2, setValue2] = useState<string>('')
    const [value3, setValue3] = useState<string>('')
    
    return (
      <div className="w-80 space-y-6">
        <div>
          <h4 className="text-sm font-medium mb-2">Default placeholder ("Please select")</h4>
          <MopSelect
            value={value1}
            onChange={(val) => setValue1(val as string)}
            options={['Option 1', 'Option 2', 'Option 3']}
          />
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">Custom placeholder</h4>
          <MopSelect
            value={value2}
            onChange={(val) => setValue2(val as string)}
            options={['Seoul', 'Busan', 'Daegu', 'Incheon']}
            placeholder="Please select a city"
          />
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">Loading state placeholder ("Loading...")</h4>
          <MopSelect
            value={value3}
            onChange={(val) => setValue3(val as string)}
            options={['Data 1', 'Data 2', 'Data 3']}
            placeholder="Please select data"
            isLoading={true}
          />
        </div>
      </div>
    )
  }
}

// Custom placeholder with children
export const CustomPlaceholderWithChildren: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Custom placeholder examples using the children composition pattern.'
      }
    }
  },
  render: () => {
    const [selectedPlan, setSelectedPlan] = useState<string>('')
    const [selectedCategory, setSelectedCategory] = useState<string>('')
    
    return (
      <div className="w-96 space-y-6">
        <div>
          <h4 className="text-sm font-medium mb-2">Select Plan</h4>
          <MopSelect
            value={selectedPlan}
            onChange={(val) => setSelectedPlan(val as string)}
            placeholder="Please select a plan"
          >
            <MopSelectOptions>
              <MopSelectOption value="basic">Basic Plan - $9.99/month</MopSelectOption>
              <MopSelectOption value="pro">Pro Plan - $19.99/month</MopSelectOption>
              <MopSelectOption value="enterprise">Enterprise Plan - $49.99/month</MopSelectOption>
            </MopSelectOptions>
          </MopSelect>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-2">Select Category</h4>
          <MopSelect
            value={selectedCategory}
            onChange={(val) => setSelectedCategory(val as string)}
            placeholder="Choose your interest"
          >
            <MopSelectOptions>
              <MopSelectOption value="marketing">Marketing</MopSelectOption>
              <MopSelectOption value="development">Development</MopSelectOption>
              <MopSelectOption value="design">Design</MopSelectOption>
              <MopSelectOption value="business">Business</MopSelectOption>
            </MopSelectOptions>
          </MopSelect>
        </div>
      </div>
    )
  }
}

// Interactive placeholder demo
export const InteractivePlaceholderDemo: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive demo showing how placeholder changes based on loading state and custom placeholder settings.'
      }
    }
  },
  render: () => {
    const [value, setValue] = useState<string>('')
    const [customPlaceholder, setCustomPlaceholder] = useState<string>('Please select an item')
    const [isLoading, setIsLoading] = useState<boolean>(false)
    
    return (
      <div className="w-96 space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-2">Interactive Placeholder Demo</h4>
          <MopSelect
            value={value}
            onChange={(val) => setValue(val as string)}
            options={['Item 1', 'Item 2', 'Item 3', 'Item 4']}
            placeholder={customPlaceholder}
            isLoading={isLoading}
          />
        </div>
        
        <div className="space-y-2">
          <div>
            <label className="block text-sm font-medium mb-1">Custom Placeholder:</label>
            <input
              type="text"
              value={customPlaceholder}
              onChange={(e) => setCustomPlaceholder(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              placeholder="Enter placeholder text"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="loading-toggle"
              checked={isLoading}
              onChange={(e) => setIsLoading(e.target.checked)}
            />
            <label htmlFor="loading-toggle" className="text-sm">
              Loading state (shows "Loading..." when checked)
            </label>
          </div>
        </div>
        
        <div className="p-3 bg-gray-50 rounded text-sm">
          <p><strong>Current displayed placeholder:</strong></p>
          <p className="text-blue-600">
            {isLoading ? '"Loading..."' : `"${customPlaceholder || 'Please select'}"`}
          </p>
        </div>
      </div>
    )
  }
}

// NEW: Multi select with checkboxes and Select All
export const MultiSelectWithCheckbox: Story = {
  parameters: {
    docs: {
      description: {
        story: '멀티 선택 + 각 옵션 체크박스 + 최상단 모두 선택 행 예시. 표시 영역은 renderSelected로 커스텀.'
      }
    }
  },
  render: () => {
    const items = [
      { value: 'prod001', label: '프리미엄 무선 이어폰' },
      { value: 'prod002', label: '프로 무선 헤드셋' },
      { value: 'prod003', label: '게이밍 마우스' },
      { value: 'prod004', label: '컴팩트 키보드' },
      { value: 'prod005', label: 'USB-C 허브' }
    ]

    const [selected, setSelected] = useState<string[]>([])

    const allValues = items.map((i) => i.value)
    const isAllSelected = selected.length === allValues.length
    const isIndeterminate = selected.length > 0 && !isAllSelected

    const toggleAll = () => {
      setSelected(isAllSelected ? [] : allValues)
    }

    const renderSelected = ({ values, getLabel }: { values: string[] | string; getLabel: (v: string) => string }) => {
      const arr = Array.isArray(values) ? values : values ? [values] : []
      if (arr.length === 0) return null
      const labels = arr.map((v) => getLabel(v))
      const MAX = 3
      if (labels.length <= MAX) return labels.join(', ')
      return `${labels.slice(0, MAX).join(', ')} 외 ${labels.length - MAX}개`
    }

    return (
      <div className="w-[420px]">
        <MopSelect
          multiple
          value={selected}
          onChange={(val) => setSelected(val as string[])}
          placeholder="상품을 선택하세요"
          renderSelected={renderSelected}
        >
          <MopSelectOptions>
            {/* Select All Row */}
            <li
              className="relative cursor-pointer select-none px-5 py-2 hover:bg-[#f9f9fb] hover:rounded transition-colors flex items-center gap-2"
              onClick={toggleAll}
            >
              <input
                type="checkbox"
                readOnly
                checked={isAllSelected}
                ref={(el) => {
                  if (el) el.indeterminate = isIndeterminate
                }}
              />
              <span className="text-sm text-[#333]">모두 선택</span>
            </li>

            {/* Options */}
            {items.map((item) => (
              <MopSelectOption key={item.value} value={item.value} render={({ active, selected: isChecked, children }) => (
                <li
                  className={`relative cursor-pointer select-none px-5 py-2 hover:bg-[#f9f9fb] hover:rounded transition-colors ${active ? 'bg-[#f9f9fb] rounded' : ''} ${isChecked ? 'bg-[#f9f9fb] rounded font-bold' : ''}`}
                >
                  <div className="flex items-center gap-2">
                    <input type="checkbox" readOnly checked={isChecked} />
                    <span className="block truncate text-sm text-[#333]">{children}</span>
                  </div>
                </li>
              )}>
                {item.label}
              </MopSelectOption>
            ))}
          </MopSelectOptions>
        </MopSelect>

        <div className="mt-3 text-sm text-gray-600">
          선택됨: {selected.length}개
        </div>
      </div>
    )
  }
}