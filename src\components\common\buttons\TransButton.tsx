import { cn } from '@utils/index'
import TransText from '../TransText'
import { TransProps } from 'react-i18next'
import { ButtonUI } from '@models/common/UI'
import { MopIcon } from '../icon'
import { MOPIcon } from '@models/common'

const preset = {
  contained: 'text-white bg-landing-black',
  icon: 'flex items-center justify-center',
  outlined: 'text-landing-black border border-landing-black',
  text: 'text-landing-black'
}

interface Props extends TransProps<string> {
  ui?: ButtonUI
  icon?: MOPIcon
  direction?: 'left' | 'right'
}

const TransButton = ({ className, as, ui = ButtonUI.Contained, icon, direction, ...restProps }: Props) => {
  return (
    <TransText
      as="button"
      iconDirection={direction}
      withIcon={!!icon}
      className={cn(
        'py-2 px-4 text-base font-bold rounded',
        'disabled:bg-opacity-30',
        ui === ButtonUI.Contained && preset.contained,
        ui === ButtonUI.ContainedIcon && [preset.contained, preset.icon],
        ui === ButtonUI.Outlined && preset.outlined,
        ui === ButtonUI.OutlinedIcon && [preset.outlined, preset.icon],
        ui === ButtonUI.Text && preset.text,
        ui === ButtonUI.TextIcon && [preset.text, preset.icon],
        className
      )}
      {...restProps}
    >
      {icon && <MopIcon name={icon} />}
    </TransText>
  )
}

export default TransButton
