.adefficiency-result-filter {
  --dropdown-width: 40px;
  display: grid;
  grid-template-columns: 130px 1fr 2fr 2fr 100px;
  grid-template-rows: 22px auto;
  margin: 16px 0;

  &__title {
    display: contents;
    span {
      color: #fff;
      height: 22px;
      background-color: #575b7c;
      font-size: 13px;
      font-weight: 300;
      text-align: center;
      padding-right: var(--dropdown-width);

      &.setting {
        padding-right: 0;
      }
    }
  }

  &__select {
    display: contents;
    &-campaign,
    &-adgroup {
      display: contents;
    }

    .MuiInputBase-root.MuiInput-root {
      min-width: 0;
      box-sizing: border-box;
      border-bottom: 1px solid #b5b7c9;
      border-left: 1px solid #b5b7c9;
      &:last-child {
        border-right: 1px solid #b5b7c9;
      }

      .MuiInputBase-input {
        font-size: 14px;
        font-weight: 300;
        color: var(--point_color);
        box-sizing: border-box;
        text-align: center;
        padding-right: var(--dropdown-width);

        .mop-undefined-chip {
          margin: 0 auto;
        }
        .item-id {
          display: none;
        }

        .item-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &:focus {
          background: none;
        }
      }

      .search-input-dropdown {
        width: var(--dropdown-width);
        min-width: var(--dropdown-width);
        height: 34px;
        position: absolute;
        right: 0px;
        border-left: 1px solid #b5b7c9;
        pointer-events: none;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-bottom: 8px;

        svg {
          display: inline-block;
          min-width: 16px;
          min-height: 16px;
          width: 16px;
          height: 16px;
          border-left: 1px solid var(--point_color);
          border-top: 1px solid var(--point_color);
          transform: rotate(-135deg);
          opacity: 0.6;

          path {
            display: none;
          }
        }

        &.search-input-dropdown-open {
          background-color: var(--point_color);

          svg {
            margin-top: 16px;
            transform: rotate(45deg);
            top: calc(50% - 3px);

            border-left: 1px solid var(--color-white);
            border-top: 1px solid var(--color-white);
          }
        }
      }

      .setting-button {
        cursor: pointer;
        width: 24px;
        height: 24px;
        margin: 0 auto;
      }
    }

    .MuiInput-underline {
      &:before,
      &:after,
      &:hover:not(.Mui-disabled):before {
        border-bottom: none;
      }
    }
  }

  &.CAMPAIGN {
    grid-template-columns: 130px 1fr 1fr 100px;
    .adefficiency-result-filter__title-adgroup,
    .adefficiency-result-filter__select-adgroup {
      display: none;
    }
  }

  &.MEDIA {
    grid-template-columns: 130px 1fr 100px;
    .adefficiency-result-filter__title,
    .adefficiency-result-filter__select {
      &-adgroup,
      &-campaign {
        display: none;
      }
    }
  }
}
