#DvOptimizationResultChart {
  width: 100%;
  height: 100%;
  #doughnuts-chart {
    width: 260px;
    height: 260px;
    padding: 10px;
    border: 1px solid var(--point_color);
    border-radius: 162px;
    > div {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 152px;
      border: none;
    }
    .chart-name {
      position: absolute;
      width: 240px;
      height: 240px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 500;
      color: var(--point_color);
      pointer-events: none;
    }
  }

  #chart-legends {
    width: 220px;
    height: 100%;
    margin-left: 42px;
    padding-top: 8px;

    .chart-legend-row {
      padding: 0 25px 0 15px;
      .label-area {
        .indicator {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          border: 1px solid #000;
        }
        .label {
          width: 55px;
          font-size: 11px;
          font-weight: 300;
          color: var(--point_color);
          line-height: 15px;
          letter-spacing: normal;
          word-break: break-all;
        }
      }
      .value-area {
        color: var(--point_color);
        em {
          color: var(--point_color);
          font-weight: 500;
        }
      }
      &:not(:first-child) {
        margin-top: 8px;
      }
    }
  }
}
