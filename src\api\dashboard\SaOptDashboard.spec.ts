import * as ApiUtil from '@utils/ApiUtil';
import { convertDateToStr, getBeforeDate } from '@utils/DateUtil';
import {
  getSaAbnormalPerformance,
  getSaAbnormalUrls,
  getSaAbnormalUtms,
  getSaCollectionItem,
  getSaCollectionPerformance,
  getSaCollectionStatus,
  getSaFlightBid,
  getSaMaintenance,
  getSaProjectionPlanning,
  getSaProjectionPredictions,
  getSaReport,
} from '@api/dashboard/SaOptDashboard';

describe('getSaReport', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 조회기간 지난 1일-7일 & 비교기간 지난 8일-14일 DvaReport를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        salesAmount: { value: '23209842', compareValue: '25570989', ratio: '-9.23' },
        impressions: { value: '1018142', compareValue: '1141608', ratio: '-10.82' },
        clicks: { value: '16108', compareValue: '19851', ratio: '-18.86' },
        transactions: { value: '291', compareValue: '372', ratio: '-21.77' },
        transactionRevenue: { value: '46504420', compareValue: '60010040', ratio: '-22.51' },
        ctr: { value: '1.58', compareValue: '1.74', ratio: '-9.2' },
        cvr: { value: '1.81', compareValue: '1.87', ratio: '-3.21' },
        cpc: { value: '1441', compareValue: '1288', ratio: '11.88' },
        cpa: { value: '79759', compareValue: '68739', ratio: '16.03' },
        roas: { value: '200.37', compareValue: '234.68', ratio: '-14.62' },
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaReport(
      1,
      [7, 1, 14, 8].map((diff) => convertDateToStr(getBeforeDate(diff)))
    );
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaReport(
      1,
      [7, 1, 14, 8].map((diff) => convertDateToStr(getBeforeDate(diff)))
    );
    expect(response).toEqual(undefined);
  });
});

describe('getSaAbnormalPerformance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          mediaType: 'KAKAO',
          accountId: '323076',
          accountName: '시원스쿨랩',
          campaignId: '213660129873649664',
          campaignName: '05-siwonlab-mo',
          message: 'ROAS of last 7 days was low at 5% compared to last 30 days',
          createdDatetime: '2023.01.26 09:40:23',
        },
      ],
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaAbnormalPerformance(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaAbnormalPerformance(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaCollectionStatus', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Collection의 상태를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        media: [
          { type: 'NAVER', useYn: 'Y', status: 'OK' },
          { type: 'KAKAO', useYn: 'Y', status: 'OK' },
          { type: 'GOOGLE', useYn: 'Y', status: 'OK' },
        ],
        analytics: [{ type: 'GA', useYn: 'Y', status: 'OK' }],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaCollectionStatus(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaCollectionStatus(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaAbnormalUrls', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 url 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        lastUpdated: '2023.01.26 02:00:34',
        abnormals: [
          {
            mediaType: 'GOOGLE',
            accountId: '**********',
            accountName: 'LGE HA_Seeding_VH',
            adgroupId: '************',
            adgroupName: 'YT - WM - Styler - Conversion - ASIA - VH - Performance - Video - VAC - CAT - Shopping',
            statusCode: ['NOT_MATCH_URL_CAMPAIGN_RULES', 'NOT_MATCH_URL_CONTENT_RULES'],
          },
        ],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaAbnormalUrls(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaAbnormalUrls(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaAbnormalUtms', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 utm 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        lastUpdated: '2023.01.26 02:00:34',
        abnormals: [
          {
            mediaType: 'GOOGLE',
            accountId: '**********',
            accountName: 'LGE HA_Seeding_VH',
            adgroupId: '************',
            adgroupName: 'YT - WM - Styler - Conversion - ASIA - VH - Performance - Video - VAC - CAT - Shopping',
            statusCode: ['NOT_MATCH_UTM_CAMPAIGN_RULES', 'NOT_MATCH_UTM_CONTENT_RULES'],
          },
        ],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaAbnormalUtms(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaAbnormalUtms(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaCollectionItem', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Collection Item 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: { optimizations: 11, campaigns: 25, adgroups: 675, keywords: 150051 },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaCollectionItem(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaCollectionItem(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaCollectionPerformance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 주어진 기간의 Collection Performance를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        lastUpdated: '2023.01.26 15:45:44',
        status: 'OK',
        performances: [0, 0, 0, 0, 0, 9679, 183479, 57696, 36489, 36922, 36999, 36203, 35915, 33966],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaCollectionPerformance(1, 14);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaCollectionPerformance(1, 14);
    expect(response).toEqual(undefined);
  });
});

describe('getSaProjectionPlanning', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Planning을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        optimizations: { lastUpdated: '2023.01.26 10:26:16', status: 'OK', totalCount: 11, runCount: 11 },
        keywords: { totalCount: 150051, targetTotalCount: 98785, bidsCount: 1590, keyManagementCount: 32 },
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaProjectionPlanning(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaProjectionPlanning(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaProjectionPredictions', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Prediction을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        models: { lastUpdated: '2023.01.20 15:19:12', status: 'OK', totalCount: 80 },
        predictions: { lastUpdated: '2023.01.26 10:23:29', status: 'OK' },
      },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaProjectionPredictions(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaProjectionPredictions(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaMaintenance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Maintenance을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        keywordCount: 2,
        rankTotalCount: 50,
        rankAchivementCount: 50,
        reachMaxCpc: [],
        monitoring: {
          startDateTime: '2023.01.25 17:20:51',
          endDateTime: '2023.01.26 16:20:45',
          hours: [17, 18, 19, 20, 21, 22, 23, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
          values: [72, 71, 12, 38, 70, 70, 70, 46, 46, 46, 46, 46, 30, 30, 30, 30, 30, 61, 74, 74, 74, 74, 72, 42],
        },
      },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaMaintenance(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaMaintenance(1);
    expect(response).toEqual(undefined);
  });
});

describe('getSaFlightBid', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, bid 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        bidsCount: [1403, 1403, 1403, 1590, 0, 1403],
        bidsHours: [0, 5, 10, 15, 20],
        lastUpdated: '2023.01.26 15:09:20',
        currentTime: '2023.01.26 16:20:50',
        nextBidTime: '2023.01.26 20:00:00',
      },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaFlightBid(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getSaFlightBid(1);
    expect(response).toEqual(undefined);
  });
});
