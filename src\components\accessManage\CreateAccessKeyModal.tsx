import React, { ReactElement, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSetRecoilState } from 'recoil';
import { Button, Dialog, DialogContent, Grid, MenuItem, Select, TextField } from '@material-ui/core';
import { createAccessKey } from '@api/accessManage/AccessManage';
import { MopIcon } from '@components/common';
import {
  AccessManageAdvertiserInfo,
  CreateAccessKeyParams,
  CreateAccessKeyResult,
} from '@models/accessManage/AccessManage';
import { MOPIcon, YNFlag } from '@models/common';
import { StatusCode } from '@models/common/CommonResponse';
import { toastState } from '@store/Toast';
import { downloadCSVFile } from '@utils/jsonToCSV';
import './CreateAccessKeyModal.scss';

type JSONDATA = {
  accessKey: string | undefined;
  accessValue: string | undefined;
}[];

interface Props {
  open: boolean;
  onClose: () => void;
  callback: () => void;
  advertiserList?: AccessManageAdvertiserInfo[];
}

const CreateAccessKeyModal: React.FC<Props> = ({ open, onClose, callback, advertiserList }: Props): ReactElement => {
  const setToast = useSetRecoilState(toastState);
  const { t } = useTranslation();
  const [advertiserId, setAdvertiserId] = useState<number>(-1);
  const [description, setDescription] = useState<string>();
  const [isDisabledSave, setIsDisabledSave] = useState(true);
  const [isDisabledClose, setIsDisabledClose] = useState(false);
  const [accessKeyInfo, setAccessKeyInfo] = useState<CreateAccessKeyResult | null>(null);

  const handleChangeSelect = (
    e: React.ChangeEvent<{
      name?: string | undefined;
      value: any;
    }>
  ) => {
    const isSelect = e.target.value !== '-1';
    setAdvertiserId(e.target.value);
    setIsDisabledSave(!isSelect);
  };

  const handleChangeText = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDescription(e.target.value);
  };

  const handleSave = async () => {
    try {
      const createParam: CreateAccessKeyParams = {
        advertiserId: advertiserId,
        ...(description && { description: description }),
      };

      const response = await createAccessKey(createParam);

      if (response.statusCode === StatusCode.SUCCESS) {
        setToast({ showToast: true, toastMessage: t('common.message.saveSuccess') });
        setIsDisabledClose(true);
        setAccessKeyInfo(response.data);
      } else {
        setToast({ showToast: true, toastMessage: t('common.message.systemError') });
      }
    } catch (error) {
      setToast({ showToast: true, toastMessage: t('common.message.systemError') });
    }
  };

  const handleCopyAccessKey = () => {
    try {
      if (accessKeyInfo) {
        const accessKey = accessKeyInfo.accessKey;
        navigator.clipboard.writeText(accessKey);
        setToast({ showToast: true, toastMessage: t('accessManage.message.modal.create.copySuccess') });
      } else {
        setToast({ showToast: true, toastMessage: t('accessManage.message.modal.create.copyFail') });
      }
    } catch (error) {
      setToast({ showToast: true, toastMessage: t('accessManage.message.modal.create.copyFail') });
    }
  };

  const handleCopyAccessValue = () => {
    try {
      if (accessKeyInfo) {
        const accessValue = accessKeyInfo.accessValue;
        navigator.clipboard.writeText(accessValue);
        setToast({ showToast: true, toastMessage: t('accessManage.message.modal.create.copySuccess') });
        setIsDisabledClose(false);
      } else {
        setToast({ showToast: true, toastMessage: t('accessManage.message.modal.create.copyFail') });
      }
    } catch (error) {
      setToast({ showToast: true, toastMessage: t('accessManage.message.modal.create.copyFail') });
    }
  };

  const handleDownload = () => {
    const jsonData: JSONDATA = [{ accessKey: accessKeyInfo?.accessKey, accessValue: accessKeyInfo?.accessValue }];
    downloadCSVFile(
      jsonData,
      accessKeyInfo?.advertiserName + t('accessManage.label.modal.create.download.fileName.suffix')
    );
    setIsDisabledClose(false);
  };

  const handleModalClose = () => {
    callback();
    onClose();
  };

  useEffect(() => {
    if (open) {
      setAdvertiserId(-1);
      setDescription('');
      setAccessKeyInfo(null);
      setIsDisabledSave(true);
      setIsDisabledClose(false);
    }
  }, [open]);

  return (
    <Dialog open={open} id="create-access-key-modal" onClose={handleModalClose}>
      <section className="create-access-key-modal__header">
        <span className="create-access-key__title">{t('accessManage.label.modal.create.titleLabel')}</span>
        <MopIcon
          name={MOPIcon.CLOSE}
          size={20}
          customClass="access-key-modal-close"
          bgColor="#f3f3f6"
          isDisabled={isDisabledClose}
          onClick={handleModalClose}
        />
      </section>
      {!accessKeyInfo && (
        <DialogContent>
          <Grid className="grid-row">
            {t('accessManage.message.modal.create.notice1')}
            <br />
            {t('accessManage.message.modal.create.notice2')}
          </Grid>
          <table className="access-key-modal-table row-height-large">
            <colgroup>
              <col width="142px" />
              <col width="*" />
            </colgroup>
            <thead>
              <tr>
                <th scope="col">{t('accessManage.label.modal.create.tableHeader.advertiserName')}</th>
                <th scope="col">{t('accessManage.label.modal.create.tableHeader.description')}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <Select
                    name="advertier"
                    value={advertiserId || ''}
                    onChange={handleChangeSelect}
                    displayEmpty
                    variant="outlined"
                    className="outline-select"
                  >
                    <MenuItem value="-1" disabled>
                      {t('accessManage.label.modal.create.placeholder.advertiserName')}
                    </MenuItem>
                    {advertiserList &&
                      advertiserList.map((advertiser) => {
                        return (
                          <MenuItem
                            className="Advertiser"
                            key={`advertiser-${advertiser.advertiserId}`}
                            value={advertiser.advertiserId}
                          >
                            <div className="advdertiser-item-text">{advertiser.advertiserName}</div>
                          </MenuItem>
                        );
                      })}
                  </Select>
                </td>
                <td>
                  <TextField
                    name="description"
                    value={description}
                    onChange={handleChangeText}
                    size="small"
                    variant="outlined"
                    placeholder={t('accessManage.label.modal.create.placeholder.description')}
                  />
                </td>
              </tr>
            </tbody>
          </table>

          <Grid className="grid-row">
            <Button className="modal-save-button" variant="contained" onClick={handleSave} disabled={isDisabledSave}>
              {t('accessManage.button.modal.create.save')}
            </Button>
          </Grid>
        </DialogContent>
      )}
      {accessKeyInfo && (
        <DialogContent>
          <Grid className="grid-row">
            {t('accessManage.message.modal.create.notice3')}
            <br />
            {t('accessManage.message.modal.create.notice4')}
          </Grid>
          <Grid container className="access-key-modal-grid">
            <Grid className="grid-row grid-row__header">
              <Grid className="grid-row__label">{t('accessManage.label.modal.create.tableHeader.advertiserName')}</Grid>
              <Grid className="grid-row__value">{accessKeyInfo.advertiserName}</Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.create.tableHeader.accessKey')}</Grid>
              <Grid className="grid-row__value">
                {accessKeyInfo.accessKey}
                <MopIcon
                  name={MOPIcon.COPY_CLIPBOARD}
                  size={15}
                  customClass="copy-clipboard-button"
                  onClick={handleCopyAccessKey}
                />
              </Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.create.tableHeader.accessValue')}</Grid>
              <Grid className="grid-row__value">
                {accessKeyInfo.accessValue}
                <MopIcon
                  name={MOPIcon.COPY_CLIPBOARD}
                  size={15}
                  customClass="copy-clipboard-button"
                  onClick={handleCopyAccessValue}
                />
              </Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.create.tableHeader.description')}</Grid>
              <Grid className="grid-row__value">{accessKeyInfo.description}</Grid>
            </Grid>
            <Grid className="grid-row">
              <Grid className="grid-row__label">{t('accessManage.label.modal.create.tableHeader.useYn')}</Grid>
              <Grid className="grid-row__value">
                {accessKeyInfo.useYn === YNFlag.Y
                  ? t('accessManage.label.modal.create.useYes')
                  : t('accessManage.label.modal.create.useNo')}
              </Grid>
            </Grid>
          </Grid>
          <Grid className="grid-row">
            <Button
              className="modal-close-button"
              variant="contained"
              onClick={handleModalClose}
              disabled={isDisabledClose}
            >
              {t('accessManage.button.modal.create.complete')}
            </Button>
            <Button className="modal-download-button" variant="contained" onClick={handleDownload}>
              {t('accessManage.button.modal.create.download')}
            </Button>
          </Grid>
        </DialogContent>
      )}
    </Dialog>
  );
};

export default CreateAccessKeyModal;
