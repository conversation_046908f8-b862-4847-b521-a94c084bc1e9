import { atom } from 'recoil';
import { ChartIndicator } from '@models/adEfficiency/AdEfficiency'

export const xAxisState = atom({
  key: 'xAxisState-key',
  default: ChartIndicator.EFFICIENCY
})

export const yAxisState = atom({
  key: 'yAxisState-key',
  default: ChartIndicator.IMPRESSIONS
})

export const sizeState = atom({
  key: 'sizeState-key',
  default: ChartIndicator.COST
})

export const chartDataset = atom({
  key: 'adEfficiency-chart-set',
  default: [] as any
})