import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useRecoilValue } from 'recoil'
import MopSelect from '@components/common/mopUI/MopSelect'
import { useToast } from '@hooks/common'
import { BusinessChannel, BusinessChannelStatus, NaverAdsNaverCommerceAccount } from '@models/createCampaign/CreateCampaign'
import { advertiserState } from '@store/Advertiser'
import './MediaAccountChannelSection.scss'
import { getChannels, getMediaAccount } from '@api/creationCampaign'
import MopSelectOptions from '@components/common/mopUI/MopSelectOptions'
import MopSelectOption from '@components/common/mopUI/MopSelectOption'

interface Props {
  mediaSelected: NaverAdsNaverCommerceAccount
  channelSelected: BusinessChannel
  setMediaSelected: Dispatch<SetStateAction<NaverAdsNaverCommerceAccount>>
  setChannelSelected: Dispatch<SetStateAction<BusinessChannel>>
}

const initialChannel: BusinessChannel = {
  businessChannelId: '',
  businessChannelName: '',
  status: BusinessChannelStatus.ELIGIBLE
}
const initialMediaAccount: NaverAdsNaverCommerceAccount = {
  customerId: '',
  customerName: '',
  sellerAccountId: '',
  sellerAccountName: ''
}

const ARTMU_CHANNEL = "http://storefarm.naver.com/artmu"

const MediaAccountChannelSection: React.FC<Props> = ({
  mediaSelected,
  channelSelected,
  setMediaSelected,
  setChannelSelected
}) => {
  const { t } = useTranslation()
  const [mediaAccounts, setMediaAccounts] = useState<NaverAdsNaverCommerceAccount[]>([])
  const [channels, setChannels] = useState<BusinessChannel[]>([])
  const advertiser = useRecoilValue(advertiserState)
  const { openToast } = useToast()

  const fetchAccounts = async () => {
    try {
      const data = await getMediaAccount(advertiser.advertiserId)
      setMediaAccounts(data)
      if (data && data.length > 0) {
        setMediaSelected(data[0])
      } else {
        setMediaSelected(initialMediaAccount) 
      }
    } catch (error) {
      openToast(t('common.message.systemError'))
    }
  }

  const fetchChannels = async () => {
    try {
      const data = await getChannels(mediaSelected.customerId, mediaSelected.sellerAccountId)
      const channelsWithArtmu: BusinessChannel[] = data.filter(
        (channel) => channel.businessChannelName === ARTMU_CHANNEL
      )

      if (channelsWithArtmu.length > 0) {
        setChannels(channelsWithArtmu)

        const eligibleChannels = channelsWithArtmu.filter(
          (c) => c.status === BusinessChannelStatus.ELIGIBLE
        )

        if (eligibleChannels.length > 0) {
          setChannelSelected(eligibleChannels[0])
        } else{
          setChannelSelected(initialChannel)
        }
      } else {
        setChannels([])
        setChannelSelected(initialChannel)
      }
    } catch (error) {
      openToast(t('common.message.systemError'))
    }
  }

  useEffect(() => {
    fetchAccounts()
  }, [])

  useEffect(() => {
    if (!mediaSelected.customerId) return
    fetchChannels()
  }, [mediaSelected.customerId])

  const optionMediaAccounts = mediaAccounts ? mediaAccounts.map((media) => ({
    label: `${media.customerName} (ID: ${media.customerId})`,
    value: media.customerId
  })) : []

  const handleMediaAccountChange = (newValue: string | string[]) => {
    const selectedMedia = mediaAccounts.find((m) => m.customerId === newValue)
    if (selectedMedia) setMediaSelected(selectedMedia)
  }

  const handleBussinessChannelChange = (newValue: string | string[]) => {
    const selectedChannel = channels.find((c) => c.businessChannelId === newValue)
    if (selectedChannel) setChannelSelected(selectedChannel)
  }

  return (
    <div
      className="flex w-full bg-campaign-background px-5 py-3 gap-5 mb-6 rounded-[4px] border border-campaign-border-light border-solid border-1 font-pretendard"
      id="MediaAccountChannelSection"
    >
      <div className="flex w-1/2 gap-2 items-center">
        <div className="select-label">{t('createCampaign.createModal.mediaAccount')}</div>
        <MopSelect
          id="select-media"
          data-testid="mediaSelect"
          options={optionMediaAccounts}
          value={mediaSelected.customerId || ''}
          onChange={handleMediaAccountChange}
          placeholder={t('createCampaign.createModal.mediaAccount')}
        />
      </div>
      <div className="flex w-1/2 gap-2 items-center">
        <div className="select-label">{t('createCampaign.createModal.channel')}</div>
        <MopSelect
          id="select-chanel"
          data-testid="chanelSelect"
          value={channelSelected.businessChannelId || ''}
          onChange={handleBussinessChannelChange}
          placeholder={t('createCampaign.createModal.channel')}
        >
          <MopSelectOptions>
            {channels.map(channel => 
            <MopSelectOption key={channel.businessChannelId} disabled={channel.status !== BusinessChannelStatus.ELIGIBLE} value={channel.businessChannelId}>
              {channel.businessChannelName}
            </MopSelectOption>)}
          </MopSelectOptions>
        </MopSelect>
      </div>
    </div>
  )
}
export default MediaAccountChannelSection
