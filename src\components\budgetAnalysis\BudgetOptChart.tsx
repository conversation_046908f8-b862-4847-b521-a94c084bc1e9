import { set, parseISO, differenceInDays } from 'date-fns'
import { BudgetData } from '@models/budgetAnalysis/Budget'
import { getChartData } from '@utils/insight/BudgetAnalysis'
import AdviceTooltip from '@components/common/AdviceTooltip'
import { numberWithCommas } from '@utils/FormatUtil'
import { ReactComponent as CircleArrowRight } from '@components/assets/images/circle_filled_arrow_right.svg'
import { useTranslation } from 'react-i18next'
import { MopIcon, OptimizationIcon } from '@components/common/icon'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { ReactComponent as CircleArrowDown } from '@components/assets/images/circle_filled_arrow_down.svg'
import LineChart from '@components/common/chart/LineChart'
import useCustomTooltip from '@components/common/hook/UseCustomTooltip'
import { useAuthority } from '@hooks/common'
import { useNavigate } from 'react-router-dom'
import { MOPIcon } from '@models/common/Icon'
import { TransText } from '@components/common'
import { MopButton } from '@components/common/buttons'

interface Props {
  budget: BudgetData
  toggleData: any
  isOpened: boolean
  toggleHidden: (id: number, type: string) => void
  handleToggleModal: (id: number) => {}
  setOpenPanelIndex: any
  index: number
}

const BudgetOptChart = ({
  budget,
  toggleData,
  isOpened,
  index,
  setOpenPanelIndex,
  toggleHidden,
  handleToggleModal
}: Props) => {
  const { t } = useTranslation()
  const { isBasicAdvertiser, advertiser } = useAuthority();
  const { tooltipInfo } = useCustomTooltip({ horizontalOrigin: 'CENTER', verticalOrigin: 'TOP' })
  const navigate = useNavigate();
  const today = set(new Date(), { hours: 0, minutes: 0, seconds: 0, milliseconds: 0 })
  const diffDay = budget.bidEndDate ? differenceInDays(parseISO(budget.bidEndDate), today) : 9999
  const data = JSON.parse(budget.data[budget.data.length - 1].data).sort((a: any, b: any) => a.budget - b.budget)
  const chartData = getChartData(data, toggleData)

  const options = {
    maintainAspectRatio: false,
    responsive: false,
    interaction: {
      mode: 'index' as const,
      intersect: false
    },
    plugins: {
      legend: {
        display: false
      },
      annotation: {
        drawTime: 'afterDatasetsDraw',
        annotations: [
          {
            type: 'line',
            mode: 'vertical',
            scaleID: 'x',
            value: '100',
            borderColor: '#EB424C',
            borderWidth: 2,
            borderDash: [10, 5],
            label: {
              enabled: true,
              display: true,
              position: 'start',
              yAdjust: -6,
              color: 'white',
              borderRadius: 8,
              content: t('competition.label.chart.currentBudget'),
              backgroundColor: '#EB424C'
            }
          },
          {
            type: 'line',
            borderColor: '#ccc',
            borderDash: [5, 5],
            borderWidth: 1,
            xScaleID: 'x',
            yScaleID: 'y',
            xMin: 0,
            xMax: 150,
            yMin: 0,
            yMax: 150
          }
        ]
      }
    },
    scales: {
      x: {
        display: true,
        type: 'linear',
        position: 'bottom',
        min: 0,
        suggestedMax: 150,
        ticks: {
          beginAtZero: true,
          stepSize: 25,
          labelOffset: 4,
          padding: 3,
          color: 'rgba(43, 49, 82, 1)',
          maxRotation: 0,
          minRotation: 0
        },
        grid: {
          display: true
        },
        title: {
          display: true,
          text: 'Budget %',
          align: 'end',
          color: '#040a45',
          font: {
            size: 10,
            weight: 400
          },
          padding: 3
        }
      },
      y: {
        display: true,
        min: 0,
        suggestedMax: 150,
        ticks: {
          beginAtZero: true,
          stepSize: 25,
          labelOffset: 4,
          padding: 3,
          color: 'rgba(43, 49, 82, 1)'
        },
        grid: {
          display: true
        },
        title: {
          display: true,
          text: '%',
          align: 'end',
          color: '#040a45',
          font: {
            size: 10,
            weight: 400
          },
          padding: 3
        }
      }
    }
  }

  return (
    <div className={`grid-item ${isOpened ? 'activated' : ''}`}>
      <div className="live-chip">
        {budget.bidYn === 'Y' && (
          <div className="live">
            Live
            {diffDay <= 7 && (
              <>
                -<b>{diffDay}d</b>
              </>
            )}
          </div>
        )}
        {budget.bidYn === 'N' && <div className="off">OFF</div>}
      </div>
      <div className={`optimization-name ${budget.newlyCreated ? 'new' : ''}`}>{budget.optimizationName}</div>
      <div className="detail">
        <div className="detail-item left">
          <div className="detail-grid">
            <span className="name">{t('competition.label.detailBox.optId')}</span>
            <span className="value">
              {budget.optimizationId}
              <button className="go-opt-button" onClick={() => handleToggleModal(budget.optimizationId)}>
                Go opt <CircleArrowRight />
              </button>
            </span>
            <span className="name">{t('competition.label.detailBox.media')}</span>
            <span className="value">{t(`common.code.media.${budget.mediaType}`)}</span>
            <span className="name">{t('competition.label.detailBox.period')}</span>
            <span className="value">
              {budget.bidStartDate}~
              {budget.bidEndDate === '9999.12.31' ? t('common.datePeriodPicker.unsetEndDate') : budget.bidEndDate}
            </span>
            <span className="name">{t('competition.label.detailBox.dailyBudget')}</span>
            <span className="value">
              {numberWithCommas(budget.dailyBudget)} <small>{t('competition.label.detailList.vatExcluded')}</small>
            </span>
            <span className="name">{t('competition.label.detailBox.optGoal')}</span>
            <span className="value">
              <OptimizationIcon goalType={budget.optimizationGoal} />
              {t(`common.code.optimizationGoal.${budget.optimizationGoal}`)}
            </span>
          </div>
          <div className="toggle-container">
            <div className="name">
              <AdviceTooltip
                id="budget-analysis-advice-tooltip"
                title={
                  <div>
                    <h1>표시 항목 안내</h1>
                    <div>
                      <div>값이 존재하지 않는 지표의 경우 View ON해도 그래프에 표시되지 않습니다.</div>
                    </div>
                  </div>
                }
                placement="right-start"
                arrow
              >
                <span className="icon">
                  <AdviceMarkIcon />
                </span>
              </AdviceTooltip>
              <span>{t(`rankMaintenance.label.RankMonitoringModal.chart.label.view`)}</span>
            </div>
            <div className="toggle-wrapper">
              <button
                className={`toggle impressions ${toggleData.impressions ? 'on' : 'off'}`}
                onClick={() => toggleHidden(budget.optimizationId, 'impressions')}
              >
                Impressions
              </button>
              <button
                className={`toggle clicks ${toggleData.clicks ? 'on' : 'off'}`}
                onClick={() => toggleHidden(budget.optimizationId, 'clicks')}
              >
                Clicks
              </button>
              <button
                className={`toggle revenue ${toggleData.revenue ? 'on' : 'off'}`}
                onClick={() => toggleHidden(budget.optimizationId, 'revenue')}
              >
                Revenue
              </button>
              <button
                className={`toggle conversions ${toggleData.conversions ? 'on' : 'off'}`}
                onClick={() => toggleHidden(budget.optimizationId, 'conversions')}
              >
                Conversions
              </button>
              <button
                className={`toggle top-impression-share ${toggleData.top_imps ? 'on' : 'off'}`}
                onClick={() => toggleHidden(budget.optimizationId, 'top_imps')}
              >
                Top Impression Share
              </button>
            </div>
          </div>
          <div className="bottom">
            <button className="learn-more" onClick={() => setOpenPanelIndex(isOpened ? -1 : index)}>
              {t(`common.label.button.more`)}
              <CircleArrowDown />
            </button>
          </div>
        </div>
        <div className="detail-item right">
          <LineChart chartData={chartData} options={options} tooltipInfo={tooltipInfo} width={330} height={330} />
          <div className="date">
            {t('competition.label.chart.date')} : {budget.data[budget.data.length - 1].prediction_date}
          </div>
        </div>
      </div>
      {isOpened && (
        <div className="panel">
          {isBasicAdvertiser ? (
            <div className="flex flex-col gap-y-6 w-full h-full justify-center items-center inset-0 bg-white/30 backdrop-blur-sm">
              <MopIcon name={MOPIcon.BLINK} size={80} />
              <TransText i18nKey="common.message.upgradeBasicToPaidPlan" className="font-bold text-lg" />
              <TransText
                as="a"
                href="#"
                onClick={()=>{
                  const path = `/setting/subscription/${advertiser.advertiserId}`
                    navigate(path, {
                      state: {
                        advertiserId: advertiser.advertiserId,
                        advertiserName: advertiser.advertiserName
                      }
                    })}
                }
                className="bg-black text-white py-2 px-5 rounded-full font-bold"
                i18nKey="common.message.upgrade"
              />
            </div>
          ) : (
            budget.data
              .slice(0, -1)
              .reverse()
              .map((el, idx) => {
                const data = JSON.parse(el.data).sort((a: any, b: any) => a.budget - b.budget)
                const chartData = getChartData(data, toggleData)
                return (
                  <div key={idx} className="panel-item">
                    <div className="dday">D-{el.date_idx}</div>
                    <LineChart
                      chartData={chartData}
                      options={options}
                      tooltipInfo={tooltipInfo}
                      className="chart"
                      width={300}
                      height={300}
                    />
                    <div className="date">Date : {el.prediction_date}</div>
                  </div>
                )
              })
          )}
        </div>
      )}
    </div>
  )
}

export default BudgetOptChart
