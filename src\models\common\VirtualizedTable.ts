import { ReactNode } from 'react'

export interface ColumnDefinition<T> {
  key: string
  label: string
  align?: 'left' | 'center' | 'right'
  width?: string | number
  render?: (item: T, index: number) => ReactNode
}

export interface TableOptions {
  height?: number | string
  width?: number | string
  rowHeight?: number
  headerHeight?: number
  stickyHeader?: boolean
  className?: string
  overscanRowCount?: number
  enableHorizontalScroll?: boolean
  minColumnWidth?: number
}

export interface SelectionOptions {
  showCheckbox?: boolean
  selectedItems?: string[]
  onItemSelection?: (itemId: string) => void
  onSelectAll?: () => void
  onDeselectAll?: () => void
}

export interface MessageOptions {
  emptyMessage?: string
  loadingMessage?: string
}

export interface VirtualizedTableProps<T> {
  data: T[]
  columns: ColumnDefinition<T>[]
  getItemId: (item: T) => string
  isItemDisabled?: (item: T) => boolean
  isLoading?: boolean
  tableOptions?: TableOptions
  selectionOptions?: SelectionOptions
  messageOptions?: MessageOptions
  sortFields?: string[]
  onOrderChange?: (field: keyof T) => void
  currentSort?: {field: keyof T, order: 'asc' | 'desc'}
}