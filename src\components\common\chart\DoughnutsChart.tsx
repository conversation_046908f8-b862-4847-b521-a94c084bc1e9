import React from 'react';

import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, registerables } from 'chart.js';

import ChartCustomTooltip, { TooltipOption } from './ChartCustomTooltip';
import { GeneralChartData } from '@models/common/ChartData';
import { TooltipInfo } from '@models/common/TooltipInfo';

interface Props {
  chartData: GeneralChartData;
  options: any;
  plugins?: any;
  tooltipId?:string;
  tooltipInfo?: TooltipInfo;
  tooltipOption?: TooltipOption;
}

const DoughnutsChart: React.FC<Props> = ({ chartData, options, plugins, tooltipInfo, tooltipId, tooltipOption }) => {
  ChartJS.register(...registerables);
  return (
    <>
      <Doughnut options={options} data={chartData as any} plugins={plugins} />
      {(tooltipInfo && tooltipInfo.visible) && (
        <ChartCustomTooltip
          tooltipId={tooltipId}
          left={tooltipInfo.left}
          top={tooltipInfo.top}
          ratio={tooltipInfo.data[0].ratio}
          title={tooltipInfo.data[0].title}
          label={tooltipInfo.data[0].label}
          value={tooltipInfo.data[0].value}
          option={tooltipOption}
        />
      )}
    </>
  );
};

export default DoughnutsChart;
