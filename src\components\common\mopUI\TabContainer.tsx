import React, { Fragment, ReactElement, ReactNode } from 'react'
import { Tab } from '@headlessui/react'
import clsx from 'clsx'

type TabContainerVariant = 'fit' | 'fill'

// 타입 재사용성을 위해 TabRenderState를 별도로 export
export type TabRenderState = { selected: boolean; disabled: boolean; index: number }

export interface TabContainerProps {
  children: ReactNode
  className?: string
  variant?: TabContainerVariant
  selectedIndex?: number
  defaultIndex?: number
  onChange?: (index: number) => void
  unstyled?: boolean
  tabListClassName?: string
  tabPanelsClassName?: string
  tabPanelClassName?: string | ((index: number) => string)
  buttonClassName?: string | ((state: TabRenderState) => string)
}

export interface TabButtonProps {
  children: ReactNode
  className?: string | ((state: TabRenderState) => string)
  disabled?: boolean
  // 안정적인 키 관리를 위한 id 추가 (선택사항)
  id?: string
}

export interface TabContentProps {
  children: ReactNode
  className?: string
  // 안정적인 키 관리를 위한 id 추가 (선택사항)
  id?: string
}

const isTabButtonElement = (child: ReactNode): child is ReactElement<TabButtonProps> => {
  return Boolean(React.isValidElement(child) && (child.type as any).displayName === 'MopTabButton')
}

const isTabContentElement = (child: ReactNode): child is ReactElement<TabContentProps> => {
  return Boolean(React.isValidElement(child) && (child.type as any).displayName === 'MopTabContent')
}

// 포커스 스타일 개선: focus-visible 사용하여 키보드 접근성 향상
const baseTabButtonClasses =
  'p-2.5 h-10 text-xs font-medium hover:cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2'

const selectedTabButtonClasses = 'font-bold border-b border-landing-black'
const unselectedTabButtonClasses = 'text-gray-700 hover:bg-gray-50'

// 안정적인 키 생성 함수
const generateStableKey = (child: ReactElement<TabButtonProps | TabContentProps>, index: number, type: 'button' | 'content'): string => {
  // id가 있으면 우선 사용
  if (child.props.id) {
    return `${type}-${child.props.id}`
  }
  
  // 텍스트 기반 키 생성 (children이 문자열인 경우)
  if (typeof child.props.children === 'string') {
    const cleanText = child.props.children.replace(/\s+/g, '-').toLowerCase()
    return `${type}-${cleanText}-${index}`
  }
  
  // fallback으로 인덱스 사용
  return `${type}-${index}`
}

export const TabButton: React.FC<TabButtonProps> = ({ children }) => {
  // This component is only a marker for the parent to clone and render into HeadlessUI's Tab
  return <>{children}</>
}
;(TabButton as any).displayName = 'MopTabButton'

export const TabContent: React.FC<TabContentProps> = ({ children }) => {
  // This component is only a marker for the parent to clone and render into HeadlessUI's Tab.Panel
  return <>{children}</>
}
;(TabContent as any).displayName = 'MopTabContent'

const TabContainer: React.FC<TabContainerProps> & {
  Button: typeof TabButton
  Content: typeof TabContent
} = ({
  children,
  className,
  variant = 'fill',
  selectedIndex,
  defaultIndex = 0,
  onChange,
  unstyled = false,
  tabListClassName,
  tabPanelsClassName,
  tabPanelClassName,
  buttonClassName,
}) => {
  const childArray = React.Children.toArray(children)

  const buttonChildren = childArray.filter(isTabButtonElement)
  const contentChildren = childArray.filter(isTabContentElement)

  // 개발 환경에서 에러 throw로 조기 발견 유도
  if (buttonChildren.length !== contentChildren.length) {
    const errorMessage = `[TabContainer] The number of TabButton and TabContent children should be equal. Received ${buttonChildren.length} buttons and ${contentChildren.length} contents.`
    
    if (process.env.NODE_ENV === 'development') {
      throw new Error(errorMessage)
    } else {
      // 프로덕션에서는 경고만 출력
      console.warn(errorMessage)
    }
  }

  const groupProps: { selectedIndex?: number; defaultIndex?: number; onChange?: (index: number) => void } = {
    onChange,
  }
  if (typeof selectedIndex === 'number') {
    groupProps.selectedIndex = selectedIndex
  } else {
    groupProps.defaultIndex = defaultIndex
  }

  return (
    <Tab.Group {...groupProps}>
      <div className={clsx('w-full', className)}>
        {/* unstyled 적용 확장: List에도 unstyled 옵션 반영 */}
        <Tab.List className={clsx(!unstyled && 'flex w-full border-b border-[#efefef]', tabListClassName)}>
          {buttonChildren.map((btn, index) => {
            const btnProps = (btn.props as TabButtonProps) || {}
            const disabled = !!btnProps.disabled
            const stableKey = generateStableKey(btn, index, 'button')
            
            return (
              // disabled를 Tab에도 전달하여 키보드 내비게이션 개선
              <Tab as={Fragment} disabled={disabled} key={stableKey}>
                {({ selected }) => {
                  const renderState: TabRenderState = { selected, disabled, index }
                  
                  const containerButtonClass =
                    typeof buttonClassName === 'function'
                      ? buttonClassName(renderState)
                      : buttonClassName
                  const childButtonClass =
                    typeof btnProps.className === 'function'
                      ? btnProps.className(renderState)
                      : btnProps.className

                  return (
                    <button
                      className={clsx(
                        !unstyled && baseTabButtonClasses,
                        !unstyled && (selected ? selectedTabButtonClasses : unselectedTabButtonClasses),
                        variant === 'fill' ? 'flex-1 text-center' : 'shrink-0',
                        containerButtonClass,
                        childButtonClass
                      )}
                      disabled={disabled}
                    >
                      {btn.props.children}
                    </button>
                  )
                }}
              </Tab>
            )
          })}
        </Tab.List>

        {/* unstyled 적용 확장: Panels에도 unstyled 옵션 반영 */}
        <Tab.Panels className={clsx(!unstyled && 'p-2.5 bg-white', tabPanelsClassName)}>
          {contentChildren.map((panel, index) => {
            const stableKey = generateStableKey(panel, index, 'content')
            
            return (
              <Tab.Panel
                key={stableKey}
                className={clsx(
                  typeof tabPanelClassName === 'function' ? tabPanelClassName(index) : tabPanelClassName,
                  (panel.props as TabContentProps).className
                )}
              >
                {panel.props.children}
              </Tab.Panel>
            )
          })}
        </Tab.Panels>
      </div>
    </Tab.Group>
  )
}

TabContainer.Button = TabButton
TabContainer.Content = TabContent

export default TabContainer


