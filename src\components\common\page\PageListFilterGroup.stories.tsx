// src/components/common/page/PageListFilterGroup.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import PageListFilterGroup from './PageListFilterGroup';
import PageListFilter from './PageListFilter';
import { MenuItem } from '@material-ui/core';
import { action } from '@storybook/addon-actions';
import { MopIcon, MediaIcon, TextIcon } from '@components/common';
import { MOPIcon } from '@models/common/Icon';
import { MediaType } from '@models/common/Media';
import { AdvertiserCurrencyCode } from '@models/common/Advertiser';
import { AccountType } from '@models/oauth/Oauth';

const meta: StorybookMeta<typeof PageListFilterGroup> = {
  title: 'Components/Common/Page/PageListFilterGroup',
  component: PageListFilterGroup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    children: {
      description: 'PageListFilter components to be grouped together',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story with simple filters
export const Default: Story = {
  render: () => (
    <PageListFilterGroup>
      <PageListFilter
        label="Status"
        name="statusFilter"
        value="ALL"
        onChange={action('onChange')}
      >
        <MenuItem value="ACTIVE">Active</MenuItem>
        <MenuItem value="INACTIVE">Inactive</MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Category"
        name="categoryFilter"
        value="ALL"
        onChange={action('onChange')}
      >
        <MenuItem value="TYPE_A">Type A</MenuItem>
        <MenuItem value="TYPE_B">Type B</MenuItem>
        <MenuItem value="TYPE_C">Type C</MenuItem>
      </PageListFilter>
    </PageListFilterGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Default PageListFilterGroup with two simple text-based filters.',
      },
    },
  },
};

// ConnectOauthPage example (comprehensive filter group)
export const ConnectOauthPageExample: Story = {
  render: () => (
    <PageListFilterGroup>
      <PageListFilter
        label="OAuth Status"
        name="statusFilter"
        value="ALL"
        onChange={action('statusChange')}
      >
        <MenuItem value="CONNECTED">
          <MopIcon name={MOPIcon.SWITCH_ON} size={32} />
        </MenuItem>
        <MenuItem value="DISCONNECTED">
          <MopIcon name={MOPIcon.SWITCH_OFF} size={32} />
        </MenuItem>
        <MenuItem value="ERROR">
          <MopIcon name={MOPIcon.SWITCH_DISABLE} size={32} />
        </MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Type"
        name="typeFilter"
        value="ALL"
        onChange={action('typeChange')}
      >
        {Object.keys(AccountType).map((accountType) => (
          <MenuItem key={accountType} value={accountType}>
            <span>{accountType}</span>
          </MenuItem>
        ))}
      </PageListFilter>
      
      <PageListFilter
        label="Media"
        name="mediaFilter"
        value="ALL"
        onChange={action('mediaChange')}
      >
        {Object.values(MediaType).map((media) => (
          <MenuItem key={media} value={media} className="mediaFilter-item">
            <MediaIcon mediaType={media} size={24} />
          </MenuItem>
        ))}
      </PageListFilter>
      
      <PageListFilter
        label="Currency"
        name="currencyFilter"
        value="ALL"
        onChange={action('currencyChange')}
      >
        {Object.keys(AdvertiserCurrencyCode).map((currency) => (
          <MenuItem key={currency} value={currency} className="currencyFilter-item">
            <TextIcon code={currency} size={24} />
          </MenuItem>
        ))}
      </PageListFilter>
    </PageListFilterGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Complete filter group as used in ConnectOauthPage, showing OAuth status, account type, media platform, and currency filters with icons and translated labels.',
      },
    },
  },
};

// With many filters (overflow example)
export const ManyFilters: Story = {
  render: () => (
    <PageListFilterGroup>
      <PageListFilter
        label="Filter 1"
        name="filter1"
        value="ALL"
        onChange={action('onChange')}
        width={150}
      >
        <MenuItem value="OPTION1">Option 1</MenuItem>
        <MenuItem value="OPTION2">Option 2</MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Filter 2"
        name="filter2"
        value="ALL"
        onChange={action('onChange')}
        width={150}
      >
        <MenuItem value="OPTION1">Option 1</MenuItem>
        <MenuItem value="OPTION2">Option 2</MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Filter 3"
        name="filter3"
        value="ALL"
        onChange={action('onChange')}
        width={150}
      >
        <MenuItem value="OPTION1">Option 1</MenuItem>
        <MenuItem value="OPTION2">Option 2</MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Filter 4"
        name="filter4"
        value="ALL"
        onChange={action('onChange')}
        width={150}
      >
        <MenuItem value="OPTION1">Option 1</MenuItem>
        <MenuItem value="OPTION2">Option 2</MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Filter 5"
        name="filter5"
        value="ALL"
        onChange={action('onChange')}
        width={150}
      >
        <MenuItem value="OPTION1">Option 1</MenuItem>
        <MenuItem value="OPTION2">Option 2</MenuItem>
      </PageListFilter>
    </PageListFilterGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Filter group with many filters to demonstrate layout behavior with multiple filters.',
      },
    },
  },
};

// Single filter in group
export const SingleFilter: Story = {
  render: () => (
    <PageListFilterGroup>
      <PageListFilter
        label="Status"
        name="statusFilter"
        value="ACTIVE"
        onChange={action('onChange')}
      >
        <MenuItem value="ACTIVE">
          <MopIcon name={MOPIcon.CHECK_CIRCLE} size={24} />
          Active
        </MenuItem>
        <MenuItem value="INACTIVE">
          <MopIcon name={MOPIcon.SWITCH_OFF} size={24} />
          Inactive
        </MenuItem>
      </PageListFilter>
    </PageListFilterGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Filter group containing a single filter - useful for consistent styling.',
      },
    },
  },
};