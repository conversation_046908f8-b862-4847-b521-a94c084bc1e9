import React from 'react'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { cn } from '@utils/index'

type PlanVariant = 'pro' | 'lite';

interface PlanButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant: PlanVariant;
}

const StyledButton = styled.button<{variant: PlanVariant}>`
  ${tw`px-4 py-1 text-sm font-semibold rounded-md text-center`}
  ${({ variant }) =>
    variant === 'pro'
      ? tw`bg-red-50 text-red-600`
      : tw`bg-indigo-50 text-indigo-600`
  }  
`;

export const PlanButton = ({ variant = 'lite', className, children, ...props }: PlanButtonProps) => {
  const fallbackText = variant === 'pro' ? 'Pro' : 'Lite';

  return (
    <StyledButton variant={variant} className={cn(className)} {...props}>
      {children || fallbackText}
    </StyledButton>
  );
};

export default PlanButton
