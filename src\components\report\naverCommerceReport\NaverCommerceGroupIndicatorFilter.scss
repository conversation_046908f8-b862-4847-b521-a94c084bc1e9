.naver-indicator-filter-button {
  background-color: var(--bg-gray-light);
  color: var(--color-active-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  border-radius: 30px;
  margin-left: auto;
  border: none;
  width: 100px;
  height: 30px;
  cursor: pointer;

  span {
    font-size: 12px;
    font-weight: 700;
  }
}

.naver-group-indicator-filter {
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  // min-width: 400px;
  display: flex;
  gap: 24px;

  &.vertical-layout {
    flex-direction: column;
  }

  .indicator-group {
    flex: 1;
    margin-bottom: 0;

    .group-header {
      background: #f9f9fb;
      padding: 8px 12px;
      border-radius: 4px;
      margin-bottom: 16px;
      border-bottom: none;

      .group-checkbox {
        .MuiFormControlLabel-label {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }

        .MuiCheckbox-root {
          padding: 4px;

          &.Mui-checked {
            color: #1976d2;
          }
        }
      }
    }

    .group-indicators {
      display: grid;
      padding-left: 24px;

      &.grid-cols-1 {
        grid-template-columns: repeat(1, 1fr);
        gap: 4px 0;
      }

      &:not(.grid-cols-1) {
        grid-template-columns: repeat(5, 1fr);
        gap: 8px 16px;
        grid-auto-rows: min-content;
      }

      .indicator-checkbox {
        margin: 0;

        .MuiFormControlLabel-label {
          font-size: 14px;
          color: #555;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .MuiCheckbox-root {
          padding: 4px;

          &.Mui-checked {
            color: #1976d2;
          }
        }

        .indicator-label {
          display: flex;
          align-items: center;
          gap: 4px;

          .vat-icon {
            width: 36px;
            height: 36px;
          }
        }
      }
    }
  }
}

.multiple-check-box {
  display: flex;
  justify-content: space-between;

  .check-box-label {
    margin: 0px;
    .MuiFormControlLabel-label {
      padding-left: 6px;
      font-weight: 400;
      display: flex;
      align-items: center;
      gap: 4px;
      color: var(--color-primary);
    }

    .MuiButtonBase-root {
      width: 10px;
      height: 10px;
      padding: 0;
      // 실제 체크박스 UI
      .MuiIconButton-label {
        svg {
          display: none;
        }
        &::before {
          content: '';
          width: 10px;
          height: 10px;
          position: absolute;
          top: 0;
          left: 0;
          border: 1px solid #dedfe4;
          background-color: #fff;
          box-sizing: content-box;
        }
      }
      &.Mui-checked {
        .MuiIconButton-label::after {
          content: '';
          position: absolute;
          top: 2px;
          left: 2px;
          width: 8px;
          height: 8px;
          background-color: var(--color-active-blue);
        }
        &.Mui-disabled .MuiIconButton-label::after {
          background-color: #bdbdbd;
        }
      }
    }
  }
}
