import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuthority } from '@hooks/common'
import { MenuType } from '@models/common/Menu'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredMenuType?: MenuType
  requirePro?: boolean
  fallbackPath?: string
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredMenuType,
  requirePro = false,
  fallbackPath = '/'
}) => {
  const { canAccessMenu, isProAdvertiser } = useAuthority()


  if (!requiredMenuType && !requirePro) {
    return <>{children}</>
  }

  if (requirePro && !isProAdvertiser) {
    return <Navigate to={fallbackPath} replace />
  }

  if (requiredMenuType && !canAccessMenu(requiredMenuType)) {
    return <Navigate to={fallbackPath} replace />
  }

  return <>{children}</>
}

export default ProtectedRoute 