<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Marketing Optimization Platform (MOP)</title>
    <link rel="icon" href="%PUBLIC_URL%/favicon/favicon64x64.ico" />
    <link rel="apple-touch-icon" sizes="57x57" href="%PUBLIC_URL%/favicon/apple-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="%PUBLIC_URL%/favicon/apple-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="%PUBLIC_URL%/favicon/apple-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="%PUBLIC_URL%/favicon/apple-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="%PUBLIC_URL%/favicon/apple-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="%PUBLIC_URL%/favicon/apple-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="%PUBLIC_URL%/favicon/apple-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="%PUBLIC_URL%/favicon/apple-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/favicon/apple-icon-180x180.png" />
    <link rel="icon" type="image/png" sizes="192x192" href="%PUBLIC_URL%/favicon/android-icon-192x192.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="%PUBLIC_URL%/favicon/favicon-96x96.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon/favicon-16x16.png" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png" />
    <meta name="theme-color" content="#ffffff" />
    <!-- <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" /> -->
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="지능화된 알고리즘에 기반한 퍼포먼스 광고 운영 최적화 솔루션입니다." />
    <meta name="naver-site-verification" content="b7eba820dbf8b39e5436566ff3a04f49983ba2b5" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <meta http-equiv="X-UA-compatible" content="IE=edge" />
    <meta http-equiv="subject" content="" />
    <meta http-equiv="title" content="" />
    <meta http-equiv="author" content="" />
    <meta name="keywords" content="" />
    <meta
      name="description"
      content='MOP는 고도화된 AI와 수리최적화 알고리즘에 기반하여 광고 예산 및 입찰을 24시간 자동 운영, 최적화하는 지능화된 "광고 성과 극대화 솔루션"입니다.'
    />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="MOP (Marketing Optimization Platform)" />
    <meta
      property="og:description"
      content='MOP는 고도화된 AI와 수리최적화 알고리즘에 기반하여 광고 예산 및 입찰을 24시간 자동 운영, 최적화하는 지능화된 "광고 성과 극대화 솔루션"입니다.'
    />
    <meta property="og:image" content="" />
    <meta property="og:site_name" content="MOP" />
    <meta property="og:url" content="https://www.mop.co.kr" />
    <!-- Google Tag Manager -->
    <script>
      ;(function (w, d, s, l, i) {
        w[l] = w[l] || []
        w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' })
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != 'dataLayer' ? '&l=' + l : ''
        j.async = true
        j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl
        f.parentNode.insertBefore(j, f)
      })(window, document, 'script', 'dataLayer', 'GTM-T3GLZTQP')
    </script>
    <!-- End Google Tag Manager -->
  </head>

  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript>
      <iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-T3GLZTQP"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe>
    </noscript>
    <!-- End Google Tag Manager (noscript) -->
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
  <script type="text/javascript" src="//wcs.naver.net/wcslog.js"></script>
  <script type="text/javascript">
    if (!wcs_add) var wcs_add = {}
    wcs_add['wa'] = 's_322dbc64f144'
    if (!_nasa) var _nasa = {}
    if (window.wcs) {
      wcs.inflow()
      wcs_do()
    }
  </script>
</html>
