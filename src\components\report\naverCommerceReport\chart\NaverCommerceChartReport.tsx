import React, { useEffect, useState } from 'react'
import { Collapse, MenuItem } from '@material-ui/core'
import { ActiveDate } from '@models/report/ActiveDate'
import { ReactComponent as CircleArrowDown } from '@components/assets/images/circle_filled_arrow_down.svg'
import { ReactComponent as LineIcon } from '@components/assets/images/chart-line.svg'
import { ReactComponent as BarIcon } from '@components/assets/images/chart-bar.svg'
import { ItemFilter } from '@components/common/filter'
import { KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE, naverIndicatorItems } from '@utils/naverCommerceReport'
import { NaverCommerceIndicator } from '@utils/naverCommerceReport'
import { useTranslation } from 'react-i18next'
import { getPeriodTypeBetweenToDates } from '@utils/DateUtil'
import './NaverCommerceChartReport.scss'
import { ReactComponent as VatIncluded } from '@components/assets/images/vat_included.svg'
import { ReactComponent as VatExcluded } from '@components/assets/images/vat_excluded.svg'
import { ChartTerm } from './chartUtils'
import NaverCommerceChart from './NaverCommerceChart'
import { NaverCommerceGraphResponse, NaverCommerceGraphData } from '@models/report/NaverCommerce'

interface ChartData {
  value: {
    [key in NaverCommerceIndicator]: string[]
  }
  compareValue?: {
    [key in NaverCommerceIndicator]: string[]
  }
  dates?: string[]
}
const DATE_TYPE = {
  PERIOD_DATE: 'PERIOD_DATE',
  COMPARE_DATE: 'COMPARE_DATE'
}
interface Props {
  chartData?: ChartData
  graphData?: NaverCommerceGraphResponse
  period?: ActiveDate
  optimization?: string
}

const NaverCommerceChartReport = ({ chartData, graphData, period, optimization }: Props) => {
  const [chartTerm, setChartTerm] = useState(ChartTerm.DAY) // 1, 7, 30
  const [firstAxis, setFirstAxis] = useState<NaverCommerceIndicator>(NaverCommerceIndicator.PAGE_VIEW)
  const [secondAxis, setSecondAxis] = useState<NaverCommerceIndicator | undefined>(NaverCommerceIndicator.PAY_AMOUNT)
  const [chartType, setChartType] = useState('line') // line, bar
  const [isOpened, setIsOpened] = useState(true)
  const [maxTerm, setMaxTerm] = useState<ChartTerm | null>(null)
  const { t } = useTranslation()

  const indicatorItems = naverIndicatorItems()
  const formatMenuPF = KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE.products.map((indicator) => {
    return {
      label: indicatorItems.find((item) => item.value === indicator)?.label,
      value: indicator
    }
  })
  const formatMenuAd = KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE.ads.map((indicator) => {
    return {
      label: indicatorItems.find((item) => item.value === indicator)?.label,
      value: indicator
    }
  })
  const formatMenu = [
    { label: t('naverReport.group.productPerformance'), value: 'header' },
    ...formatMenuPF,
    { label: t('naverReport.group.advertisingPerformance'), value: 'header' },
    ...formatMenuAd
  ]
  // Convert graph data to chart data format
  const convertGraphDataToChartData = (graphData: NaverCommerceGraphResponse): ChartData => {
    const value: { [key in NaverCommerceIndicator]: string[] } = {} as any
    const compareValue: { [key in NaverCommerceIndicator]: string[] } = {} as any

    Object.values(NaverCommerceIndicator).forEach((indicator) => {
      value[indicator] = []
      compareValue[indicator] = []
    })

    // Sort graphData by date to ensure correct order
    const sortedGraphData = [...graphData].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
    // Separate data by division type
    const periodDates: string[] = []
    const compareDates: string[] = []

    sortedGraphData.forEach((item) => {
      const date = item.date
      const dayData = item.value[0] // Get the first (and typically only) data item for this date

      if (dayData.division === DATE_TYPE.PERIOD_DATE) {
        periodDates.push(date)
        Object.values(NaverCommerceIndicator).forEach((indicator) => {
          const fieldValue = dayData[indicator as keyof NaverCommerceGraphData]
          if (fieldValue !== null && fieldValue !== undefined) {
            value[indicator].push(fieldValue.toString())
          } else {
            value[indicator].push('0')
          }
        })
      } else if (dayData.division === DATE_TYPE.COMPARE_DATE) {
        compareDates.push(date)
        Object.values(NaverCommerceIndicator).forEach((indicator) => {
          const fieldValue = dayData[indicator as keyof NaverCommerceGraphData]
          if (fieldValue !== null && fieldValue !== undefined) {
            compareValue[indicator].push(fieldValue.toString())
          } else {
            compareValue[indicator].push('0')
          }
        })
      }
    })

    return { value, compareValue, dates: periodDates }
  }

  const finalChartData = graphData ? convertGraphDataToChartData(graphData) : chartData
  const safeChartData = finalChartData || {
    value: {} as { [key in NaverCommerceIndicator]: string[] },
    compareValue: undefined,
    dates: []
  }

  const openChart = () => {
    setIsOpened(!isOpened)
  }

  const changeChartTerm = ({ currentTarget: { value } }: React.MouseEvent<HTMLButtonElement>) => {
    const targetValue: ChartTerm = parseInt(value)
    setChartTerm(targetValue)
  }

  const changeChartType = ({ currentTarget: { value } }: React.MouseEvent<HTMLButtonElement>) => {
    setChartType(value)
  }

  const changeAxis = (event: React.ChangeEvent<{ name?: string; value: unknown }>) => {
    try {
      const { name, value } = event.target
      if (name === 'firstAxis') {
        setFirstAxis(value as NaverCommerceIndicator)
      } else {
        setSecondAxis(value as NaverCommerceIndicator | undefined)
      }
    } catch (error) {
      console.error('Error in changeAxis:', error)
    }
  }

  useEffect(() => {
    if (!period) return
    const maxKey = getPeriodTypeBetweenToDates(period.startDate.replace(/-/g, ''), period.endDate.replace(/-/g, ''))
    setMaxTerm(maxKey ? ChartTerm[maxKey] : null)
  }, [period])

  return (
    <section className="campaign-chart-report">
      <Collapse in={isOpened} className="campaign-chart-report__collapse">
        <section className="campaign-chart-report__collapse-header">
          <div className="campaign-chart-report__axis-wrapper ">
            <ItemFilter
              name="firstAxis"
              value={firstAxis}
              all={false}
              font={12}
              dropdownSize={28}
              onChange={changeAxis}
            >
              {formatMenu.map(({ value, label }, index) => {
                if (value === 'header')
                  return (
                    <MenuItem
                      style={{
                        backgroundColor: '#E4E7EE'
                      }}
                      disabled
                      key={index}
                    >
                      {label}
                    </MenuItem>
                  )
                return (
                  <MenuItem key={index} value={value} disabled={secondAxis === value}>
                    <span className="indicator-label indicator-label--first">
                      {label}
                      {value === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded />}
                      {value === NaverCommerceIndicator.COST && <VatExcluded />}
                    </span>
                  </MenuItem>
                )
              })}
            </ItemFilter>
            <ItemFilter
              name="secondAxis"
              value={secondAxis}
              all={false}
              font={12}
              dropdownSize={28}
              onChange={changeAxis}
            >
              {formatMenu.map(({ value, label }, index) => {
                if (value === 'header')
                  return (
                    <MenuItem
                      style={{
                        backgroundColor: '#E4E7EE'
                      }}
                      disabled
                      key={index}
                    >
                      {label}
                    </MenuItem>
                  )
                return (
                  <MenuItem key={index} value={value} disabled={firstAxis === value}>
                    <span className="indicator-label indicator-label--second">
                      {label}
                      {value === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded />}
                      {value === NaverCommerceIndicator.COST && <VatExcluded />}
                    </span>
                  </MenuItem>
                )
              })}
            </ItemFilter>
          </div>
          <div className="campaign-chart-report__button-wrapper">
            <div className="campaign-chart-report__button-group">
              {[ChartTerm.DAY, ChartTerm.WEEK, ChartTerm.FORTNIGHT, ChartTerm.MONTH].map((termValue) => (
                <button
                  key={termValue}
                  className={`${chartTerm === termValue && 'campaign-chart-report__button--selected'}`}
                  value={termValue}
                  onClick={changeChartTerm}
                  disabled={termValue > (maxTerm ?? 0)}
                >
                  {`${termValue}`} {termValue === 1 ? t('report.label.button.day') : t('report.label.button.days')}
                </button>
              ))}
            </div>
            <div className="campaign-chart-report__button-group">
              {['line', 'bar'].map((typeValue, idx) => (
                <button
                  key={idx}
                  className={`button-icon ${chartType === typeValue && 'campaign-chart-report__button--selected'}`}
                  value={typeValue}
                  onClick={changeChartType}
                >
                  {typeValue === 'line' ? <LineIcon /> : <BarIcon />}
                </button>
              ))}
            </div>
          </div>
        </section>
        <section className="campaign-chart-report__collapse-content">
          <div className="chart-y-axis-title">
            <span className="y-axis-label y-axis-label__left">
              {t(`naverReport.indicator.lableWithUnit.${firstAxis}`)}
            </span>{' '}
            <span className="y-axis-label y-axis-label__right">
              {secondAxis ? t(`naverReport.indicator.lableWithUnit.${secondAxis}`) : ''}
            </span>
          </div>
          <NaverCommerceChart
            period={period}
            chartData={safeChartData}
            chartType={chartType}
            firstAxis={firstAxis}
            secondAxis={secondAxis}
            chartTerm={chartTerm}
            optimization={optimization}
          />
        </section>
      </Collapse>
      <button className="campaign-chart-report__collapse-button" onClick={openChart}>
        {isOpened ? t('report.label.button.collapse') : t('report.label.button.expand')} <CircleArrowDown />
      </button>
    </section>
  )
}

export default NaverCommerceChartReport
