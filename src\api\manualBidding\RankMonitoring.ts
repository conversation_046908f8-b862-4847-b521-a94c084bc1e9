/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  ShoppingCompetitorInfo,
  CompetitorsInfoUpdateRequest,
  KeywordRankMonitoringChartRequest,
  KeywordRankMonitoringReportRequest,
  ShoppingRankMonitoringChartResult,
  KeywordRankMonitoringTablePerDate,
  KeywordRankMonitoringTableRequest,
  KeywordRankMonitoringViewType,
  SSCompetitorsInfoUpdateRequest,
  RankMonitoringRequest,
  RankMonitoringReportResponse
} from '@models/rankMaintenance/KeywordRankMonitoring';
import { SearchMonitoringKeywordRequest } from '@models/rankMaintenance/SearchMonitoringKeywordRequest';
import { ShoppingMonitoringKeywordResponse } from '@models/rankMaintenance/SearchMonitoringKeywordResponse';

// /v1/rank-maintenance/shopping/monitoring/ad


// 전체 모니터링 소재 순위 조회

export const getTotalKeywordRanks = async(
  queryParam: SearchMonitoringKeywordRequest,
  isLoading = true
): Promise<ShoppingMonitoringKeywordResponse> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/rank-maintenance/shopping/monitoring/ad',
    method: Method.GET,
    params: { queryParams: { ...queryParam } },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as ShoppingMonitoringKeywordResponse;
}

// 소재별 순위유지 모니터링 목록 조회
export const getChartTypeKeywordRankMonitorings = async (
  param: KeywordRankMonitoringReportRequest,
  isLoading = true
): Promise<ShoppingRankMonitoringChartResult[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/monitoring/ad/${param.monitoringId}`,
    method: Method.GET,
    params: {
      queryParams: {
        viewType: KeywordRankMonitoringViewType.GRAPH,
        startDate: param.startDate,
        endDate: param.endDate,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as ShoppingRankMonitoringChartResult[];
};
// TODO : refactoring
export const getTableTypeKeywordRankMonitorings = async (
  param: KeywordRankMonitoringReportRequest,
  isLoading = true
): Promise<KeywordRankMonitoringTablePerDate[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/monitoring/ad/${param.monitoringId}`,
    method: Method.GET,
    params: {
      queryParams: {
        viewType: KeywordRankMonitoringViewType.TABLE,
        startDate: param.startDate,
        endDate: param.endDate,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as KeywordRankMonitoringTablePerDate[];
};
export const getRankMonitoringReport = async (
  param: RankMonitoringRequest,
  isLoading = true,
): Promise<RankMonitoringReportResponse[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/monitoring/ad/${param.monitoringId}`,
    method: Method.GET,
    params: {
      queryParams: {
        viewType: param.viewType,
        startDate: param.startDate,
        endDate: param.endDate,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as RankMonitoringReportResponse[];
};
// 소재 모니터링 경쟁사 목록 조회
export const getKeywordRankMonitoringCompetitors = async (
  monitoringId: number,
  isLoading = true
): Promise<ShoppingCompetitorInfo[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/monitoring/ad/${monitoringId}/competitors`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : []) as ShoppingCompetitorInfo[];
};


/* istanbul ignore next */
export const updateKeywordRankMonitoringCompetitors = async (param: SSCompetitorsInfoUpdateRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/shopping/monitoring/ad/${param.monitoringId}/competitors`,
    method: Method.PUT,
    params: {
      bodyParams: param.competitors,
    },
    config: {
      isLoading: isLoading,
    },
  });
};


/* istanbul ignore next */
export const deleteKeywordRankMonitoringCompetitor = async (
  monitoringId: number,
  displayUrl: string,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/rank-maintenance/monitoring/keyword/${monitoringId}/competitors`,
    method: Method.DELETE,
    params: {
      queryParams: {
        displayUrl: displayUrl,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};
