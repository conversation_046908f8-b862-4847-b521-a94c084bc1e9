import React from 'react';
import {
  AdEfficiencyAnalysisOffHistoryItem,
} from '@models/adEfficiency/AdEfficiency';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  withStyles,
  Tooltip,
} from '@material-ui/core';
import AdEfficiencyTooltip from './AdEfficiencyTooltip';

import './AdEfficiencyResultOffHistoryModal.scss';
import CloseIcon from '@components/assets/images/icon_close.png';
import { ReactComponent as CheckIcon } from '@components/assets/images/icon_count_check.svg';
import { ReactComponent as ErrorIcon } from '@components/assets/images/icon_utm_not_exist.svg';

interface Props {
  open: boolean;
  onClose: () => void;
  data: AdEfficiencyAnalysisOffHistoryItem[];
}

const TableTooltip = withStyles((theme) => ({
  tooltip: {
    '&.MuiTooltip-tooltip': {
      backgroundColor: 'transparent',
      maxWidth: 'unset',
    },
    '& .chart-tooltip__box': {
      position: 'relative',
    },
  },
}))(Tooltip)

const AdEfficiencyResultOffHistoryModal: React.FC<Props> = ({ open, onClose, data }: Props) => {
  // Google VA 광고는 off를 지원하지 않기 때문에 해당 케이스의 description으로 필터링해줌
  const rows = data.slice().reverse().filter(row => row.status === 'OK' || row.statusDescription !== 'Mutates are not allowed for the requested resource.')
  return (
    <Dialog id="AdEfficiencyResultOffHistoryModal" open={open} onClose={() => onClose()}>
      <DialogTitle>
        <IconButton className="modal-close" aria-label="close" onClick={() => onClose()}>
          <img alt="close-image" src={CloseIcon} />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <table className="creative-history-table">
          <thead>
            <th className="status">Status</th>
            <th className="creative">Creatives</th>
            <th className="updated">Datetime</th>
            <th className="description">Error Message</th>
          </thead>
          <tbody>
            {
              rows.map((row) => (
                <tr>
                  <td className="status">{ row.status === 'OK' ? <CheckIcon /> : <ErrorIcon /> }</td>
                  <td className="creative">
                    <TableTooltip
                      title={ <AdEfficiencyTooltip
                        color="#040A45"
                        top={0}
                        left={0}
                        image={row.mediaType === 'KAKAO' ? JSON.parse(row.imageUrl.replace(/&quot;/g, '"'))?.url : row.imageUrl}
                        campaignId={row.campaignId}
                        accountId={row.accountId}
                        adgroupId={row.adgroupId}
                        mediaType={row.mediaType}
                        creativeId={row.creativeId}
                        creativeName={row.creativeName}
                        campaignName={row.campaignName}
                        adgroupName={row.adgroupName}
                      /> }
                      placement="right-start"
                      PopperProps={{
                        modifiers: {
                          preventOverflow: {
                            enabled: true,
                            boundariesElement: 'viewport',
                          }
                        },
                      }}
                    >
                      <div><b>{ row.creativeName }</b><br/>{ row.creativeId }</div>
                    </TableTooltip>
                  </td>
                  <td className="updated">{ row.updatedDateTime }</td>
                  <td className="description">{ row.statusDescription }</td>
                </tr>
              ))
            }
            {
              rows.length === 0 && <tr><td colSpan={4}>소재OFF 이력이 없습니다.</td></tr>
            }
          </tbody>
        </table>
      </DialogContent>
    </Dialog>
  )
}

export default AdEfficiencyResultOffHistoryModal;
