import { useEffect } from "react"
import { useAuthority } from "@hooks/common"
import { getAllSpaAnomaly } from '@utils/anomaly'
import { getUtmsAnomalyDetection, getUrlsAnomalyDetection } from '@api/dashboard/AbnomalyDetection'
import { anomalyUrlNoti, anomalyUtmNoti, anomalySpaNoti } from '@store/detectionCenter'
import { useSetRecoilState } from 'recoil'

const DecectionCenter = () => {
  const { advertiser } = useAuthority()
  const setSpaAnomaly = useSetRecoilState(anomalySpaNoti)
  const setUtmAnomaly = useSetRecoilState(anomalyUtmNoti)
  const setUrlAnomaly = useSetRecoilState(anomalyUrlNoti)

  const checkAnomaly = async (advertiserId: number) => {
    const spaAnomaly = await getAllSpaAnomaly(advertiserId)
    const utmAnomaly = await getUtmsAnomalyDetection(advertiserId)
    const urlAnomaly = await getUrlsAnomalyDetection(advertiserId)
    setSpaAnomaly(spaAnomaly.length > 0)
    setUtmAnomaly(utmAnomaly?.summaries ? utmAnomaly?.summaries.length > 0 : false)
    setUrlAnomaly(urlAnomaly?.abnormals ? urlAnomaly?.abnormals.length > 0 : false)
  }

  useEffect(() => {
    if (advertiser.advertiserId) checkAnomaly(advertiser.advertiserId)
  }, [advertiser.advertiserId])
  return <></>
}

export default DecectionCenter