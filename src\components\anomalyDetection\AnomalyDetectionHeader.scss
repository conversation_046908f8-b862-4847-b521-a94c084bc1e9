.detection-header {
  display: flex;
  align-items: center;
  gap: 2.5rem;

  &__title-container {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex: auto;
  }

  &__title {
    color: var(--color-black);
    font-size: 2rem;
    margin: 0;
  }

  &__anomaly-case {
    color: #909090;
    font-size: 12px;
    background-color: #f2f4f7;
    padding: 0.25rem 1rem;
    border-radius: 9999px;
    margin-left: 0.5rem;
  }
  &__anomaly-num {
    font-size: 22px;
    font-weight: 700;
    &--color {
      color: #eb414c;
    }
  }

  &__status-icons {
    display: flex;
    align-items: center;
    gap: 0.5rem;

    span {
      font-weight: 700;
      font-size: 12px;
      color: var(--point_color);
    }
  }

  &__download-button {
    --button-size: 28px;
    padding: 0;

    span {
      font-size: 14px;
      color: var(--point_color);
    }

    svg {
      width: var(--button-size);
      height: var(--button-size);
    }
  }
}
