import React, { ReactElement, useEffect, useState } from 'react';
import { useRecoilState, useResetRecoilState, useRecoilValue } from 'recoil';
import { filteredSpaDetectionState, spaDetectionFilterQuery, spaDetectionPage, spaDetectionRowsPerPage } from '@store/AnomalyDetection'
import { useTranslation } from 'react-i18next';
import { FixedLayoutTable, TablePagination } from '@components/common/table'
import SpaDetectionTableFormatter  from './SpaDetectionTableFormatter'

import { pageSizeOptions } from '@models/common/CommonConstants';

import { SpaAnomalyDetectionColumn, SpaAnomalyDetectionTable } from '@models/anomalyDetection';
import EmptyDetection from '@components/anomalyDetection/EmptyDetection'
import { ReactComponent as EmptyNotice } from '@components/assets/images/icon_notice_outline.svg';
import './DetectionTable.scss';

const tableColumns = new SpaDetectionTableFormatter();

const SpaDetectionTable: React.FC = (): ReactElement => {
  const { t } = useTranslation()
  const allColumns: SpaAnomalyDetectionColumn[] = tableColumns.getColumnFormat();
  const filteredItmes = useRecoilValue(filteredSpaDetectionState);
  const [page, setPage] = useRecoilState(spaDetectionPage);
  const resetPage = useResetRecoilState(spaDetectionPage);
  const [rowsPerPage, setRowsPerPage] = useRecoilState(spaDetectionRowsPerPage);
  const resetRowsPerPage = useResetRecoilState(spaDetectionRowsPerPage)
  const [pagedItems, setPagedItems] = useState<SpaAnomalyDetectionTable[]>([])
  const query = useRecoilValue(spaDetectionFilterQuery);

  const handlePageList = (page: number, rowsPerPage: number) => {
    const startIndex = (page - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    setPagedItems(filteredItmes.slice(startIndex, endIndex))
  }

  useEffect(() => {
    resetPage()
    resetRowsPerPage()
  }, [query]) //eslint-disable-line

  useEffect(() => {
    handlePageList(page, rowsPerPage)
  }, [filteredItmes, page, rowsPerPage]) //eslint-disable-line
  return (
    <>
      <FixedLayoutTable
        id="AnomalyDetectionTable"
        columns={allColumns}
        data={pagedItems.map((obj) => Object.create(obj)) || []}
        localization={{
          body: {
            emptyDataSourceMessage: (
              <div style={{height: '600px'}}>
                <EmptyDetection icon={<EmptyNotice />} content={t('anomalyDetection.empty.spa')}/>
              </div>
            )
          }
        }}
      />
      {filteredItmes.length > 0 && (
        <TablePagination
          id="spa-anomaly-detection-pagination"
          totalCount={filteredItmes.length}
          page={page}
          rowsPerPage={rowsPerPage || pageSizeOptions[0]}
          onPageChange={(page) => setPage(page)}
          onRowsPerPageChange={(rowsPage) => setRowsPerPage(rowsPage)}
        />
      )}
    </>
  )
}

export default SpaDetectionTable;