/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil'
import CommonResponse from '@models/common/CommonResponse'
import { Service } from '@models/common/Service'
import {
  NaverCommerceIndicatorSummary,
  NaverCommerceOptimization,
  NaverCommerceProduct,
  NaverCommerceGraphResponse,
  StatsByProduct,
  NaverCommerceBodyParams,
  StatsByDate,
  ProductDetailResponse
} from '@models/report/NaverCommerce'
import { AvailableDate, IndicatorSummaryQuery, ReportCategoryType } from '@models/report/Common'
import { ActiveDate } from '@models/report/ActiveDate'

// Get NaverCommerce products
export const getNaverCommerceProducts = async (
  advertiserId: number,
  isLoading = true,
  queryParams: {
    optimizationIds: string[]
  }
): Promise<NaverCommerceProduct[] | undefined> => {
  const response: CommonResponse<NaverCommerceProduct[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/products`,
    method: Method.GET,
    params: {
      queryParams: queryParams
    },
    config: {
      isLoading: isLoading
    }
  })
  return response.successOrNot === 'Y' ? response.data : []
}

// Get NaverCommerce optimizations
export const getNaverCommerceOptimizations = async (
  advertiserId: number,
  isLoading = true
): Promise<NaverCommerceOptimization[] | undefined> => {
  const response: CommonResponse<NaverCommerceOptimization[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/optimizations`,
    method: Method.GET,
    config: {
      isLoading: isLoading
    }
  })
  return response.successOrNot === 'Y' ? response.data : []
}

export const getNaverCommerceReportSummary = async (
  advertiserId: number,
  queryParams: IndicatorSummaryQuery,
  bodyParams: NaverCommerceBodyParams,
  isLoading = true
): Promise<NaverCommerceIndicatorSummary | undefined> => {
  const response: CommonResponse<NaverCommerceIndicatorSummary> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/summary`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : undefined
}

export const getNaverCommerceMinMaxDate = async (
  advertiserId: number,
  reportCategoryType: ReportCategoryType,
  isLoading = true
): Promise<AvailableDate | undefined> => {
  const response: CommonResponse<AvailableDate> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/available-period/${advertiserId}`,
    method: Method.GET,
    params: {
      queryParams: {
        reportType: reportCategoryType
      }
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : undefined
}

// Get NaverCommerce graph data
export const getNaverCommerceGraph = async (
  advertiserId: number,
  queryParams: ActiveDate,
  bodyParams: NaverCommerceBodyParams,
  isLoading = true
): Promise<NaverCommerceGraphResponse | undefined> => {
  const response: CommonResponse<NaverCommerceGraphResponse> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/graph`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : undefined
}

export const getNaverCommerceReportByProduct = async (
  advertiserId: number,
  queryParams: IndicatorSummaryQuery,
  bodyParams: NaverCommerceBodyParams,
  isLoading = true
): Promise<StatsByProduct | undefined> => {
  const response: CommonResponse<StatsByProduct> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/product-table`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : undefined
}

export const getNaverCommerceReportByDate = async (
  advertiserId: number,
  queryParams: IndicatorSummaryQuery,
  bodyParams: NaverCommerceBodyParams,
  isLoading = true
): Promise<StatsByDate | undefined> => {
  const response: CommonResponse<StatsByDate> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/daily-table`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : undefined
}

export const getNaverCommerceProductOptimizations = async (
  advertiserId: number,
  channelProductId: string,
  queryParams: IndicatorSummaryQuery,
  isLoading = true
): Promise<ProductDetailResponse | undefined> => {
  const response: CommonResponse<ProductDetailResponse> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/product/${advertiserId}/product-table/${channelProductId}`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams }
    },
    config: {
      isLoading: isLoading
    }
  })

  return response.successOrNot === 'Y' ? response.data : undefined
}
