import { useState } from 'react';
import { startCase } from 'lodash';
import { numberWithCommas } from '@utils/FormatUtil';
import { useRecoilValue } from 'recoil';
import { xAxisState, yAxisState, sizeState } from './AdEfficiencyChart/store'
import './AdEfficiencyTooltip.scss';
export interface AdEfficiencyTooltipProps {
  // position
  top: number;
  left: number;
  // chart 축 데이터
  x?: number;
  y?: number;
  size?: number;
  color: string;
  // adEfficiency result
  campaignName: string;
  mediaType: string;
  campaignId: string;
  accountId: string;
  adgroupName: string;
  adgroupId: string;
  creativeId: string;
  creativeName: string;
  image: string;
}

const AdEfficiencyTooltip = (props: AdEfficiencyTooltipProps) => {
  const xAxis = useRecoilValue(xAxisState)
  const yAxis = useRecoilValue(yAxisState)
  const sizeAxis = useRecoilValue(sizeState)
  const [hideImage, setHideImage] = useState(false)
  const [sideMode, setSideMode] = useState(false)

  const handleOnLoade = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const img = e.currentTarget as HTMLImageElement
    if (img.naturalWidth < img.naturalHeight) {
      setSideMode(true)
    }
  }

  return (
    <div className={`chart-tooltip__box ${sideMode ? 'side-mode' : ''}`} style={{
      '--tooltip-color': props.color,
      '--top': `${props.top}px`,
      '--left': `${props.left}px`
    } as React.CSSProperties}>
      <div className="chart-tooltip__header">
        <span className='title'>{ props.creativeName }</span>
        <span className='id'>{ props.creativeId }</span>
      </div>
      <section className='chart-tooltip__content'>
        { typeof props.x === 'number' && typeof props.y === 'number' && typeof props.size === 'number' &&
          <div className='chart-tooltip__value'>
            <span>{ startCase(xAxis) }</span>
            <span>{ numberWithCommas(props.x) }</span>
            <span>{ startCase(yAxis) }</span>
            <span> { numberWithCommas(props.y) }</span>
            <span>{ startCase(sizeAxis) }</span>
            <span>{ numberWithCommas(props.size) }</span>
          </div>
        }
        <div className='chart-tooltip__category'>
          <div className='chart-tooltip__header'>
            <span className='title'> Media / Campaign / Ad group</span>
          </div>
          <div className='chart-tooltip__media'>
            <div className='chart-tooltip__media-box'>
              <span className='title'>{ props.mediaType }</span>
              <span className='id'>{ props.accountId }</span>
            </div>
            <div className='chart-tooltip__media-box'>
              <span className='title'>{ props.campaignName }</span>
              <span className='id'>{ props.campaignId }</span>
            </div>
            <div className='chart-tooltip__media-box'>
              <span className='title'>{ props.adgroupName }</span>
              <span className='id'>{ props.adgroupId }</span>
            </div>
          </div>
        </div>
      </section>
      <div className='chart-tooltip__image'>
        { props.image &&
          <img
            src={props.image}
            alt={props.creativeName}
            referrerPolicy="no-referrer"
            onLoad={e => handleOnLoade(e)}
            onError={e => setHideImage(true)}
            style={{display: hideImage ? 'none' : ''}}
          />
        }
        { !props.image && <span>수집된 이미지가 없습니다.</span> }
        { props.image && hideImage && <span>이미지가 만료되었거나 사용할 수 없습니다.</span> }
      </div>
    </div>
  )
}

export default AdEfficiencyTooltip