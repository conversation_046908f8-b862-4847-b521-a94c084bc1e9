import React from 'react'
import { useTranslation } from 'react-i18next'
import { KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE, NaverCommerceIndicator } from '../../../utils/naverCommerceReport'
import NaverCommerceIndicatorReportCard from './NaverCommerceIndicatorReportCard'
import { NaverCommerceIndicatorSummary } from '@models/report/NaverCommerce'

interface GroupProps {
  summaryIndicator: string[]
  summaryData?: NaverCommerceIndicatorSummary
}
type KeyIndicatorSummary = keyof NaverCommerceIndicatorSummary

const NaverCommerceIndicatorGroup: React.FC<GroupProps> = ({ summaryIndicator, summaryData = {} }) => {
  const { t } = useTranslation()


  // Create allKeys object similar to your example
  const allKeys = {
    sales: KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE.products,
    ads: KEY_NAVER_COMMERCE_INDICATORS_PERFORMANCE.ads
  }

  const renderCard = (indicator: string) => (
    <NaverCommerceIndicatorReportCard indicator={indicator as KeyIndicatorSummary} summary={summaryData[indicator as KeyIndicatorSummary]} />
  )


  return (
    <div className="campaign-report__indicator--wrapper">
      {summaryIndicator.some((indicator) => allKeys.sales.includes(indicator as NaverCommerceIndicator)) && (
        <div className="flex-1 flex-col">
          <div className="flex items-center w-full justify-center bg-campaign-background py-1 border-[#E4E7EE] border-[1px] text-sm font-semibold">
            {t('naverReport.productPerformance')}
          </div>
          <div className="flex">
            {summaryIndicator
              .filter((indicator) => allKeys.sales.includes(indicator as NaverCommerceIndicator))
              .map((indicator) => (
                <div key={indicator} className="flex-1">
                  {renderCard(indicator)}
                </div>
              ))}
          </div>
        </div>
      )}
      {summaryIndicator.some((indicator) => allKeys.ads.includes(indicator as NaverCommerceIndicator)) && (
        <div className="flex-1 flex-col">
          <div className="flex items-center w-full justify-center bg-campaign-background py-1 border-[#E4E7EE] border-[1px] font-semibold text-sm">
            {t('naverReport.adPerformance')}
          </div>
          <div className="flex">
            {summaryIndicator
              .filter((indicator) => allKeys.ads.includes(indicator as NaverCommerceIndicator))
              .map((indicator) => (
                <div key={indicator} className="flex-1">
                  {renderCard(indicator)}
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default NaverCommerceIndicatorGroup
