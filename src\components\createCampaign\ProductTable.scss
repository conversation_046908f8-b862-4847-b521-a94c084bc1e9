.product-table-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .product-table {
    width: 100%;
    height: 100%;
    flex: 1;
  }

  .virtualized-table {
    width: 100%;
    height: 100% !important;
    border: 1px solid #EFEFEF;
    border-radius: 4px;
    overflow: hidden;
    background-color: #ffffff;

    .virtualized-header-row {
      background-color: #F2F3F6;

      .virtualized-header-cell {
        font-weight: 600;
        font-size: 14px;
        text-align: center;
        color: #333;


        &:last-child {
          border-right: none;
        }

        &.checkbox-header {
          justify-content: center;
        }
      }
    }

    .virtualized-data-row {
      border-bottom: 1px solid #EFEFEF;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .virtualized-data-cell {
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #333;
        &:last-child {
          border-right: none;
        }

        &.checkbox-cell {
          justify-content: center;
        }

        .product-image {
          display: flex;
          align-items: center;
          justify-content: center;
          
          img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 4px;
          }
        }
      }
    }

    .virtualized-data-list {
      outline: none;
    }

  }

  // Responsive design
  @media (max-width: 768px) {
    .virtualized-data-cell {
      padding: 0 8px;
      font-size: 12px;
    }

    .virtualized-header-cell {
      padding: 0 8px;
      font-size: 12px;
    }
  }
}
