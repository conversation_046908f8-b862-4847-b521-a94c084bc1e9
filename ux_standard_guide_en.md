# Color System
## 🎨 Color Palette
### Base Colors (CSS Variables)
Refer to App.scss variables for implementation
### Chart Colors by Media Type
Refer to budgetOpt.ts - mediaColorPalette for implementation

# Typography
## Font Family
- **Default**: Noto Sans Korean (for Korean text)
- **English**: <PERSON><PERSON><PERSON>, <PERSON>serrat, Poppins
- **Code**: source-code-pro, Menlo, Monaco
## Font Weight
- `200` - Thin
- `300` - Light  
- `400` - Regular (default)
- `500` - Medium
- `700` - Bold
- `900` - Black

# 📚 Storybook
## Accessing Storybook
```bash
npm run storybook
```
## Component Navigation Structure
The location of components in Storybook follows the project directory structure.
