#AnomalyDetectionPage, #UtmAbnormalLandingUrlModal {
  .report-title {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    width: 100%;
    height: 28px;
    padding: 0;

    div {
      display: flex;
      align-items: center;
      gap: 0.25rem;

      .headTitle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
    }

    .headTitle {
      --head-size: 20px;
      color: var(--point_color);
      font-weight: 700;
      font-size: var(--head-size);
    }

    .subTitle {
      --subhead-size: 16px;

      font-weight: 300;
      font-size: var(--subhead-size);
      padding-left: 0.5rem;
    }
    .ga-view-id {
      background-color: var(--point_color);
      border-radius: 9999px;
      padding: 4px 16px;
      display: inline-block;
      color: white;
    }
    .side.right {
      display: flex;
      align-items: center;
    }
    .utm-status-icon-grid {
      display: inline-grid;
      grid-template-columns: repeat(6,auto);
      align-items: center;
      gap: 8px;
      margin: 0 12px;
      span {
        padding-right: 8px;
        font-weight: 700;
        font-size: 12px;
        color: var(--point_color);
      }
    }
    .download-button {
      --button-size: 28px;
      // display: none;
      width: var(--button-size);
      height: var(--button-size);
      min-width: var(--button-size);

      span {
        width: var(--button-size);
        height: var(--button-size);
      }

      svg {
        width: var(--button-size);
        height: var(--button-size);
      }
    }
  }
}

#anomaly-detection-advice-tooltip {
  &-spa, &-url, &-utm, &-landing-url, &-utm-rules {
    border: 1px solid var(--point_color);
    background-color: #fff;

    .MuiTooltip-tooltip {
      padding: 0px;
      margin: 0px;
      background-color: transparent;

      .indent1 {
        padding: 0 12px 0 14px;
        text-indent: -14px;
      }

      .indent2 {
        padding: 0 12px 0 30px;
        text-indent: -14px;
      }

      .indent3 {
        padding: 0 12px 0 38px;
        text-indent: -38px;
      }

      >div {
        >div {
          color: var(--point_color);
          font-size: 12px;
          font-weight: 500;

          &:first-child {
            padding: 12px 0px;
            border-bottom: 1px solid #9196a4;
            font-size: 14px;
            font-weight: 700;
            display: flex;
            justify-content: center;
          }

          &:nth-child(2) {
            padding: 12px;

            .bid-desc {
              margin-bottom: 5px;
              line-height: 22px;
            }

            ul {
              margin: 0;

              li {
                p {
                  margin: 0;
                  display: inline-block;
                  width: 110px;
                }
              }
            }

            strong {
              padding: 0;
            }

            >div {
              display: flex;

              >div:first-child {
                width: 90px;
                font-weight: 700;
              }

              >div:last-child {
                flex: 1;

                >span {
                  color: var(--error_color);
                }
              }

              .bid-img {
                width: 42px !important;
                height: auto !important;
                margin-right: 10px;
                background-repeat: no-repeat;
                background-size: 100% auto;
                background-position: center center;
              }

              .bid-disable {
                background-image: url(~@images/switch_disable.svg);
              }

              .bid-on {
                background-image: url(~@images/switch_on.svg);
              }

              .bid-off {
                background-image: url(~@images/switch_off.svg);
              }
            }
          }
        }
      }

      span.red {
        color: var(--error_color);
      }

      .MuiTooltip-arrow::before {
        border-color: var(--point_color);
      }
    }
  }
}