import * as ApiUtil from '@utils/ApiUtil';
import {
  getDvaAbnormalPerformance,
  getDVaAbnormalUrls,
  getDVaAbnormalUtms,
  getDVaBudgetUpdateBatchHistory,
  getDVaCollectionItem,
  getDVaCollectionPerformance,
  getDVaCollectionStatus,
  getDVaPreviousBudgetUsage,
  getDVaProjectionAttribution,
  getDVaProjectionPlanning,
  getDVaProjectionPredictions,
  getDvaReport,
  getDVaTodayBudgetUsage,
} from '@api/dashboard/DVaOptDashboard';
import { convertDateToStr, getBeforeDate } from '@utils/DateUtil';

describe('getDvaReport', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 조회기간 지난 1일-7일 & 비교기간 지난 8일-14일 DvaReport를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        salesAmount: { value: '39479', compareValue: '36810', ratio: '7.25' },
        impressions: { value: '85315267', compareValue: '39009609', ratio: '118.7' },
        views: { value: '3964184', compareValue: '2647305', ratio: '49.74' },
        clicks: { value: '2466866', compareValue: '965457', ratio: '155.51' },
        transactions: { value: '0', compareValue: '0', ratio: '0' },
        vtr: { value: '11.25', compareValue: '11.90', ratio: '-5.46' },
        ctr: { value: '2.89', compareValue: '2.47', ratio: '17' },
        cvr: { value: '0.00', compareValue: '0.00', ratio: '0' },
        cpv: { value: '0', compareValue: '0', ratio: '0' },
        cpc: { value: '0', compareValue: '0', ratio: '0' },
        cpa: { value: '0', compareValue: '0', ratio: '0' },
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDvaReport(
      1,
      [7, 1, 14, 8].map((diff) => convertDateToStr(getBeforeDate(diff)))
    );
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDvaReport(
      1,
      [7, 1, 14, 8].map((diff) => convertDateToStr(getBeforeDate(diff)))
    );
    expect(response).toEqual(undefined);
  });
});

describe('getDvaAbnormalPerformance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          mediaType: 'GOOGLE',
          accountId: '**********',
          accountName: 'LGE HA_Seeding_VH',
          campaignId: '***********',
          campaignName: 'HQ-HA_HA_ST_VN_22_Q4_CNS_Styler:GDN:Viral:Conversion:Visit_lg.com',
          adgroupId: '************',
          adgroupName: 'GDN - WM - Styler - Conversion -  ASIA - VH - Initial - Banner-P - RDA - CAT',
          message: 'Impressions was 1791280 and it decreased 55.53% compared to 2023-01-24.',
          createdDatetime: '2023.01.26 09:41:06',
        },
      ],
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDvaAbnormalPerformance(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, 반 배열을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDvaAbnormalPerformance(1);
    expect(response).toEqual([]);
  });
});

describe('getDVaCollectionStatus', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Collection의 상태를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        media: [
          { type: 'KAKAO', useYn: 'N' },
          { type: 'GOOGLE', useYn: 'Y', status: 'OK' },
          { type: 'META', useYn: 'Y', status: 'ERROR' },
        ],
        analytics: [{ type: 'GA', useYn: 'Y', status: 'OK' }],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaCollectionStatus(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaCollectionStatus(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaAbnormalUrls', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 url 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        lastUpdated: '2023.01.26 02:00:34',
        abnormals: [
          {
            mediaType: 'GOOGLE',
            accountId: '**********',
            accountName: 'LGE HA_Seeding_VH',
            adgroupId: '************',
            adgroupName: 'YT - WM - Styler - Conversion - ASIA - VH - Performance - Video - VAC - CAT - Shopping',
            statusCode: ['NOT_MATCH_URL_CAMPAIGN_RULES', 'NOT_MATCH_URL_CONTENT_RULES'],
          },
        ],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaAbnormalUrls(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaAbnormalUrls(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaAbnormalUtms', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 이상감지 utm 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        lastUpdated: '2023.01.26 02:00:34',
        abnormals: [
          {
            mediaType: 'GOOGLE',
            accountId: '**********',
            accountName: 'LGE HA_Seeding_VH',
            adgroupId: '************',
            adgroupName: 'YT - WM - Styler - Conversion - ASIA - VH - Performance - Video - VAC - CAT - Shopping',
            statusCode: ['NOT_MATCH_UTM_CAMPAIGN_RULES', 'NOT_MATCH_UTM_CONTENT_RULES'],
          },
        ],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaAbnormalUtms(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaAbnormalUtms(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaCollectionItem', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Collection Item 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: { optimizations: 0, campaigns: 2, adgroups: 34, creatives: 84 },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaCollectionItem(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaCollectionItem(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaCollectionPerformance', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 주어진 기간의 Collection Performance를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        lastUpdated: '2023.01.26 15:45:44',
        status: 'OK',
        performances: [0, 0, 0, 0, 0, 9679, 183479, 57696, 36489, 36922, 36999, 36203, 35915, 33966],
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaCollectionPerformance(1, 14);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaCollectionPerformance(1, 14);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaProjectionPlanning', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Planning을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        optimizations: { lastUpdated: '2023.01.26 14:57:25', status: 'OK', totalCount: 0, runCount: 0 },
        budgetDistribution: { mediaCount: 0, campaignsCount: 0, adgroupsCount: 0 },
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaProjectionPlanning(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaProjectionPlanning(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaProjectionPredictions', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Prediction을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: { predictions: { lastUpdated: '2023.01.26 09:32:06', status: 'OK' } },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaProjectionPredictions(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaProjectionPredictions(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaProjectionAttribution', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Projection Attribution을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: { lastUpdated: '2023.01.26 10:04:17', status: 'OK', markov: 78047, shapley: 258 },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaProjectionAttribution(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaProjectionAttribution(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaTodayBudgetUsage', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 오늘 Budget 사용량을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: { lastUpdated: '2023.03.02 03:00:10', costs: [{ mediaType: 'GOOGLE', cost: 12340 }] },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaTodayBudgetUsage(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaTodayBudgetUsage(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaPreviousBudgetUsage', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, 과거 Budget 사용량 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        date: '2023-03-01',
        dailyBudget: 12300,
        costs: [
          {
            mediaType: 'GOOGLE',
            costs: [
              100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900,
              2000, 2100, 2200, 2300, 2400,
            ],
          },
        ],
      },
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaPreviousBudgetUsage(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaPreviousBudgetUsage(1);
    expect(response).toEqual(undefined);
  });
});

describe('getDVaBudgetUpdateBatchHistory', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, Budget 변경 배치 수행 이력을 리턴한다', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          createdDateTime: '2023.03.02 03:00:10',
          status: 'ERROR',
          media: 1,
          campaign: 2,
          adgroup: 3,
        },
        {
          createdDateTime: '2023.03.02 07:00:10',
          status: 'UPDATE',
          media: 1,
          campaign: 2,
          adgroup: 3,
        },
      ],
    };
    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaBudgetUpdateBatchHistory(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getDVaBudgetUpdateBatchHistory(1);
    expect(response).toEqual(undefined);
  });
});
