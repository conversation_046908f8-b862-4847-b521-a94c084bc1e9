import './MopDialog.scss'
import type { PropsWithChildren } from 'react'
import { getSubComponent } from '@utils/index'
import { Dialog } from '@material-ui/core'
import { MopIcon } from '@components/common'
import { MOPIcon } from '@models/common'
import { MopActionButton } from '@components/common/buttons'

interface Props {
  open: boolean
  actionLabel: string
  cancelLabel?: string
  customClass?: string
  handleAction: () => void
  handleClose: () => void
}

interface HeaderProps {
  showClose?: boolean
  handleClose?: any
  hasBackground?: boolean
}

const Header = ({ children, handleClose, showClose, hasBackground }: PropsWithChildren<HeaderProps>) => {
  const headerStyle = `
    mop-dialog__header
    ${ hasBackground ? 'mop-dialog__header--background' : '' }
  `.trim()
  return (
    <section className={headerStyle}>
      {showClose && <MopIcon name={MOPIcon.CLOSE} onClick={handleClose} /> }
      { children }
    </section>
  )
}

const Body = ({ children }: PropsWithChildren<{}>) => {
  return (
    <section className="mop-dialog__content">
      { children }
    </section>
  )
}

// const Footer = ({ children, handleClose, handleAction, cancelLabel }: PropsWithChildren<Props>) => {
//   return (
//     <section className="mop-dialog__footer">
//       { cancelLabel && <MopActionButton label={cancelLabel} theme="cancel" onClick={handleClose} /> }
//       <MopActionButton label="추가" onClick={handleAction} />
//     </section>
//   )
// }

const MopDialog = ({ children, open, customClass = '', handleClose, handleAction, actionLabel, cancelLabel }: PropsWithChildren<Props>) => {
  const dialogStyle = `mop-dialog ${customClass}`.trim()
  const header = getSubComponent(children, 'Header')
  const body = getSubComponent(children, 'Body')
  // const footer = getSubComponent(children, 'Footer')
  return (
    <Dialog className={dialogStyle} open={open} onClose={handleClose}>
      { header }
      { body }
      <section className="mop-dialog__footer">
        { cancelLabel && <MopActionButton label={cancelLabel} theme="cancel" onClick={handleClose} /> }
        <MopActionButton label={actionLabel} onClick={handleAction} />
      </section>
    </Dialog>
  )
}

Header.displayName = 'Header'
Body.displayName = 'Body'
// Footer.displayName = 'Footer'

MopDialog.Header = Header
MopDialog.Body = Body
// MopDialog.Footer = Footer

export default MopDialog