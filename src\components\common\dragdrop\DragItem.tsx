import { memo } from 'react'
import type { MouseEvent } from 'react'
import { useDrag } from 'react-dnd'
import type { ReactNode, CSSProperties } from 'react'
import type { DragSourceMonitor } from 'react-dnd'

interface Props<T> {
  type: string
  canDrag?: boolean
  isDropped?: boolean
  className?: string
  children?: ReactNode
  data?: T
  onClick?: (_event: MouseEvent<HTMLDivElement>) => void
}

const DragItem = memo(function DragItem<T>({
  type, children, canDrag = true, data, className, isDropped, onClick
}: Props<T>) {
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type,
      canDrag, item: data,
      collect: (monitor: DragSourceMonitor) => ({
        isDragging: monitor.isDragging()
      })
    }),
    [canDrag, type],
  )

  return (
    <div ref={drag}
      className={`
        ${className}
        ${isDragging ? `${className}--is-dragging` : ''}
        ${isDropped ? `${className}--is-dropped` : ''}
        ${canDrag ? '' : `${className}--is-disabled` }
      `.trim()}
      style={{ cursor: canDrag ? 'move' : '' }}
      onClick={onClick}
    >
      { children }
    </div>
  )
})

export default DragItem