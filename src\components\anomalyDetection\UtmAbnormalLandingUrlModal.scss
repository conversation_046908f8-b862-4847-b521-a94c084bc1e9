#UtmAbnormalLandingUrlModal {
  .MuiDialog-paper {
    width: 1600px;
    max-width: 1600px;
    min-width: 1400px;
    height: fit-content;
    background-color: var(--bg-gray-light);
    border-radius: 0;
    padding: 16px 32px;
    position: relative;
    .modal-close {
      position: absolute;
      top: 16px;
      right: 16px;
      padding: 0px;
      width: 24px;
      height: 24px;
      background-color: white;
      border-radius: 100%;
      z-index: 1000;
    }
  }
  .utm-modal {
    display: flex;
    align-items: center;
    flex-direction: column;
    &.header {
      position: relative;
    }
    &.content {
      section {
        flex: 1;
        width: 100%;
        margin: 8px 0;

        .landing-urls {
          table-layout: fixed;
          border-top: 1px solid var(--border-table-sub);
          border-bottom: 1px solid var(--border-table-sub);
          .MuiTableCell-root {
            border-bottom: none;
          }
          .MuiTableCell-head {
            background: #d7d8e2;
            border-bottom: none;
            color: var(--point_color);
            height: 50px;
            font-size: 16px;
            vertical-align: middle;
            padding: 10px 20px;
            text-align: center;
          }

          .MuiTableRow-root {
            background-color: var(--bg-table-main);
            &:nth-child(even) {
              background-color: #ffffff;
            }
            .MuiTableCell-body {
              color: var(--point_color);
              height: 50px;
              font-size: 16px;
              vertical-align: middle;
              padding: 10px 20px;
              div {
                word-wrap: break-word;
                word-break: break-all;
              }
              .highlight {
                background-color: #0094ff20;
                &.channel,
                &.utm_source,
                &.pid {
                  background-color: #fed20050;
                }
                &.campaign,
                &.utm_medium,
                &.af_channel {
                  background-color: #6ec7c250;
                }
                &.ad_group,
                &.utm_campaign,
                &.af_keywords {
                  background-color: #ff662550;
                }
                &.term,
                &.utm_content,
                &.c {
                  background-color: #387fff1a;
                }
                &.ad_creative,
                &.utm_term,
                &.af_adset {
                  background-color: #d16fff12;
                }
                &.af_ad {
                  background-color: #5ab65529;
                }
              }
            }
          }
        }
        .copy-button {
          margin-left: auto;
          display: block;
          background-color: var(--point_color);
          border: none;
          color: white;
          text-transform: uppercase;
          padding: 4px 28px;
          margin-top: 16px;
          border-radius: 9999px;
          font-weight: 900;
          font-size: 16px;
          cursor: pointer;
        }

        .ga-utm-rules-table {
          border-top: 1px solid var(--border-table-sub);
          border-bottom: 1px solid var(--border-table-sub);

          .MuiTableHead-root {
            padding: 0;
            background: #d7d8e2;
            border-bottom: none;
            color: var(--point_color);
            font-size: 16px;
            vertical-align: middle;
            text-align: center;
            // color: var(--point_color);
            .rule {
              width: 15%;
              text-align: center;
              color: var(--point_color);
              border-bottom: 1px solid var(--border-table-sub);
              p {
                display: flex;
                align-items: center;
                justify-content: center;
              }
            }
            .settings {
              width: 85%;
              padding: 0;
              color: var(--point_color);
              border-bottom: 1px solid var(--border-table-sub);
              .utm-rule {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;

                &.head {
                  flex-direction: column;
                  .utm-rule.title {
                    padding: 0.5rem 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 0.5rem;
                    border-bottom: 1px solid var(--border-table-sub);
                  }
                }

                p {
                  flex: 1;
                  margin: 0;
                  padding: 8px 0;
                  text-align: center;
                  text-transform: uppercase;
                }
              }
            }
          }
          .MuiTableBody-root {
            .MuiTableCell-root {
              border-bottom: none;
              background-color: white;
              padding: 8px 0;
            }
            .rule-result .MuiTableCell-root {
              background-color: #e3e5ed;

              border-bottom: 1px solid var(--border-table-sub);
            }
            .rule {
              text-align: center;
              color: var(--point_color);
              font-weight: 700;
            }
            .settings {
              // padding:0;
              display: flex;
              align-items: center;
              justify-content: space-between;

              &-box {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .utm-label {
                display: inline-block;
                border-radius: 6px;
                background-color: #e1e6ff;
                color: var(--color-active-blue);
                font-size: 12px;
                padding: 5px 12px;
                margin: 0 4px;
              }
              .utm-label-box {
                display: inline-flex;
                align-items: center;
                background-color: #e1e6ff;
                border-radius: 6px;
                padding: 2px 4px;
                margin: 0 4px;
                .utm-label {
                  padding: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

#utm-type-settings-tooltip {
  width: fit-content;
  .MuiTooltip-tooltip {
    padding: 0px;
    margin: 0px;
    border-radius: 0;
    min-width: fit-content;
    max-width: fit-content;
  }
  .utm-type-settings {
    padding: 16px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--point_color);
    .subTitle {
      margin: 0;
      font-size: 10px;
      font-weight: 700;
    }
    .title {
      font-size: 16px;
    }

    .content {
      width: 100%;
    }
    .settings-list {
      border-bottom: 1px solid #909090;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 0;

      .chip {
        border-radius: 9999px;
        padding: 4px 12px;
        display: inline-block;
      }
      .type-title {
        background-color: var(--point_color);
        color: white;
        margin: 4px 0;
        margin-right: 32px;
        width: 80px;
        &.PLATFORM {
          width: 60px;
        }
      }
      .type-value {
        border: 1px solid var(--point_color);
        background-color: var(--bg-gray-light);
        margin: 4px;
      }
    }
  }
}
