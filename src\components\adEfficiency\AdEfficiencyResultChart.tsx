import React, { useState, useEffect } from 'react';
import Plotly from "plotly.js-basic-dist-min";
import createPlotlyComponent from "react-plotly.js/factory";
import ChartSetting from './AdEfficiencyChart/ChartSetting';
import './AdEfficiencyResultChart.scss';
import { generateLayout, generateChartData } from './AdEfficiencyChart/utils'
import { useRecoilState, useRecoilValue, useSetRecoilState } from 'recoil';
import { xAxisState, yAxisState, sizeState } from './AdEfficiencyChart/store'
import { AdEfficiencyAnalysisResultDetails } from '@models/adEfficiency/AdEfficiency';
import { adEfficiencyMutableAllData, adEfficiencyFilteredDataBySelect,
  adEfficiencyTableData, adEfficiencyChartData } from '@store/AdEfficiency'
import AdEfficiencyTooltip from './AdEfficiencyTooltip'
import _ from 'lodash'

const Plot = createPlotlyComponent(Plotly);

const AdEfficiencyResultChart: React.FC = () => {
  const xAxis = useRecoilValue(xAxisState)
  const yAxis = useRecoilValue(yAxisState)
  const sizeAxis = useRecoilValue(sizeState)
  const mutableAllData = useRecoilValue(adEfficiencyMutableAllData)
  const selectedFilterData = useRecoilValue(adEfficiencyFilteredDataBySelect)
  const [allChartData, setAllChartData] = useRecoilState(adEfficiencyChartData)
  const setAdEfficiencyTableData = useSetRecoilState(adEfficiencyTableData)
  const [revision, setRevision] = useState(0)
  const [layout, setLayout] = useState(generateLayout({ datarevision: 0 }))
  const [chartData, setChartData] = useState<Plotly.Data[]>([])
  const [totalData, setTotalData] = useState(chartData)
  const [showTooltip, setShowTooltip] = useState(false)
  const [tooltipData, setTooltipData] = useState({
    x: 0, y: 0, size: 0, campaignName: '', mediaType: '',
    campaignId: '0', accountId: '0', adgroupName: '', adgroupId: '0',
    creativeId: '0', creativeName: '', color: '', top: 0, left: 0, image: ''
  })
  const imageUrl = tooltipData.mediaType === 'KAKAO' ? JSON.parse(tooltipData.image.replace(/&quot;/g, '"'))?.url : tooltipData.image

  const handleHover = ({ points, event }: Plotly.PlotHoverEvent) => {
    const { pointIndex, data } = points[0];
    const { pointerX, pointerY } = event as MouseEvent & { pointerX: number, pointerY: number }
    const { marker, x, y, customdata } = data;
    const selectedData = customdata as unknown as AdEfficiencyAnalysisResultDetails

    setTooltipData({
      x: x[pointIndex] as number,
      y: y[pointIndex] as number,
      size: ((marker as any).originalsize as number[])[pointIndex],
      color: (points[0] as unknown as any).fullData.marker.color,
      top: pointerY - 100,
      left: pointerX + 50,
      campaignId: selectedData.campaignId,
      accountId: selectedData.accountId,
      adgroupId: selectedData.adgroupId,
      mediaType: selectedData.mediaType,
      creativeId: selectedData.creativeId,
      creativeName: selectedData.creativeName,
      campaignName: selectedData.campaignName,
      adgroupName: selectedData.adgroupName,
      image: selectedData.image
    })
    setShowTooltip(true)
  }
  const handleUnHover = () => {
    setShowTooltip(false)
  }

  const updateChart = (newData:any, type: string) => {
    if(type === 'axis') {
      setTotalData(newData)
    }
    setChartData(newData)
  }

  const handleClick = ({ points }: Plotly.PlotMouseEvent) => {
    const data = _.map(points, 'data.customdata') as unknown as AdEfficiencyAnalysisResultDetails[]
    setAdEfficiencyTableData(data)
  }

  const handleSelected = (event: Plotly.PlotSelectionEvent) => {
    if (!event) return
    const data = _.map(event.points, 'data.customdata') as unknown as AdEfficiencyAnalysisResultDetails[]
    setAdEfficiencyTableData(data)
  }

  const handleRelayout = (event: Plotly.PlotRelayoutEvent) => {
    // @ts-ignore
    if (event.selections) return
    if (event['xaxis.range[1]']) {
      const xMax = event['xaxis.range[1]'] as number
      const xMin = event['xaxis.range[0]'] as number
      const yMax = event['yaxis.range[1]'] as number
      const yMin = event['yaxis.range[0]'] as number
      const zommedDataInChart = _.filter(allChartData, (detail) => {
        const xValue = Number(detail[xAxis])
        const yValue = Number(detail[yAxis])
        const isBetweenX = xMin < xValue && xValue < xMax
        const isBetweenY = yMin < yValue && yValue < yMax
        return isBetweenX && isBetweenY
      })
      setAdEfficiencyTableData(zommedDataInChart)
      return
    }
    setAdEfficiencyTableData(allChartData)
  }

  useEffect(() => {
    // 1. 차트, 테이블에 초기데이터 설정
    if(selectedFilterData) {
      const allData = []
      for (const [_key, value] of Object.entries(selectedFilterData)) {
        allData.push(...value)
      }
      setAllChartData(allData) //초기 데이터 저장용
      setAdEfficiencyTableData(allData)
    }
  }, [selectedFilterData])

  useEffect(() => {
    // 2. 차트 포맷으로 데이터 변환하여 차트 데이터 설정
    // 초기데이터, x,y,size 가 변경될 때 트리거되어 차트데이터 변경
    const chartData: any[] = []
    allChartData.forEach((detail) => {
      chartData.push({
        x: [Number(detail[xAxis])],
        y: [Number(detail[yAxis])],
        label: detail.creativeName,
        size: [Number(detail[sizeAxis])],
        customdata: detail
      })
    })
    const formattedChartData = generateChartData(chartData)
    setChartData(formattedChartData)
    setTotalData(formattedChartData)
  }, [allChartData, xAxis, yAxis, sizeAxis])

  useEffect(() => {
    // 3. x,y,size, 차트 상단 탭, 필터 셀렉트 변경시 차트 레이아웃 변경
    // 초기 차트 데이터가 변경되는 것이 아님
    // mutableAllData 차트 상단 탭, 필터 셀렉트가 변경될 경우 트리거 됨
    const newRevision = revision + 1
    setRevision(newRevision)
    setLayout(generateLayout({ datarevision: newRevision, uirevision: Math.random() }))
  }, [xAxis, yAxis, sizeAxis, mutableAllData])

  return (
    <div id="AdEfficiencyResultChart">
      { showTooltip &&
        <AdEfficiencyTooltip {...tooltipData} image={imageUrl} />
      }
      <div className='chart-container'>
        <span className="x-axis-label">{ _.startCase(xAxis) }</span>
        <span className="y-axis-label">{ _.startCase(yAxis) }</span>
        <Plot
          divId="adEfficiencyChart"
          data={chartData}
          revision={revision}
          useResizeHandler
          layout={layout}
          onHover={handleHover}
          onClick={handleClick}
          onSelected={handleSelected}
          onRelayout={handleRelayout}
          onUnhover={handleUnHover}
        />
      </div>
      <ChartSetting
        isDisabled={false}
        total={totalData}
        data={chartData}
        setChartData={updateChart}
      />
    </div>
  )
}

export default AdEfficiencyResultChart