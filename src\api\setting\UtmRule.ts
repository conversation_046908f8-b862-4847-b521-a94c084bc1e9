/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
// import { YNFlag } from '@models/common'
import CommonResponse from '@models/common/CommonResponse'
import { UtmRuleBodyParmas, UtmRule } from '@models/setting/UtmRule'

export const getUtmRule = async (ruleId: string, isLoading = true) => {
  const response: CommonResponse<UtmRule> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/utm-rule/${ruleId}`,
    method: Method.GET,
    config: { isLoading }
  })

  return response
}

export const createUtmRule = async (bodyParams: UtmRuleBodyParmas, isLoading = true) => {
  const response: CommonResponse<any> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/setting/utm-rule',
    method: Method.POST,
    params: { bodyParams },
    config: { isLoading }
  })
  return response
}

export const updateUtmRule = async (ruleId: string, bodyParams: UtmRuleBodyParmas, isLoading = true) => {
  const response: CommonResponse<any> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/utm-rule/${ruleId}`,
    method: Method.PUT,
    params: { bodyParams },
    config: { isLoading }
  })

  return response
}

export const deleteUtmRule = async (ruleId: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/utm-rule/${ruleId}`,
    method: Method.DELETE,
    config: { isLoading }
  })
}