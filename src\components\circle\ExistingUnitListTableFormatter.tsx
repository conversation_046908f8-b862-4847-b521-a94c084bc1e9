import { useTranslation } from 'react-i18next'
import { Fragment } from 'react'
import { ExistingUnit, ComputedStatus, UnitAnalyticsType } from '@models/circle'
import { TableTitle, FixedLayoutColumn } from '@components/common/table'
import { MediaIcon, MopIcon, Badge } from '@components/common'
import CommonTooltip from '@components/common/CommonTooltip'
import { MOPIcon } from '@models/common'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { mediaActiveStatus } from '@utils/common/MediaType'
import InnerHtml from '@components/common/InnerHtml'
import { useNavigate } from 'react-router-dom'

import './CircleListTableFormatter.scss'
import './commonTable.scss'
import { useAuthority } from '@hooks/common'
type TableRow = FixedLayoutColumn<ExistingUnit>

export default class TableFormatter {
  field: string
  order: 'asc' | 'desc'

  constructor() {
    this.field = ''
    this.order = 'asc'
  }
  handleDelete(_id: number) {}
  handleEdit(_circle: ExistingUnit) {}
  getColumnFormat = (): Array<TableRow> => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { hasSystemViewerAuthority } = useAuthority()

    const columnStatus = (): TableRow => {
      const tooltipList = t('circle.tooltip.createUnit.status.list', { returnObjects: true })
      return {
        title: (
          <TableTitle titleStr={t('circle.existingUnit.columnHeader.status')}>
            <CommonTooltip
              id={`common-table-tooltip`}
              placement="right-start"
              arrow
              title={
                <>
                  <h1>{t('circle.tooltip.createUnit.status.title')}</h1>
                  <div className="common-style">
                    <dl className="w22_w78">
                      {Object.values(tooltipList).map(([label, desc], index) => (
                        <Fragment key={index}>
                          <dt>{label}</dt>
                          <dd>
                            <InnerHtml innerHTML={desc} />
                          </dd>
                        </Fragment>
                      ))}
                    </dl>
                  </div>
                </>
              }
            >
              <AdviceMarkIcon className="tooltip-icon" />
            </CommonTooltip>
          </TableTitle>
        ),
        field: 'status',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'status' ? this.order : undefined,
        customSort: (a, b) => a.computedStatus.localeCompare(b.computedStatus),
        cellStyle: { width: '5%' },
        render: ({ computedStatus, analyticsType }) => {
          const uploadNeededAirbridge =
            analyticsType === UnitAnalyticsType.AIRBRIDGE && computedStatus === ComputedStatus.MANUAL_UPLOAD_NEEDED
          // NOTE: AIRBRIDGE가 Appsflyer 와 동일하게 MANUAL_UPLOAD_NEEDED 값을 받아오나
          // Appsflyer와 다른 상태명이 필요하고 클릭 이벤트가 없기 때문에 AIRBRIDGE_UPLOAD_NEEDED 으로 상태명을 변경함.
          const changedStatus = uploadNeededAirbridge ? 'AIRBRIDGE_UPLOAD_NEEDED' : computedStatus
          const handleRoute = () => {
            if (changedStatus !== ComputedStatus.MANUAL_UPLOAD_NEEDED) return
            navigate('/setting/connect-data', { state: { filter: UnitAnalyticsType.APPSFLYER } })
          }
          return (
            <div className={`cell-body-box ${changedStatus}`} onClick={handleRoute}>
              <span>{t(`circle.existingUnit.status.${changedStatus}`)}</span>
            </div>
          )
        }
      }
    }

    const columnNote = (): TableRow => {
      return {
        title: (
          <TableTitle titleStr={t('circle.existingUnit.columnHeader.note')}>
            <CommonTooltip
              id={`common-table-tooltip`}
              placement="right-start"
              arrow
              title={
                <>
                  <h1>{t('circle.tooltip.createUnit.error.title')}</h1>
                  <div className="common-style">
                    <p className="desc bold">
                      <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.text')} />
                    </p>
                    <p className="desc bold">
                      <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.text2')} />
                    </p>
                    <InnerHtml
                      tagName="ul"
                      className="desc-list"
                      innerHTML={t('circle.tooltip.createUnit.error.list')}
                    />
                    <dl className="w32_w68">
                      <dt>{t('circle.tooltip.createUnit.error.label.1')}</dt>
                      <dd>{t('circle.tooltip.createUnit.error.desc.1')}</dd>
                      <dt>{t('circle.tooltip.createUnit.error.label.2')}</dt>
                      <dd>
                        <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.desc.2')} />
                      </dd>
                      <dt>{t('circle.tooltip.createUnit.error.label.3')}</dt>
                      <dd>
                        <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.desc.3')} />
                      </dd>
                      <dt>{t('circle.tooltip.createUnit.error.label.4')}</dt>
                      <dd>
                        <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.desc.4')} />
                      </dd>
                    </dl>
                    <p className="desc bold">
                      <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.text3')} />
                    </p>
                    <dl className="w32_w68">
                      <dt>{t('circle.tooltip.createUnit.error.label.99')}</dt>
                      <dd>
                        <InnerHtml innerHTML={t('circle.tooltip.createUnit.error.desc.99')} />
                      </dd>
                    </dl>
                  </div>
                </>
              }
            >
              <AdviceMarkIcon className="tooltip-icon" />
            </CommonTooltip>
          </TableTitle>
        ),
        field: 'note',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'note' ? this.order : undefined,
        customSort: (a, b) => {
          if (!a.statusCode && a.computedStatus === ComputedStatus.PREPROCESSED_BID) a.statusCode = '00'
          if (!b.statusCode && a.computedStatus === ComputedStatus.PREPROCESSED_BID) b.statusCode = '00'
          if (!a.statusCode) return -1
          if (!b.statusCode) return 1
          return a.statusCode.localeCompare(b.statusCode)
        },
        cellStyle: { width: '5%' },
        render: ({ statusCode, computedStatus }) => {
          let code: string | undefined
          if (computedStatus === ComputedStatus.PREPROCESSED_BID) code = '00'
          if (computedStatus === ComputedStatus.ERROR) code = '99'
          if (computedStatus === ComputedStatus.NOT_PREPROCESSED_BID) code = statusCode
          if (!code || code === '06' || code === '07') return <></>
          return (
            <div className="cell-body-box status-code">
              <InnerHtml
                tagName="span"
                className={`${computedStatus}_${code}`}
                innerHTML={t(`circle.existingUnit.status.${code}`)}
              />
            </div>
          )
        }
      }
    }

    const columnAccount = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.existingUnit.columnHeader.media')} />,
        field: 'adType',
        align: 'center',
        sorting: true,
        cellStyle: { width: '15%' },
        render: ({ mediaAccountId, mediaAccountName, mediaType }) => {
          return (
            <div className="media-account-cell">
              <MediaIcon mediaType={mediaType} />
              <span className="media-account-cell__name">{mediaAccountName}</span>
              <span>{mediaAccountId}</span>
            </div>
          )
        }
      }
    }

    const columnCampaignType = (): TableRow => {
      return {
        title: (
          // FIXME: 중복됨 Platform tooltip
          <TableTitle titleStr={t('circle.existingUnit.columnHeader.adType')}>
            <CommonTooltip
              id={`common-table-tooltip`}
              placement="right-start"
              arrow
              title={
                <>
                  <h1>{t('circle.tooltip.createUnit.adType.title')}</h1>
                  <div className="common-style">
                    <p>{t('circle.tooltip.createUnit.adType.content.0')}</p>
                    <dl className="w20_w80">
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type1"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail1')}</dd>
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type2"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail2')}</dd>
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type3"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail3')}</dd>
                      <dt>
                        <Badge
                          size="sm"
                          className="text-active-blue border-active-blue"
                          outlined
                          i18nKey="circle.tooltip.createUnit.adType.content.1.type4"
                        />
                      </dt>
                      <dd>{t('circle.tooltip.createUnit.adType.content.1.detail4')}</dd>
                    </dl>
                  </div>
                </>
              }
            >
              <AdviceMarkIcon className="tooltip-icon" />
            </CommonTooltip>
          </TableTitle>
        ),
        field: 'platformType',
        align: 'center',
        sorting: true,
        cellStyle: { width: '5%' },
        render: ({ platformType }) => {
          return (
            <div className="cell-body-box">
              <Badge
                size="sm"
                className="text-active-blue border-active-blue"
                outlined
                i18nKey={`common.code.platform.${platformType}`}
              />
            </div>
          )
        }
      }
    }

    const columnCampaign = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.existingUnit.columnHeader.campaign')} />,
        field: 'campaignName',
        align: 'center',
        sorting: true,
        cellStyle: { width: '20%' },
        render: ({ campaignName, campaignId }) => {
          return (
            <div className="circle-cell__campaign">
              <span className="circle-cell__campaign-name">{campaignName}</span>
              <span className="circle-cell__campaign-id">{campaignId}</span>
            </div>
          )
        }
      }
    }

    const columnActive = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.existingUnit.columnHeader.onoff')} />,
        field: 'active',
        align: 'center',
        sorting: true,
        cellStyle: { width: '5%' },
        render: ({ active, mediaType }) => {
          return <span className="cell-body-box">{mediaActiveStatus(active, mediaType)}</span>
        }
      }
    }

    const columnAnalyticsType = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.existingUnit.columnHeader.tool')} />,
        field: 'analyticsType',
        align: 'center',
        sorting: true,
        cellStyle: { width: '5%' },
        render: ({ analyticsType, mediaType }) => {
          return (
            <div className="cell-body-box">
              <MediaIcon mediaType={analyticsType === 'MEDIA' ? mediaType : analyticsType} />
            </div>
          )
        }
      }
    }

    const columnAnalyticsConversion = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.existingUnit.columnHeader.conversion')} />,
        field: 'advertiserName',
        align: 'center',
        sorting: true,
        cellStyle: { width: '10%' },
        render: ({ analyticsMetricName }) => {
          return <div className="cell-body-box">{analyticsMetricName}</div>
        }
      }
    }

    const columnSetting = (): TableRow => {
      return {
        field: 'setting',
        align: 'center',
        cellStyle: { width: '5%' },
        render: (rowData) => {
          return (
            <div className="cell-body-box pointer">
              {/* NOTE: 임시로 숨김 */}
              {/* <MopIcon name={MOPIcon.EDIT} size={24} onClick={() => this.handleEdit(rowData)}/> */}
              {!hasSystemViewerAuthority && (
                <MopIcon
                  name={MOPIcon.DELETE}
                  size={24}
                  onClick={() => this.handleDelete(rowData.unitId)}
                  gtmId="unit-delete"
                />
              )}
            </div>
          )
        }
      }
    }

    return [
      columnStatus(),
      columnAccount(),
      columnCampaignType(),
      columnCampaign(),
      columnActive(),
      columnAnalyticsType(),
      columnAnalyticsConversion(),
      columnNote(),
      columnSetting()
    ]
  }
}
