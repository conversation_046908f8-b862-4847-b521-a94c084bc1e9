import SelectBottom from '@components/common/SelectBottom';
import { SelectDropdownIcon } from '@components/common/icon'
import { MenuItem } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import './RoundedDropdown.scss'
import React, { ReactElement } from 'react';
import TagManager from 'react-gtm-module'

interface Props {
  name: string
  value: any
  width?: string
  tooltip?: ReactElement
  label?: string
  all?: boolean
  font?: number
  dropdownSize?: number
  disabled?: boolean
  searchEl?: React.ReactNode
  gtmId?: string
  renderValue?: (_value: unknown) => React.ReactNode
  onChange: (
    _event: React.ChangeEvent<{
      name?: string | undefined
      value: unknown
    }>
  ) => void
  onClick?: () => void
}

const RoundedDropdown = ({
  children,
  disabled,
  value,
  name,
  onChange,
  onClick,
  searchEl,
  renderValue,
  tooltip,
  label,
  width,
  font,
  dropdownSize,
  all = true,
  gtmId
}: React.PropsWithChildren<Props>) => {
  const { t } = useTranslation()
  const customStyle = { '--width': width, '--font-size': `${font}px`, '--dropdown-width': `${dropdownSize}px` }
  const handleClick = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'click',
        gtm_id: gtmId
      }
    })

    onClick?.()
  }

  return (
    <div
      className={`rounded-dropdown rounded-dropdown-${name}`}
      style={customStyle as React.CSSProperties}
      onClick={handleClick}
      data-gtm-id={gtmId}
    >
      {label && (
        <span className="rounded-dropdown__label">
          {tooltip}
          {label}
        </span>
      )}
      <SelectBottom
        name={name}
        value={value}
        displayEmpty
        onChange={onChange}
        disabled={disabled}
        MenuProps={{ className: 'filter-options-popover filter-options-popover__rounded-dropdown' }}
        IconComponent={(props) => <SelectDropdownIcon {...props} size={dropdownSize} />}
        renderValue={renderValue}
        header={searchEl}
      >
        {all && <MenuItem value={'ALL'}>{t('common.label.filter.all')}</MenuItem>}
        {children}
        <MenuItem value="" disabled>
          <span className="need-select">{t('common.label.filter.needSelect')}</span>
        </MenuItem>
      </SelectBottom>
    </div>
  )
}

export default RoundedDropdown