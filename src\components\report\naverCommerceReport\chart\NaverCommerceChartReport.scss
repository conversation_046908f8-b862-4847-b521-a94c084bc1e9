.campaign-chart-report {
  width: 100%;

  &__collapse {
    padding: 24px 16px;
    border: 1px solid var(--gray-lighter);

    .MuiCollapse-wrapperInner {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 0 16px;
      min-width: 0;
    }

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &-content {
      width: 100%;
      height: 320px;
      .chart-y-axis-title {
        width: 100%;
        display: flex;

        .y-axis-label {
          color: var(--point_color);
          font-size: 10px;
          font-weight: 700;
          line-height: 1;

          &__left {
            color: #0081ff;
          }

          &__right {
            color: #eb414c;
            margin-left: auto;
          }
        }
      }
    }
  }

  &__axis-wrapper {
    display: flex;
    align-items: center;
    width: 320px;

    .item-filter {
      // NOTE mui-component-select-{filterName}
      .MuiSelect-select.MuiSelect-selectMenu {
        .indicator-label {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          height: 14px;
          gap: 4px;
          &::before {
            position: absolute;
            content: '';
            width: 10px;
            height: 10px;
            border-radius: 100%;
            top: calc(50% - 5px);
            left: -14px;
          }

          &--first::before {
            background-color: #0081ff;
          }
          &--second::before {
            background-color: #eb414c;
          }
        }
      }
    }
  }

  &__button-wrapper {
    display: flex;
    align-items: center;
    gap: 24px;
  }

  &__button-group {
    display: flex;

    button {
      border: 1px solid var(--gray-lighter);
      border-right: unset;
      border-radius: 0;
      outline: unset;
      background-color: transparent;
      color: var(--gray);
      font-size: 12px;
      height: 28px;
      line-height: 28px;
      padding: 0 24px;
      cursor: pointer;

      &:disabled {
        color: var(--gray-lighter);
        cursor: not-allowed;
      }
    }

    & button:last-child {
      border-right: 1px solid var(--gray-lighter);
    }

    button.campaign-chart-report__button--selected {
      border-color: var(--color-active-blue);
      background-color: var(--color-active-blue);
      color: white;
      font-weight: 700;
    }
    .button-icon {
      color: var(--color-active-blue);
      display: flex;
      align-items: center;
      padding: 0 16px;

      &.campaign-chart-report__button--selected {
        color: white;
      }
    }
  }

  &__collapse-button {
    width: 100%;
    background-color: var(--bg-gray-light);
    border: 1px solid var(--gray-lighter);
    padding: 8px 0;
    font-size: 14px;
    font-weight: 700;
    color: var(--point_color);
    cursor: pointer;

    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    svg {
      transform: rotate(180deg);
    }
  }

  &:has(.MuiCollapse-hidden) {
    .campaign-chart-report__collapse {
      padding: 0;
    }

    .campaign-chart-report__collapse-button svg {
      transform: rotate(0);
    }
  }
}
