import LineChart from '@components/common/chart/LineChart';
import useCustomTooltip from '@components/common/hook/UseCustomTooltip';
import { getChartData, options } from './utils';
interface Props {
  showDate?: boolean
  toggleData: any
  datum: {
    data: string,
    date_idx: number,
    prediction_date: string
  }
}
const BudgetAnalysisChart = ({toggleData, datum}: Props) => {
  const parsedData = JSON.parse(datum.data).sort((a: any, b: any) => a.budget - b.budget)
  const { tooltipInfo } = useCustomTooltip({ horizontalOrigin: 'CENTER', verticalOrigin: 'TOP' });
  const chartData = getChartData(parsedData, toggleData)
  return (
    <div className="panel-item">
      <div className="dday">D-{datum.date_idx}</div>
      <LineChart
        chartData={chartData}
        options={options}
        // plugins={plugins.current}
        tooltipInfo={tooltipInfo}
        className="chart"
        width={300}
        height={300}
      />
      <div className="date">Date : {datum.prediction_date}</div>
    </div>
  )
}

export default BudgetAnalysisChart