/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil'
import CommonResponse from '@models/common/CommonResponse'
import { Service } from '@models/common/Service'
import {
  Keyword,
  ShoppingAd,
  AdIdResponse,
  SearchKeywordByAdIdResponse,
  SearchKeywordByIdReqeust,
  SearchKeywordRequest,
  SearchAdByIdReqeust
} from '@models/common/Keyword'

export const getKeywords = async (searchParam: SearchKeywordRequest, isLoading = true): Promise<Keyword[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/keywords',
    method: Method.POST,
    params: {
      bodyParams: {
        searchKeyword: searchParam.searchKeyword,
        adgroupIds: searchParam.adgroupIds,
        mediaType: searchParam.mediaType
      }
    },
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : []) as Keyword[]
}

export const getKeywordById = async (param: SearchKeywordByIdReqeust, isLoading = true): Promise<Keyword> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/keywords/${param.keywordId}`,
    method: Method.GET,
    params: {
      queryParams: {
        mediaType: param.mediaType,
        advertiserId: param.advertiserId
      }
    },
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : null) as Keyword
}

export const getShoppingAds = async (param: SearchKeywordRequest, isLoading = true): Promise<ShoppingAd[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/shopping/ads',
    method: Method.POST,
    params: {
      bodyParams: param
    },
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : []) as ShoppingAd[]
}

export const getAdById = async (param: SearchAdByIdReqeust, isLoading = true): Promise<AdIdResponse> => {
  const { adTitleId: adId, ...restParmas } = param //mediaType, advertiserId, deviceType
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/ads/${adId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...restParmas
      }
    },
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : null) as AdIdResponse
}

export const getSearchKeywordByAdId = async (
  param: SearchAdByIdReqeust,
  isLoading = true
): Promise<SearchKeywordByAdIdResponse[]> => {
  const { adTitleId: adId, ...restParmas } = param //mediaType, advertiserId, deviceType
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/ads/searchKeywords/${adId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...restParmas
      }
    },
    config: {
      isLoading: isLoading
    }
  })
  return (response.successOrNot === 'Y' ? response.data : null) as SearchKeywordByAdIdResponse[]
}
