import { But<PERSON>, <PERSON><PERSON>, <PERSON>alogContent, DialogTitle, OutlinedInput } from '@material-ui/core'
import React, { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import { AdEfficiencyAnalysisParams, ChartIndicator } from '@models/adEfficiency/AdEfficiency'
import { indicatorOptions, standardOptions } from './AdEfficiencyChart/utils'
import { RoundedSelect } from '@components/common'
import { YNFlag } from '@models/common/YNFlag'
import { Close } from '@material-ui/icons'
import DecimalNumberFormat from '@components/common/DecimalNumberFormat'
import './AdEfficiencySettingModal.scss'
interface Props {
  open: boolean
  onClose: () => void
  save: (_setting: AdEfficiencyAnalysisParams) => void
  settingValues: { indicator: ChartIndicator; analysisValue: number; isGreater: YNFlag }
  setSettingValues: ({
    indicator,
    analysisValue,
    isGreater
  }: {
    indicator: ChartIndicator
    analysisValue: number
    isGreater: YNFlag
  }) => void
}

const AdEfficiencySettingModal: React.FC<Props> = ({
  open,
  onClose,
  save,
  settingValues,
  setSettingValues
}: Props): ReactElement => {
  const { t } = useTranslation()

  const handleSave = async () => {
    save({
      isGreater: settingValues.isGreater,
      targetFilter: settingValues.indicator,
      value: settingValues.analysisValue
    })
    onClose()
  }

  return (
    <Dialog open={open} id="adEfficiency-setting-modal" onClose={onClose}>
      <DialogTitle className="modal-title-container">
        <span className="title">Settings</span>
        <span className="sub-title">설정</span>
        <button className="modal-close-button" onClick={onClose}>
          <Close />
        </button>
      </DialogTitle>
      <DialogContent>
        <div className="modal-content-form">
          <span>분석기준지표</span>
          <RoundedSelect
            options={indicatorOptions()}
            value={settingValues.indicator}
            updateValue={(newValue) =>
              setSettingValues({
                indicator: newValue,
                analysisValue: settingValues.analysisValue,
                isGreater: settingValues.isGreater
              })
            }
          />
          <span>수치</span>
          <OutlinedInput
            className="modal-content-input"
            value={settingValues.analysisValue}
            type="text"
            inputComponent={DecimalNumberFormat}
            onChange={(e) =>
              setSettingValues({
                indicator: settingValues.indicator,
                analysisValue: +e.target.value,
                isGreater: settingValues.isGreater
              })
            }
          />
          <span>기준</span>
          <RoundedSelect
            options={standardOptions()}
            value={settingValues.isGreater}
            updateValue={(newValue) =>
              setSettingValues({
                indicator: settingValues.indicator,
                analysisValue: settingValues.analysisValue,
                isGreater: newValue
              })
            }
          />
        </div>
        <Button className="modal-save-button" variant="contained" onClick={handleSave}>
          {t('setting.member.list.label.save')}
        </Button>
      </DialogContent>
    </Dialog>
  )
}

export default AdEfficiencySettingModal
