import { useTranslation } from 'react-i18next';
import { NaverCommerceIndicator } from '@utils/naverCommerceReport';
import useReportIndicatorFormat from '@components/report/hook/useReportIndicatorFormat';
import { ReactComponent as VatExcluded } from '@components/assets/images/vat_excluded.svg';
import { ReactComponent as VatIncluded } from '@components/assets/images/vat_included.svg';
import './NaverReportChartTooltip.scss'

interface Props {
  top: string
  left: string
  data: {
    [key in string] ?: { date: string, pointer: string, value: string }
  }
}

const NaverReportChartTooltip = ({ top, left, data }: Props) => {
  const { t } = useTranslation();
  const { indicatorFormat } = useReportIndicatorFormat();
  return (
    <div id="report-chart-tooltip" style={{ left, top }}>
      <div className="report-chart-tooltip__title">
        <span>Date</span>
        <span>Pointer</span>
        <span>Value</span>
      </div>
      <div className="report-chart-tooltip__content">
        { data?.first && (
          <div className="report-chart-tooltip__current">
            <span>{data.first.date}</span>
            <span className='first-pointer'>
              { (data.first.pointer === NaverCommerceIndicator.SALES_AMOUNT || data.first.pointer === NaverCommerceIndicator.COST) ?
                  t(`naverReport.indicator.label.${data.first.pointer}`) :
                  t(`naverReport.indicator.lableWithUnit.${data.first.pointer}`)
              }
              { data.first.pointer === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded/> }
              { data.first.pointer === NaverCommerceIndicator.COST && <VatExcluded/> }
            </span>
            <span>{indicatorFormat(data.first.value, data.first.pointer as any)}</span>
          </div>
        )}
        { data?.firstCompare && (
          <div className="report-chart-tooltip__compare">
            <span>{data.firstCompare.date}</span>
            <span className='first-pointer'>
              { (data.firstCompare.pointer === NaverCommerceIndicator.SALES_AMOUNT || data.firstCompare.pointer === NaverCommerceIndicator.COST) ?
                  t(`naverReport.indicator.label.${data.firstCompare.pointer}`) :
                  t(`naverReport.indicator.lableWithUnit.${data.firstCompare.pointer}`)
              }
              { data.firstCompare.pointer === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded/> }
              { data.firstCompare.pointer === NaverCommerceIndicator.COST && <VatExcluded/> }
            </span>
            <span>{indicatorFormat(data.firstCompare.value, data.firstCompare.pointer as any)}</span>
          </div>
        )}
        <hr className="divider"></hr>
        { data?.second && (
          <div className="report-chart-tooltip__current">
            <span>{data.second.date}</span>
            <span className='second-pointer'>
              { (data.second.pointer === NaverCommerceIndicator.SALES_AMOUNT || data.second.pointer === NaverCommerceIndicator.COST) ?
                  t(`naverReport.indicator.label.${data.second.pointer}`) :
                  t(`naverReport.indicator.lableWithUnit.${data.second.pointer}`)
              }
              { data.second.pointer === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded/> }
              { data.second.pointer === NaverCommerceIndicator.COST && <VatExcluded/> }
            </span>
            <span>{indicatorFormat(data.second.value, data.second.pointer as any)}</span>
          </div>
        )}
        { data?.secondCompare && (
          <div className="report-chart-tooltip__compare">
            <span>{data.secondCompare.date}</span>
            <span className='second-pointer'>
              { (data.secondCompare.pointer === NaverCommerceIndicator.SALES_AMOUNT || data.secondCompare.pointer === NaverCommerceIndicator.COST) ?
                  t(`naverReport.indicator.label.${data.secondCompare.pointer}`) :
                  t(`naverReport.indicator.lableWithUnit.${data.secondCompare.pointer}`)
              }
              { data.secondCompare.pointer === NaverCommerceIndicator.SALES_AMOUNT && <VatIncluded/> }
              { data.secondCompare.pointer === NaverCommerceIndicator.COST && <VatExcluded/> }
            </span>
            <span>{indicatorFormat(data.secondCompare.value, data.secondCompare.pointer as any)}</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default NaverReportChartTooltip   