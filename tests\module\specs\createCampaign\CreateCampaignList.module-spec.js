/// <reference types="Cypress" />

import CreateCampaignPage from '@pages/createCampaign/CreationCampaignList';
import CreateCampaignMock from '@mock/CreateCampaignMock';
import AdvertiserMock from '@mock/AdvertiserMock';

const page = new CreateCampaignPage();
const service = new CreateCampaignMock();
const advertiserService = new AdvertiserMock();

const sessionInfo = {
  sessionId: 'test-session-id',
  memberId: 1,
  memberName: 'Hong Gil-dong', 
  roleType: 'USER',
};

describe('Create Campaign List Page', () => {
  describe('Campaign List Display', () => {
    it.guide('When navigating to the Create Campaign List page, the campaign list is displayed.', { 
      mockFunc: () => {
        advertiserService.successWhenGetAdvertisers();
        service.successWhenGetCampaigns();
      },
      actionFunc: () => {
        page.fakeSession(sessionInfo);
        page.visit('/campaign'); 
        cy.url().should('include', '/campaign');
      },
      waitFunc: () => {
        cy.wait('@successWhenGetAdvertisers');
        cy.wait('@successWhenGetCampaigns');
      },
      assertFunc: () => {
        page.assertCreateCampaignPageDisplayed();
        page.assertCreateButtonExists();
        page.assertCampaignTableExists();
        page.assertCampaignInTable('[브랜드A] 베스트 상품 프로모션');
      },
    });
  });

  describe('Campaign Search', () => {
    it.guide('When searching by campaign name, only the corresponding campaign is displayed.', { 
      mockFunc: () => {
        advertiserService.successWhenGetAdvertisers();
        service.successWhenGetCampaigns();
      },
      actionFunc: () => {
        page.fakeSession(sessionInfo);
        page.visit();
        cy.url().should('include', '/campaign');
        cy.wait('@successWhenGetAdvertisers');
        service.successWhenSearchCampaigns('A');
        // Type search keyword
        page.typeCampaignName('A');
      },
      assertFunc: () => {
        // Should filter to show only Brand A campaign
        page.assertCampaignInTable('[브랜드A] 베스트 상품 프로모션');
        page.assertCampaignCount(1); // Only 1 campaign should match
      },
    });
  });

  describe('Campaign Creation', () => {
    it.guide('When clicking the Create button, the campaign creation modal opens.', {
      mockFunc: () => {
        advertiserService.successWhenGetAdvertisers();
        service.successWhenGetCampaigns();
        service.successWhenGetMediaAccount();
        service.successWhenGetShoppingMall();
        service.successWhenGetProducts();
      },
      actionFunc: () => {
        page.fakeSession(sessionInfo);
        page.visit();
        cy.url().should('include', '/campaign');
        cy.wait('@successWhenGetAdvertisers');
        cy.wait('@successWhenGetCampaigns');

        // Click create button
        page.clickCreateButton();
        cy.wait('@successWhenGetMediaAccount');
        cy.wait('@successWhenGetShoppingMall');
        cy.wait('@successWhenGetProducts');
      },
      assertFunc: () => {
        page.assertCreateFormPage();
      },
    });
  });
});