import { useTranslation } from 'react-i18next';
import { AccessKeyListColumn } from '@models/accessManage/AccessManage';
import { TableTitle } from '@components/common/table';
import { ReactComponent as EditIcon } from '@components/assets/images/icon_edit.svg';
import './AccessKeyListTableFormatter.scss';
import { YNFlag } from '@models/common';

export default class AccessKeyListTableFormatter {
  constructor() {
    this.field = '';
    this.order = 'asc';
  }
  field: string;
  order: 'asc' | 'desc';
  handleEdit(advertiserId: number, accessKey: string) {}
  getColumnFormat = (): Array<AccessKeyListColumn> => {
    const { t } = useTranslation();

    const columnAdvertiserName = (): AccessKeyListColumn => {
      return {
        title: <TableTitle titleStr={t('accessManage.label.list.columnHeader.advertiserName')} />,
        field: 'advertiserName',
        align: 'left',
        sorting: true,
        defaultSort: this.field === 'advertiserName' ? this.order : undefined,
        cellStyle: {
          width: '30%',
        },
        render: (rowData) => {
          return <div className="cell-body-box advertiser-name">{rowData.advertiserName}</div>;
        },
      };
    };

    const columnAccessKey = (): AccessKeyListColumn => {
      return {
        title: <TableTitle titleStr={t('accessManage.label.list.columnHeader.accessKey')} />,
        field: 'accessKey',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '30%',
        },
        render: (rowData) => {
          const openEditModal = (e: any) => {
            e.stopPropagation();
            this.handleEdit(rowData.advertiserId, rowData.accessKey);
          };
          return (
            <div className="cell-body-box access-key pointer">
              {rowData.accessKey}
              {<EditIcon onClick={openEditModal} />}
            </div>
          );
        },
      };
    };

    const columnDescription = (): AccessKeyListColumn => {
      return {
        title: <TableTitle titleStr={t('accessManage.label.list.columnHeader.description')} />,
        field: 'description',
        align: 'left',
        sorting: false,
        cellStyle: {
          width: '30%',
        },
        render: (rowData) => {
          return <div className="cell-body-box description">{rowData.description}</div>;
        },
      };
    };

    const columnUseYn = (): AccessKeyListColumn => {
      return {
        title: <TableTitle titleStr={t('accessManage.label.list.columnHeader.useYn')} />,
        field: 'useYn',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
        },
        render: (rowData) => {
          return (
            <div className="cell-body-box use-yn">
              {rowData.useYn === YNFlag.Y ? t('accessManage.label.list.useYes') : t('accessManage.label.list.useNo')}
            </div>
          );
        },
      };
    };

    return [columnAdvertiserName(), columnAccessKey(), columnDescription(), columnUseYn()];
  };
}
