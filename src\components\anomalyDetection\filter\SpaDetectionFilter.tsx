import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ListItemFilterGroup, ListItemFilter } from '@components/common/filter'
import { MenuItem } from '@material-ui/core';
import { useRecoilValue, useRecoilState } from 'recoil';
import {
  spaDetectionFilterQuery,
  queryStringCampaign,
  queryStringAdgroup,
  queryStringAd,
  spaAnomalyAdgroupItems,
  spaAnomalyCampaignItems,
  spaAnomalyAdItems
} from '@store/AnomalyDetection'
import SelectBottom from '@components/common/SelectBottom'
import { MopSearch } from '@components/common'
import { getAdgroupTypeItems } from '@utils/CodeUtil';
import { SelectDropdownIcon } from '@components/common/icon'
import { SpaFilterKey } from '@models/anomalyDetection'
import './DetectionFilter.scss'

const SaDetectionFilter: React.FC = (): ReactElement => {
  const { t } = useTranslation();
  const [query, setQuery] = useRecoilState(spaDetectionFilterQuery)
  const filteredCampaignItems = useRecoilValue(spaAnomalyCampaignItems)
  const filteredAdgroupItems = useRecoilValue(spaAnomalyAdgroupItems)
  const filteredAdItems = useRecoilValue(spaAnomalyAdItems)

  const [searchCampaign, setSearchCampaign] = useRecoilState(queryStringCampaign)
  const [searchAdgroup, setSearchAdgroup] = useRecoilState(queryStringAdgroup)
  const [searchAd, setSearchAd] = useRecoilState(queryStringAd)

  const [filter, setFilter] = useState<SpaFilterKey>({
    spaType: 'ALL',
    campaignName: 'ALL',
    adgroupId: 'ALL',
    productName: 'ALL'
  })

  const resetQuery = (query: Partial<SpaFilterKey>, filter: SpaFilterKey, keys: (keyof SpaFilterKey)[]) => {
    keys.forEach((key) => {
      if (query[key]) {
        delete query[key]
        filter[key] = 'ALL'
      }
    })
    return { query, filter }
  }

  const selectFilter = (e: React.ChangeEvent<{
    name?: string | undefined;
    value: unknown;
  }>) => {
    const name = e.target.name as keyof SpaFilterKey
    let newQuery = { ...query }
    let newFilter = { ...filter }
    if (e.target.value === 'ALL' && newQuery[name]) {
      delete newQuery[name]
      setQuery(newQuery)
    } else {
      if (name === 'campaignName') {
        const removed = resetQuery(newQuery, newFilter, ['adgroupId', 'productName'])
        newQuery = removed.query
        newFilter = removed.filter
      }
      if (name === 'adgroupId') {
        const removed = resetQuery(newQuery, newFilter, ['productName'])
        newQuery = removed.query
        newFilter = removed.filter
      }
      setQuery({ ...newQuery, [name]: e.target.value })
    }
    setFilter({ ...newFilter, [name]: e.target.value })
  }

  return (
    <ListItemFilterGroup customClass="spa-detection-filter-group">
      <ListItemFilter
        label={t('anomalyDetection.label.filter.type')}
        name="spaType"
        value={filter.spaType}
        onChange={selectFilter}
      >
        {getAdgroupTypeItems().map((item) => (
          <MenuItem key={item.type} value={item.type}>
            { item.name }
          </MenuItem>
        ))}
      </ListItemFilter>
      <ListItemFilter
        label={t('anomalyDetection.label.filter.campaign')}
        name="campaignName"
        value={filter.campaignName}
        onChange={selectFilter}
        isCustomSelect
      >
        <SelectBottom
          displayEmpty
          onChange={selectFilter}
          name="campaignName"
          value={filter.campaignName}
          MenuProps={{
            className: 'filter-options-popover',
            anchorOrigin: { vertical: 24, horizontal: 'left' }
          }}
          IconComponent={(props) => <SelectDropdownIcon {...props} />}
        >
          <MopSearch
            placeholder={'Search'}
            value={searchCampaign}
            onChange={(e) => setSearchCampaign(e.target.value)}
            onSearch={() => setSearchCampaign(searchCampaign)}
            onFocus={() => setSearchCampaign('')}
          />
          <MenuItem value={'ALL'} className="filter-item">
            {t('common.label.filter.all')}
          </MenuItem>
          {filteredCampaignItems.map((item, i) => (
            <MenuItem
              key={`${item.campaignId}-${i}`}
              value={item.campaignName}
            >
              <div className="filter-item-group">
                <span className="filter-item-group__head">{item.campaignName}</span>
                <span>{item.campaignId}</span>
              </div>
            </MenuItem>
          ))}
        </SelectBottom>
      </ListItemFilter>
      <ListItemFilter
        label={t('anomalyDetection.label.filter.adgroup')}
        name="adgroupId"
        value={filter.adgroupId}
        onChange={selectFilter}
        isCustomSelect
      >
        <SelectBottom
          displayEmpty
          onChange={selectFilter}
          name="adgroupId"
          value={filter.adgroupId}
          MenuProps={{
            className: 'filter-options-popover',
            anchorOrigin: { vertical: 24, horizontal: 'left' }
          }}
          IconComponent={(props) => <SelectDropdownIcon {...props} />}
        >
          <MopSearch
            placeholder={'Search'}
            value={searchAdgroup}
            onChange={(e) => setSearchAdgroup(e.target.value)}
            onSearch={() => setSearchAdgroup(searchAdgroup)}
            onFocus={() => setSearchAdgroup('')}
          />
          <MenuItem value={'ALL'} className="filter-item">
            {t('common.label.filter.all')}
          </MenuItem>
          {filteredAdgroupItems.map((item, i) => (
            <MenuItem
              key={`${item.adgroupId}-${i}`}
              value={item.adgroupId}
            >
              <div className="filter-item-group">
                <span className="filter-item-group__head">{item.adgroupName}</span>
                <span>{item.adgroupId}</span>
              </div>
            </MenuItem>
          ))}
        </SelectBottom>
      </ListItemFilter>
      <ListItemFilter
        label={t('anomalyDetection.label.filter.ad')}
        name="productName"
        value={filter.productName}
        onChange={selectFilter}
        isCustomSelect
      >
        <SelectBottom
          displayEmpty
          onChange={selectFilter}
          value={filter.productName}
          name="productName"
          MenuProps={{
            className: 'filter-options-popover',
            anchorOrigin: { vertical: 24, horizontal: 'left' }
          }}
          IconComponent={(props) => <SelectDropdownIcon {...props} />}
        >
          <MopSearch
            id="anomaly-search-name"
            placeholder={'Search'}
            value={searchAd}
            onChange={(e) => setSearchAd(e.target.value)}
            onSearch={() => setSearchAd(searchAd)}
            onFocus={() => setSearchAd('')}
          />
          <MenuItem value={'ALL'} className="filter-item">
            {t('common.label.filter.all')}
          </MenuItem>
          {filteredAdItems.map(item => (
            <MenuItem key={item.adId} value={item.productName}>
              <div className="filter-item-group">
                <span className="filter-item-group__head">{item.productName}</span>
                <span>{item.adId}</span>
              </div>
            </MenuItem>
          ))}
        </SelectBottom>
      </ListItemFilter>
    </ListItemFilterGroup>
  )
}

export default SaDetectionFilter;