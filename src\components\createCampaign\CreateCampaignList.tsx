import React, { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import { pageSizeOptions } from '@models/common/CommonConstants'
import { CampaignInfo } from '@models/createCampaign/CreateCampaign'
import { FixedLayoutTable, TablePagination } from '@components/common/table'
import CreateCampaignListFormatter from './CreateCampaignListFormatter'
import { useAuthority } from '@hooks/common'
import { MOPIcon } from '@models/common/Icon'
import { MopIcon } from '@components/common'
import EmptyDetection from '@components/anomalyDetection/EmptyDetection'
import { MopButton } from '@components/common/buttons'
import { useNavigate } from 'react-router-dom'

interface Props {
  campaigns: CampaignInfo[]
  totalCount: number
  page: number
  rowsPerPage: number
  onPageChange: (page: number) => void
  onRowsPerPageChange: (rowsPerPage: number) => void
  onOrderChange?: (orderBy: number, orderDirection: 'asc' | 'desc') => void
  isLoading: boolean
}

const createCampaignListFormatter = new CreateCampaignListFormatter()

const CreateCampaignList: React.FC<Props> = ({
  campaigns,
  totalCount,
  page,
  rowsPerPage,
  onPageChange,
  onRowsPerPageChange,
  onOrderChange,
  isLoading
}: Props): ReactElement => {
  const { t } = useTranslation()
  const allColumns = createCampaignListFormatter.getColumnFormat()
  const { isProAdvertiser, advertiser } = useAuthority()
  const navigate = useNavigate()
  const handleNavigate = () => {
    const path = `/setting/subscription/${advertiser.advertiserId}`
    navigate(path, {
      state: {
        advertiserId: advertiser.advertiserId,
        advertiserName: advertiser.advertiserName
      }
    })
  }
  return (
    <div id="createCampaignList">
      {campaigns && allColumns && (
        <>
          <FixedLayoutTable
            id="createCampaignTable"
            tableType="list-table"
            onOrderChange={onOrderChange}
            columns={allColumns}
            data={campaigns.map((obj) => Object.create(obj)) || []}
            isLoading={isLoading}
            localization={
              !isProAdvertiser
                ? {
                    body: {
                      emptyDataSourceMessage: (
                        <div style={{ height: '600px' }}>
                          <div className="flex flex-col justify-center items-center h-full">
                            <MopIcon name={MOPIcon.BLINK} size={108} />
                            <p className="font-bold text-2xl mb-3 ">{t('createCampaign.message.notPermissionPro')}</p>
                            <MopButton
                              label={t('createCampaign.label.button.upgrade')}
                              bgColor="#171717"
                              textColor="#ffffff"
                              contained={true}
                              onClick={handleNavigate}
                            />
                          </div>
                        </div>
                      )
                    }
                  }
                : campaigns.length === 0
                ? { body: { emptyDataSourceMessage: t('createCampaign.label.table.noCampaignsFound') } }
                : { body: { emptyDataSourceMessage: '' } }
            }
          />
          {totalCount > 0 && isProAdvertiser && (
            <TablePagination
              id="create-campaign-list-pagination"
              totalCount={totalCount || 0}
              page={page || 1}
              rowsPerPage={rowsPerPage || pageSizeOptions[0]}
              onPageChange={onPageChange}
              onRowsPerPageChange={onRowsPerPageChange}
            />
          )}
        </>
      )}
    </div>
  )
}

export default CreateCampaignList
