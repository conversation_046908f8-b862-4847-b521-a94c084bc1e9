// src/components/common/page/PageListFilter.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import PageListFilter from './PageListFilter';
import PageListFilterGroup from './PageListFilterGroup';
import { MenuItem } from '@material-ui/core';
import { action } from '@storybook/addon-actions';
import { MopIcon, MediaIcon, TextIcon } from '@components/common';
import { MOPIcon } from '@models/common/Icon';
import { MediaType } from '@models/common/Media';
import { AdvertiserCurrencyCode } from '@models/common/Advertiser';
import { AccountType } from '@models/oauth/Oauth';

const meta: StorybookMeta<typeof PageListFilter> = {
  title: 'Components/Common/Page/PageListFilter',
  component: PageListFilter,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Input field name attribute for the filter',
    },
    label: {
      control: 'text',
      description: 'Label text displayed on the left side of the filter',
    },
    value: {
      control: 'text',
      description: 'Currently selected value of the filter',
    },
    onChange: {
      action: 'onChange',
      description: 'Callback function triggered when filter value changes',
    },
    width: {
      control: 'number',
      description: 'Custom width of the filter dropdown in pixels',
    },
    dropdownSize: {
      control: 'number',
      description: 'Size of the dropdown arrow icon',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    name: 'defaultFilter',
    label: 'Filter',
    value: 'ALL',
    onChange: action('onChange'),
    width: 224,
    dropdownSize: 40,
  },
  render: (args) => (
    <PageListFilter 
      name={args.name}
      label={args.label}
      value={args.value}
      onChange={args.onChange}
      width={args.width}
      dropdownSize={args.dropdownSize}
    >
      <MenuItem value="OPTION1">Option 1</MenuItem>
      <MenuItem value="OPTION2">Option 2</MenuItem>
      <MenuItem value="OPTION3">Option 3</MenuItem>
    </PageListFilter>
  ),
  parameters: {
    docs: {
      description: {
        story: 'The default PageListFilter component with basic text options.',
      },
    },
  },
};

// Multiple filters in a group (like ConnectOauthPage usage)
export const FilterGroup: Story = {
  render: () => (
    <PageListFilterGroup>
      <PageListFilter
        label="Status"
        name="statusFilter"
        value="ALL"
        onChange={action('statusChange')}
      >
        <MenuItem value="ACTIVE">
          <MopIcon name={MOPIcon.SWITCH_ON} size={32} />
        </MenuItem>
        <MenuItem value="INACTIVE">
          <MopIcon name={MOPIcon.SWITCH_OFF} size={32} />
        </MenuItem>
      </PageListFilter>
      
      <PageListFilter
        label="Type"
        name="typeFilter"
        value="ALL"
        onChange={action('typeChange')}
      >
        {Object.keys(AccountType).map((accountType) => (
          <MenuItem key={accountType} value={accountType}>
            <span>{accountType}</span>
          </MenuItem>
        ))}
      </PageListFilter>
      
      <PageListFilter
        label="Media"
        name="mediaFilter"
        value="ALL"
        onChange={action('mediaChange')}
      >
        {Object.values(MediaType).slice(0, 3).map((media) => (
          <MenuItem key={media} value={media}>
            <MediaIcon mediaType={media} size={24} />
          </MenuItem>
        ))}
      </PageListFilter>
      
      <PageListFilter
        label="Currency"
        name="currencyFilter"
        value="ALL"
        onChange={action('currencyChange')}
      >
        {Object.keys(AdvertiserCurrencyCode).map((currency) => (
          <MenuItem key={currency} value={currency}>
            <TextIcon code={currency} size={24} />
          </MenuItem>
        ))}
      </PageListFilter>
    </PageListFilterGroup>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Multiple PageListFilter components grouped together as used in ConnectOauthPage, demonstrating various filter types with different content patterns.',
      },
    },
  },
};

// Interactive showcase with different states
export const InteractiveShowcase: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px',
      padding: '16px',
      width: 'fit-content'
    }}>
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Standard Filters
        </h4>
        <PageListFilterGroup>
          <PageListFilter
            label="Simple"
            name="simpleFilter"
            value="OPTION1"
            onChange={action('onChange')}
            width={180}
          >
            <MenuItem value="OPTION1">Option 1</MenuItem>
            <MenuItem value="OPTION2">Option 2</MenuItem>
          </PageListFilter>
          
          <PageListFilter
            label="With Icons"
            name="iconFilter"
            value="ACTIVE"
            onChange={action('onChange')}
            width={200}
          >
            <MenuItem value="ACTIVE">
              <MopIcon name={MOPIcon.CHECK_CIRCLE} size={24} />
            </MenuItem>
            <MenuItem value="WARNING">
              <MopIcon name={MOPIcon.WARNING} size={24} />
            </MenuItem>
          </PageListFilter>
        </PageListFilterGroup>
      </div>
      
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Advanced Filters
        </h4>
        <PageListFilterGroup>
          <PageListFilter
            label="Media"
            name="advancedMediaFilter"
            value="NAVER"
            onChange={action('onChange')}
            width={250}
          >
            {Object.values(MediaType).slice(0, 3).map((media) => (
              <MenuItem key={media} value={media}>
                <MediaIcon mediaType={media} size={20} />
              </MenuItem>
            ))}
          </PageListFilter>
          
          <PageListFilter
            label="Currency"
            name="advancedCurrencyFilter"
            value="KRW"
            onChange={action('onChange')}
            width={200}
          >
            {Object.keys(AdvertiserCurrencyCode).map((currency) => (
              <MenuItem key={currency} value={currency}>
                <TextIcon code={currency} size={20} />
              </MenuItem>
            ))}
          </PageListFilter>
        </PageListFilterGroup>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive showcase of PageListFilter variations demonstrating different content types, sizes, and grouping patterns used throughout the application.',
      },
    },
  },
}; 