// src/components/common/buttons/MopButton.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react'
import MopButton from './MopButton'
import { MOPIcon } from '@models/common'
import { action } from '@storybook/addon-actions'

const meta: StorybookMeta<typeof MopButton> = {
  title: 'Components/Common/Buttons/MopButton',
  component: MopButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Button text label displayed inside the button',
    },
    onClick: {
      action: 'onClick',
      description: 'Callback function triggered when the button is clicked',
    },
    size: {
      control: 'select',
      options: ['mini', 'sm', 'md', 'lg'],
      description: 'Button size - controls font size, padding, and icon size',
    },
    rounded: {
      control: 'select',
      options: ['sm', 'md', 'full'],
      description: 'Border radius style - sm (4px), md (8px), full (pill shape)',
    },
    contained: {
      control: 'boolean',
      description: 'Controls whether the button has a filled background or outlined style',
    },
    bgColor: {
      control: 'color',
      description: 'Custom background color',
    },
    borderColor: {
      control: 'color',
      description: 'Custom border color',
    },
    textColor: {
      control: 'color',
      description: 'Custom text color',
    },
    width: {
      control: 'text',
      description: 'Custom width (e.g., "200px", "100%")',
    },
    height: {
      control: 'text',
      description: 'Custom height (e.g., "40px")',
    },
    leftIcon: {
      control: 'select',
      options: [undefined, ...Object.values(MOPIcon)],
      description: 'Icon to display on the left side of the button',
    },
    rightIcon: {
      control: 'select',
      options: [undefined, ...Object.values(MOPIcon)],
      description: 'Icon to display on the right side of the button',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    gtmId: {
      control: 'text',
      description: 'Google Tag Manager identifier for analytics tracking',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

// 기본 스토리
export const Default: Story = {
  args: {
    label: 'Default Button',
    borderColor: '#5472FF',
    onClick: action('button-clicked'),
  },
}

// 커머스 계정 연동 예시 (요청된 스토리)
export const CommerceAccountIntegration: Story = {
  args: {
    label: '커머스 계정 연동',
    rightIcon: MOPIcon.PLUS,
    bgColor: '#ffffff',
    textColor: '#5472FF',
    borderColor: '#5472FF',
    onClick: action('commerce-integration-clicked'),
    size: 'md',
    rounded: 'full',
    contained: true,
  },
}

// 크기별 예시
export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
      <MopButton label="Mini" size="mini" onClick={action('mini-clicked')} />
      <MopButton label="Small" size="sm" onClick={action('sm-clicked')} />
      <MopButton label="Medium" size="md" onClick={action('md-clicked')} />
      <MopButton label="Large" size="lg" onClick={action('lg-clicked')} />
    </div>
  ),
}

// 둥글기별 예시
export const RoundedVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
      <MopButton label="Small Radius" rounded="sm" onClick={action('sm-rounded-clicked')} />
      <MopButton label="Medium Radius" rounded="md" onClick={action('md-rounded-clicked')} />
      <MopButton label="Full Radius" rounded="full" onClick={action('full-rounded-clicked')} />
    </div>
  ),
}

// 아이콘 예시
export const WithIcons: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '10px', alignItems: 'center', flexWrap: 'wrap' }}>
      <MopButton label="Left Icon" leftIcon={MOPIcon.PLUS} onClick={action('left-icon-clicked')} />
      <MopButton label="Right Icon" rightIcon={MOPIcon.ARROW_RIGHT} onClick={action('right-icon-clicked')} />
      <MopButton label="Both Icons" leftIcon={MOPIcon.PLUS} rightIcon={MOPIcon.ARROW_RIGHT} onClick={action('both-icons-clicked')} />
      <MopButton leftIcon={MOPIcon.SEARCH} onClick={action('icon-only-clicked')} />
    </div>
  ),
}

// 스타일 변형 예시
export const StyleVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '10px', alignItems: 'center', flexWrap: 'wrap' }}>
      <MopButton label="Contained" contained={true} onClick={action('contained-clicked')} />
      <MopButton label="Outlined" contained={false} onClick={action('outlined-clicked')} />
      <MopButton label="Custom Colors" bgColor="#171717" textColor="#ffffff" contained={true} onClick={action('custom-color-clicked')} />
      <MopButton label="Disabled" disabled={true} onClick={action('disabled-clicked')} />
    </div>
  ),
}

// 커스텀 스타일 예시
export const CustomStyles: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '10px', alignItems: 'center', flexDirection: 'column' }}>
      <MopButton 
        label="Wide Button" 
        width="200px" 
        onClick={action('wide-clicked')} 
      />
      <MopButton 
        label="Tall Button" 
        height="50px" 
        onClick={action('tall-clicked')} 
      />
      <MopButton 
        label="Custom Style" 
        customStyle={{ boxShadow: '0 4px 8px rgba(0,0,0,0.2)' }}
        onClick={action('custom-style-clicked')} 
      />
    </div>
  ),
} 