import React, { useState, useEffect } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import InnerHtml from '@components/common/InnerHtml';
import { useTranslation } from 'react-i18next';
import {
  getBudgetOptimizationResult,
  getBudgetOptimizationCampaigns,
} from '@api/budgetOpt/BudgetOpt';
import {
  BudgetOptimizationStatus,
  BudgetOptimizationInfo,
  BudgetOptimizationResult,
  BudgetOptimizationCampaign,
  OptImpactKeys,
  OptBudgetKeys
} from '@models/budgetOpt/BudgetOpt';
import { MediaType } from '@models/common/Media';
import { getRandomColor } from '@utils/ColorUtil';
import { defaultImpactValues, budgetKeys, impactKeys, BudgetColorByMedia } from '@utils/budgetOpt'

import { ReactComponent as ProcessingIcon } from '@components/assets/images/processing.svg';
import './BudgetOptResult.scss';
import BudgetResultSummary from './BudgetResultSummary'
import BudgetResultTable from './BudgetResultTable'

interface Props {
  optimization: BudgetOptimizationInfo;
  refetchOptimization: () => Promise<void>;
  hasAuthority: boolean;
}

const getSums = (targets: OptImpactKeys[]|OptBudgetKeys[], arr: any[]) => {
  return arr.reduce((result, obj) => {
    for (const key of targets) {
      result[key] = (result[key] ?? 0) + Number(obj[key])
    }
    return result
  }, {} as Record<OptImpactKeys, number>|Record<OptBudgetKeys, number>)
}

const BudgetOptResult: React.FC<Props> = ({ optimization, refetchOptimization }: Props) => {
  const { t } = useTranslation();
  const status = optimization.status
  const [resultData, setResultData] = useState<BudgetOptimizationResult[] | null>(null)
  const [campaigns, setCampaigns] = useState<BudgetOptimizationCampaign[] | null>(null)
  const [refetchingKey, setRefetchingKey] = useState<number>(-1)
  const refetchingPeriod = 5000

  const _format = (new Intl.NumberFormat('ko-KR', { maximumFractionDigits: 0 })).format
  const format = (value: string|number) => _format(Number(value))

  const impactPrev = resultData && getSums(impactKeys, resultData.map((item) => item.mopPrevTwoWeeks ?? defaultImpactValues))
  const impactRecommend = resultData && getSums(impactKeys, resultData.map((item) => item.mopRecommend ?? defaultImpactValues))
  const impact: Record<string, number> = impactPrev && impactRecommend && Object.fromEntries(impactKeys.map(key => {
    const recommend = Number(impactRecommend[key])
    const prev = Number(impactPrev[key])
    const rate = prev === 0 ? 0 : (recommend - prev) / prev * 100
    return [key, rate]
  }))

  const campaignObject = campaigns ? Object.fromEntries(campaigns.map(item => [item.campaignId, item])) : {}
  const uniqueCheck: Record<string,boolean> = {}
  const uniqueKeys: any[] = []
  if (resultData && campaigns) {
    resultData.forEach(campaign => {
      const accountId = campaignObject[campaign.campaignId].accountId
      const key = `${campaign.mediaType}-${accountId}-${campaign.adType}`
      if (!uniqueCheck[key]) {
        uniqueCheck[key] = true
        uniqueKeys.push([campaign.mediaType, accountId, campaign.adType])
      }
    })
  }
  const colorIndex = Object.fromEntries(Object.keys(BudgetColorByMedia).map(mediaType => [mediaType, 0]))
  const colors = Object.fromEntries(uniqueKeys.map(keys => {
    const mediaType = keys[0] as MediaType
    const color = BudgetColorByMedia[mediaType].colors?.[colorIndex[mediaType]]
    if (color) {
      colorIndex[mediaType]++
    }
    return [keys.join('-'), color ?? getRandomColor()]
  }))
  const budgetPrevTwoWeeks = resultData && Object.fromEntries(
    uniqueKeys.map(([mediaType, accountId, adType]) => [
      `${mediaType}-${accountId}-${adType}`,
      getSums(budgetKeys, resultData.filter(item =>
        item.mediaType === mediaType &&
        campaignObject[item.campaignId].accountId === accountId &&
        item.adType === adType
      ).map(item => item.mopPrevTwoWeeks ?? defaultImpactValues)),
    ]).map(([key, values]) => {
      values.color = colors[key]
      values.budget = format(values.budget)
      return [
        key,
        values,
      ]
    })
  )
  const budgetRecommend = resultData && budgetPrevTwoWeeks && Object.fromEntries(
    uniqueKeys.map(([mediaType, accountId, adType]) => [
      `${mediaType}-${accountId}-${adType}`,
      getSums(budgetKeys, resultData.filter(item =>
        item.mediaType === mediaType &&
        campaignObject[item.campaignId].accountId === accountId &&
        item.adType === adType
      ).map(item => item.mopRecommend)),
    ]).map(([key, values]) => {
      const prev = Number(budgetPrevTwoWeeks[key].budget.replace(/,/g, ''));
      values.ratio = (values.budget - prev) / prev * 100
      values.color = colors[key]
      values.budget = format(values.budget)
      return [
        key,
        values,
      ]
    })
  )
  console.log(budgetRecommend)
  const getResult = async() => {
    const result = await getBudgetOptimizationResult(optimization.optimizationId)
    const resultCampaigns = await getBudgetOptimizationCampaigns(optimization.optimizationId)
    unstable_batchedUpdates(() => {
      setResultData(result)
      setCampaigns(resultCampaigns.campaigns)
    })
  }

  useEffect(() => {
    window.clearInterval(refetchingKey)
    if ( status === BudgetOptimizationStatus.SETTING || status === BudgetOptimizationStatus.RUNNING || status === BudgetOptimizationStatus.ERROR ) {
      const key = window.setInterval(() => {
        refetchOptimization()
      }, refetchingPeriod)
      setRefetchingKey(key)
    } else {
      if (status === BudgetOptimizationStatus.FINISHED) {
        getResult()
      }
    }
    return () => window.clearInterval(refetchingKey)
  }, [status]) //eslint-disable-line

  useEffect(() => {
    refetchOptimization()
  }, []) //eslint-disable-line

  if (
    status === BudgetOptimizationStatus.SETTING ||
    status === BudgetOptimizationStatus.RUNNING ||
    status === BudgetOptimizationStatus.FINISHED && resultData === null
  ) {
    return (
      <div id="BudgetOptResult">
        <div className="budget-result-running">
          <ProcessingIcon />
          <div>{t('optimization.label.budgetOpt.result.message.optimizing')}</div>
        </div>
      </div>
    )
  } else if (status === BudgetOptimizationStatus.FINISHED && resultData !== null) {
    return (
      <div id="BudgetOptResult">
        <BudgetResultSummary
          optimization={optimization}
          budgetPrevTwoWeeks={budgetPrevTwoWeeks}
          budgetRecommend={budgetRecommend}
          kpiImpact={impact}
        />
        <BudgetResultTable optimization={optimization} optimizationResult={resultData} colors={colors} />
      </div>
    )
  }
  return (
    <div id="BudgetOptResult">
      <div className="budget-result-running">
        <ProcessingIcon />
        {/* <div>An error occurred during optimization.<br/>Please contact the operations staff.</div> */}
        <div>
          <InnerHtml innerHTML={t('optimization.label.budgetOpt.result.message.error')} />
        </div>
      </div>
    </div>
  )
}

export default BudgetOptResult