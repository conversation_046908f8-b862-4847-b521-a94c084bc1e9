// src/components/common/buttons/MopActionButton.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import MopActionButton from './MopActionButton';
import { action } from '@storybook/addon-actions';

const meta: StorybookMeta<typeof MopActionButton> = {
  title: 'Components/Common/Buttons/MopActionButton',
  component: MopActionButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    label: {
      control: 'text',
      description: 'Button text label displayed inside the button',
    },
    onClick: {
      action: 'onClick',
      description: 'Callback function triggered when the button is clicked',
    },
    rounded: {
      control: 'select',
      options: ['full', 'half', 'sm', 'md'],
      description: 'Border radius style - full (pill shape), half (semi-rounded), sm (small radius), md (medium radius)',
    },
    theme: {
      control: 'select',
      options: ['primary', 'secondary', 'cancel', 'create'],
      description: 'Visual theme and color scheme of the button',
    },
    type: {
      control: 'select',
      options: ['button', 'submit', 'reset'],
      description: 'HTML button type attribute',
    },
    contained: {
      control: 'boolean',
      description: 'Controls whether the button has a filled background or outlined style',
    },
    qaId: {
      control: 'text',
      description: 'Quality assurance identifier for testing purposes',
    },
    gtmId: {
      control: 'text',
      description: 'Google Tag Manager identifier for analytics tracking',
    },
    children: {
      control: 'text',
      description: 'Additional content to display alongside the label',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    label: 'Default Button',
    onClick: action('button-clicked'),
  },
  parameters: {
    docs: {
      description: {
        story: 'The default MopActionButton with primary theme, half-rounded corners, and contained styling.',
      },
    },
  },
};

// Theme variations
export const ThemeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px',
      flexWrap: 'wrap'
    }}>
      <MopActionButton
        label="Primary"
        theme="primary"
        onClick={action('primary-clicked')}
      />
      <MopActionButton
        label="Secondary"
        theme="secondary"
        onClick={action('secondary-clicked')}
      />
      <MopActionButton
        label="Cancel"
        theme="cancel"
        onClick={action('cancel-clicked')}
      />
      <MopActionButton
        label="Create"
        theme="create"
        onClick={action('create-clicked')}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates all available theme variants of MopActionButton including primary, secondary, cancel, and create themes.',
      },
    },
  },
};

// Rounded variations
export const RoundedVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px',
      flexWrap: 'wrap'
    }}>
      <div style={{ textAlign: 'center' }}>
        <MopActionButton
          label="Full Rounded"
          rounded="full"
          onClick={action('full-clicked')}
        />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Full (pill)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopActionButton
          label="Half Rounded"
          rounded="half"
          onClick={action('half-clicked')}
        />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Half (default)</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopActionButton
          label="Medium Rounded"
          rounded="md"
          onClick={action('md-clicked')}
        />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Medium</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MopActionButton
          label="Small Rounded"
          rounded="sm"
          onClick={action('sm-clicked')}
        />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>Small</div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows different border radius options for MopActionButton components.',
      },
    },
  },
};

// Contained vs Outlined
export const ContainedVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px',
      padding: '16px'
    }}>
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Contained (Default)
        </h4>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton
            label="Primary"
            theme="primary"
            contained={true}
            onClick={action('primary-contained')}
          />
          <MopActionButton
            label="Secondary"
            theme="secondary"
            contained={true}
            onClick={action('secondary-contained')}
          />
          <MopActionButton
            label="Cancel"
            theme="cancel"
            contained={true}
            onClick={action('cancel-contained')}
          />
          <MopActionButton
            label="Create"
            theme="create"
            contained={true}
            onClick={action('create-contained')}
          />
        </div>
      </div>
      
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Outlined
        </h4>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton
            label="Primary"
            theme="primary"
            contained={false}
            onClick={action('primary-outlined')}
          />
          <MopActionButton
            label="Secondary"
            theme="secondary"
            contained={false}
            onClick={action('secondary-outlined')}
          />
          <MopActionButton
            label="Cancel"
            theme="cancel"
            contained={false}
            onClick={action('cancel-outlined')}
          />
          <MopActionButton
            label="Create"
            theme="create"
            contained={false}
            onClick={action('create-outlined')}
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comparison of contained (filled background) and outlined (border only) button styles across all themes.',
      },
    },
  },
};

// Button types
export const ButtonTypes: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px',
      flexWrap: 'wrap'
    }}>
      <MopActionButton
        label="Button Type"
        type="button"
        onClick={action('button-type')}
      />
      <MopActionButton
        label="Submit Type"
        type="submit"
        onClick={action('submit-type')}
      />
      <MopActionButton
        label="Reset Type"
        type="reset"
        onClick={action('reset-type')}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates different HTML button types - button (default), submit, and reset.',
      },
    },
  },
};

// With children content
export const WithChildren: Story = {
  args: {
    label: 'Button with',
    onClick: action('children-clicked'),
    children: '🚀 Icon',
  },
  parameters: {
    docs: {
      description: {
        story: 'MopActionButton with additional children content displayed alongside the label.',
      },
    },
  },
};

// With QA and GTM attributes
export const WithAttributes: Story = {
  args: {
    label: 'Tracked Button',
    onClick: action('tracked-clicked'),
    qaId: 'test-button',
    gtmId: 'header-cta-button',
  },
  parameters: {
    docs: {
      description: {
        story: 'MopActionButton with quality assurance (qaId) and Google Tag Manager (gtmId) attributes for testing and analytics.',
      },
    },
  },
};

// Interactive examples
export const InteractiveExamples: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '24px',
      padding: '16px'
    }}>
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Form Actions
        </h4>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton
            label="Save"
            type="submit"
            theme="primary"
            onClick={action('save-form')}
          />
          <MopActionButton
            label="Cancel"
            type="button"
            theme="cancel"
            onClick={action('cancel-form')}
          />
          <MopActionButton
            label="Reset"
            type="reset"
            theme="secondary"
            contained={false}
            onClick={action('reset-form')}
          />
        </div>
      </div>
      
      <div>
        <h4 style={{ marginBottom: '12px', fontSize: '14px', fontWeight: 'bold' }}>
          Action Buttons
        </h4>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton
            label="Create New"
            theme="create"
            rounded="full"
            onClick={action('create-new')}
            children="+"
          />
          <MopActionButton
            label="Edit"
            theme="secondary"
            onClick={action('edit-item')}
            children="✏️"
          />
          <MopActionButton
            label="Delete"
            theme="cancel"
            contained={false}
            onClick={action('delete-item')}
            children="🗑️"
          />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Examples showing how MopActionButton can be used in real-world scenarios like forms and action panels.',
      },
    },
  },
};

// All variations showcase
export const AllVariationsShowcase: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '32px',
      padding: '16px'
    }}>
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
          Theme Variations (Contained)
        </h3>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton label="Primary" theme="primary" onClick={action('showcase-primary')} />
          <MopActionButton label="Secondary" theme="secondary" onClick={action('showcase-secondary')} />
          <MopActionButton label="Cancel" theme="cancel" onClick={action('showcase-cancel')} />
          <MopActionButton label="Create" theme="create" onClick={action('showcase-create')} />
        </div>
      </div>
      
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
          Rounded Variations
        </h3>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton label="Full" rounded="full" onClick={action('showcase-full')} />
          <MopActionButton label="Half" rounded="half" onClick={action('showcase-half')} />
          <MopActionButton label="Small" rounded="sm" onClick={action('showcase-sm')} />
          <MopActionButton label="Medium" rounded="md" onClick={action('showcase-md')} />
        </div>
      </div>
      
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
          Outlined Variants
        </h3>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton label="Primary" theme="primary" contained={false} onClick={action('outline-primary')} />
          <MopActionButton label="Secondary" theme="secondary" contained={false} onClick={action('outline-secondary')} />
          <MopActionButton label="Cancel" theme="cancel" contained={false} onClick={action('outline-cancel')} />
          <MopActionButton label="Create" theme="create" contained={false} onClick={action('outline-create')} />
        </div>
      </div>
      
      <div>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
          With Children Content
        </h3>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <MopActionButton label="Add" children="+" theme="create" onClick={action('add-with-icon')} />
          <MopActionButton label="Save" children="💾" theme="primary" onClick={action('save-with-icon')} />
          <MopActionButton label="Delete" children="🗑️" theme="cancel" contained={false} onClick={action('delete-with-icon')} />
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive showcase of all MopActionButton variations organized by category for easy comparison.',
      },
    },
  },
}; 