import DoughnutsChart from '@components/common/chart/DoughnutsChart';
import useDoughnutsChart from './hook/useDoughnutsChart';
import useCustomTooltip from './hook/useBudgetCustomTooltip';
import outlinedLabel from './hook/outlineLabelPlugin';

const convertData = (data: any) => {
  const result = []
  for(const [key, value] of Object.entries(data)) {
    result.push({
      key, ...value as any
    })
  }
  return result
}
interface Props {
  spending: any
}

const SpendingAnalyzeChart = ({ spending }: Props) =>{
  const reports = convertData(spending)
  const { chartData } = useDoughnutsChart({
    reports,
    targetDataKey: 'budgetRatio'
  })
  const { tooltipInfo, customTooltip } = useCustomTooltip({
    reportData: reports
  });

  const options: any = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: 45,
    legend: {
      display: false,
      position: 'right',
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
        external: customTooltip,
      },
      outlinedLabel: {
        id: 'budget-analyze-chart'
      }
    },
    elements: {
      arc: {
        borderWidth: 0,
      },
    },
  };
  return (
    <>
      <DoughnutsChart
        key="SpendingAnalyzeChart"
        tooltipId="arrow-boxed-tooltip"
        tooltipInfo={tooltipInfo}
        tooltipOption={{
          valueUnit: 'NONE'
        }}
        chartData={chartData}
        options={options}
        // plugins={[outlinedLabel(reports, 'spending')]}
      />
    </>

  )
}

export default SpendingAnalyzeChart