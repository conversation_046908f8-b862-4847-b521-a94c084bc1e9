import { useTranslation } from 'react-i18next'
import { CampaignInfo, DEVICE_TYPE } from '@models/createCampaign/CreateCampaign'
import { FixedLayoutColumn } from '@components/common/table'
import CreativeReviewStatus from './CreativeReviewStatus'
import './CreateCampaignListFormatter.scss'
import StatusStepper from './StatusStepper'
import { Tooltip, withStyles } from '@material-ui/core'
import { InnerHtml } from '@components/common'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
const convertDeviceType = (type: DEVICE_TYPE) => {
  switch (type) {
    case DEVICE_TYPE.ALL:
      return 'PC/Mobile'
    case DEVICE_TYPE.PC:
      return 'PC'
    case DEVICE_TYPE.MOBILE:
      return 'Mobile'
    default:
      return ''
  }
}
const AdviceTooltip = withStyles((theme) => ({
  tooltip: {
    backgroundColor: '#ffffff',
    color: '#2b2b2b',
    minWidth: '360px',
    boxShadow: theme.shadows[2],
    fontSize: 13
  },
  arrow: {
    '&:before': {
      border: '1px solid #E6E8ED'
    },
    color: '#ffffff'
  }
}))(Tooltip)

export default class CreateCampaignListFormatter {
  getColumnTitle = (columnType: string) => {
    const { t } = useTranslation()
    return (
      <span className="no-sorting-advice">
        <AdviceTooltip
          id="create-campaign-advice-tooltip"
          title={<InnerHtml innerHTML={t(`createCampaign.tooltip.${columnType}`) || ''} />}
          placement="right-start"
          arrow
        >
          <span id="adviceIcon">
            <AdviceMarkIcon />
          </span>
        </AdviceTooltip>
        {t(`createCampaign.label.table.${columnType}`)}
      </span>
    )
  }
  getColumnFormat = (): Array<FixedLayoutColumn<CampaignInfo>> => {
    const { t } = useTranslation()

    const getColumnCampaignName = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: t('createCampaign.label.table.campaignName'),
        field: 'campaignName',
        align: 'center',
        sorting: true,
        cellStyle: { width: '18%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box campaign-name">
              <span>{campaign.campaignName}</span>
            </div>
          )
        }
      }
    }

    const getColumnMediumAccount = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: t('createCampaign.label.table.mediumAccount'),
        field: 'mediumAccount',
        align: 'center',
        sorting: true,
        cellStyle: { width: '13%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box">
              <span>{campaign.customerName} ({campaign.customerId})</span>
            </div>
          )
        }
      }
    }

    const getColumnShoppingMall = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: t('createCampaign.label.table.shoppingMall'),
        field: 'shoppingMall',
        align: 'center',
        sorting: true,
        cellStyle: { width: '18%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box">
              <span>{campaign.businessChannelName}</span>
            </div>
          )
        }
      }
    }

    const getColumnDailyBudget = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: t('createCampaign.label.table.dailyBudget'),
        field: 'dailyBudget',
        align: 'center',
        sorting: true,
        cellStyle: { width: '11%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box budget-cell">
              <span>
                {campaign.dailyBudget === 0 
                  ? t('createCampaign.label.table.unlimited')
                  : `${campaign.dailyBudget.toLocaleString('ko-KR')} ${t(
                      'createCampaign.createModal.productFilter.currency'
                    )}`
                }
              </span>
            </div>
          )
        }
      }
    }

    const getColumnDeviceType = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: t('createCampaign.label.table.deviceType'),
        field: 'deviceType',
        align: 'center',
        sorting: true,
        cellStyle: { width: '9%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box device-type-cell">
              <div className="device-type">
                <span>{convertDeviceType(campaign.device)}</span>
              </div>
            </div>
          )
        }
      }
    }

    const getColumnStatus = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: this.getColumnTitle('status'),
        field: 'status',
        align: 'center',
        sorting: true,
        cellStyle: { width: '11%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box status-cell">
              <StatusStepper campaignInfo={campaign} />
            </div>
          )
        }
      }
    }

    const getColumnCreativeReview = (): FixedLayoutColumn<CampaignInfo> => {
      return {
        title: this.getColumnTitle('creativeReview'),
        field: 'creativeReview',
        align: 'center',
        sorting: true,
        cellStyle: { width: '11%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box creative-review-cell">
              <CreativeReviewStatus
                status={campaign.adsReviewStatus}
                eligibleCount={campaign.adsEligibleCount}
                approvedCount={campaign.adsApprovedCount}
                underReviewCount={campaign.adsUnderReviewCount}
                pendingCount={campaign.adsPendingCount}
                deniedCount={campaign.adsDeniedCount}
              />
            </div>
          )
        }
      }
    }

    return [
      getColumnCampaignName(),
      getColumnMediumAccount(),
      getColumnShoppingMall(),
      getColumnDailyBudget(),
      getColumnDeviceType(),
      getColumnStatus(),
      getColumnCreativeReview()
    ]
  }
}
