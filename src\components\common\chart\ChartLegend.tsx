import { ChartLegendData, DirectionType } from '@models/common/ChartData';
import React from 'react';
import ChartLegendRow from './ChartLegendRow';
import './ChartLegend.scss';
import ChartLegendMore from './ChartLegendMore';
import { UnitType } from '@models/common/ChartData';

interface Props {
  legendData: ChartLegendData[];
  useTooltip?: boolean;
  legendOption?: {
    maxCount?: number;
    unitType?: UnitType;
    direction?: DirectionType;
  };
}

const ChartLegend: React.FC<Props> = ({
  legendData,
  useTooltip,
  legendOption = { maxCount: 10, unitType: 'PERCENT', direction: 'COL' },
}) => {
  const { maxCount = 10, unitType = 'PERCENT', direction = 'COL' } = legendOption;

  return (
    <div id="ChartLegend" className={direction === 'ROW' ? 'flex-row' : ''}>
      {legendData.slice(0, maxCount).map((legend, idx) => (
        <ChartLegendRow
          key={idx}
          className={legend.className}
          backgroundColor={legend.backgroundColor}
          label={legend.label}
          value={legend.value}
          useTooltip={useTooltip}
          unitType={unitType}
        />
      ))}
      {legendData.length > maxCount && (
        <ChartLegendMore legends={legendData.slice(maxCount)} useTooltip={useTooltip} unitType={unitType} />
      )}
    </div>
  );
};

export default ChartLegend;
