#adEfficiency-setting-modal {
  .MuiPaper-root.MuiDialog-paper {
    width: 450px;
    border-radius: 0;
  }
  p {
    margin: 0;
    padding: 0;
    color: var(--point_color);
  }
  .modal-title-container {
    text-align: center;
    position: relative;

    .title {
      font-size: 20px;
      font-weight: 900;
      color: var(--point_color);
    }
    .sub-title {
      color: var(--point_color);
      font-size: 14px;
      padding-left: 8px;
    }

    .modal-close-button {
      --size: 24px;
      --half-size: calc(var(--size) / 2);
      position: absolute;
      right: var(--half-size);
      top: calc(50% - var(--half-size));
      width: var(--size);
      height: var(--size);
      border: none;
      background: var(--gray-light);
      border-radius: 100%;
      padding: 0;
      svg {
        width: 16px;
      }
    }
  }

  .MuiDialogContent-root {
    background-color: #f2f3f6;
    text-align: center;
  }
  .modal-content-form {
    display: grid;
    grid-template-columns: 120px 1fr;
    align-items: center;
    row-gap: 16px;
    margin: 16px 0;

    span {
      font-weight: 700;
      color: var(--point_color);
    }

    .modal-content-input {
      width: 100%;
      height: 35px;
      border-radius: 35px;
      font-size: 14px;
      background-color: #fff;
      .MuiOutlinedInput-input {
        text-align: center;
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
        }
      }
    }
  }

  .modal-save-button {
    color: white;
    width: 120px;
    height: 32px;
    border-radius: 20px;
    background-color: var(--point_color);
    margin: 16px auto;
    &:disabled {
      opacity: 0.6;
    }
  }
}
