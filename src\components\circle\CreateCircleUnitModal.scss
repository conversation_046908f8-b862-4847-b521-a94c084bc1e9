.create-circle-unit-modal {
  .MuiPaper-root.MuiDialog-paper {
    border-radius: 0;
    max-width: unset;
    width: 1200px;
    min-height: 500px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  &__header {
    background-color: var(--bg-gray-light);
    position: relative;
    text-align: center;
    font-size: 28px;
    padding: 28px 0;

    .mop-icon-box {
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }

  &__content,
  &__footer {
    padding: 0 32px;
  }

  &__content {
    flex: 1;
  }

  &__footer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;

    .mop-action-button {
      margin: 0;
    }
  }
}
