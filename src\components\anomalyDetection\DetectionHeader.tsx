import React, { ReactElement } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@material-ui/core';

import { AnalyticsType } from '@models/common'
import { MediaIcon } from '@components/common'
import { ReactComponent as DownloadIcon } from '@components/assets/images/icon_download.svg';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';

import { ReactComponent as IconNormal } from '@components/assets/images/icon_utm_normal.svg';
import { ReactComponent as IconNotExist } from '@components/assets/images/icon_utm_not_exist.svg';
import { ReactComponent as IconNotMatch } from '@components/assets/images/icon_utm_not_match.svg';

import AdviceTooltip from '@components/common/AdviceTooltip';
import InnerHtml from '@components/common/InnerHtml';

import './DetectionHeader.scss'


interface Props {
  showDownload?: boolean;
  headTitle: string;
  headSize?: string;
  subTitle?: string;
  subSize?: string;
  tooltipId?: string;
  tooltipHtml?: string;
  gaViewId?: string;
  analytics?: AnalyticsType
  downloadFn?: React.MouseEventHandler<HTMLButtonElement>;
}
const UrlDetectionHeader: React.FC<Props> = (props: Props): ReactElement => {
  const { t } = useTranslation();
  const headStyle = {
    '--head-size': `${props.headSize ?? '20px'}`
  } as React.CSSProperties

  const subheadStyle = {
    '--subhead-size': `${props.subSize ?? '16px'}`
  } as React.CSSProperties

  return (
    <div className='report-title'>
      {/* title */}
      <div>
        {
          (props.tooltipId && props.tooltipHtml) && (
            <AdviceTooltip
              id={`anomaly-detection-advice-tooltip-${props.tooltipId}`}
              title={<InnerHtml innerHTML={props.tooltipHtml} />}
              placement="bottom-start"
              arrow
            >
              <span className={`icon-tooltip`} id="adviceIcon">
                <AdviceMarkIcon />
              </span>
            </AdviceTooltip>
          )
        }

        <span className='headTitle' style={headStyle}>
          {props.headTitle} {props.analytics && <MediaIcon mediaType={props.analytics} />}
        </span>
        {/* FIXME */}
        {(props.gaViewId && props.analytics) && (
          <p className='ga-view-id'>{t(`anomalyDetection.utmRules.subIdLabel.${props.analytics}`)} <strong>{props.gaViewId}</strong></p>
        )}
        {props.subTitle && <span className='subTitle' style={subheadStyle}>{props.subTitle}</span>}
      </div>
      {/* right side tools */}
      <div className='side right'>
        {props.tooltipId === 'utm' && (
          <div className='utm-status-icon-grid'>
            <IconNormal /><span>{t('anomalyDetection.label.status.normal')}</span>
            <IconNotMatch /><span>{t('anomalyDetection.label.status.notMatch')}</span>
            <IconNotExist /><span>{t('anomalyDetection.label.status.notExitst')}</span>
          </div>
        )}
        {props.showDownload && (
          <Button className="download-button" disableRipple onClick={props.downloadFn}>
            <DownloadIcon />
          </Button>
        )}
      </div>
    </div>
  )
}

export default UrlDetectionHeader;