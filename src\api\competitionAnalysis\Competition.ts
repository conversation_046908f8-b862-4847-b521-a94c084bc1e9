/* istanbul ignore file */

import { callApi, downloadByteArray, Method, openDownloadLink } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import {
  GetCompetitionsParams,
  GetCompetitionsResponse,
  DownloadCompetitionsParams,
  GetCompetitionsTableParams,
  GetCompetitionsTableResponse
} from '@models/competitionAnalysis/Competition'
import CommonResponse from '@models/common/CommonResponse'

import { AxiosResponse } from 'axios'

export const getCompetitions = async (advertiserId: number, queryParams: GetCompetitionsParams) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/competitions/${advertiserId}`,
    method: Method.GET,
    params: { queryParams: { ...queryParams } }
  })

  return (response.successOrNot === 'Y' ? response.data : {}) as GetCompetitionsResponse
}

export const getCompetitionTable = async (
  advertiserId: number,
  queryParams: GetCompetitionsTableParams,
  isLoading = true
) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/competitions/${advertiserId}/table`,
    method: Method.GET,
    config: {
      isLoading: isLoading
    },
    params: {
      queryParams: {
        ...queryParams
      }
    }
  })

  return (response.successOrNot === 'Y' ? response.data : null) as GetCompetitionsTableResponse
}

export const downloadCompetitions = async (
  advertiserId: number,
  queryParams: DownloadCompetitionsParams,
  isLoading = true
) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/competitions/${advertiserId}/raw-data`,
    method: Method.GET,
    config: {
      isLoading: isLoading
    },
    params: {
      queryParams: {
        ...queryParams
      }
    }
  })

  if (response.data) {
    openDownloadLink(response)

    return null
  } else {
    return response as unknown as CommonResponse
  }
}
