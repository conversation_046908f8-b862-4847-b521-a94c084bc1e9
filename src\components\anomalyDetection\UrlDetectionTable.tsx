import React, { ReactElement, useEffect, useState } from 'react';
import { useRecoilState, useResetRecoilState, useRecoilValue } from 'recoil';
import { filteredUrlDetectionState, urlDetectionFilterQuery, urlDetectionPage, urlDetectionRowsPerPage } from '@store/AnomalyDetection'
import { useTranslation } from 'react-i18next';
import { FixedLayoutTable, TablePagination } from '@components/common/table'
import UrlDetectionTableFormatter from './UrlDetectionTableFormatter'

import { pageSizeOptions } from '@models/common/CommonConstants';

import { UrlAnomalyDetectionColumn,  UrlAnomalyDetectionTable } from '@models/anomalyDetection';
import EmptyDetection from '@components/anomalyDetection/EmptyDetection'
import { ReactComponent as EmptyNotice } from '@components/assets/images/icon_notice_outline.svg';
import './UrlDetectionTable.scss';

const urlTableColumns = new UrlDetectionTableFormatter();

const UrlDetectionTable: React.FC = (): ReactElement => {
  const { t } = useTranslation()
  const allColumns: UrlAnomalyDetectionColumn[] = urlTableColumns.getColumnFormat();
  const filteredItmes = useRecoilValue(filteredUrlDetectionState);
  const [page, setPage] = useRecoilState(urlDetectionPage);
  const resetPage = useResetRecoilState(urlDetectionPage);
  const [rowsPerPage, setRowsPerPage] = useRecoilState(urlDetectionRowsPerPage);
  const resetRowsPerPage = useResetRecoilState(urlDetectionRowsPerPage)
  const [pagedItems, setPagedItems] = useState<UrlAnomalyDetectionTable[]>([])
  const query = useRecoilValue(urlDetectionFilterQuery);

  const handlePageList = (page: number, rowsPerPage: number) => {
    const startIndex = (page - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    setPagedItems(filteredItmes.slice(startIndex, endIndex))
  }

  useEffect(() => {
    resetPage()
    resetRowsPerPage()
  }, [query]) //eslint-disable-line

  useEffect(() => {
    handlePageList(page, rowsPerPage)
  }, [filteredItmes, page, rowsPerPage]) //eslint-disable-line
  return (
    <>
      <FixedLayoutTable
        id="UrlAnomalyDetectionTable"
        columns={allColumns}
        data={pagedItems.map((obj) => Object.create(obj)) || []}
        localization={{
          body: {
            emptyDataSourceMessage: (
              <div style={{height: '600px'}}>
                <EmptyDetection icon={<EmptyNotice />} content={t('anomalyDetection.empty.url')}/>
              </div>
            )
          }
        }}
      />
      {filteredItmes.length > 0 && (
        <TablePagination
          id="url-anomaly-detection-pagination"
          totalCount={filteredItmes.length}
          page={page}
          rowsPerPage={rowsPerPage || pageSizeOptions[0]}
          onPageChange={(page) => setPage(page)}
          onRowsPerPageChange={(rowsPage) => setRowsPerPage(rowsPage)}
        />
      )}
    </>
  )
}

export default UrlDetectionTable;