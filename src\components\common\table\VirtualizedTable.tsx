import React, { useMemo, useCallback, useRef, useState } from 'react'
import {
  Checkbox,
  CircularProgress
} from '@material-ui/core'
import {
  List,
  Grid,
  ScrollSync,
  AutoSizer
} from 'react-virtualized'
import scrollbarSize from 'dom-helpers/scrollbarSize'
import { VirtualizedTableProps } from '@models/common/VirtualizedTable'
import './VirtualizedTable.scss'
import { ReactComponent as CheckboxBorderIcon } from '@components/assets/images/check-box-outline.svg'
import { ReactComponent as CheckboxIcon } from '@components/assets/images/checked-box.svg'
import { ArrowDropDownOutlined } from '@material-ui/icons';
import TruncatedText from '../TruncatedText'

const VirtualizedTable = <T,>({
  data,
  columns,
  getItemId,
  isItemDisabled,
  tableOptions = {},
  selectionOptions = {
    onSelectAll: () => {},
    onDeselectAll: () => {}
  },
  messageOptions = {},
  isLoading = false,
  sortFields,
  onOrderChange,
  currentSort
}: VirtualizedTableProps<T>) => {
  const {
    height = 700,
    width = 800, // Add default width
    rowHeight = 80,
    headerHeight = 56,
    className = '',
    overscanRowCount = 10,
    stickyHeader = true,
    enableHorizontalScroll = false,
    minColumnWidth = 120
  } = tableOptions

  const containerRef = useRef<HTMLDivElement>(null)
  const [scrollbarWidth, setScrollbarWidth] = useState(scrollbarSize)

  // Ensure height is a number (width will be handled by AutoSizer)
  const numericHeight = typeof height === 'string' ? parseInt(height, 10) : height



  // Custom scroll handler to detect scrollbar changes
  const handleScroll = useCallback((scrollParams: any) => {
    // Check if vertical scrollbar appeared/disappeared
    if (containerRef.current) {
      const container = containerRef.current
      const hasVerticalScrollbar = container.scrollHeight > container.clientHeight
      const currentScrollbarWidth = hasVerticalScrollbar ? scrollbarSize() : 0
      
      if (currentScrollbarWidth !== scrollbarWidth) {
        setScrollbarWidth(currentScrollbarWidth)
      }
    }
  }, [scrollbarWidth])

  const { showCheckbox = false, selectedItems = [], onItemSelection } = selectionOptions

  const { emptyMessage = 'No data available', loadingMessage = 'Loading...' } = messageOptions

  // Get enabled items for selection
  const enabledItems = useMemo(() => {
    return isItemDisabled ? data.filter((item) => !isItemDisabled(item)) : data
  }, [data, isItemDisabled])

  const enabledItemIds = useMemo(() => {
    return enabledItems.map(getItemId)
  }, [enabledItems, getItemId])

  const selectedEnabledItems = useMemo(() => {
    return selectedItems?.filter((id) => enabledItemIds.includes(id))
  }, [selectedItems, enabledItemIds])

  const isAllEnabledSelected = enabledItemIds.length > 0 && selectedEnabledItems.length === enabledItemIds.length
  const isIndeterminate = selectedEnabledItems.length > 0 && selectedEnabledItems.length < enabledItemIds.length

  // Selection handlers
  const handleSelectAll = useCallback(() => {
    if (isAllEnabledSelected) {
      selectionOptions.onDeselectAll?.()
    } else {
      selectionOptions.onSelectAll?.()
    }
  }, [isAllEnabledSelected, selectionOptions])

  const handleItemSelection = useCallback((itemId: string) => {
    onItemSelection?.(itemId)
  }, [onItemSelection])

  const calculateColumnWidths = useCallback((availableWidth: number) => {
      const checkboxWidth = showCheckbox ? 40 : 0
      const totalAvailableWidth = availableWidth - checkboxWidth

      const fixedColumns = columns.filter(column => column.width)
      const flexibleColumns = columns.filter(column => !column.width)

      const totalFixedWidth = fixedColumns.reduce((sum, column) => {
        const width = typeof column.width === 'string'
          ? parseInt(column.width.replace('px', ''), 10)
          : column.width || 0
        return sum + width
      }, 0)

      // Calculate minimum total width needed
      const minTotalWidth = totalFixedWidth + (flexibleColumns.length * minColumnWidth)
      
      // If horizontal scroll is enabled, check if we need to scroll
      if (enableHorizontalScroll) {
        // If available width is enough, try to fit all columns
        if (totalAvailableWidth >= minTotalWidth) {
          const remainingWidth = Math.max(0, totalAvailableWidth - totalFixedWidth)
          const flexibleWidth = flexibleColumns.length > 0
            ? Math.floor(remainingWidth / flexibleColumns.length)
            : 0

          return columns.map((column) => {
            if (column.width) {
              return typeof column.width === 'string'
                ? parseInt(column.width.replace('px', ''), 10)
                : column.width
            }
            return Math.max(flexibleWidth, minColumnWidth)
          })
        } else {
          // Use fixed widths when scrolling is needed
          return columns.map((column) => {
            if (column.width) {
              return typeof column.width === 'string'
                ? parseInt(column.width.replace('px', ''), 10)
                : column.width
            }
            return Math.max(minColumnWidth, 150)
          })
        }
      }

      // Original logic for responsive columns (no horizontal scroll)
      const remainingWidth = Math.max(0, totalAvailableWidth - totalFixedWidth)
      const flexibleWidth = flexibleColumns.length > 0
        ? Math.floor(remainingWidth / flexibleColumns.length)
        : 0

      return columns.map((column) => {
        if (column.width) {
          return typeof column.width === 'string'
            ? parseInt(column.width.replace('px', ''), 10)
            : column.width
        }
        return Math.max(flexibleWidth, minColumnWidth)
      })
    }, [columns, showCheckbox, enableHorizontalScroll, minColumnWidth])

  const renderHeader = useCallback(({ style, width }: { style: React.CSSProperties, width: number }) => {
    const columnWidths = calculateColumnWidths(width)

    return (
      <div style={style} className="virtualized-header-row">
        {showCheckbox && (
          <div className="virtualized-header-cell checkbox-header bg-campaign-background w-20">
            <div className="flex justify-center items-center h-full ">
              <Checkbox
                checked={isAllEnabledSelected}
                indeterminate={isIndeterminate}
                onChange={handleSelectAll}
                disabled={enabledItemIds.length === 0}
                className={enabledItemIds.length === 0 ? 'invisible' : 'visible'}
                icon={<CheckboxBorderIcon />}
                checkedIcon={<CheckboxIcon />}
              />
            </div>
          </div>
        )}
        {columns.map((column, index) => (
          <div
            key={column.key}
            className={`virtualized-header-cell bg-campaign-background flex items-center font-bold ${
              column.align === 'left' ? 'justify-start pl-4 pr-4' : 
              column.align === 'right' ? `justify-end pl-4 ${sortFields?.includes(column.key) ? 'pr-0' : 'pr-4'}` : 'justify-center px-4'
              }`}
            style={{ width: columnWidths[index] }}
          >
            {sortFields?.includes(column.key) ? 
            <div 
              className={`flex items-center gap-1 cursor-pointer ${
                column.align === 'left' ? 'justify-start' : 
                column.align === 'right' ? 'justify-end' : 'justify-center'
              }`} 
              onClick={() => { onOrderChange && onOrderChange(column.key as keyof T)}}
            >
              {column.label} 
              {currentSort?.field === column.key && <ArrowDropDownOutlined className={`!w-[30px] !h-[30px] ${currentSort?.order === 'asc' ? 'rotate-180': ''}`}/>}  
            </div> 
            : column.label}
          </div>
        ))}
      </div>
    )
  }, [columns, showCheckbox, isAllEnabledSelected, isIndeterminate, enabledItemIds.length, calculateColumnWidths, handleSelectAll, sortFields, currentSort?.field, currentSort?.order, onOrderChange])

  const renderRow = useCallback(
    ({ index, style, width, key }: { index: number; style: React.CSSProperties; width: number; key: string }) => {
      const item = data[index]
      if (!item) return null

      // Use the same width calculation as header for consistency
      const columnWidths = calculateColumnWidths(width)

      return (
        <div style={style} key={key} className="virtualized-data-row">
          {showCheckbox && (
            <div className="virtualized-data-cell checkbox-cell w-20">
              <div className="flex justify-center items-center h-full">
                <Checkbox
                  checked={selectedItems.includes(getItemId(item))}
                  onChange={() => handleItemSelection(getItemId(item))}
                  disabled={isItemDisabled ? isItemDisabled(item) : false}
                  data-testid='checkbox-cell-item'
                  icon={<CheckboxBorderIcon />}
                  checkedIcon={<CheckboxIcon />}
                />
              </div>
            </div>
          )}
          {columns.map((column, columnIndex) => {
            const isDisabled = isItemDisabled ? isItemDisabled(item) : false
            return (
              <div
                key={column.key}
              className={`virtualized-data-cell flex items-center px-4 ${
                isDisabled ? 'text-gray-400 opacity-60' : ''
                  }`}
                style={{ width: columnWidths[columnIndex] }}
              >
              <div className={`cell-content ${
                column.align === 'left' ? 'text-left' : 
                column.align === 'right' ? 'text-right' : 'text-center'
              }`}>
                  <TruncatedText className="cell-text">
                  {column.render 
                    ? column.render(item, index)
                    : (item as T)[column.key as keyof T]
                  }
                  </TruncatedText>
                </div>
              </div>
            )
          })}
        </div>
      )
  }, [data, columns, getItemId, selectedItems, isItemDisabled, showCheckbox, handleItemSelection, calculateColumnWidths])

  if (isLoading) {
    return (
      <div className={`virtualized-table ${className}`} style={{ height: numericHeight, width: typeof width === 'string' && width === '100%' ? '100%' : width }}>
        <div className="loading-state flex items-center justify-center h-full">
          <div className="flex flex-col items-center gap-4">
            <CircularProgress size={40} />
            <span className="text-gray-600 text-sm">{loadingMessage}</span>
          </div>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className={`virtualized-table ${className}`} style={{ width: typeof width === 'string' && width === '100%' ? '100%' : width }}>
        <div className="empty-state">
          <span>{emptyMessage}</span>
        </div>
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className={`virtualized-table ${className}`} 
      style={{ height: '100%', width: '100%' }}
    >
      <AutoSizer>
          {({ width: autoWidth, height: autoHeight }) => {
            const columnWidths = calculateColumnWidths(autoWidth)
            const totalTableWidth = columnWidths.reduce((sum, colWidth) => sum + colWidth, 0) + (showCheckbox ? 40 : 0)
            const actualHeight = typeof height === 'string' && height === '100%' ? autoHeight : numericHeight

            if (enableHorizontalScroll) {
              // Check if we need horizontal scroll
              const needsHorizontalScroll = totalTableWidth > autoWidth
              const headerWidth = needsHorizontalScroll ? autoWidth - scrollbarWidth : autoWidth
              
              return (
                <ScrollSync>
                  {({ onScroll, scrollLeft, scrollTop }) => {
                    // Combine custom scroll handler with ScrollSync's onScroll
                    const combinedOnScroll = (scrollParams: any) => {
                      handleScroll(scrollParams)
                      onScroll(scrollParams)
                    }
                    
                    return (
                    <div style={{ width: autoWidth, height: actualHeight, position: 'relative' }}>
                      {stickyHeader && (
                        <div 
                          style={{ 
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            zIndex: 10,
                            width: headerWidth,
                            height: headerHeight
                          }}
                        >
                          <Grid
                            width={headerWidth}
                            height={headerHeight}
                            rowCount={1}
                            columnCount={1}
                            rowHeight={headerHeight}
                            columnWidth={totalTableWidth}
                            cellRenderer={({ key, style }) => (
                              <div key={key} style={style} className="virtualized-header-row">
                                {showCheckbox && (
                                  <div className="virtualized-header-cell checkbox-header bg-campaign-background w-20">
                                    <div className="flex justify-center items-center h-full">
                                      <Checkbox
                                        checked={isAllEnabledSelected}
                                        indeterminate={isIndeterminate}
                                        onChange={handleSelectAll}
                                        disabled={enabledItemIds.length === 0}
                                        className={enabledItemIds.length === 0 ? 'invisible' : 'visible'}
                                        icon={<CheckboxBorderIcon />}
                                        checkedIcon={<CheckboxIcon />}
                                      />
                                    </div>
                                  </div>
                                )}
                                {columns.map((column, index) => (
                                  <div
                                    key={column.key}
                                    className={`virtualized-header-cell bg-campaign-background flex items-center font-bold ${
                                      column.align === 'left' ? 'justify-start pl-4 pr-4' : 
                                      column.align === 'right' ? `justify-end pl-4 ${sortFields?.includes(column.key) ? currentSort?.field === column.key ? 'pr-0' : 'pr-4' : 'pr-4'}` : 'justify-center px-4'
                                    }`}
                                    style={{ width: columnWidths[index] }}
                                  >
                                    {sortFields?.includes(column.key) ? 
                                    <div 
                                      className={`flex items-center gap-1 cursor-pointer ${
                                        column.align === 'left' ? 'justify-start' : 
                                        column.align === 'right' ? 'justify-end' : 'justify-center'
                                      }`} 
                                      onClick={() => { onOrderChange && onOrderChange(column.key as keyof T)}}
                                    >
                                      {column.label} 
                                      {currentSort?.field === column.key && <ArrowDropDownOutlined className={`!w-[30px] !h-[30px] ${currentSort?.order === 'asc' ? 'rotate-180': ''}`}/>}  
                                    </div> 
                                    : column.label}
                                  </div>
                                ))}
                              </div>
                            )}
                            scrollLeft={scrollLeft}
                            className="virtualized-header-grid"
                          />
                        </div>
                      )}

                      <div 
                        style={{
                          position: 'absolute',
                          top: stickyHeader ? headerHeight : 0,
                          left: 0,
                          width: autoWidth,
                          height: actualHeight - (stickyHeader ? headerHeight : 0)
                        }}
                      >
                        <Grid
                          width={autoWidth}
                          height={actualHeight - (stickyHeader ? headerHeight : 0)}
                          rowCount={data.length}
                          columnCount={1}
                          rowHeight={rowHeight}
                          columnWidth={totalTableWidth}
                          cellRenderer={({ rowIndex, key, style }) => {
                            const item = data[rowIndex]
                            if (!item) return null
                            // Use the same columnWidths as header for perfect alignment
                            return (
                              <div style={style} key={key} className="virtualized-data-row">
                                {showCheckbox && (
                                  <div className="virtualized-data-cell checkbox-cell w-20">
                                    <div className="flex justify-center items-center h-full">
                                      <Checkbox
                                        checked={selectedItems.includes(getItemId(item))}
                                        onChange={() => handleItemSelection(getItemId(item))}
                                        disabled={isItemDisabled ? isItemDisabled(item) : false}
                                        data-testid='checkbox-cell-item'
                                        icon={<CheckboxBorderIcon />}
                                        checkedIcon={<CheckboxIcon />}
                                      />
                                    </div>
                                  </div>
                                )}
                                {columns.map((column, columnIndex) => {
                                  const isDisabled = isItemDisabled ? isItemDisabled(item) : false
                                  return (
                                    <div
                                      key={column.key}
                                    className={`virtualized-data-cell flex items-center px-4 ${
                                      isDisabled ? 'text-gray-400 opacity-60' : ''
                                        }`}
                                      style={{ width: columnWidths[columnIndex] }}
                                    >
                                    <div className={`cell-content ${
                                      column.align === 'left' ? 'text-left' : 
                                      column.align === 'right' ? 'text-right' : 'text-center'
                                    }`}>
                                        <TruncatedText className="cell-text">
                                        {column.render 
                                          ? column.render(item, rowIndex)
                                          : (item as T)[column.key as keyof T]
                                        }
                                        </TruncatedText>
                                      </div>
                                    </div>
                                  )
                                })}
                              </div>
                            )
                          }}
                          overscanRowCount={overscanRowCount}
                          className="virtualized-data-list"
                          onScroll={combinedOnScroll}
                          scrollTop={scrollTop}
                          scrollLeft={scrollLeft}
                        />
                      </div>
                    </div>
                    )
                  }}
                </ScrollSync>
              )
            }

            // Original rendering for non-horizontal scroll mode
            return (
              <div style={{ width: autoWidth, height: actualHeight }}>
                {stickyHeader && (
                  <div className="sticky top-0 z-10">
                    {renderHeader({ style: { height: headerHeight }, width: autoWidth })}
                  </div>
                )}

                <div 
                  style={{
                    height: actualHeight - (stickyHeader ? headerHeight : 0)
                  }}>
                  <List
                    width={autoWidth}
                    height={actualHeight - (stickyHeader ? headerHeight : 0)}
                    rowCount={data.length}
                    rowHeight={rowHeight}
                    rowRenderer={(props) => renderRow({ ...props, width: autoWidth })}
                    overscanRowCount={overscanRowCount}
                    className="virtualized-data-list"
                  />
                </div>
              </div>
            )
          }}
        </AutoSizer>
    </div>
  )
}

export default VirtualizedTable