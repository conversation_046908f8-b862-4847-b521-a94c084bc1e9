import {
  isProductEnabled,
  convertProductStatus,
  convertAdStatus,
  convertAdReviewStatus,
  generateNaverCommerceBodyParams
} from './index'
import {
  ProductStatus,
  AdvertisingStatus,
  AdInspectStatusEnum,
  ProductData
} from '@models/createCampaign/CreateCampaign'

describe('createCampaign utils', () => {
  const mockT = (key: string) => 'translated_' + key

  describe('isProductEnabled', () => {
    it('should return true when product status is SALE and ad status is ELIGIBLE', () => {
      const product: ProductData = {
        productId: '1',
        productName: 'Test Product',
        categoryName: 'Test Category',
        price: 1000,
        stock: 10,
        adRevenue: 100,
        totalRevenue: 1000,
        status: ProductStatus.SALE,
        adStatus: AdvertisingStatus.ELIGIBLE,
        adInspectStatus: AdInspectStatusEnum.APPROVED
      }

      expect(isProductEnabled(product)).toBe(true)
    })

    it('should return true when product status is SALE and ad status is INELIGIBLE but adInspectStatus is ELIGIBLE', () => {
      const product: ProductData = {
        productId: '1',
        productName: 'Test Product',
        categoryName: 'Test Category',
        price: 1000,
        stock: 10,
        adRevenue: 100,
        totalRevenue: 1000,
        status: ProductStatus.SALE,
        adStatus: AdvertisingStatus.INELIGIBLE,
        adInspectStatus: AdInspectStatusEnum.ELIGIBLE
      }

      expect(isProductEnabled(product)).toBe(true)
    })

    it('should return true when product status is SALE and ad status is INELIGIBLE but adInspectStatus is APPROVED', () => {
      const product: ProductData = {
        productId: '1',
        productName: 'Test Product',
        categoryName: 'Test Category',
        price: 1000,
        stock: 10,
        adRevenue: 100,
        totalRevenue: 1000,
        status: ProductStatus.SALE,
        adStatus: AdvertisingStatus.INELIGIBLE,
        adInspectStatus: AdInspectStatusEnum.APPROVED
      }

      expect(isProductEnabled(product)).toBe(true)
    })

    it('should return false when product status is not SALE', () => {
      const product: ProductData = {
        productId: '1',
        productName: 'Test Product',
        categoryName: 'Test Category',
        price: 1000,
        stock: 10,
        adRevenue: 100,
        totalRevenue: 1000,
        status: ProductStatus.WAIT,
        adStatus: AdvertisingStatus.ELIGIBLE,
        adInspectStatus: AdInspectStatusEnum.APPROVED
      }

      expect(isProductEnabled(product)).toBe(false)
    })

    it('should return false when product status is SALE but ad status is INELIGIBLE and adInspectStatus is not ELIGIBLE or APPROVED', () => {
      const product: ProductData = {
        productId: '1',
        productName: 'Test Product',
        categoryName: 'Test Category',
        price: 1000,
        stock: 10,
        adRevenue: 100,
        totalRevenue: 1000,
        status: ProductStatus.SALE,
        adStatus: AdvertisingStatus.INELIGIBLE,
        adInspectStatus: AdInspectStatusEnum.DENIED
      }

      expect(isProductEnabled(product)).toBe(false)
    })

    it('should return false when product status is SALE but ad status is INELIGIBLE and adInspectStatus is null', () => {
      const product: ProductData = {
        productId: '1',
        productName: 'Test Product',
        categoryName: 'Test Category',
        price: 1000,
        stock: 10,
        adRevenue: 100,
        totalRevenue: 1000,
        status: ProductStatus.SALE,
        adStatus: AdvertisingStatus.INELIGIBLE,
        adInspectStatus: null
      }

      expect(isProductEnabled(product)).toBe(false)
    })
  })

  describe('convertProductStatus', () => {
    it('should return translated text for ALL status', () => {
      const result = convertProductStatus(ProductStatus.ALL, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.all')
    })

    it('should return translated text for WAIT status', () => {
      const result = convertProductStatus(ProductStatus.WAIT, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.wait')
    })

    it('should return translated text for SALE status', () => {
      const result = convertProductStatus(ProductStatus.SALE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.sale')
    })

    it('should return translated text for OUTOFSTOCK status', () => {
      const result = convertProductStatus(ProductStatus.OUTOFSTOCK, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.outOfStock')
    })

    it('should return translated text for UNADMISSION status', () => {
      const result = convertProductStatus(ProductStatus.UNADMISSION, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.unAdmission')
    })

    it('should return translated text for REJECTION status', () => {
      const result = convertProductStatus(ProductStatus.REJECTION, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.rejection')
    })

    it('should return translated text for SUSPENSION status', () => {
      const result = convertProductStatus(ProductStatus.SUSPENSION, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.suspension')
    })

    it('should return translated text for CLOSE status', () => {
      const result = convertProductStatus(ProductStatus.CLOSE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.close')
    })

    it('should return translated text for PROHIBITION status', () => {
      const result = convertProductStatus(ProductStatus.PROHIBITION, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.prohibition')
    })

    it('should return translated text for DELETE status', () => {
      const result = convertProductStatus(ProductStatus.DELETE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.statusFilter.delete')
    })

    it('should return empty string for unknown status', () => {
      const result = convertProductStatus('UNKNOWN' as ProductStatus, mockT)
      expect(result).toBe('')
    })
  })

  describe('convertAdStatus', () => {
    it('should return translated text for ALL status', () => {
      const result = convertAdStatus(AdvertisingStatus.ALL, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adStatusFilter.all')
    })

    it('should return translated text for ELIGIBLE status', () => {
      const result = convertAdStatus(AdvertisingStatus.ELIGIBLE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adStatusFilter.on')
    })

    it('should return translated text for INELIGIBLE status', () => {
      const result = convertAdStatus(AdvertisingStatus.INELIGIBLE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adStatusFilter.off')
    })

    it('should return translated text for null status', () => {
      const result = convertAdStatus(null, mockT)
      expect(result).toBe('translated_createCampaign.label.productTable.noHistory')
    })

    it('should return translated text for unknown status', () => {
      const result = convertAdStatus('UNKNOWN' as AdvertisingStatus, mockT)
      expect(result).toBe('translated_createCampaign.label.productTable.noHistory')
    })
  })

  describe('convertAdReviewStatus', () => {
    it('should return translated text for UNDER_REVIEW status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.UNDER_REVIEW, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.underReview')
    })

    it('should return translated text for APPROVED status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.APPROVED, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.approved')
    })

    it('should return translated text for ELIGIBLE status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.ELIGIBLE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.eligible')
    })

    it('should return translated text for PENDING status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.PENDING, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.pending')
    })

    it('should return translated text for DENIED status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.DENIED, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.denied')
    })

    it('should return translated text for LIMITED_ELIGIBLE status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.LIMITED_ELIGIBLE, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.limitedEligible')
    })

    it('should return translated text for PAUSED status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.PAUSED, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.paused')
    })

    it('should return translated text for DELETED status', () => {
      const result = convertAdReviewStatus(AdInspectStatusEnum.DELETED, mockT)
      expect(result).toBe('translated_createCampaign.createModal.adReviewStatus.deleted')
    })

    it('should return translated text for null status', () => {
      const result = convertAdReviewStatus(null, mockT)
      expect(result).toBe('translated_createCampaign.label.productTable.noHistory')
    })

    it('should return translated text for unknown status', () => {
      const result = convertAdReviewStatus('UNKNOWN' as AdInspectStatusEnum, mockT)
      expect(result).toBe('translated_createCampaign.label.productTable.noHistory')
    })
  })
})

describe('generateNaverCommerceBodyParams()', () => {
  test('✅ productIds is not empty → set only channelProductIds', () => {
    const result = generateNaverCommerceBodyParams(['p1', 'p2'], ['o1'])
    expect(result).toEqual({ channelProductIds: ['p1', 'p2'] })
  })

  test('✅ productIds is empty, optimizationIds is not empty → set only optimizationIds', () => {
    const result = generateNaverCommerceBodyParams([], ['o1', 'o2'])
    expect(result).toEqual({ optimizationIds: ['o1', 'o2'] })
  })

  test('✅ both arrays are empty → set optimizationIds and channelProductIds as empty arrays', () => {
    const result = generateNaverCommerceBodyParams([], [])
    expect(result).toEqual({
      optimizationIds: [],
      channelProductIds: []
    })
  })

  test('🔍 productIds is not empty and optimizationIds is empty → set only channelProductIds', () => {
    const result = generateNaverCommerceBodyParams(['p1'], [])
    expect(result).toEqual({ channelProductIds: ['p1'] })
  })
})
