import { Service } from "@models/common"
import CommonResponse from "@models/common/CommonResponse"
import { BusinessChannel, CampaignListParams, CampaignListRes, CampaignParams, NaverAdsNaverCommerceAccount, ProductData } from "@models/createCampaign/CreateCampaign"
import { callApi, cleanQueryParams, Method } from "@utils/ApiUtil"
export const getCampaigns = async (advertiserId: number, params: CampaignListParams): Promise<CampaignListRes | undefined> => {
  const response: CommonResponse<CampaignListRes> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/campaign/sa/shopping/${advertiserId}`,
    method: Method.GET,
    params: {
      queryParams: cleanQueryParams({
        campaignName: params.campaignName,
        pageIndex: params.pageIndex,
        pageSize: params.pageSize,
        creationStatus: params.creationStatus,
        adReviewStatus: params.adReviewStatus
      })
    }
  })

  return response.data
}

export const getMediaAccount = async (
  advertiserId: number,
  isLoading = false
): Promise<NaverAdsNaverCommerceAccount[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/campaign/sa/shopping/paired-accounts/${advertiserId}`,
    method: Method.GET,
    config: { isLoading }
  })
  return (response.successOrNot === 'Y' ? response.data : []) as NaverAdsNaverCommerceAccount[]
}

export const getChannels = async (customerId: string, sellerId: string, isLoading = false): Promise<BusinessChannel[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/campaign/sa/shopping/business-channels/${customerId}/${sellerId}`,
    method: Method.GET,
    config: { isLoading }
  })
  return (response.successOrNot === 'Y' ? response.data : []) as BusinessChannel[]
}

export const getProducts = async (
  customerID: string,
  sellerId: string,
  channelId: string,
  isLoading = false
): Promise<ProductData[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/campaign/sa/shopping/products/${customerID}/${sellerId}/${channelId}`,
    method: Method.GET,
    config: { isLoading }
  })
  return (response.successOrNot === 'Y' ? response.data : []) as ProductData[]
}

export const createCampaign = async (campaignParams: CampaignParams, isLoading = false) => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/campaign/sa/shopping`,
    method: Method.POST,
    params: {
      bodyParams: campaignParams
    },
    config: { isLoading }
  })
  return response
}

