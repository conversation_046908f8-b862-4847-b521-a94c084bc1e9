import { useState } from 'react'
import type { <PERSON>a, StoryObj } from '@storybook/react'
import TabContainer, { type TabRenderState } from './TabContainer'

const meta: Meta<typeof TabContainer> = {
  title: 'Components/Common/MopUI/TabContainer',
  component: TabContainer,
  tags: ['autodocs'],
  parameters: {
    docs: {
      description: {
        component:
          'A reusable tab component wrapping HeadlessUI Tab. Provides default Tailwind styles internally, and can be fully customized externally via `unstyled` or functional `className`. Supports both Controlled (`selectedIndex`) and Uncontrolled (`defaultIndex`) modes. Improved accessibility with proper disabled state handling and focus management. Enhanced with stable key management and development-time error checking.',
      },
    },
  },
  argTypes: {
    variant: {
      control: { type: 'radio' },
      options: ['fit', 'fill'],
      description: 'Tab button width layout. fit: content width, fill: full container width.',
      table: { type: { summary: '"fit" | "fill"' }, defaultValue: { summary: '"fill"' } },
    },
    selectedIndex: {
      control: { type: 'number' },
      description: 'Selected tab index in controlled mode.',
      table: { type: { summary: 'number' } },
    },
    defaultIndex: {
      control: { type: 'number' },
      description: 'Initial tab index in uncontrolled mode.',
      table: { type: { summary: 'number' }, defaultValue: { summary: '0' } },
    },
    onChange: {
      description: 'Callback function called when tab changes (index) => void',
      table: { type: { summary: '(index: number) => void' } },
    },
    unstyled: {
      control: { type: 'boolean' },
      description: 'Disable ALL default Tailwind styles (buttons, list, panels). Use for complete customization.',
      table: { type: { summary: 'boolean' }, defaultValue: { summary: 'false' } },
    },
    tabListClassName: {
      control: { type: 'text' },
      description: 'Additional classes for tab button list wrapper.',
      table: { type: { summary: 'string' } },
    },
    tabPanelsClassName: {
      control: { type: 'text' },
      description: 'Additional classes for panels wrapper.',
      table: { type: { summary: 'string' } },
    },
    tabPanelClassName: {
      control: { type: 'text' },
      description: 'Classes for each panel. String or (index) => string function.',
      table: { type: { summary: 'string | (index: number) => string' } },
    },
    buttonClassName: {
      control: { type: 'text' },
      description: 'Button classes. String or TabRenderState function.',
      table: {
        type: { summary: 'string | (state: TabRenderState) => string' },
      },
    },
  },
}

export default meta
type Story = StoryObj<typeof TabContainer>

export const Default: Story = {
  render: () => (
    <div className="p-4 bg-[#f9f9fb] border border-[#efefef]">
      <TabContainer>
        <TabContainer.Button>Tab 1</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Default Tab 1 Content</div>
        </TabContainer.Content>

        <TabContainer.Button>Tab 2</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Default Tab 2 Content</div>
        </TabContainer.Content>
      </TabContainer>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'The simplest usage example, rendered with default styles and Uncontrolled mode (`defaultIndex=0`).',
      },
    },
  },
}

export const FitTabs: Story = {
  render: () => (
    <div className="p-4 bg-gray-50">
      <TabContainer variant="fit">
        <TabContainer.Button>Recommended</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Recommended tab content</div>
        </TabContainer.Content>

        <TabContainer.Button>Keywords</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Keywords tab content</div>
        </TabContainer.Content>

        <TabContainer.Button>Negative Keywords</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Negative keywords tab content</div>
        </TabContainer.Content>
      </TabContainer>
    </div>
  ),
}

export const FillTabs: Story = {
  render: () => (
    <div className="p-4 bg-gray-50">
      <TabContainer variant="fill">
        <TabContainer.Button>Recommended</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Recommended tab content</div>
        </TabContainer.Content>

        <TabContainer.Button>Keywords</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Keywords tab content</div>
        </TabContainer.Content>

        <TabContainer.Button>Negative Keywords</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-white">Negative keywords tab content</div>
        </TabContainer.Content>
      </TabContainer>
    </div>
  ),
}

export const ControlledTabs: Story = {
  render: () => {
    const [index, setIndex] = useState(1)
    return (
      <div className="p-4 bg-gray-50 space-y-3">
        <div className="flex gap-2">
          <button className="px-3 py-1 rounded border" onClick={() => setIndex(0)}>
            Go to First
          </button>
          <button className="px-3 py-1 rounded border" onClick={() => setIndex(1)}>
            Go to Second
          </button>
          <button className="px-3 py-1 rounded border" onClick={() => setIndex(2)}>
            Go to Third
          </button>
        </div>
        <TabContainer variant="fit" selectedIndex={index} onChange={(i) => setIndex(i)}>
          <TabContainer.Button>Recommended</TabContainer.Button>
          <TabContainer.Content>
            <div className="p-4 bg-white">Recommended tab content</div>
          </TabContainer.Content>

          <TabContainer.Button>Keywords</TabContainer.Button>
          <TabContainer.Content>
            <div className="p-4 bg-white">Keywords tab content</div>
          </TabContainer.Content>

          <TabContainer.Button>Negative Keywords</TabContainer.Button>
          <TabContainer.Content>
            <div className="p-4 bg-white">Negative keywords tab content</div>
          </TabContainer.Content>
        </TabContainer>
      </div>
    )
  },
}

// unstyled 기능이 완전히 동작하도록 수정된 CustomStyled 스토리
export const CustomStyled: Story = {
  render: () => (
    <div className="p-4 bg-white">
      <TabContainer
        variant="fill"
        unstyled
        tabListClassName="flex w-full gap-0 border-b-2 border-gray-200"
        buttonClassName={({ selected, disabled }: TabRenderState) =>
          `px-4 py-3 text-sm font-semibold transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 ${
            selected 
              ? 'text-indigo-600 border-b-2 border-indigo-600' 
              : 'text-gray-500 hover:text-gray-700 border-b-2 border-transparent'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`
        }
        tabPanelsClassName="mt-6 bg-gray-50 p-4"
        tabPanelClassName={(i) => (i % 2 === 0 ? 'bg-white p-4 rounded shadow-sm' : 'bg-gray-100 p-4 rounded shadow-sm')}
      >
        <TabContainer.Button>Overview</TabContainer.Button>
        <TabContainer.Content>
          <div>Overview content with custom styling. This panel has white background.</div>
        </TabContainer.Content>

        <TabContainer.Button>Details</TabContainer.Button>
        <TabContainer.Content>
          <div>Details content with alternating styling. This panel has gray background.</div>
        </TabContainer.Content>

        <TabContainer.Button disabled>Disabled</TabContainer.Button>
        <TabContainer.Content>
          <div>This tab is disabled and should be skipped during keyboard navigation.</div>
        </TabContainer.Content>
      </TabContainer>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Complete custom styling example using `unstyled=true`. All default styles are removed from buttons, list, and panels, allowing full customization via className props. Uses typed TabRenderState for better type safety.',
      },
    },
  },
}

export const FunctionalClassNames: Story = {
  render: () => {
    const [active, setActive] = useState(0)
    return (
      <div className="p-4 bg-white">
        <TabContainer
          selectedIndex={active}
          onChange={setActive}
          buttonClassName={({ selected, disabled, index }: TabRenderState) =>
            `px-4 py-2 text-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 ${
              selected ? 'text-indigo-600 font-semibold' : 'text-gray-500 hover:text-gray-700'
            } ${disabled ? 'opacity-40 cursor-not-allowed' : ''} ${
              index === 0 ? 'rounded-l-md' : index === 2 ? 'rounded-r-md' : ''
            }`
          }
          tabPanelClassName={(i) => `p-4 rounded-md border ${i === active ? 'bg-indigo-50' : 'bg-white'}`}
        >
          <TabContainer.Button>Overview</TabContainer.Button>
          <TabContainer.Content>
            <div>Selected tab changes panel background color.</div>
          </TabContainer.Content>

          <TabContainer.Button>Details</TabContainer.Button>
          <TabContainer.Content>
            <div>Button classes change dynamically based on selection/index/disabled state.</div>
          </TabContainer.Content>

          <TabContainer.Button disabled>Settings</TabContainer.Button>
          <TabContainer.Content>
            <div>Disabled tab has opacity and cursor styles applied to the button.</div>
          </TabContainer.Content>
        </TabContainer>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story:
          'Example showing dynamic styling by passing functions to `buttonClassName` and `tabPanelClassName` based on selection/disabled/index/current active tab state. Uses exported TabRenderState type for better type safety.',
      },
    },
  },
}

// 새로운 접근성 테스트 스토리 추가
export const AccessibilityTest: Story = {
  render: () => (
    <div className="p-4 bg-white space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Keyboard Navigation Test</h3>
        <p className="text-sm text-gray-600">
          Use Tab key to focus, Arrow keys to navigate between tabs, Space/Enter to select.
          Disabled tabs should be skipped during keyboard navigation.
        </p>
      </div>
      
      <TabContainer variant="fit">
        <TabContainer.Button>First Tab</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-blue-50 rounded">
            <p>First tab content. Try using keyboard navigation:</p>
            <ul className="mt-2 list-disc list-inside text-sm">
              <li>Tab key to focus the tab list</li>
              <li>Arrow keys to move between tabs</li>
              <li>Space or Enter to activate a tab</li>
              <li>Notice how disabled tabs are skipped</li>
            </ul>
          </div>
        </TabContainer.Content>

        <TabContainer.Button disabled>Disabled Tab</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-red-50 rounded">
            This content should not be accessible since the tab is disabled.
          </div>
        </TabContainer.Content>

        <TabContainer.Button>Third Tab</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-green-50 rounded">
            Third tab content. The disabled tab should be skipped when navigating with arrow keys.
          </div>
        </TabContainer.Content>

        <TabContainer.Button>Fourth Tab</TabContainer.Button>
        <TabContainer.Content>
          <div className="p-4 bg-yellow-50 rounded">
            Fourth tab content. Focus indicators should be visible when using keyboard navigation.
          </div>
        </TabContainer.Content>
      </TabContainer>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Test story for keyboard accessibility. Demonstrates proper focus management, disabled tab handling, and focus-visible styling. Use keyboard navigation to test accessibility features.',
      },
    },
  },
}

// 완전 unstyled 테스트 스토리 추가
export const CompletelyUnstyled: Story = {
  render: () => (
    <div className="p-4 bg-white">
      <div className="space-y-2 mb-4">
        <h3 className="text-lg font-semibold">Completely Unstyled Example</h3>
        <p className="text-sm text-gray-600">
          This example uses `unstyled=true` with no additional className props to show the raw, unstyled component.
        </p>
      </div>
      
      <TabContainer unstyled>
        <TabContainer.Button>Raw Tab 1</TabContainer.Button>
        <TabContainer.Content>
          Raw content 1 - no default styles applied
        </TabContainer.Content>

        <TabContainer.Button>Raw Tab 2</TabContainer.Button>
        <TabContainer.Content>
          Raw content 2 - completely customizable
        </TabContainer.Content>

        <TabContainer.Button disabled>Raw Disabled</TabContainer.Button>
        <TabContainer.Content>
          Raw disabled content
        </TabContainer.Content>
      </TabContainer>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Shows the component with `unstyled=true` and no additional styling. All default styles are removed from buttons, list, and panels.',
      },
    },
  },
}

// 안정적인 키 관리 및 동적 탭 테스트 스토리
export const StableKeyManagement: Story = {
  render: () => {
    const [tabs, setTabs] = useState([
      { id: 'home', label: 'Home', content: 'Home content' },
      { id: 'about', label: 'About', content: 'About content' },
      { id: 'contact', label: 'Contact', content: 'Contact content' },
    ])
    
    const addTab = () => {
      const newId = `tab-${Date.now()}`
      setTabs([...tabs, { 
        id: newId, 
        label: `Tab ${tabs.length + 1}`, 
        content: `Dynamic content for ${newId}` 
      }])
    }
    
    const removeLastTab = () => {
      if (tabs.length > 1) {
        setTabs(tabs.slice(0, -1))
      }
    }

    return (
      <div className="p-4 bg-white space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Stable Key Management Test</h3>
          <p className="text-sm text-gray-600">
            Tests stable key generation for dynamic tabs. Keys are based on id or text content rather than array indices.
          </p>
          <div className="flex gap-2">
            <button 
              className="px-3 py-1 rounded border bg-blue-500 text-white hover:bg-blue-600"
              onClick={addTab}
            >
              Add Tab
            </button>
            <button 
              className="px-3 py-1 rounded border bg-red-500 text-white hover:bg-red-600"
              onClick={removeLastTab}
              disabled={tabs.length <= 1}
            >
              Remove Last Tab
            </button>
          </div>
        </div>
        
        <TabContainer variant="fit">
          {tabs.map((tab) => [
            <TabContainer.Button key={`btn-${tab.id}`} id={tab.id}>{tab.label}</TabContainer.Button>,
            <TabContainer.Content key={`content-${tab.id}`} id={tab.id}>
              <div className="p-4 bg-gray-50 rounded">
                <p>{tab.content}</p>
                <p className="text-xs text-gray-500 mt-2">ID: {tab.id}</p>
              </div>
            </TabContainer.Content>
          ]).flat()}
        </TabContainer>
      </div>
    )
  },
  parameters: {
    docs: {
      description: {
        story: 'Demonstrates stable key management for dynamic tabs. Uses id props for consistent keys even when tabs are added/removed. This prevents React reconciliation issues and maintains proper focus management.',
      },
    },
  },
}

// 개발 환경 에러 테스트 (주석 처리된 예시)
export const DevelopmentErrorExample: Story = {
  render: () => (
    <div className="p-4 bg-white space-y-4">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Development Error Handling</h3>
        <p className="text-sm text-gray-600">
          In development mode, mismatched button/content pairs will throw an error instead of just showing a console warning.
        </p>
        <p className="text-xs text-red-600">
          Note: The example below is commented out to prevent breaking the storybook. 
          Uncomment the TabContainer below to test error handling in development mode.
        </p>
      </div>
      
      {/* 
      Uncomment this to test development error handling:
      
      <TabContainer>
        <TabContainer.Button>Button 1</TabContainer.Button>
        <TabContainer.Content>Content 1</TabContainer.Content>
        
        <TabContainer.Button>Button 2</TabContainer.Button>
        // Missing TabContainer.Content - this will throw an error in development
      </TabContainer>
      */}
      
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
        <p className="text-sm">
          To test error handling, uncomment the TabContainer in the story source code. 
          In development mode, it will throw an error for mismatched button/content pairs.
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Example showing how the component handles mismatched button/content pairs. In development mode, it throws an error for early detection. In production, it shows a console warning.',
      },
    },
  },
}


