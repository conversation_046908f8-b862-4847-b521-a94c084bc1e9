#!/bin/bash
# 라이센스 파일 생성 스크립트

set -e  # 오류 발생 시 스크립트 중단

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 함수 정의
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 메인 로직
main() {
    print_info "라이센스 파일 생성 시작..."
    
    # 필요한 파일 존재 확인
    if [ ! -f "package.json" ]; then
        print_error "package.json 파일을 찾을 수 없습니다."
        exit 1
    fi
    
    # 기존 파일 통계 (비교용)
    if [ -f "LEGAL_NOTICE.TXT" ]; then
        OLD_PACKAGE_COUNT=$(grep -c "├─" LEGAL_NOTICE.TXT 2>/dev/null || echo "0")
        # 숫자가 비어있거나 유효하지 않으면 0으로 설정
        if [ -z "$OLD_PACKAGE_COUNT" ] || ! [[ "$OLD_PACKAGE_COUNT" =~ ^[0-9]+$ ]]; then
            OLD_PACKAGE_COUNT=0
        fi
    else
        OLD_PACKAGE_COUNT=0
    fi
    
    # 고정 헤더 설정 (Noto Sans Korean 폰트 라이센스 포함)
    HEADER="LEGAL NOTICE ABOUT OPEN SOURCE SOFTWARE
--------------------------------------
Copyright $(date +%Y) LG CNS, Inc. All rights reserved.
This product contains the open source software detailed below.
Please refer to the indicated open source licenses (as are included following this notice) for the terms and conditions of their use.

Noto Sans Korean	v12-latin	(https://fonts.google.com/noto/specimen/Noto+Sans+KR, SIL Open Font License 1.1)"
    
    # license-checker 명령어 설정
    if ! command -v license-checker &> /dev/null; then
        print_warning "license-checker가 설치되지 않았습니다. yarn을 사용합니다."
        if grep -q '"license:list"' package.json; then
            LICENSE_CMD="yarn license:list"
        else
            LICENSE_CMD="yarn license-checker --plainVertical"
        fi
    else
        LICENSE_CMD="license-checker --plainVertical"
    fi
    
    # 현재 프로젝트 경로 확인
    PROJECT_PATH=$(pwd)
    
    # 새로운 라이센스 정보 생성 (경로 정리 포함)
    print_info "라이센스 정보 수집 중..."
    if ! $LICENSE_CMD > temp_licenses_raw.txt 2>/dev/null; then
        print_error "라이센스 정보 생성에 실패했습니다."
        print_error "다음 명령어를 먼저 실행해보세요:"
        print_error "  yarn add -D license-checker"
        rm -f temp_licenses_raw.txt
        exit 1
    fi
    
    # 절대 경로를 프로젝트 상대 경로로 변경
    print_info "경로 정리 중..."
    sed "s|$PROJECT_PATH/||g" temp_licenses_raw.txt > temp_licenses.txt
    rm -f temp_licenses_raw.txt
    
    # 라이센스 전문 템플릿 파일 확인
    FOOTER_TEMPLATE="license_footer_template.txt"
    if [ ! -f "$FOOTER_TEMPLATE" ]; then
        print_warning "라이센스 전문 템플릿 파일이 없습니다. 라이센스 목록만 포함됩니다."
        FOOTER_TEMPLATE=""
    fi
    
    # 헤더, 라이센스 정보, 라이센스 전문을 결합하여 새 파일 생성
    print_info "새 파일 생성 중..."
    {
        echo "$HEADER"
        echo ""
        cat temp_licenses.txt
        
        # 라이센스 전문 추가 (템플릿 파일이 있는 경우)
        if [ -n "$FOOTER_TEMPLATE" ] && [ -f "$FOOTER_TEMPLATE" ]; then
            echo ""
            echo ""
            cat "$FOOTER_TEMPLATE"
        fi
    } > LEGAL_NOTICE.TXT
    
    # 임시 파일 정리
    rm -f temp_licenses.txt
    
    print_success "LEGAL_NOTICE.TXT 파일이 생성되었습니다!"
    
    # 통계 출력
    if [ -f "LEGAL_NOTICE.TXT" ]; then
        PACKAGE_COUNT=$(grep -c "├─" LEGAL_NOTICE.TXT 2>/dev/null || echo "0")
        LINE_COUNT=$(wc -l < LEGAL_NOTICE.TXT)
        FILE_SIZE=$(du -h LEGAL_NOTICE.TXT | cut -f1)
        
        echo ""
        print_info "📊 생성된 파일 통계:"
        echo "   - 총 패키지 수: $PACKAGE_COUNT"
        echo "   - 총 라인 수: $LINE_COUNT"
        echo "   - 파일 크기: $FILE_SIZE"
        
        # 기존 파일과 비교 (숫자 유효성 검사)
        if [[ "$PACKAGE_COUNT" =~ ^[0-9]+$ ]] && [[ "$OLD_PACKAGE_COUNT" =~ ^[0-9]+$ ]]; then
            DIFF=$((PACKAGE_COUNT - OLD_PACKAGE_COUNT))
        else
            DIFF=0
        fi
        
        echo ""
        print_info "📈 변경사항:"
        echo "   - 기존 패키지 수: $OLD_PACKAGE_COUNT"
        echo "   - 새로운 패키지 수: $PACKAGE_COUNT"
        if [ $DIFF -gt 0 ]; then
            echo -e "   - 변경사항: ${GREEN}+$DIFF${NC} (패키지 추가됨)"
        elif [ $DIFF -lt 0 ]; then
            echo -e "   - 변경사항: ${RED}$DIFF${NC} (패키지 제거됨)"
        else
            echo -e "   - 변경사항: ${YELLOW}변경 없음${NC}"
        fi
        
        # 라이센스 유형 통계
        echo ""
        print_info "📋 라이센스 유형 분석:"
        if grep -q "├─ licenses:" LEGAL_NOTICE.TXT; then
            grep "├─ licenses:" LEGAL_NOTICE.TXT | sed 's/├─ licenses: //' | sort | uniq -c | sort -nr | head -10 | while read count license; do
                echo "   - $license: $count개"
            done
        else
            print_warning "라이센스 정보를 찾을 수 없습니다."
        fi
    fi
    
    echo ""
    print_success "라이센스 파일 생성이 완료되었습니다! 🎉"
    print_info "생성된 파일: LEGAL_NOTICE.TXT"
}

# 스크립트 실행
main "$@"