import React, { ReactElement, useEffect, useState } from 'react';
import { Checkbox, FormControlLabel } from '@material-ui/core';

import { t } from 'i18next';
import { KpiType } from '@models/adEfficiency/AdEfficiency';
import './MultiGoalSettings.scss';
import { ActionType } from '@models/common/CommonConstants';

export interface Props {
  kpis: Map<KpiType, boolean>;
  setKpis: (_kpis: KpiType) => void;
  actionType: ActionType;
}

type SettingChecked = {
  [key in KpiType]?: boolean;
};

const kpiChecks = Object.keys(KpiType).reduce((allKpis, currentKpi) => {
  allKpis[currentKpi as KpiType] = false
  return allKpis
}, {} as SettingChecked)

const MultiGoalSettings: React.FC<Props> = ({ kpis, setKpis, actionType }: Props): ReactElement => {
  const disabledKpis = actionType === ActionType.READ;
  const [settingChecked, setSettingChecked] = useState<SettingChecked>(kpiChecks);

  const handleKpiCheck = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSettingChecked({ ...settingChecked, [e.target.name]: e.target.checked })
    setKpis(e.target.name as KpiType)
  }

  useEffect(() => {
    const existingKpis = { ...settingChecked }
    for(const [kpiKey, _kpiValue] of kpis) {
      existingKpis[kpiKey] = kpis.has(kpiKey)
    }
    setSettingChecked(existingKpis)
  }, []) //eslint-disable-line
  return (
    <section id="budget-opt-config-multi-goal">
      {
        Object.keys(KpiType).map((kpi, i) => (
          <FormControlLabel
            key={i}
            className="kpi-label"
            control={
              <Checkbox
                name={kpi}
                checked={settingChecked[kpi as keyof SettingChecked]}
                disabled={disabledKpis}
                onChange={handleKpiCheck}
              />
            }
            label={t(`common.code.kpis.${kpi}`)}
          />
        ))
      }
    </section>
  );
};

export default MultiGoalSettings;
