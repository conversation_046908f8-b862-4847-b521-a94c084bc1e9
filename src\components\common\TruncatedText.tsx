import { useEffect, useRef, useState } from 'react'

const TruncatedText = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  const textRef = useRef<HTMLDivElement>(null)
  const [isTruncated, setIsTruncated] = useState(false)
  const [textContent, setTextContent] = useState('')

  useEffect(() => {
    if (textRef.current) {
      const element = textRef.current
      const isTextTruncated = element.scrollWidth > element.clientWidth
      setIsTruncated(isTextTruncated)
      setTextContent(element.textContent || '')
    }
  }, [children])

  return (
    <div ref={textRef} className={className} title={isTruncated ? textContent : undefined}>
      {children}
    </div>
  )
}
export default TruncatedText
