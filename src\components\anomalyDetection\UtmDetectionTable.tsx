import React, { ReactElement, useEffect, useMemo } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { selectedUtmAnomalySummary, selectedUtmAnomalySummaryDetails } from '@store/AnomalyDetection'
import { getUtmsAnomalyDetectionDetail } from '@api/dashboard/AbnomalyDetection';
import { FixedLayoutTable } from '@components/common/table'
import UtmDetectionTableFormatter from './UtmDetectionTableFormatter';
import { generateUtmColumns } from '@utils/AnomalyDetectionUtils';
import { UtmSummary } from '@models/anomalyDetection'
import { useAuthority } from "@hooks/common"
import './UrlDetectionTable.scss';
interface Props {
  isExpanded: boolean;
  summary: UtmSummary;
}

const UtmDetectionTable: React.FC<Props> = ({ isExpanded, summary }: Props): ReactElement => {
  const { advertiser } = useAuthority()
  const utmAnomaly = useRecoilValue(selectedUtmAnomalySummary)
  const [utmAnomalyDetail, setUtmAnomalyDetail] = useRecoilState(selectedUtmAnomalySummaryDetails);
  const utmTableColumns = new UtmDetectionTableFormatter(summary.analyticsType)
  const allColumns: any[] = utmTableColumns.getColumnFormat()
  const detectionData = useMemo(() => utmAnomalyDetail.details.map((obj) => Object.create(obj)) || [], [utmAnomalyDetail])

  const getDetectionDetail = async () => {
    const { mediaType, campaignId, analyticsType } = utmAnomaly
    const response = await getUtmsAnomalyDetectionDetail({
      advertiserId: advertiser.advertiserId, mediaType, campaignId })
    if (response.successOrNot === 'Y' && response.data) {
      const details = generateUtmColumns(response.data)
      setUtmAnomalyDetail({ analyticsType, campaignId, details })
    }
  }

  useEffect(() => {
    if (isExpanded && summary.campaignId !== utmAnomalyDetail.campaignId) {
      const { campaignId, analyticsType } = utmAnomaly
      setUtmAnomalyDetail({ analyticsType, campaignId, details: [] })
      getDetectionDetail()
    }
  }, [isExpanded])

  return (
    <FixedLayoutTable
      id="UtmAnomalyDetectionTable"
      columns={allColumns}
      data={detectionData}
      localization={{ body: { emptyDataSourceMessage: 'Loading...' } }}
    />
  )
}

export default UtmDetectionTable;