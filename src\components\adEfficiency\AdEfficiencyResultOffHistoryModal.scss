#AdEfficiencyResultOffHistoryModal {
  .MuiDialog-paper {
    width: fit-content;
    max-width: 936px;
    height: fit-content;
    max-height: 80vh;
    background-color: #fff;
    box-sizing: border-box;

    .MuiDialogTitle-root {
      position: absolute;
      top: 10px;
      right: 10px;
      padding: 0;
      margin: 0;
    }

    .MuiDialogContent-root {
      padding: 0px;

      .creative-history-table {
        border-collapse: collapse;
        th,
        td {
          color: var(--point_color);
          text-align: center;
          font-size: 12px;
          b {
            font-size: 14px;
          }
          padding: 0 10px;
        }
        th {
          height: 56px;
          background-color: var(--bg-table-main);
          &.status {
            width: 100px;
          }
          &.description {
            padding: 0 50px;
          }
        }
        td {
          height: 60px;
          border-bottom: 1px solid var(--border-spacer);
          &.creative {
            text-align: left;
          }
        }
      }
    }
  }
}
