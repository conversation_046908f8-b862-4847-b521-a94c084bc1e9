import * as ApiUtil from '@utils/ApiUtil';
import { getOverview } from '@api/dashboard/Overview';

describe('getOverview', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('991 - 응답이 정상인 경우, overview를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        advertiserId: 1,
        advertiserName: '시원스쿨랩',
        media: [
          { type: 'NAVER', useYn: 'Y', status: 'OK' },
          { type: 'KAKAO', useYn: 'Y', status: 'OK' },
          { type: 'GOOGLE', useYn: 'Y', status: 'OK' },
          { type: 'META', useYn: 'N' },
        ],
        analytics: [{ type: 'GA', useYn: 'Y', status: 'OK' }],
        status: {
          sa: { collection: 'OK', prejection: 'OK', flight: 'OK' },
          da: { collection: 'OK', prejection: 'OK' },
        },
      },
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getOverview(1);
    expect(response).toEqual(responseMock.data);
  });

  it('991 - 응답이 비정상인 경우, undefined 를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
    };

    mockCallApi.mockResolvedValue(responseMock);
    const response = await getOverview(1);
    expect(response).toEqual(undefined);
  });
});
