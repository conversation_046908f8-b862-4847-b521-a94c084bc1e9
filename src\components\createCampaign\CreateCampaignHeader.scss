.createCampaignHeader {
  .py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
  .commerce {
    --height: 40px;
    width: 100%;
    height: var(--height);
    border: 1px solid #bbbdcd;
    border-radius: calc(var(--height) / 2);

  }
  .px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .bg-white {
    background-color: #fff;
  }

  .border-b {
    border-bottom-width: 1px;
  }

  .border-gray-200 {
    border-color: #e5e7eb;
  }

  .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .space-x-2 > * + * {
    margin-left: 0.5rem;
  }

  .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .font-bold {
    font-weight: 700;
  }

  .text-gray-900 {
    color: #111827;
  }

  .text-blue-500 {
    color: #3b82f6;
  }

  .hover\:text-blue-600:hover {
    color: #2563eb;
  }

  .cursor-help {
    cursor: help;
  }

  // Style cho OutlinedInput search
  .search-input-container {
    display: flex;
    align-items: center;
  }

  .MuiInputBase-root {
    width: 370px;
    height: 35px;
    padding-right: 7px;
    font-size: 14px;
    font-weight: 300;
    color: var(--mop20-text-color, #333);
    border: 1px solid var(--mop20-table-border, #e5e7eb);
    border-radius: 22.5px;
    background-color: #fff;

    fieldset {
      border: none;
    }

    &.MuiInput-underline {
      &::before,
      &::after {
        display: none;
      }
    }

    .MuiInputBase-input {
      width: 100%;
      height: 23px;
      padding: 1px 15px 1px 15px;
      text-align: right;
      font-size: 14px;
      font-weight: 300;
      color: #333;
      box-sizing: border-box;

      &:focus {
        background: none;
      }

      &::placeholder {
        font-family: 'NotoSans', sans-serif;
        font-weight: 400;
        font-size: 13px;
        color: #707070;
      }
    }

    input {
      padding: 1px 15px 1px 15px;
    }

    &.MuiInputBase-adornedEnd .search-icon {
      width: 26px;
      height: 26px;
      cursor: pointer;
    }
  }
} 
.MuiSvgIcon-root{
  font-size: 20px !important;
}