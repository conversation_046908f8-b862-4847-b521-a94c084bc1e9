import TagManager from 'react-gtm-module'
import './MopActionButton.scss'
import { PropsWithChildren, forwardRef } from 'react'

interface Props {
  label?: string
  onClick: () => void
  rounded?: 'full' | 'half' | 'sm' | 'md'
  theme?: 'primary' | 'secondary' | 'cancel' | 'create'
  type?: 'button' | 'submit' | 'reset'
  contained?: boolean
  qaId?: string
  gtmId?: string
  disabled?: boolean
}

const MopActionButton = (
  {
    children,
    label,
    type = 'button',
    onClick,
    rounded = 'half',
    theme = 'primary',
    contained = true,
    qaId,
    gtmId,
    disabled
  }: PropsWithChildren<Props>,
  ref: any
) => {
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (gtmId) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'click',
          gtm_id: gtmId
        }
      })
    }
    onClick()
  }

  return (
    <button
      ref={ref}
      type={type}
      className={`
        mop-action-button
        mop-action-button--radius-${rounded}
        mop-action-button--${theme}
        ${contained ? 'mop-action-button--contained' : ''}
      `}
      data-qa={qaId}
      data-gtm-id={gtmId}
      onClick={handleClick}
      disabled={disabled}
    >
      {label} {children}
    </button>
  )
}

export default forwardRef<PropsWithChildren<Props>, any>(MopActionButton)
