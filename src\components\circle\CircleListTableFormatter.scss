#CircleListPage {
  .tooltip-icon {
    margin: 0 2px -2px 0;
    display: inline;
    vertical-align: baseline;
  }
}

.status {
  font-weight: 500;
  &.CREATE_UNIT_NEEDED {
    color: #ed6d47;
  }
  &.INSPECTION_ERROR {
    color: #b51b32;
  }
}

.icon-wrapper {
  padding: 0 10px;
  border-radius: 73px;
  background: var(--bg-gray-light);
}
.text-icon-wrapper {
  display: inline-flex;
  gap: 2px;
  align-items: center;
  border-right: 2px solid #e4e7ee;
  margin: 6px 0;
  padding: 2px 10px;

  &:last-child {
    border-right: 0;
  }
  .count-number {
    padding-left: 3px;
    color: var(--color-blue-darker);
    font-size: 14px;
    font-weight: 500;
  }
}

.setting--disable {
  color: #909090;
  text-align: center;
  font-size: 14px;
  font-weight: 700;

  &::before {
    content: '!';
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-right: 4px;
    color: white;
    background-color: #909090;
    border-radius: 100%;
    font-size: 12px;
  }
}
