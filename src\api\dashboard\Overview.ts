import CommonResponse from '@models/common/CommonResponse';
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import AdvertiserStatus from '@models/dashboard/AdvertiserStatus';

export const getOverview = async (advertiserId: number, isLoading = false, signal?: AbortSignal) => {
  const response: CommonResponse<AdvertiserStatus> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/dashboard/overview/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
    signal: signal,
  });
  return response.successOrNot === 'Y' ? response.data : undefined;
};
