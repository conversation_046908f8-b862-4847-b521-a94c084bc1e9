/* istanbul ignore file */

import { callApi, Method } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';
import {
  SearchOptimizationDetail,
  SearchOptimizationInfo,
  SearchOptimizationList,
  SearchOptimizationsRequest,
  SearchOptimizationSaveRequest,
  SearchOptimizationSaveRequestWithoutAdgroup,
  SearchOptimizationCostsRequest,
  SearchOptimizationCost,
} from '@models/optimization/SearchOptimization';
import { pageSizeOptions } from '@models/common/CommonConstants';

export const getSearchOptimizations = async (
  param: SearchOptimizationsRequest,
  isLoading = true
): Promise<SearchOptimizationList> => {
  const ret: SearchOptimizationList = {
    totalCount: 0,
    pageSize: pageSizeOptions[0],
    pageIndex: 1,
    optimizations: [],
    currentAdgroupsCount: 0,
    maxAdgroupsCount: 50,
    currentItemsCount: 0,
    maxItemsCount: -1,
  };

  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa',
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  if (response.successOrNot === 'Y' && (response.data.totalCount as number) > 0) {
    ret.totalCount = response.data.totalCount as number;
    ret.optimizations = response.data.optimizations as SearchOptimizationInfo[];
    ret.pageSize = param.pageSize;
    ret.pageIndex = param.pageIndex;
    
    if (response.data.currentAdgroupsCount !== undefined) {
      ret.currentAdgroupsCount = response.data.currentAdgroupsCount as number;
    }
    if (response.data.maxAdgroupsCount !== undefined) {
      ret.maxAdgroupsCount = response.data.maxAdgroupsCount as number;
    }
    if (response.data.currentItemsCount !== undefined) {
      ret.currentItemsCount = response.data.currentItemsCount as number;
    }
    if (response.data.maxItemsCount !== undefined) {
      ret.maxItemsCount = response.data.maxItemsCount as number;
    }
  }

  return ret;
};

export const updateSearchOptimizationBidYn = async (optimizationId: number, bidYn: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/${optimizationId}`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        bidYn: bidYn,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const deleteSearchOptimization = async (optimizationId: number, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/${optimizationId}`,
    method: Method.DELETE,
    config: {
      isLoading: isLoading,
    },
  });
};

export const createSearchOptimization = async (param: SearchOptimizationSaveRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa',
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateSearchOptimization = async (
  optimizationId: number,
  param: SearchOptimizationSaveRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/${optimizationId}`,
    method: Method.PUT,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateSearchOptimizationWithoutAdgroup = async (
  optimizationId: number,
  param: SearchOptimizationSaveRequestWithoutAdgroup,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/${optimizationId}/config`,
    method: Method.PATCH,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const getSearchOptimizationDetail = async (
  optimizationId: number,
  isLoading = true
): Promise<SearchOptimizationDetail> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: `/v1/optimizations/sa/${optimizationId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as SearchOptimizationDetail;
};

export const getSearchOptimizationCosts = async (param: SearchOptimizationCostsRequest, isLoading = false): Promise<SearchOptimizationCost[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/costs',
    method: Method.POST,
    params: {
      bodyParams: param,
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as SearchOptimizationCost[];
};

export const getSearchOptimizationNewAdgroup = async (param: {advertiserId: number}, isLoading = true): Promise<string> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/optimizations/sa/new-adgroup',
    method: Method.GET,
    params: {
      queryParams: {
        ...param,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as string;
};
