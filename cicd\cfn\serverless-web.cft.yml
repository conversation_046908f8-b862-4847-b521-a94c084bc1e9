---
# Purpose: CloudFormation Template to create S3 Hosted Website via CloudFront
# This template can be used with a static hosted website approach for a modern web app, such as <PERSON>ular, React, VueJS, etc.
# SETUP requires:
#  - Route53 Hosted Zones be created for each environment
#  - an ACL cert be provisioned in the us-east-1 region of each AWS Account
#  - an IAM Role be configured for the CI server
#
# The template creates three main resources:
# 1) The Website bucket where the static files will live
# 2) The CloudFront distribution which serves the static assets over CDN
# 3) The Route53 DNS record set for your custom domain.
#
# It also creates the following resources to help support this setup and establish good logging and security.
# - resCloudFrontOriginAccessIdentity, which is an identity used to grant CloudFront permission to read the S3 bucket
# - resS3WebsiteLoggingBucket, for logging changes to your static assets
# - resS3WebsiteBucketPolicy, for defining permissions on your S3 website bucket

AWSTemplateFormatVersion: 2010-09-09
Description: The Modern Web Serverless Website

# Input Parameters
Parameters:
  paramAppName: # TODO: This is currently unused. It is intended to be used to import the IAM Role ARN for the CI server
    Type: String
    Description: A short name to be used in the resource names for this project
    Default: service-definition-ui
  paramEnvironment:
    Type: String
    Description: Which environment do you want to deploy to? (dev,qa,stg,prd)
    AllowedValues:
      - dev
      - qa
      - stg
      - prd
    Default: dev
  paramBranch:
    Type: String
    Description: What branch are you building?
    Default: ""
  paramDomainName:
    Type: String
  paramHostedZone:
    Type: String
  paramAcmCertificateArn:
    Type: String
  paramLoggingBucketID:
    Type: String
  paramWafGlobalWebAclArn:
    Type: String
Conditions:
  condBuildCloudFront: !Equals [!Ref paramBranch, ''] # paramBranch 불러오는데 Null 값일 경우 condBuildCloudFront 발동
  condEnvironmentIsProd: !Equals [!Ref paramEnvironment, 'prd'] # paramEnvironment 불러오는데 prd 일 경우 condEnvironmentIsProd 발동

# All Resources
Resources:
  resCloudFrontOriginAccessIdentity:
    Type: 'AWS::CloudFront::CloudFrontOriginAccessIdentity'
    Condition: condBuildCloudFront
    Properties:
      CloudFrontOriginAccessIdentityConfig:
        Comment: !If
          - condBuildCloudFront     # condition_name : feature 가 아닐 경우 '' 와 !Equals 이므로 true
          - !Sub "${paramDomainName}"                  # value_if_true    현재 CDS 에서 정한 MI naming rule 은 AppName은 안들어가서 삭제.
          - !Sub "${paramBranch}.${paramDomainName}"   # value_if_false

  # resS3WebsiteLoggingBucket:      # 로깅 버킷은 랜딩존 생성시 관리하므로 따로 생성하지 않는다.
  #   Type: AWS::S3::Bucket
  #   Condition: condBuildCloudFront
  #   Properties:
  #     AccessControl: LogDeliveryWrite
  #     BucketName: !If
  #       - condBuildCloudFront
  #       - !Sub "${paramAppName}.${paramDomainName}"
  #       - !Sub "${paramBranch}-${paramAppName}.${paramDomainName}"
  #     LifecycleConfiguration:
  #       Rules:
  #         - ExpirationInDays: 730
  #           Status: Enabled
  #     BucketEncryption:
  #       ServerSideEncryptionConfiguration:
  #         - ServerSideEncryptionByDefault:
  #             SSEAlgorithm: AES256

  resS3WebsiteBucket:
    Type: AWS::S3::Bucket
    Properties:
      OwnershipControls:
          Rules:
            - ObjectOwnership: ObjectWriter
      BucketName: !If
        - condBuildCloudFront
        - !Sub "${paramDomainName}" # 현재 CDS 에서 정한 MI naming rule 은 AppName은 안들어가서 삭제.
        - !Sub "${paramBranch}.${paramDomainName}"
      WebsiteConfiguration:
        IndexDocument: index.html
        ErrorDocument: index.html
      VersioningConfiguration: !If
        - condBuildCloudFront
        - Status: Enabled
        - !Ref AWS::NoValue # No versioning for branch builds so that deleting the bucket is easier
      LoggingConfiguration: !If
        - condBuildCloudFront
        - DestinationBucketName: !Ref paramLoggingBucketID
          LogFilePrefix: !Sub ${paramDomainName}  # prefix 도 naming rule을 따라 AppName 뺄지 결정!
        - !Ref AWS::NoValue # No bucket logging needed for branch builds
      PublicAccessBlockConfiguration: !If
        - condBuildCloudFront
        - BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        - BlockPublicAcls: true
          BlockPublicPolicy: false
          IgnorePublicAcls: false
          RestrictPublicBuckets: false
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256

  resS3WebsiteBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref resS3WebsiteBucket
      PolicyDocument:
        Version: 2012-10-17
        Statement: !If
          - condBuildCloudFront     # Stg와 Prd를 통합했다.
          - - Action:
                - s3:List*
                - s3:GetObject
              Effect: Allow
              Resource:
                - !Sub "arn:aws:s3:::${resS3WebsiteBucket}/*"
                - !Sub "arn:aws:s3:::${resS3WebsiteBucket}"
              Principal:
                AWS: !Sub "arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${resCloudFrontOriginAccessIdentity}"
            - Action:
                - s3:*
              Effect: Deny
              Resource:
                - !Sub "arn:aws:s3:::${resS3WebsiteBucket}/*"
                - !Sub "arn:aws:s3:::${resS3WebsiteBucket}"
              Condition:
                Bool:
                  aws:SecureTransport:
                    - false
              Principal: "*"
          - - Action:
                - s3:GetObject
              Effect: Allow
              Resource: !Sub "arn:aws:s3:::${resS3WebsiteBucket}/*"
              Principal: "*"
              Condition:
                IpAddress:
                  aws:SourceIp:
                    - "*************/32"
                    - "*************/32"
                    - "************/32"
                    - "*************/32"
                    - "************/32"
                    - "************/32"
                    - "***********/32"
            - Action:
                - s3:GetObject
              Effect: Allow
              Resource: !Sub "arn:aws:s3:::${resS3WebsiteBucket}/*"
              Principal: "*"
              Condition:
                IpAddress:
                  aws:VpcSourceIp:
                    - "10.0.0.0/8"

  # resS3WebsiteLoggingBucketPolicy:  # Logging Bucket을 랜딩존 생성시 자동으로 생성해 주고 그 ID를 mi-infra에서 parameter store에
  #   Type: AWS::S3::BucketPolicy     # paramLoggingBucketID로 등록해 주었기 때문에 더 이상 Logging Bucket 관련 자원은 생성 불필요
  #   Condition: condBuildCloudFront
  #   Properties:
  #     Bucket: !Ref resS3WebsiteLoggingBucket
  #     PolicyDocument:
  #       Statement:
  #         - Action:
  #             - s3:GetObject
  #           Effect: Allow
  #           Resource: !Sub 'arn:aws:s3:::${resS3WebsiteLoggingBucket}/*'
  #           Principal: !If
  #             - condBuildCloudFront
  #             - AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${resCloudFrontOriginAccessIdentity}'
  #             - '*'
  #         - Action:
  #             - s3:*
  #           Effect: Deny
  #           Resource:
  #             - !Sub 'arn:aws:s3:::${resS3WebsiteLoggingBucket}/*'
  #             - !Sub 'arn:aws:s3:::${resS3WebsiteLoggingBucket}'
  #           Condition:
  #             Bool:
  #               aws:SecureTransport:
  #                 - false
  #           Principal: '*'
  resCloudFrontWebsite:
    Type: AWS::CloudFront::Distribution
    Condition: condBuildCloudFront
    Properties:
      DistributionConfig:
        Aliases:
          - !Sub "${paramDomainName}"
          - !Sub "www.${paramDomainName}"
        Origins:
          - DomainName: !Sub ${resS3WebsiteBucket}.s3.amazonaws.com
            Id: !Ref resS3WebsiteBucket
            S3OriginConfig:
              OriginAccessIdentity: !Sub origin-access-identity/cloudfront/${resCloudFrontOriginAccessIdentity}
        Enabled: true
        HttpVersion: http2
        Comment: !Sub '${paramBranch} ${paramEnvironment} environment Serverless Website' # SETUP - You might want something more descriptive here
        DefaultRootObject: index.html
        CustomErrorResponses:
          - ErrorCachingMinTTL: 300
            ErrorCode: 404
            ResponseCode: 200
            ResponsePagePath: /index.html
          - ErrorCachingMinTTL: 300
            ErrorCode: 403
            ResponseCode: 200
            ResponsePagePath: /index.html
        Logging:
          IncludeCookies: false
          Bucket: !Sub ${paramLoggingBucketID}.s3.amazonaws.com
          Prefix: !Sub ${paramAppName}.${paramDomainName}
        DefaultCacheBehavior:
          AllowedMethods:
            - GET
            - HEAD
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # Managed Cache policy - CachingDisabled
          Compress: true
          ForwardedValues:
            QueryString: false
            Cookies:
              Forward: none
          DefaultTTL: 60
          MaxTTL: 60
          MinTTL: 60
          ResponseHeadersPolicyId: !Ref resCloudFrontBehavior
          TargetOriginId: !Ref resS3WebsiteBucket
          ViewerProtocolPolicy: redirect-to-https
        PriceClass: PriceClass_200
        ViewerCertificate:
          AcmCertificateArn: !Ref paramAcmCertificateArn
          SslSupportMethod: sni-only
          MinimumProtocolVersion: TLSv1.2_2021    # 1.0이랑 1.1은 웹 취약점 점검사항 기준을 충족하지 못하므로 1.2로 변경
        WebACLId: !Ref paramWafGlobalWebAclArn

  resCloudFrontBehavior:
    Type: AWS::CloudFront::ResponseHeadersPolicy
    Condition: condBuildCloudFront
    Properties:
      ResponseHeadersPolicyConfig:
        CustomHeadersConfig:
          Items:
            - Header: Cache-Control
              Override: false
              Value: no-cache, no-store, must-revalidate
        Name: Cache-Control

  resRoute53RecordSet:
    Type: "AWS::Route53::RecordSet"
    Properties:
      Name: !Ref resS3WebsiteBucket
      Type: !If [condBuildCloudFront, A, CNAME]
      HostedZoneId: !Ref paramHostedZone
      TTL: !If
        - condBuildCloudFront
        - !Ref AWS::NoValue # TTL is invalid for A Records
        - 300
      ResourceRecords: !If
        - condBuildCloudFront
        - !Ref AWS::NoValue # When using CloudFront, we create an A Record Alias, so ResourceRecords isn't needed
        - - !GetAtt [resS3WebsiteBucket, WebsiteURL]
      AliasTarget: !If
        - condBuildCloudFront
        - DNSName: !GetAtt resCloudFrontWebsite.DomainName
          HostedZoneId: Z2FDTNDATAQYW2 # This ID is used for all CloudFront domains
        - !Ref AWS::NoValue # Branch builds use a CNAME instead of an A record

  resHealthCheck:
    Type: "AWS::Route53::HealthCheck"
    Condition: condEnvironmentIsProd
    Properties:
      HealthCheckConfig:
        Type: "HTTPS"
        FullyQualifiedDomainName: !Sub "${paramDomainName}"
        RequestInterval: "30"
        FailureThreshold: "3"
        EnableSNI: True
      HealthCheckTags:
        - Key: Name
          Value: !Sub "${paramEnvironment}-${paramAppName}"

Outputs:
  outWebsiteUrl:
    Description: URL Domain for the deployed website
    Value: !Join
      - ''
      - - !If [condBuildCloudFront, 'https://', 'http://']
        - !Ref resS3WebsiteBucket
  outBucketName:
    Description: Name of the created S3 bucket.
    Value: !Ref resS3WebsiteBucket
