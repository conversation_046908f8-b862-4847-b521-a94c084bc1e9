import {
  CompetitorInfo,
  KeywordRankMonitoringTablePerDate,
  KeywordRankMonitoringChartResult,
} from '@models/rankMaintenance/KeywordRankMonitoring';
import * as ApiUtil from '@utils/ApiUtil';
import {
  getChartTypeKeywordRankMonitorings,
  getKeywordRankMonitoringCompetitors,
  getTableTypeKeywordRankMonitorings,
} from './KeywordRankMonitoring';

describe('KeywordRankMonitoring', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('257 - 키워드 순위 모니터링 차트 데이터 조회 응답이 정상인 경우, 차트용 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        ranks: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0, 1, 2, 3, 4],
        targetRanks: [1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 4, 4, 4, 4],
        bidAmounts: [
          10000, 11000, 11000, 11000, 11000, 12000, 12000, 12000, 13000, 13000, 14000, 14000, 10000, 11000, 11000,
          11000, 11000, 12000, 12000, 12000, 13000, 13000, 14000, 14000,
        ],
        competitors: [
          {
            displayUrl:
              'test1.lgcns.com/longlengthurl1234567890123456789012345678901234567890123456789012345678909999999999',
            alias: 'alias1long1234567890',
            ranks: [3, 3, 3, 2, 2, 1, 3, 3, 4, 4, 4, 1, 4, 4, 2, 2, 0, 0, 0, 0, 1, 1, 1, 1],
          },
          {
            displayUrl: 'www.naver.com/',
            alias: 'alias2',
            ranks: [2, 2, 2, 2, 0, 0, 0, 0, 2, 2, 2, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2],
          },
          {
            displayUrl: 'www.siwon.com',
            alias: 'alias3',
            ranks: [3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 7, 7, 7, 7, 3, 3, 3, 3],
          },
          {
            displayUrl: 'test5.lgcns.com/monitoringYsystemInputYalias',
            alias: 'alias4',
            ranks: [4, 4, 4, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 2, 2, 2, 4, 4, 4, 4, 4, 2, 2, 2],
          },
          {
            displayUrl: 'test6.lgcns.com/monitoringYsystemInputYadTitlealias',
            alias: 'alias5',
            ranks: [3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 2, 2, 3, 4, 4, 4, 3],
          },
        ],
      },
    };

    const requestParam = {
      keywordMonitoringId: 1,
      startDate: '20220505',
      endDate: '20220505',
    };

    const response = responseMock.data;

    mockCallApi.mockResolvedValue(responseMock);
    const chartData = await getChartTypeKeywordRankMonitorings(requestParam, true);
    expect(chartData).toEqual(response);
  });

  it('257 - 키워드 순위 모니터링 차트 데이터 조회 응답이 비정상인 경우, 빈 오브젝트를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const requestParam = {
      keywordMonitoringId: 1,
      startDate: '20220505',
      endDate: '20220505',
    };

    const response: KeywordRankMonitoringChartResult[] = [];

    mockCallApi.mockResolvedValue(responseMock);
    const chartData = await getChartTypeKeywordRankMonitorings(requestParam, true);
    expect(chartData).toEqual(response);
  });

  it('257 - 키워드 순위 모니터링 테이블 데이터 조회 응답이 정상인 경우, 테이블용 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          date: '2022.05.28',
          ranks: [1, 2, 2, 3, 3, 4, 2, 2, 1, 2, 0, 2, 1, 1, 4, 4, 3, 3, 2, 1, 1, 2, 2, 1],
          targetRanks: [4, 2, 3, 2, 1, 4, 4, 3, 2, 5, 0, 1, 2, 3, 3, 3, 3, 3, 5, 4, 3, 5, 5, 4],
        },
        {
          date: '2022.05.29',
          ranks: [1, 2, 2, 3, 3, 4, 2, 2, 1, 2, 0, 2, 1, 1, 4, 4, 3, 3, 2, 1, 1, 2, 2, 1],
          targetRanks: [4, 2, 3, 2, 1, 4, 4, 3, 2, 5, 0, 1, 2, 3, 3, 3, 3, 3, 5, 4, 3, 5, 5, 4],
        },
        {
          date: '2022.06.02',
          ranks: [1, 2, 2, 3, 3, 4, 2, 2, 1, 2, 0, 2, 1, 1, 4, 4, 3, 3, 2, 1, 1, 2, 2, 1],
          targetRanks: [4, 2, 3, 2, 1, 4, 4, 3, 2, 5, 0, 1, 2, 3, 3, 3, 3, 3, 5, 4, 3, 5, 5, 4],
        },
      ],
    };

    const requestParam = {
      keywordMonitoringId: 1,
      startDate: '20220505',
      endDate: '20220510',
    };

    const response = responseMock.data;

    mockCallApi.mockResolvedValue(responseMock);
    const tableData = await getTableTypeKeywordRankMonitorings(requestParam, true);
    expect(tableData).toEqual(response);
  });

  it('257 - 키워드 순위 모니터링 테이블 데이터 조회 응답이 비정상인 경우, 빈배열의 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const requestParam = {
      keywordMonitoringId: 1,
      startDate: '20220505',
      endDate: '20220510',
    };

    const response: KeywordRankMonitoringTablePerDate[] = [];

    mockCallApi.mockResolvedValue(responseMock);
    const tableData = await getTableTypeKeywordRankMonitorings(requestParam, true);
    expect(tableData).toEqual(response);
  });

  it('257 - 경쟁사 목록 조회 응답이 정상인 경우, 경쟁사 목록 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: [
        {
          monitoringYn: 'Y',
          systemInputYn: 'N',
          displayUrl:
            'test1.lgcns.com/longlengthurl1234567890123456789012345678901234567890123456789012345678909999999999',
          adTitle: '타이틀 long~~~~~~~~~~~~~~~~~~ 12345678901234567890123456789012345678901234567890',
          alias: 'alias1long1234567890',
        },
        {
          monitoringYn: 'Y',
          systemInputYn: 'N',
          displayUrl: 'www.naver.com/',
          adTitle: '',
          alias: 'alias2',
        },
        {
          monitoringYn: 'Y',
          systemInputYn: 'N',
          displayUrl: 'www.siwon.com',
          adTitle: '',
          alias: 'alias3',
        },
        {
          monitoringYn: 'N',
          systemInputYn: 'N',
          displayUrl: 'www.lge.com',
          adTitle: '',
          alias: '',
        },
        {
          monitoringYn: 'Y',
          systemInputYn: 'Y',
          displayUrl: 'test5.lgcns.com/monitoringYsystemInputYalias',
          adTitle: '',
          alias: 'alias4',
        },
        {
          monitoringYn: 'Y',
          systemInputYn: 'Y',
          displayUrl: 'test6.lgcns.com/monitoringYsystemInputYadTitlealias',
          adTitle: '타이틀6',
          alias: 'alias5',
        },
      ],
    };

    const response = responseMock.data;

    mockCallApi.mockResolvedValue(responseMock);
    const competitors = await getKeywordRankMonitoringCompetitors(1, true);
    expect(competitors).toEqual(response);
  });

  it('257 - 경쟁사 목록 데이터 조회 응답이 비정상인 경우, 빈배열의 데이터를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const response: CompetitorInfo[] = [];

    mockCallApi.mockResolvedValue(responseMock);
    const competitors = await getKeywordRankMonitoringCompetitors(1, true);
    expect(competitors).toEqual(response);
  });
});
