import { ReactComponent as IconNormal } from '@components/assets/images/icon_utm_normal.svg';
import { ReactComponent as IconNotExist } from '@components/assets/images/icon_utm_not_exist.svg';
import { ReactComponent as IconNotMatch } from '@components/assets/images/icon_utm_not_match.svg';

const UtmRuleStatusIcons = ({ status }: { status: string[] }) => {
  if (status.length === 0) return <IconNormal />
  return (
    <>
      {status.map((code) => (
        <>
          {code === 'NOT_EXIST' && <span className='utm-rules-icon'><IconNotExist /></span>}
          {code === 'NOT_MATCH' && <span className='utm-rules-icon'><IconNotMatch /></span>}
        </>
      ))}
    </>
  )
}

export default UtmRuleStatusIcons