import { AdInspectStatusEnum, AdvertisingStatus, ProductData, ProductStatus } from "@models/createCampaign/CreateCampaign"
import { NaverCommerceBodyParams } from "@models/report/NaverCommerce"

export const isProductEnabled = (product: ProductData): boolean => {
  const isProductStatusValid = product.status === ProductStatus.SALE
  const isAdvertisingStatusInValid =
    product.adStatus === AdvertisingStatus.INELIGIBLE &&
    product.adInspectStatus !== AdInspectStatusEnum.ELIGIBLE &&
    product.adInspectStatus !== AdInspectStatusEnum.APPROVED
  return isProductStatusValid && !isAdvertisingStatusInValid
}

export const convertProductStatus = (type: ProductStatus, t: (key: string) => string) => {
  switch (type) {
    case ProductStatus.ALL:
      return t('createCampaign.createModal.statusFilter.all')
    case ProductStatus.WAIT:
      return t('createCampaign.createModal.statusFilter.wait')
    case ProductStatus.SALE:
      return t('createCampaign.createModal.statusFilter.sale')
    case ProductStatus.OUTOFSTOCK:
      return t('createCampaign.createModal.statusFilter.outOfStock')
    case ProductStatus.UNADMISSION:
      return t('createCampaign.createModal.statusFilter.unAdmission')
    case ProductStatus.REJECTION:
      return t('createCampaign.createModal.statusFilter.rejection')
    case ProductStatus.SUSPENSION:
      return t('createCampaign.createModal.statusFilter.suspension')
    case ProductStatus.CLOSE:
      return t('createCampaign.createModal.statusFilter.close')
    case ProductStatus.PROHIBITION:
      return t('createCampaign.createModal.statusFilter.prohibition')
    case ProductStatus.DELETE:
      return t('createCampaign.createModal.statusFilter.delete')
    default:
      return ""
  }
}

export const convertAdStatus = (type: AdvertisingStatus | null, t: (key: string) => string) => {
  switch (type) {
    case AdvertisingStatus.ALL:
      return t("createCampaign.createModal.adStatusFilter.all")
    case AdvertisingStatus.ELIGIBLE:
      return t("createCampaign.createModal.adStatusFilter.on")
    case AdvertisingStatus.INELIGIBLE:
      return t("createCampaign.createModal.adStatusFilter.off")
    default:
      return t('createCampaign.label.productTable.noHistory')
  }
}

export const convertAdReviewStatus = (type: AdInspectStatusEnum | null, t: (key: string) => string) => {
  switch (type) {
    case AdInspectStatusEnum.UNDER_REVIEW:
      return t("createCampaign.createModal.adReviewStatus.underReview")
    case AdInspectStatusEnum.APPROVED:
      return t("createCampaign.createModal.adReviewStatus.approved")
    case AdInspectStatusEnum.ELIGIBLE:
      return t("createCampaign.createModal.adReviewStatus.eligible")
    case AdInspectStatusEnum.PENDING:
      return t("createCampaign.createModal.adReviewStatus.pending")
    case AdInspectStatusEnum.DENIED:
      return t("createCampaign.createModal.adReviewStatus.denied")
    case AdInspectStatusEnum.LIMITED_ELIGIBLE:
      return t("createCampaign.createModal.adReviewStatus.limitedEligible")
    case AdInspectStatusEnum.PAUSED:
      return t("createCampaign.createModal.adReviewStatus.paused")
    case AdInspectStatusEnum.DELETED:
      return t("createCampaign.createModal.adReviewStatus.deleted")
    default:
      return t('createCampaign.label.productTable.noHistory')
  }
}
