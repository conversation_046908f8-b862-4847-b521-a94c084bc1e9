import { callApi, Method } from '@utils/ApiUtil';
import { CommonCode } from '@models/common/CommonCode';
import CommonResponse from '@models/common/CommonResponse';
import { Service } from '@models/common/Service';

export const getCommonCode = async (groupCode: string, isLoading = true): Promise<CommonCode[]> => {
  const response: CommonResponse = await callApi({
    service: Service.MOP_BE,
    url: '/v1/commonCode',
    method: Method.GET,
    params: {
      queryParams: {
        groupCode: groupCode,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : null) as CommonCode[];
};
