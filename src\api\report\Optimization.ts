/* istanbul ignore file */
import { callApi, Method, downloadByteArray, openDownloadLink } from '@utils/ApiUtil';
import CommonResponse from '@models/common/CommonResponse';
import {
  OptimizationReportByAdType,
  OptimizationReportBodyParams,
  OptimizationReportTable
} from '@models/report/Optimization'
import { IndicatorSummary, IndicatorSummaryQuery, ReportTableQuery } from '@models/report/Common'
import { Service } from '@models/common/Service';

export const getOptimizationReportOptimizations = async (advertiserId: number, isLoading = true): Promise<OptimizationReportByAdType[]|undefined> => {
  const response: CommonResponse<OptimizationReportByAdType[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/opt/${advertiserId}/optimizations`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });
  return (response.successOrNot === 'Y' ? response.data : []);
};

export const getOptimizationReportSummary = async (
  advertiserId: number,
  queryParams: IndicatorSummaryQuery,
  bodyParams: OptimizationReportBodyParams,
  isLoading = true
): Promise<IndicatorSummary|undefined> => {
  const response: CommonResponse<IndicatorSummary> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/opt/${advertiserId}/summary`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : undefined);
};

export const getOptimizationReportTable = async (
  advertiserId: number,
  queryParams: ReportTableQuery,
  bodyParams: OptimizationReportBodyParams,
  isLoading = true
): Promise<OptimizationReportTable|undefined> => {
  const response: CommonResponse<OptimizationReportTable> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/report/opt/${advertiserId}/table`,
    method: Method.POST,
    params: {
      queryParams: { ...queryParams },
      bodyParams,
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : undefined)
};
