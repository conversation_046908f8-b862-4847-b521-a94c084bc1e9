import CustomTooltip from '@components/common/CustomTooltip';
import React, { ReactNode } from 'react';

import './ChartLegendRow.scss';
import { UnitType } from '@models/common/ChartData';
import _ from 'lodash';

interface Props {
  backgroundColor: string;
  className?: string;
  label: string;
  value: string | ReactNode;
  useTooltip?: boolean;
  unitType: UnitType;
}

const ChartLegendRow: React.FC<Props> = ({
  backgroundColor,
  className,
  label,
  value,
  useTooltip,
  unitType = 'PERCENT',
}) => {
  return (
    <div className={`chart-legend-row ${className}`}>
      <span className="label-area">
        <div className="indicator" style={{ backgroundColor: backgroundColor }} />
        {useTooltip ? (
          <CustomTooltip title={label} placement="bottom-start">
            <span className="label">{label}</span>
          </CustomTooltip>
        ) : (
          <span className="label">{label}</span>
        )}
      </span>
      {_.isString(value) ? (
        <span className="value-area">
          <em>{value}</em>
          {unitType === 'PERCENT' && '%'}
        </span>
      ) : (
        value
      )}
    </div>
  );
};

export default ChartLegendRow;
