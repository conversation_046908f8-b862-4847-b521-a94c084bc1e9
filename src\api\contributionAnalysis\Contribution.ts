/* istanbul ignore file */

import { callApi, downloadByteArray, Method, openDownloadLink } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import {
  GetContributionDownloadRequest,
  GetContributionDownloadTrendRequest,
  ContributionItems,
  GetContributionResponse,
  GetContributionTrendRequest,
  GetContributionTrendResponse,
} from '@models/contributionAnalysis/Contribution';
import CommonResponse from '@models/common/CommonResponse';
import { AxiosResponse } from 'axios';

export const getContribution = async (advertiserId: number, isLoading = true) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/contributions/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetContributionResponse;
};

export const getContributionTrend = async (
  advertiserId: number,
  queryParam: GetContributionTrendRequest,
  isLoading = true
) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/contributions/trend/${advertiserId}`,
    method: Method.GET,
    params: {
      queryParams: {
        ...queryParam,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as GetContributionTrendResponse[];
};

export const getContributionItemsList = async (advertiserId: number, isLoading = true) => {
  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/contributions/items/${advertiserId}`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return (response.successOrNot === 'Y' ? response.data : null) as ContributionItems;
};

export const downloadContribution = async (
  advertiserId: number,
  queryParams: GetContributionDownloadRequest,
  isLoading = true
) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/contributions/${advertiserId}/download`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
    params: {
      queryParams: {
        ...queryParams,
      },
    },
  });

  if (response.data) {
    openDownloadLink(response);

    return null;
  } else {
    return (response as unknown) as CommonResponse;
  }
};

export const downloadContributionRawData = async (
  advertiserId: number,
  queryParams: GetContributionDownloadTrendRequest,
  isLoading = true
) => {
  const response: CommonResponse | AxiosResponse = await downloadByteArray({
    service: Service.MOP_BE,
    url: `/v1/contributions/trend/${advertiserId}/download`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
    params: {
      queryParams: {
        ...queryParams,
      },
    },
  });

  if (response.data) {
    openDownloadLink(response);

    return null;
  } else {
    return (response as unknown) as CommonResponse;
  }
};
