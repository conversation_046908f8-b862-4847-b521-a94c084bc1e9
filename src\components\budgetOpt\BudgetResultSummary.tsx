import { Switch } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import {
  Budget<PERSON><PERSON>,
  SpendingAnalyze<PERSON>hart,
  EstimatedImpactChart
} from './Charts'
import BudgetChartLegend from './Charts/BudgetChartLegend';
import { BudgetOptimizationInfo, ContributionType, KpiItem } from '@models/budgetOpt/BudgetOpt';
import { ReactComponent as PerformImpactHigh } from '@assets/icon/icon-perform-impact-high.svg'
import { ReactComponent as PerformImpactLow } from '@assets/icon/icon-perform-impact-low.svg'
import { ReactComponent as PerformImpactNormal } from '@assets/icon/icon-perform-impact-normal.svg';
import { ReactComponent as PerformImpactArrow } from '@components/assets/images/long-arrow.svg';
import { DvKpiType } from '@models/optimization/Kpi';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg';
import CommonTooltip from '@components/common/CommonTooltip';
import TooltipCard from '@components/common/tooltip/TooltipCard';
import './BudgetResultSummary.scss';
import { YNFlag } from '@models/common';

interface Props {
  optimization: BudgetOptimizationInfo;
  budgetPrevTwoWeeks: any;
  budgetRecommend: any;
  kpiImpact: {[key: string]: number};
}

const defaultKpiItems: KpiItem[] = [
  { kpiType: DvKpiType.IMPRESSIONS, kpiValue: -1 },
  { kpiType: DvKpiType.VIEWS, kpiValue: -1 },
  { kpiType: DvKpiType.CLICKS, kpiValue: -1 },
  { kpiType: DvKpiType.CONVERSIONS, kpiValue: -1 },
  { kpiType: DvKpiType.REVENUE, kpiValue: -1 }
]

const BudgetResultSummary = ({ optimization, budgetPrevTwoWeeks, budgetRecommend, kpiImpact }: Props) => {
  const { t } = useTranslation();
  const { mediaBudgetFix, engineRunDate, kpis, contributionType, budgetChangeRate, performImproveRate } = optimization
  const isSelectedKpi = (kpiType: DvKpiType) => {
    if (optimization.defaultYn === YNFlag.Y) return true
    return kpis.find(kpi => kpi.kpiType === kpiType) ? true : false
  }
  const changeRate = budgetChangeRate * 0.01
  const lowRange = 0 < performImproveRate && performImproveRate <= (0.5 * changeRate)
  const normalRange = (0.5 * changeRate) < performImproveRate && performImproveRate <= changeRate
  const highRange = changeRate < performImproveRate
  const performImpact = lowRange ? 'LOW' : normalRange ? 'NORMAL' : highRange ? 'HIGH' : ''

  return (
    <section className="budget-result-report-summary">
      <div className="budget-opt-analyze">
        <div className="budget-analyze-table">
          <div className="budget-analyze-table__row">
            <div className="label__box">
              <span className="label__en">{t('optimization.label.budgetOpt.result.label.date')}</span>
              {/* <span className="label__ko">{t('optimization.label.budgetOpt.result.label.date.ko')}</span> */}
            </div>
            <span className='chip budget-date'>{ engineRunDate }</span>
          </div>
          <div className="budget-analyze-table__row">
            <div className="label__box">
              <span className="label__en">{t('optimization.label.budgetOpt.result.label.goal')}</span>
              {/* <span className="label__ko">{t('optimization.label.budgetOpt.result.label.goal.ko')}</span> */}
            </div>
            <div className='kpi-chip-box'>
              {Object.keys(DvKpiType).map((kpiItem, index) => (
                <span
                  key={index}
                  className={`chip
                  ${isSelectedKpi(kpiItem as DvKpiType) ? kpiItem : 'disabled'}`}
                >
                  { t(`common.code.kpis.${kpiItem}`) }
                </span>
              ))}
            </div>
          </div>
          <div className="budget-analyze-table__row">
            <div className="label__box">
              <span className="label__en">{t('optimization.label.budgetOpt.result.label.variance')}</span>
              {/* <span className="label__ko">{t('optimization.label.budgetOpt.result.label.variance.ko')}</span> */}
            </div>
            <span>{`${budgetChangeRate}% ↓`}</span>
          </div>
          <div className="budget-analyze-table__row">
            <div className="label__box">
              <span className="label__en">{t('optimization.label.budgetOpt.result.label.ratio')}</span>
              {/* <span className="label__ko">{t('optimization.label.budgetOpt.result.label.ratio.ko')}</span> */}
            </div>
            <div>
              <Switch
                className="budget-opt-switch readOnly"
                edge="end"
                color="primary"
                checked={mediaBudgetFix === 'Y'}
                onChange={() => {}}
              />
            </div>
          </div>
          <div className="budget-analyze-table__row">
            <div className="label__box">
              <span className="label__en">{t('optimization.label.budgetOpt.result.label.attribution')}</span>
              {/* <span className="label__ko">{t('optimization.label.budgetOpt.result.label.attribution.ko')}</span> */}
            </div>
            <div>
              { contributionType === ContributionType.NONE
                ? (
                  <Switch
                    className="budget-opt-switch readOnly"
                    edge="end"
                    color="primary"
                    checked={false}
                    onChange={() => {}}
                  />
                )
                : <span>{t(`common.code.contributionType.${contributionType}`)}</span>
              }
            </div>
          </div>
        </div>

        <div className="budget-analyze-chart">
          <CommonTooltip
            title={
              <>
                <h1>{t('optimization.label.budgetOpt.tooltip.spending.title')}</h1>
                <p>{t('optimization.label.budgetOpt.tooltip.spending.contents.0')}</p>
                <p className="indent2">{t('optimization.label.budgetOpt.tooltip.spending.contents.1')}</p>
                <p className="indent2">{t('optimization.label.budgetOpt.tooltip.spending.contents.2')}</p>
              </>
            }
            placement="bottom"
            arrow
          >
            <span className='label'>
              <AdviceMarkIcon />
              <strong>{t('optimization.label.budgetOpt.result.label.WeeklySpending')}​</strong>
            ​</span>
          </CommonTooltip>
          <div id="budget-analyze-chart" className='chart-container flex items-center'>
            <div className="relative w-[133px] h-[133px]">
              <SpendingAnalyzeChart spending={budgetPrevTwoWeeks} />
              <span className='chart-center'>{t('optimization.label.budgetOpt.result.label.spending')}</span>
            </div>
            <div>
              <BudgetChartLegend regendInfo={budgetPrevTwoWeeks} type="spending" />
            </div>
          </div>
        </div>

      </div>
      <div className="budget-opt-prediction">
        <div className="budget-opt-recommendation">
          <CommonTooltip
            title={
              <TooltipCard
                tKey={'optimization.label.budgetOpt.tooltip.recommend'}
                type={'paragraph'} />
            }
            placement="bottom"
            arrow
          >
            <span className='label'>
              <AdviceMarkIcon />
              <strong>{t('optimization.label.budgetOpt.result.label.recommendation')}</strong>
            </span>
          </CommonTooltip>
          <div id="recommendation-chart" className='chart-container flex items-center'>
            <div className="relative w-[160px] h-[160px]">
              <BudgetChart recommend={budgetRecommend} />
              <span className='chart-center'>{t('optimization.label.budgetOpt.result.label.budget')}</span>
            </div>
            <div>
              <BudgetChartLegend regendInfo={budgetRecommend} type="recommend" />
            </div>
          </div>

        </div>
        <div className="budget-opt-estimated">
          <CommonTooltip
            title={
              <>
                <h1>{t('optimization.label.budgetOpt.tooltip.estimated.title')}</h1>
                <p>{t('optimization.label.budgetOpt.tooltip.estimated.contents.0')}</p>
                <p className="indent2">{t('optimization.label.budgetOpt.tooltip.estimated.contents.1')}</p>
                <p className="indent2">{t('optimization.label.budgetOpt.tooltip.estimated.contents.2')}</p>
                <p className="indent2">{t('optimization.label.budgetOpt.tooltip.estimated.contents.3')}</p>
              </>
            }
            placement="bottom"
            arrow
          >
            <span className='label'><AdviceMarkIcon />
              <strong>{t('optimization.label.budgetOpt.result.label.estimatedImpact')}</strong>
            </span>
          </CommonTooltip>
          <div className='estimated-box'>
            <div className='chart-container'>
              <EstimatedImpactChart kpiImpact={kpiImpact} selectedKpis={
                optimization.defaultYn === YNFlag.Y ? defaultKpiItems : optimization.kpis} />
            </div>
            <div className='avg-impact'>
              <span className='avg-impact-label'>
                {t('optimization.label.budgetOpt.result.label.avgImpact')}
              </span>
              <div className='avg-impact-rate'>
                <span className='avg-impact-num'>{Math.abs(performImproveRate * 100).toFixed(1)}<span className='avg-impact-pct'>%</span></span>
                <span className={performImproveRate < 0 ? 'arror-rotate' : ''}>
                  <PerformImpactArrow />
                </span>
              </div>
              { performImpact &&
                <div className='perform-impact__box'>
                  <div className='perform-impact__icon'>
                    { performImpact === 'HIGH' && <PerformImpactHigh width={24} /> }
                    { performImpact === 'LOW' && <PerformImpactLow width={24} /> }
                    { performImpact === 'NORMAL' && <PerformImpactNormal width={24} /> }
                  </div>
                  <div className='perform-impact__label'>
                    <span className='perform-impact__label-en'>
                      {t(`optimization.label.budgetOpt.list.performImpact.${performImpact}`)}
                    </span>
                    {/* <span className='perform-impact__label-ko'>
                      {t(`optimization.label.budgetOpt.list.performImpact.${performImpact}.ko`)}
                    </span> */}
                  </div>
                </div>
              }
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default BudgetResultSummary