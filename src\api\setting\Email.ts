/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil'
import { Service } from '@models/common/Service'
import CommonResponse from '@models/common/CommonResponse'
import { EmailConfig, EmailType, EmailTypeConfig, ConfigBase } from '@models/setting/Email'

export const getEmailConfig = async (isLoading = true) => {
  const response: CommonResponse<EmailConfig[]> = await callApi({
    service: Service.MOP_BE,
    url: '/v1/setting/email/config',
    method: Method.GET,
    config: { isLoading }
  })

  return response
}

export const getEmailTypeConfig = async (emailType: EmailType, isLoading = true) => {
  const response: CommonResponse<EmailTypeConfig[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/email/${emailType}`,
    method: Method.GET,
    config: { isLoading }
  })

  return response
}

export const updateEmailTypeConfig = async (emailType: EmailType, config: ConfigBase[], isLoading = true) => {
  const response: CommonResponse<EmailTypeConfig[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/email/${emailType}`,
    method: Method.PUT,
    params: {
      bodyParams: config
    },
    config: { isLoading }
  })

  return response
}
