import * as ApiUtil from '@utils/ApiUtil';
import { updateDashboardSetting } from '@api/setting/DashboardSetting';
import { DashboardSetting } from '@models/setting/DashboardSetting';

describe('updateDashboardSetting', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('1025 - 대시보드 설정이 정상적으로 완료되면 true를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {},
    };

    const requestParam: DashboardSetting[] = [
      { advertiserId: 17, sortOrder: 1 },
      { advertiserId: 14, sortOrder: 2 },
      { advertiserId: 18, sortOrder: 3 },
      { advertiserId: 3, sortOrder: 4 },
      { advertiserId: 12, sortOrder: 5 },
      { advertiserId: 22, sortOrder: 6 },
      { advertiserId: 28, sortOrder: 7 },
      { advertiserId: 27, sortOrder: 8 },
      { advertiserId: 25, sortOrder: 9 },
      { advertiserId: 13, sortOrder: 10 },
      { advertiserId: 26, sortOrder: 11 },
      { advertiserId: 1, sortOrder: 12 },
    ];

    mockCallApi.mockResolvedValue(responseMock);
    const reportResponse = await updateDashboardSetting(requestParam, true);
    expect(reportResponse).toEqual(true);
  });

  it('1025 - 대시보드 설정 저장에 실패하면 false를 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const requestParam: DashboardSetting[] = [];

    mockCallApi.mockResolvedValue(responseMock);
    const reportResponse = await updateDashboardSetting(requestParam, true);
    expect(reportResponse).toEqual(false);
  });
});
