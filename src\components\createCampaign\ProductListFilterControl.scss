#product-list-filter-control-wrapper {
  .text-red-allert {
    color: #e10106;
  }
}

.product-table-wraper {
  width: 100%;
  height: 100%;

  .virtualized-data-row .virtualized-data-cell {
    .product-image img {
      width: 40px !important;
      height: 40px !important;
    }

    .virtualized-data-cell {
      padding: 0;
    }
  }
}


#ProductListFilterControl {
  .mop-input {
    input::placeholder {
      font-size: 14px;
      color: gray;
    }

    fieldset {
      border-color: #efefef;
    }
  }

  .wrapper-input {
    .MuiInputBase-root:hover .MuiOutlinedInput-notchedOutline {
      border: 1px solid #efefef;
    }

    .MuiInputBase-root.Mui-focused .MuiOutlinedInput-notchedOutline {
      border: 1px solid #17171780 !important;
    }
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  button,
  li[aria-selected='false'],
  li[aria-selected='false']:hover {
    font-size: 14px;
    font-weight: 500;
  }

  // /* Firefox */
  // input[type=number] {
  //   -moz-appearance: textfield;
  // }
  .placeholder-input {
    color: #999999;
    text-align: center;
  }

  .optimization-search-area {
    display: flex;
    justify-content: flex-end;

    .search-area {
      width: 100%;
      height: 40px;
    }

    .MuiInputBase-root.disable-input {
      background-color: #f3f4f6;
    }

    .MuiInputBase-root {
      width: 100%;
      height: 40px;
      padding-right: 18px;
      font-size: 14px;
      font-weight: 300;
      color: var(--mop20-text-color);
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #efefef;

      &.MuiInput-underline {

        &::before,
        &::after {
          display: none;
        }
      }

      .MuiInputBase-input {
        width: 100%;
        height: 23px;
        padding: 1px 15px 1px 15px;
        text-align: right;
        font-size: 14px;
        font-weight: 300;
        color: #333;
        box-sizing: border-box;

        &:focus {
          background: none;
        }

        &::placeholder {
          font-family: 'NotoSans';
          font-weight: 400;
          font-size: 14px;
          color: #707070;
        }
      }

      input {
        padding: 1px 15px 1px 15px;
      }

      &.MuiInputBase-adornedEnd .search-icon {
        width: 20px;
        height: 20px;
        cursor: pointer;
      }
    }
  }

  .MuiTypography-body1 {
    font-size: 14px;
    font-weight: 500;
    font-family: 'Pretendard', 'sans-serif',
  }

  .product-filter-title {
    font-size: 12px;
    color: #999999;
  }

  
}