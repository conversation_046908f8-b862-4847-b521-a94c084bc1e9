import React, { useState } from 'react';
import './DvOptimizationResultChart.scss';
import { TooltipInfo } from '@models/common/TooltipInfo';
import DoughnutsChart from '@components/common/chart/DoughnutsChart';
import ChartLegend from '@components/common/chart/ChartLegend';
import useDvReportDoughnut from '@components/budgetOptimization/hook/UseDvReportDoughnut';
import { GetDvReportResponse } from '@models/budgetOptimization/DvReport';
import { t } from 'i18next';
import { Grid } from '@material-ui/core';
import useCustomTooltip from '@components/common/hook/UseCustomTooltip';

interface Props {
  report?: GetDvReportResponse;
}

const DvOptimizationResultChart: React.FC<Props> = ({ report }) => {
  const { tooltipInfo, customTooltip } = useCustomTooltip({});
  const { mediaChartData, mediaLegendData } = useDvReportDoughnut({
    report,
  });

  const options: any = {
    maintainAspectRatio: false,
    legend: {
      display: false,
      position: 'right',
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: false,
        external: customTooltip,
      },
    },
    elements: {
      arc: {
        borderWidth: 0,
      },
    },
  };

  return (
    <Grid container id="DvOptimizationResultChart">
      <Grid item id="doughnuts-chart">
        <div>
          <span className="chart-name">{t('optimization.label.DvOptimizationResultModal.results')}</span>
          <DoughnutsChart chartData={mediaChartData} options={options} tooltipInfo={tooltipInfo} />
        </div>
      </Grid>
      <Grid item id="chart-legends" direction="column">
        <ChartLegend legendData={mediaLegendData} />
      </Grid>
    </Grid>
  );
};

export default DvOptimizationResultChart;
