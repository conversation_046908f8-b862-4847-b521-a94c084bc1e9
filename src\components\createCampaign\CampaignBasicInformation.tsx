import { Dispatch, SetStateAction } from 'react'
import { useTranslation } from 'react-i18next'
import { Link } from 'react-router-dom'
import { Box, InputAdornment } from '@material-ui/core'
import { Radio } from '@components/common'
import { MopButton } from '@components/common/buttons'
import { CampaignBasicInfo, DeviceTypeEnum, FormErrors, UseDailyBudgetTypeEnum } from '@models/createCampaign/CreateCampaign'
import './CampaignBasicInformation.scss'
import { MuiInput } from '@components/common/mopUI'

interface Props {
  basicInfo: CampaignBasicInfo
  setBasicInfo: Dispatch<SetStateAction<CampaignBasicInfo>>
  handleCreateButtonClick: () => void
  disableButton: boolean
  formErrors: FormErrors
  setFormErrors: Dispatch<SetStateAction<FormErrors>>
}

const CampaignBasicInformation: React.FC<Props> = ({
  basicInfo,
  setBasicInfo,
  handleCreateButtonClick,
  disableButton,
  formErrors,
  setFormErrors
}) => {
  const { t } = useTranslation()
  const handleCampaignNameChange = (name: string, inputValue: string) => {
    if (inputValue.length > 30) {
      inputValue = inputValue.slice(0, 30)
    }
    
    setBasicInfo((prev) => ({
      ...prev,
      [name]: inputValue
    }))
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  }

  const handleBudgetValueChange = (name: string, val: string) => {
    setBasicInfo((prev) => ({
      ...prev,
      dailyBudget: Number(val)
    }))
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  }

  return (
    <Box className="CampaignBasicInformation font-pretendard">
      <div className="text-xl font-bold mb-8">{t('createCampaign.createModal.basicCampaign.label')}</div>
      <div className="flex flex-col mb-8 gap-2 ">
        <div className="font-bold text-sm">{t('createCampaign.createModal.basicCampaign.name')}</div>
        <div className='wrapper-input'>
          <MuiInput
            name='campaignName'
            hasFocus
            onChange={(e) => { handleCampaignNameChange("campaignName", e.target.value) }}
            placeholder={t('createCampaign.createModal.basicCampaign.name')}
            value={basicInfo.campaignName}
            inputProps={{ maxLength: 30 }}
          />
          {formErrors.campaignName && <div className='text-alert mt-3'>{t(formErrors.campaignName)}</div>}
        </div>
      </div>
      <div className="flex flex-col mb-8 gap-2">
        <div className="font-bold text-sm">{t('createCampaign.createModal.basicCampaign.budget')}</div>
        <div className="flex gap-5 font-medium h-[35px] relative">
          <Radio
            name={UseDailyBudgetTypeEnum.UNLIMITED}
            value={UseDailyBudgetTypeEnum.UNLIMITED}
            checked={basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.UNLIMITED}
            disabled={false}
            label={t('createCampaign.createModal.basicCampaign.noRestriction')}
            onChange={(e) => {
              setBasicInfo({
                ...basicInfo,
                useDailyBudget: e.target.value,
                dailyBudget: ''
              } as any)
            }}
          />
          <Radio
            name={UseDailyBudgetTypeEnum.LIMITED}
            value={UseDailyBudgetTypeEnum.LIMITED}
            checked={basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.LIMITED}
            disabled={false}
            label={t('createCampaign.createModal.basicCampaign.direct')}
            onChange={(e) => {
              setBasicInfo({
                ...basicInfo,
                useDailyBudget: e.target.value
              } as any)
            }}
          />
          {basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.LIMITED && (
            <div className='wraper-input-daily-buget wrapper-input'>
              <MuiInput
                hasFocus
                name="dailyBudget"
                onChange={(e) => { handleBudgetValueChange("dailyBudget", e.target.value) }}
                placeholder={t('createCampaign.createModal.basicCampaign.holderBudgetInput')}
                value={basicInfo.dailyBudget === 0 ? '' : basicInfo.dailyBudget}
                numberFormatProps={{
                  allowNegative: false,
                  decimalSeparator: '.',
                  thousandSeparator: true
                }}
                endAdornment={
                  basicInfo.dailyBudget ? (
                    <InputAdornment position="end" className="currency-budget">
                      {t('createCampaign.createModal.productFilter.currency')}
                    </InputAdornment>
                  ) : null
                }
                useNumberFormat
                inputProps={{ maxLength: 13 }}
              />
              <div className="vat-excluded">{t('createCampaign.createModal.basicCampaign.vatExcluded')}</div>
              
            </div>
          )}
        </div>
       {basicInfo.useDailyBudget === UseDailyBudgetTypeEnum.LIMITED && formErrors.dailyBudget && <p className='text-alert mt-5'>{t(formErrors.dailyBudget)}</p>}
      </div>
      <div className="flex flex-col mb-8 gap-2">
        <div className="font-bold text-sm">{t('createCampaign.createModal.basicCampaign.selectType')}</div>
        <div className="flex gap-5 font-medium">
          <Radio
            name={DeviceTypeEnum.ALL}
            value={DeviceTypeEnum.ALL}
            checked={basicInfo.deviceType === DeviceTypeEnum.ALL}
            label={t('createCampaign.createModal.basicCampaign.all')}
            onChange={(e) => {
              setBasicInfo({
                ...basicInfo,
                deviceType: e.target.value
              } as any)
            }}
          />
          <Radio
            name={DeviceTypeEnum.PC}
            value={DeviceTypeEnum.PC}
            checked={basicInfo.deviceType === DeviceTypeEnum.PC}
            label={t('createCampaign.createModal.basicCampaign.pc')}
            onChange={(e) => {
              setBasicInfo({
                ...basicInfo,
                deviceType: e.target.value
              } as any)
            }}
          />
          <Radio
            name={DeviceTypeEnum.MOBILE}
            value={DeviceTypeEnum.MOBILE}
            checked={basicInfo.deviceType === DeviceTypeEnum.MOBILE}
            label={t('createCampaign.createModal.basicCampaign.mobile')}
            onChange={(e) => {
              setBasicInfo({
                ...basicInfo,
                deviceType: e.target.value
              } as any)
            }}
          />
        </div>
      </div>
      <div className="mt-auto flex justify-center gap-4 font-pretendard">
        <Link to="/campaign">
          <MopButton
            bgColor="#ffffff"
            textColor="#333333"
            customStyle={{ padding: '10px 40px', fontSize: '16px', border: '1px solid #171717' }}
            label={t('createCampaign.createModal.buttons.back')}
            contained={true}
            onClick={() => { }}
          />
        </Link>
        <MopButton
          label={t('createCampaign.createModal.buttons.create')}
          customStyle={{ padding: '10px 20px', fontSize: '16px' }}
          bgColor="#171717"
          textColor="#ffffff"
          contained={true}
          onClick={() => handleCreateButtonClick()}
          disabled={disableButton}
        />
      </div>
    </Box>
  )
}

export default CampaignBasicInformation
