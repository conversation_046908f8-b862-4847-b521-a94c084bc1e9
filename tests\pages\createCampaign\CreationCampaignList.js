/// <reference types="Cypress" />

import BasePage from '@pages/BasePage';

class CreationCampaignList extends BasePage {
  // Selectors
  CREATE_CAMPAIGN_PAGE = '.create-campaign-page';
  CREATE_BUTTON = '[data-testid="createButton"]';
  CAMPAIGN_NAME_INPUT = '#outlined-adornment-weight';
  SEARCH_BUTTON = '[data-testid="searchButton"]';
  CREATE_CAMPAIGN_TABLE = '#createCampaignList';
  CREATE_FORM_PAGE = '#createFormPage';
  
  // Modal selectors
  MODAL_CONTENT = '.modal-content';
  PRODUCT_LIST = '.product-list';
  SUBMIT_BUTTON = '.submit-btn';
  CANCEL_BUTTON = '.cancel-btn';

  visit() {
    super.visit('/campaign');
  }

  // Actions
  clickCreateButton() {
    cy.get(this.CREATE_BUTTON).click();
  }

  typeCampaignName(text) {
    cy.get(this.CAMPAIGN_NAME_INPUT).type(text);
  }

  clickSearchButton() {
    cy.get(this.SEARCH_BUTTON).click();
  }

  clickSubmitButton() {
    cy.get(this.SUBMIT_BUTTON).click();
  }

  clickCancelButton() {
    cy.get(this.CANCEL_BUTTON).click();
  }

  assertCreateCampaignPageDisplayed() {
    cy.get(this.CREATE_CAMPAIGN_PAGE).should('be.visible');
  }

  assertCreateButtonExists() {
    cy.get(this.CREATE_BUTTON).should('be.visible');
  }

  assertCampaignTableExists() {
    cy.get(this.CREATE_CAMPAIGN_TABLE).should('be.visible');
  }
  assertModalOpened() {
    cy.get(this.MODAL_CONTENT).should('be.visible');
  }

  assertModalClosed() {
    cy.get(this.MODAL_CONTENT).should('not.exist');
  }

  assertCampaignInTable(campaignName) {
    cy.get(this.CREATE_CAMPAIGN_TABLE)
      .should('contain.text', campaignName);
  }

  assertCampaignCount(count) {
    cy.get(this.CREATE_CAMPAIGN_TABLE)
      .find('tbody tr')
      .should('have.length', count);
  }

  assertCreateFormPage() {
    cy.get(this.CREATE_FORM_PAGE).should('be.visible');
  }
}

export default CreationCampaignList; 