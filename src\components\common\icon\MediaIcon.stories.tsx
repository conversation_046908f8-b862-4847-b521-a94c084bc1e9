// src/components/common/icon/MediaIcon.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import MediaIcon from './MediaIcon';
import { AnalyticsType, MediaAnalyticsType, MediaType } from '@models/common';

const meta: StorybookMeta<typeof MediaIcon> = {
  title: 'Components/Common/Icon/MediaIcon',
  component: MediaIcon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    mediaType: {
      control: 'select',
      options: [
        ...Object.values(MediaType),
        ...Object.values(AnalyticsType),
        ...Object.values(MediaAnalyticsType),
      ],
      description: 'Media type',
    },
    size: {
      control: { type: 'range', min: 16, max: 128, step: 4 },
      description: 'Icon size (px)',
    },
    filled: {
      control: 'boolean',
      description: 'Whether to fill the background color',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 기본 스토리
export const Default: Story = {
  args: {
    mediaType: MediaType.NAVER,
    size: 32,
    filled: true,
  },
};

// Media Types
export const Naver: Story = {
  args: {
    mediaType: MediaType.NAVER,
    size: 32,
    filled: true,
  },
};

export const Kakao: Story = {
  args: {
    mediaType: MediaType.KAKAO,
    size: 32,
    filled: true,
  },
};

export const Google: Story = {
  args: {
    mediaType: MediaType.GOOGLE,
    size: 32,
    filled: true,
  },
};

export const Meta: Story = {
  args: {
    mediaType: MediaType.META,
    size: 32,
    filled: true,
  },
};

export const Criteo: Story = {
  args: {
    mediaType: MediaType.CRITEO,
    size: 32,
    filled: true,
  },
};

// Analytics Types
export const GA4: Story = {
  args: {
    mediaType: AnalyticsType.GA4,
    size: 32,
    filled: true,
  },
};

export const AppsFlyer: Story = {
  args: {
    mediaType: AnalyticsType.APPSFLYER,
    size: 32,
    filled: true,
  },
};

export const Airbridge: Story = {
  args: {
    mediaType: AnalyticsType.AIRBRIDGE,
    size: 32,
    filled: true,
  },
};

// Media Analytics Types
export const ApiCenter: Story = {
  args: {
    mediaType: MediaAnalyticsType.APICENTER,
    size: 32,
    filled: true,
  },
};

// 크기 변형
export const SmallSize: Story = {
  args: {
    mediaType: MediaType.NAVER,
    size: 24,
    filled: true,
  },
};

export const LargeSize: Story = {
  args: {
    mediaType: MediaType.NAVER,
    size: 48,
    filled: true,
  },
};

// 배경색 변형
export const Unfilled: Story = {
  args: {
    mediaType: MediaType.NAVER,
    size: 32,
    filled: false,
  },
};

// 모든 미디어 타입 그리드
export const AllMediaTypes: Story = {
  render: () => (
    <div style={{ 
      display: 'grid', 
      gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', 
      gap: '16px',
      padding: '16px'
    }}>
      {/* Media Types */}
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.NAVER} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>NAVER</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.KAKAO} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>KAKAO</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.GOOGLE} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>GOOGLE</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.META} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>META</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.CRITEO} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>CRITEO</div>
      </div>
      
      {/* Analytics Types */}
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={AnalyticsType.GA4} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>GA4</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={AnalyticsType.APPSFLYER} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>APPSFLYER</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={AnalyticsType.AIRBRIDGE} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>AIRBRIDGE</div>
      </div>
      
      {/* Media Analytics Types */}
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaAnalyticsType.APICENTER} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>APICENTER</div>
      </div>
    </div>
  ),
};

// 다양한 크기 변형
export const SizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '16px',
      padding: '16px'
    }}>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.NAVER} size={16} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>16px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.NAVER} size={24} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>24px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.NAVER} size={32} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>32px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.NAVER} size={48} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>48px</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <MediaIcon mediaType={MediaType.NAVER} size={64} filled={true} />
        <div style={{ marginTop: '8px', fontSize: '12px' }}>64px</div>
      </div>
    </div>
  ),
}; 